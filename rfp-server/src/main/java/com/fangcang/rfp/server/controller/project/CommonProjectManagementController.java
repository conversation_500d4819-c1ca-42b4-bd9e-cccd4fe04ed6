package com.fangcang.rfp.server.controller.project;

import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.BidStrategyResponse;
import com.fangcang.rfp.common.dto.response.ProjectBasicInfoResponse;
import com.fangcang.rfp.common.dto.response.QueryProjectHotelPriceDetailResponse;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.server.core.shiro.token.manager.TokenManager;
import com.fangcang.util.DateUtil;
import com.itextpdf.text.DocumentException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/24 18:03
 */
@RestController
@RequestMapping("/commonProjectManagement")
public class CommonProjectManagementController {

    private static final Logger logger = LoggerFactory.getLogger(CommonProjectManagementController.class);

    @Autowired
    private ProjectManagementService projectManagementService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private ProjectHotelBidStrategyService projectHotelBidStrategyService;

    @Autowired
    private UserService userService;

    /**
     * 招标项目管理
     *
     * @param projectManagementQueryRequest
     * @return
     */
    @RequestMapping(value = "/queryProjectManagementInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryProjectManagementInfo(@RequestBody ProjectManagementQueryRequest projectManagementQueryRequest) {
        Response response = new Response();
        if (projectManagementQueryRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        // 根据用户信息设置查询参数
        UserDTO userDTO = TokenManager.getUserDTO();
        projectManagementQueryRequest.setUserId(userDTO.getUserId());
        projectManagementQueryRequest.setUserOrgId(userDTO.getOrgId());
        projectManagementQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        projectManagementQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        projectManagementQueryRequest.setUserRelatedOrgIdList(userService.getUserRelatedChannelOrgIdList(userDTO));

        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            projectManagementQueryRequest.setUserIds(userId);
        }
        return projectManagementService.enterpriseAndPlatformQueryProjectInfo(projectManagementQueryRequest);
    }

    /**
     * 查询评标详情
     *
     * @param projectId
     * @return
     */
    @RequestMapping(value = "/queryBidHeaderDetails/{projectId}", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public Response queryBidHeaderDetails(@PathVariable Long projectId) {
        return projectManagementService.queryBidHeaderDetails(projectId);
    }

    /**
     * 查询项目投标信息
     *
     * @param bidHotelInfoQueryRequest
     * @return
     */
    @RequestMapping(value = "/queryBidHotelInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryBidHotelInfo(@RequestBody BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        if (bidHotelInfoQueryRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        bidHotelInfoQueryRequest.setUserId(userDTO.getUserId());
        bidHotelInfoQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        bidHotelInfoQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            bidHotelInfoQueryRequest.setUserIds(userId);
        }
        return projectManagementService.queryBidHotelInfo(bidHotelInfoQueryRequest);
    }

    /**
     * 查询地图坐标项目酒店投标信息
     *
     * @param bidHotelInfoQueryRequest
     * @return
     */
    @RequestMapping(value = "/queryMapBidHotelInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapBidHotelInfo(@RequestBody BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        if (bidHotelInfoQueryRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        bidHotelInfoQueryRequest.setUserId(userDTO.getUserId());
        bidHotelInfoQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        bidHotelInfoQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            bidHotelInfoQueryRequest.setUserIds(userId);
        }
        return projectManagementService.queryMapBidHotelInfo(bidHotelInfoQueryRequest);
    }

    /**
     * 查询地图坐标项目酒店投标信息统计
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/queryMapBidHotelInfoStat", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapBidHotelInfoStat(@RequestBody QueryMapBidHotelInfoStatRequest queryMapBidHotelInfoStatRequest) throws Exception {
        Response response = new Response();
        if (queryMapBidHotelInfoStatRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        return projectManagementService.queryMapBidHotelInfoStat(queryMapBidHotelInfoStatRequest);
    }




    /**
     * 查询地图坐标项目推荐酒店列表
     *
     * @param bidHotelInfoQueryRequest
     * @return
     */
    @RequestMapping(value = "/queryMapRecommendHotelInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapRecommendHotelInfo(@RequestBody BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        if (bidHotelInfoQueryRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        bidHotelInfoQueryRequest.setUserId(userDTO.getUserId());
        bidHotelInfoQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        bidHotelInfoQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            bidHotelInfoQueryRequest.setUserIds(userId);
        }
        return projectManagementService.queryMapRecommendHotelInfo(bidHotelInfoQueryRequest);
    }

    /**
     * 查询项目酒店和POI坐标标信息
     *
     * @return
     */
    @RequestMapping(value = "/queryMapPoiBidHotelInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapPoiBidHotelInfo(@RequestBody QueryMapPoiBidHotelInfoRequest queryHotelPoiBidHotelInfoRequest) throws Exception {
        Response response = new Response();
        if (queryHotelPoiBidHotelInfoRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }

        return projectManagementService.queryMapHotelPoiBidHotelInfo(queryHotelPoiBidHotelInfoRequest);
    }

    /**
     * 查询热力图信息
     * @return
     */
    @RequestMapping(value = "/queryHeatMapBidInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryHeatMapBidInfo(@RequestBody QueryMapPoiBidHotelInfoRequest queryHotelPoiBidHotelInfoRequest) throws Exception {
        Response response = new Response();
        if (queryHotelPoiBidHotelInfoRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }

        return projectManagementService.queryHeatMapBidInfo(queryHotelPoiBidHotelInfoRequest);
    }


    /**
     * 查询项目投标状态统计 根据地图筛选条件
     * @return
     */
    @RequestMapping(value = "/queryMapGroupByHotelBidState", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapGroupByHotelBidState(@RequestBody BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        if (bidHotelInfoQueryRequest == null || bidHotelInfoQueryRequest.getProjectId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        bidHotelInfoQueryRequest.setUserId(userDTO.getUserId());
        bidHotelInfoQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        bidHotelInfoQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            bidHotelInfoQueryRequest.setUserIds(userId);
        }

        return projectManagementService.queryGroupByHotelBidState(bidHotelInfoQueryRequest);
    }
    /**
     * 查询项目投标状态统计
     * @return
     */
    @RequestMapping(value = "/queryGroupByHotelBidState/{projectId}", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public Response queryGroupByHotelBidState(@PathVariable("projectId") Long projectId)  {
        Response response = new Response();
        if (projectId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO == null) {
            response.setResult(ReturnResultEnum.NOT_LOGIN.errorNo);
            response.setMsg(ReturnResultEnum.NOT_LOGIN.message);
            return response;
        }
        BidHotelInfoQueryRequest bidHotelInfoQueryRequest = new BidHotelInfoQueryRequest();
        bidHotelInfoQueryRequest.setProjectId(projectId);
        bidHotelInfoQueryRequest.setUserId(userDTO.getUserId());
        bidHotelInfoQueryRequest.setRoleCodeType(userDTO.getRoleCodeType());
        bidHotelInfoQueryRequest.setOrgType(userDTO.getOrgDTO().getOrgType());
        List<Long> userId = departmentService.queryUserByDepart(userDTO);
        if(userId != null && userId.size()>0){
            bidHotelInfoQueryRequest.setUserIds(userId);
        }

        return projectManagementService.queryGroupByHotelBidState(bidHotelInfoQueryRequest);
    }

    /**
     * 修改项目主体信息
     *
     * @param updateProjectIntentHotelDto
     * @return
     */
    @RequestMapping(value = "/updateProjectSubjectInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateProjectSubjectInfo(@RequestBody UpdateProjectIntentHotelDto updateProjectIntentHotelDto) {
        Response response = new Response();
        if (updateProjectIntentHotelDto == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateProjectSubjectInfo(updateProjectIntentHotelDto);
    }

    /**
     * 查询项目详情
     *
     * @param projectId
     * @return
     */
    @RequestMapping(value = "/queryProjectDetails/{projectId}", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public Response queryProjectDetails(@PathVariable Long projectId) {
        return projectManagementService.queryProjectDetails(projectId);
    }

    /**
     * 修改报价机构信息
     * @return
     */
    @RequestMapping(value = "/updateBidOrgInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateBidOrgInfo(@RequestBody UpdateBidOrgInfoRequest updateBidOrgInfoRequest) {
        Response response = new Response();
        if (updateBidOrgInfoRequest == null || updateBidOrgInfoRequest.getBidOrgType() == null || updateBidOrgInfoRequest.getBidOrgId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        updateBidOrgInfoRequest.setOperator(userDTO.getOperator());
        return projectManagementService.updateBidOrgInfo(updateBidOrgInfoRequest, userDTO);
    }

    /**
     * 修改项目状态
     *
     * @param updateProjectStateDto
     * @return
     */
    @RequestMapping(value = "/updateProjectState", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateProjectState(@RequestBody UpdateProjectStateDto updateProjectStateDto) {
        Response response = new Response();
        if (updateProjectStateDto == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        Integer projectState = updateProjectStateDto.getProjectState();
        if (projectState == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        if (userDTO != null) {
            updateProjectStateDto.setOperator(userDTO.getOperator());
        }
        return projectManagementService.updateProjectState(updateProjectStateDto);
    }


    /**
     * 导出报价
     *
     * @param projectId
     * @return
     */
    @RequestMapping(value = "/exportTenderPrice/{projectId}/{requestSequenceNo}", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public void queryProjectDetails(@PathVariable Long projectId, @PathVariable String requestSequenceNo, HttpServletRequest request, HttpServletResponse response) {
        if (projectId == null || projectId <= 0) {
            logger.error("导出报价参数异常,项目id有误");
            return;
        }
        Response projectResponseInfo = projectService.queryProjectBasicInformation(projectId);
        if(projectResponseInfo.getData() == null){
            logger.error("未查询到项目信息");
            return;
        }
        ProjectBasicInfoResponse projectBasicInfoResponse = (ProjectBasicInfoResponse)projectResponseInfo.getData();
        String fileName = projectBasicInfoResponse.getProjectName() + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        InputStream excelStream = null;
        ServletOutputStream out = null;
        String key = RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO + requestSequenceNo;
        try {
            RedisService.setex(key, String.valueOf(1), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
            excelStream = projectManagementService.exportTenderPrice(projectId, request, response);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bis = new BufferedInputStream(excelStream);
            bos = new BufferedOutputStream(out);
            byte[] buff = new byte[2048];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
            RedisService.setex(key, String.valueOf(2), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
        } catch (Exception e) {
            logger.error("导出报价异常：", e);
            PrintWriter writer = null;
            try {
                response.setContentType("text/html;charset=utf-8");
                writer = response.getWriter();
                writer.print("导出报价异常");
                writer.flush();
            } catch (IOException e1) {
                logger.error("导出报价异常：", e1);
            }finally {
                if(writer != null) {
                    writer.close();
                }
            }
            try {
                RedisService.setex(key, String.valueOf(0), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
            } catch (Exception ex){
                logger.error("RedisSave 异常：", ex);
            }
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    logger.error("导出报价异常：", e);
                }
            }
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    logger.error("导出报价异常：", e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error("导出报价异常：", e);
                }
            }
            if (excelStream != null) {
                try {
                    excelStream.close();
                } catch (IOException e) {
                    logger.error("导出报价异常：", e);
                }
            }
        }

    }

    /**
     * 导出数据迁移报价
     *
     * @param projectId
     * @return
     */
    @RequestMapping(value = "/exportDataTransferPrice/{projectId}/{requestSequenceNo}", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public void exportDataTransferPrice(@PathVariable Long projectId, @PathVariable String requestSequenceNo, HttpServletRequest request, HttpServletResponse response) {
        if (projectId == null || projectId <= 0) {
            logger.error("导出数据迁移报价参数异常,项目id有误");
            return;
        }
        Response projectResponseInfo = projectService.queryProjectBasicInformation(projectId);
        if(projectResponseInfo.getData() == null){
            logger.error("未查询到项目信息");
            return;
        }
        ProjectBasicInfoResponse projectBasicInfoResponse = (ProjectBasicInfoResponse)projectResponseInfo.getData();
        String fileName = projectBasicInfoResponse.getProjectName() + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        BufferedInputStream bis = null;
        BufferedOutputStream bos = null;
        InputStream excelStream = null;
        ServletOutputStream out = null;
        String key = RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO + requestSequenceNo;
        try {
            RedisService.setex(key, String.valueOf(1), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
            excelStream = projectManagementService.exportTenderPriceForTransfer(projectId, request, response);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            out = response.getOutputStream();
            bis = new BufferedInputStream(excelStream);
            bos = new BufferedOutputStream(out);
            byte[] buff = new byte[2048];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
            RedisService.setex(key, String.valueOf(2), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
        } catch (Exception e) {
            logger.error("导出数据迁移报价异常：", e);
            PrintWriter writer = null;
            try {
                RedisService.setex(key, String.valueOf(0), RedisConstant.EXPORT_DATA_REQUEST_SEQUENCE_NO_EXPIRY_TIME);
                response.setContentType("text/html;charset=utf-8");
                writer = response.getWriter();
                writer.print("导出数据迁移报价异常");
                writer.flush();
            } catch (Exception e1) {
                logger.error("导出数据迁移报价异常：", e1);
            }finally {
                if(writer != null) {
                    writer.close();
                }
            }

        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    logger.error("导出数据迁移报价异常：", e);
                }
            }
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    logger.error("导出数据迁移报价异常：", e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error("导出数据迁移报价异常：", e);
                }
            }
            if (excelStream != null) {
                try {
                    excelStream.close();
                } catch (IOException e) {
                    logger.error("导出数据迁移报价异常：", e);
                }
            }
        }
    }

    /**
     * 查询报价详情
     *
     * @param projectHotelBidStrategyRequest
     * @return
     */
    @RequestMapping(value = "/queryPriceDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryPriceDetail(@RequestBody ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest) {
        Response response = new Response();
        if (projectHotelBidStrategyRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectHotelBidStrategyRequest.getProjectIntentHotelId() == null ||
                projectHotelBidStrategyRequest.getProjectId() == null ||
                projectHotelBidStrategyRequest.getHotelId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        Response priceResponse = projectHotelBidStrategyService.selectProjectHotelBidStrategy(projectHotelBidStrategyRequest);
        if(priceResponse.getResult() == null || ReturnResultEnum.SUCCESS.errorNo != priceResponse.getResult() || priceResponse.getData() == null){
            return priceResponse;
        }

        BidStrategyResponse bidStrategyResponse = (BidStrategyResponse)priceResponse.getData();
        QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse = new QueryProjectHotelPriceDetailResponse();
        BeanUtils.copyProperties(bidStrategyResponse,queryProjectHotelPriceDetailResponse);
        queryProjectHotelPriceDetailResponse.setProjectCustomBidStrategies(bidStrategyResponse.getProjectCustomBidStrategies());
        return projectManagementService.queryPriceDetail(projectHotelBidStrategyRequest, queryProjectHotelPriceDetailResponse);
    }

    /**
     * 修改报价人信息
     */
    @RequestMapping(value = "/updateBidContactInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateBidContactInfo(@RequestBody UpdateBidContactInfoRequest updateBidContactInfoRequest) {
        Response response = new Response();
        if(updateBidContactInfoRequest == null || updateBidContactInfoRequest.getProjectIntentHotelId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateBidContactInfo(updateBidContactInfoRequest, TokenManager.getUserDTO());
    }

    /**
     * 修改酒店集团报价人信息
     */
    @RequestMapping(value = "/updateHotelGroupBidContactInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateHotelGroupBidContactInfo(@RequestBody UpdateHotelGroupBidContactInfoRequest updateHotelGroupBidContactInfoRequest) {
        Response response = new Response();
        if(updateHotelGroupBidContactInfoRequest == null || updateHotelGroupBidContactInfoRequest.getProjectIntentHotelId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateHotelGroupBidContactInfo(updateHotelGroupBidContactInfoRequest, TokenManager.getUserDTO());
    }

    /**
     * 修改报价酒店主题信息
     */
    @RequestMapping(value = "/updateBidHotelSubjectInfo", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateBidHotelSubjectInfo(@RequestBody UpdateBidHotelSubjectInfoRequest updateBidHotelSubjectInfoRequest) {
        Response response = new Response();
        if(updateBidHotelSubjectInfoRequest == null || updateBidHotelSubjectInfoRequest.getProjectIntentHotelId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateBidHotelSubjectInfo(updateBidHotelSubjectInfoRequest, TokenManager.getUserDTO());
    }

    /**
     * 修改报价日期
     */
    @RequestMapping(value = "/updateBidDay", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateBidDay(@RequestBody UpdateBidDayRequest updateBidDayRequest) {
        Response response = new Response();
        if(updateBidDayRequest == null || updateBidDayRequest.getProjectIntentHotelId() == null ||
                updateBidDayRequest.getBidApplyDayList() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateBidDay(updateBidDayRequest, TokenManager.getUserDTO());
    }

    /**
     * 修改房型
     */
    @RequestMapping(value = "/updateBidLevelRoom", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateBidLevelRoom(@RequestBody UpdateBidLevelRoomRequest updateBidLevelRoomRequest) {
        Response response = new Response();
        if(updateBidLevelRoomRequest == null || updateBidLevelRoomRequest.getProjectIntentHotelId() == null ||
                updateBidLevelRoomRequest.getRoomLevelNo() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateBidLevelRoom(updateBidLevelRoomRequest, TokenManager.getUserDTO());
    }

    /**
     * 上传凭证
     */
    @RequestMapping(value = "/updateCertsUrl", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updateCertsUrl(@RequestBody UpdateCertsUrlRequest updateCertsUrlRequest) {
        Response response = new Response();
        if(updateCertsUrlRequest == null || updateCertsUrlRequest.getProjectIntentHotelId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updateCertsUrl(updateCertsUrlRequest, TokenManager.getUserDTO());
    }

    /**
     * 修改报价人信息
     */
    @RequestMapping(value = "/updatePriceGroupLocked", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response updatePriceGroupLocked(@RequestBody UpdatePriceGroupLockedRequest updatePriceGroupLockedRequest) {
        Response response = new Response();
        if(updatePriceGroupLockedRequest == null || updatePriceGroupLockedRequest.getHotelPriceGroupId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.updatePriceGroupLocked(updatePriceGroupLockedRequest, TokenManager.getUserDTO());
    }


    /**
     * 查询报地图酒店详情
     * @return
     */
    @RequestMapping(value = "/queryMapKey", method = RequestMethod.GET, produces = {"application/json;charset=UTF-8"})
    public Response queryMapKey() {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(BaseConfig.getRfpTDMapKey());
        return response;
    }


    /**
     * 查询报地图酒店详情
     * @return
     */
    @RequestMapping(value = "/queryMapHotelDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapHotelDetail(@RequestBody ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        Response response = new Response();
        if (projectMapHotelPriceDetailRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectMapHotelPriceDetailRequest.getProjectId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        return projectManagementService.queryMapHotelPriceDetail(projectMapHotelPriceDetailRequest);

    }

    /**
     * 查询报地图酒店详情
     * @return
     */
    @RequestMapping(value = "/queryRecommendMapHotelDetail", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryRecommendMapHotelDetail(@RequestBody ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        Response response = new Response();
        if (projectMapHotelPriceDetailRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectMapHotelPriceDetailRequest.getProjectId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        return projectManagementService.queryMapRecommendHotelPriceDetail(projectMapHotelPriceDetailRequest);

    }


    /**
     * 查询报地图房档价格
     * @return
     */
    @RequestMapping(value = "/queryMapHotelBidLevelPrice", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapHotelBidLevelPrice(@RequestBody ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        Response response = new Response();
        if (projectMapHotelPriceDetailRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectMapHotelPriceDetailRequest.getProjectId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.queryMapHotelBidLevelPrice(projectMapHotelPriceDetailRequest);

    }

    /**
     * 查询地图酒店统计数据
     */
    @RequestMapping(value = "/queryMapHotelPriceBidStat", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapHotelPriceBidStat(@RequestBody ProjectMapHotelPriceStatRequest projectMapHotelPriceStatRequest) {
        Response response = new Response();
        if (projectMapHotelPriceStatRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectMapHotelPriceStatRequest.getProjectId() == null || projectMapHotelPriceStatRequest.getHotelId() == null ||
                projectMapHotelPriceStatRequest.getStatType() == null || projectMapHotelPriceStatRequest.getStatType() <=0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        return projectManagementService.queryMapHotelPriceBidStat(projectMapHotelPriceStatRequest);

    }

    /**
     * 查询POI统计数据
     */
    @RequestMapping(value = "/queryMapPoiBidStat", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryMapPoiBidStat(@RequestBody ProjectMapPoiStatRequest projectMapPoiStatRequest) {
        Response response = new Response();
        if (projectMapPoiStatRequest == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectMapPoiStatRequest.getProjectId() == null || projectMapPoiStatRequest.getPoiId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        return projectManagementService.queryMapPoiBidStat(projectMapPoiStatRequest);

    }

    /**
     * 酒店未报价管理
     *
     * @param queryUnpricedManagementRequest
     * @return
     */
    @RequestMapping(value = "/queryUnpricedHotelManagement", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response queryUnpricedHotelManagement(@RequestBody QueryUnpricedManagementRequest queryUnpricedManagementRequest) {
        return projectManagementService.queryUnpricedHotelManagement(queryUnpricedManagementRequest);
    }

    /**
     * 报价状态处理
     *
     * @param processingBidStateRequest
     * @return
     */
    @RequestMapping(value = "/processingBidState", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response processingBidState(@RequestBody ProcessingBidStateRequest processingBidStateRequest) {
        Response response = new Response();
        UserDTO userDTO = TokenManager.getUserDTO();
        if(processingBidStateRequest == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        processingBidStateRequest.setOperator(userDTO.getOperator());
        processingBidStateRequest.setOperatorId(userDTO.getUserId());
        processingBidStateRequest.setPlatformContactName(userDTO.getOperator());
        return projectManagementService.processingBidState(processingBidStateRequest);
    }

    /**
     *  批量注册酒店机构
     *
     * @param batchHotelRegisterRequest
     * @return
     */
    @RequestMapping(value = "/batchHotelRegister", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response batchHotelRegister(@RequestBody BatchHotelRegisterRequest batchHotelRegisterRequest) {
        Response response = new Response();
        UserDTO userDTO = TokenManager.getUserDTO();
        if(batchHotelRegisterRequest == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if(CollectionUtils.isEmpty(batchHotelRegisterRequest.getProjectIntentHotelIds())){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }

        batchHotelRegisterRequest.setOperator(userDTO.getOperator());
        return projectManagementService.batchHotelRegister(batchHotelRegisterRequest);
    }

    /**
     * 未报价指派平台跟进人
     *
     * @param assignPlatformUserRequest
     * @return
     */
    @RequestMapping(value = "/assignPlatformUser", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Response assignPlatformUser(@RequestBody AssignPlatformUserRequest assignPlatformUserRequest) {
        Response response = new Response();
        if(assignPlatformUserRequest == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        UserDTO userDTO = TokenManager.getUserDTO();
        assignPlatformUserRequest.setOperator(userDTO.getOperator());
        return projectManagementService.assignPlatformUser(assignPlatformUserRequest);
    }

    /**
     * 下载签约状态导入模版
     */
    @RequestMapping(value = "downloadProjectBidStateTemplate")
    public void downloadProjectBidStateTemplate(HttpServletResponse httpServletResponse) {
        projectManagementService.downloadProjectBidStateTemplate(httpServletResponse);
    }

    /**
     * 导入酒店签约状态
     */
    @RequestMapping(value = "importHotelBidState")
    public Response importHotelBidState(MultipartFile uploadFile, Long projectId) throws Exception {
        Response response = new Response();
        UserDTO userDTO = TokenManager.getUserDTO();

        if(uploadFile == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if(projectId == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.importHotelBidState(uploadFile, userDTO, projectId);
    }

    /**
     * 下载批量更新房档模版
     */
    @RequestMapping(value = "downloadProjectBidRoomTypeTemplate")
    public void downloadProjectBidRoomTypeTemplate(HttpServletResponse httpServletResponse) {
        projectManagementService.downloadProjectBidRoomTypeTemplate(httpServletResponse);
    }

    /**
     * 导入批量更新房档
     */
    @RequestMapping(value = "importHotelBidRoomType")
    public Response importHotelBidRoomType(MultipartFile uploadFile, Long projectId) throws Exception {
        Response response = new Response();
        UserDTO userDTO = TokenManager.getUserDTO();

        if(uploadFile == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if(projectId == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.importHotelBidRoomType(uploadFile, userDTO, projectId);
    }

    /**
     * 下载批量更新联系信息及承诺信息模板
     */
    @RequestMapping(value = "downloadProjectBidContactInfoAndPromiseTemplate")
    public void downloadProjectBidContactInfoAndPromiseTemplate(HttpServletResponse httpServletResponse) {
        projectManagementService.downloadProjectBidContactInfoAndPromiseTemplate(httpServletResponse);
    }

    /**
     * 导入批量更新联系信息及承诺信息
     */
    @RequestMapping(value = "importProjectBidContactInfoAndCommitment")
    public Response importProjectBidContactInfoAndCommitment(MultipartFile uploadFile, Long projectId) throws IOException, InvalidFormatException {
        Response response = new Response();
        UserDTO userDTO = TokenManager.getUserDTO();

        if (uploadFile == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (projectId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        return projectManagementService.importProjectBidContactInfoAndCommitment(uploadFile, userDTO, projectId);
    }

    @RequestMapping(value = "/generateBidPdf/{projectIntentHotelId}", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public void previewPdf(@PathVariable Long projectIntentHotelId, HttpServletResponse response) throws DocumentException, IOException {
        projectHotelBidStrategyService.generateBidPdf(projectIntentHotelId, response);
    }
}
