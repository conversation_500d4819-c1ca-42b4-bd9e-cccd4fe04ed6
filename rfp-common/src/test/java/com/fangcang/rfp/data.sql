CREATE TABLE "HTL_RFP"."T_HOTEL" (
                                     "RFP_HOTELID" NUMBER NOT NULL,
                                     "DATA_SOURCE"   VARCHAR2(32 BYTE),
                                     "HOTELID" NUMBER NOT NULL,
                                     "HOTELCD" VARCHAR2(32 BYTE),
                                     "CHN_NAME" NVARCHAR2(400),
                                     "ENG_NAME" NVARCHAR2(400),
                                     "COUNTRY" VARCHAR2(32 BYTE),
                                     "PROVINCE" VARCHAR2(32 BYTE),
                                     "CITY" VARCHAR2(32 BYTE),
                                     "BUSINESSZONE" VARCHAR2(32 BYTE),
                                     "DISTRICT" VARCHAR2(32 BYTE),
                                     "LAYER_HIGH" VARCHAR2(32 BYTE),
                                     "LAYER_COUNT" VARCHAR2(32 BYTE),
                                     "HOTEL_STAR" VARCHAR2(2 BYTE),
                                     "CHN_ADDRESS" VARCHAR2(500 BYTE),
                                     "ENG_ADDRESS" VARCHAR2(500 BYTE),
                                     "PRACICE_DATE" DATE,
                                     "FITMENT_DATE" DATE,
                                     "TELEPHONE" VARCHAR2(300 BYTE),
                                     "POST_CODE" VARCHAR2(32 BYTE),
                                     "FAX" VARCHAR2(300 BYTE),
                                     "HOTEL_TYPE" VARCHAR2(256 BYTE),
                                     "ACCEP_CUSTOM" VARCHAR2(512 BYTE),
                                     "INTRODUCE" VARCHAR2(4000 BYTE),
                                     "ENG_INTRODUCE" VARCHAR2(4000 BYTE),
                                     "TRAVEL_CHECK" VARCHAR2(256 BYTE),
                                     "HOTEL_GROUP" VARCHAR2(256 BYTE),
                                     "CHECKIN_TIME" VARCHAR2(16 BYTE),
                                     "CHECKOUT_TIME" VARCHAR2(16 BYTE),
                                     "ALERT_MESSAGE" VARCHAR2(512 BYTE),
                                     "HOTELBRAND" VARCHAR2(32 BYTE),
                                    "ISACTIVE" CHAR(1 BYTE),
                                    "HOTELINFOKEY" VARCHAR2(4000 BYTE),
                                    "MAPPOSITION_ID" NUMBER,
                                    "ZONESID" NUMBER,
                                    "CITY_NAME" VARCHAR2(128 BYTE),
                                    "BUSINESSZONE_NAME" VARCHAR2(64 BYTE),
                                    "DISTRICT_NAME" VARCHAR2(64 BYTE),
                                    "LNG_GPS" NUMBER,
                                    "LAT_GPS" NUMBER,
                                    "LNG_BAIDU" NUMBER,
                                    "LAT_BAIDU" NUMBER,
                                    "LNG_GOOGLE" NUMBER,
                                    "LAT_GOOGLE" NUMBER,
                                    "ICON_CONTENT" VARCHAR2(2000 BYTE),
                                    "CTRIP_HOTELID" VARCHAR2(32 BYTE),
                                    "ALLOWS_PET" CHAR(1 BYTE),
                                    "PET_TEXT" VARCHAR2(200 BYTE),
                                    "IS_CHARGE_PARK" CHAR(1 BYTE),
                                    "IS_PARK" CHAR(1 BYTE),
                                    "HOTEL_CATEGORY" VARCHAR2(16 BYTE),
                                    "HOTEL_SUB_CATEGORY" VARCHAR2(16 BYTE),
                                    "RATING" VARCHAR2(16 BYTE),
                                    "LNG_AUTONAVI" NUMBER,
                                    "LAT_AUTONAVI" NUMBER,
                                    "CHECKIN_LATE_TIME" VARCHAR2(16 BYTE),
                                    "CHECKOUT_EARLY_TIME" VARCHAR2(16 BYTE),
                                    "GREEN_HOTEL_RATING" VARCHAR2(1 BYTE),
                                    "CREATETIME" DATE,
                                    "CREATOR" VARCHAR2(32 BYTE),
                                    "MODIFYTIME" DATE,
                                    "MODIFYBY" VARCHAR2(32 BYTE)
);
COMMENT ON COLUMN "HTL_RFP."T_HOTEL"."RFP_HOTELID" IS 'RFP酒店ID';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."DATA_SOURCE" '数据来源 :SAAS, GLINK';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTELID" IS '酒店ID';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTELCD" IS '酒店编码';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHN_NAME" IS '中文酒店名称';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ENG_NAME" IS '英文酒店名称';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."COUNTRY" IS '国家';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."PROVINCE" IS '省份';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CITY" IS '城市';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."BUSINESSZONE" IS '商业区';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."DISTRICT" IS '行政区';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAYER_HIGH" IS '层高';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAYER_COUNT" IS '楼层数';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTEL_STAR" IS '酒店星级';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHN_ADDRESS" IS '酒店中文地址';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ENG_ADDRESS" IS '酒店英文地址';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."PRACICE_DATE" IS '开业日期';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."FITMENT_DATE" IS '装修日期';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."TELEPHONE" IS '联系电话';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."POST_CODE" IS '邮政编码';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."FAX" IS '传真',
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTEL_TYPE" IS '酒店类型';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ACCEP_CUSTOM" IS '接受客人,基础信息系统启用字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."INTRODUCE" IS '中文简介';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ENG_INTRODUCE" IS '英文简介';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTEL_GROUP" IS '所属酒店集团';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHECKIN_TIME" IS '规定入住时间';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHECKOUT_TIME" IS '规定退房时间';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ALERT_MESSAGE" IS '提示信息';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTELBRAND" IS '酒店品牌';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ISACTIVE" IS '是否有效（1有效，0无效）';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTELINFOKEY" IS '酒店关键字';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CITY_NAME" IS '城市名称，新版基础信息系统新增字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."BUSINESSZONE_NAME" IS '行政区名称，新版基础信息系统新增字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."DISTRICT_NAME" IS '商业区名称，新版基础信息系统新增字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LNG_GPS" IS 'GPS经度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAT_GPS" IS 'GPS纬度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LNG_BAIDU" IS '百度经度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAT_BAIDU" IS '百度纬度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LNG_GOOGLE" IS '谷歌经度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAT_GOOGLE" IS '谷歌纬度';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ICON_CONTENT" IS '列表页展示图标内容';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CTRIP_HOTELID" IS '携程酒店ID';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."ALLOWS_PET" IS '是否允许携带宠物 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."PET_TEXT" IS '携带宠物描述 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."IS_CHARGE_PARK" IS '是否有充电车位 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."IS_PARK" IS '是否有停车场 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTEL_CATEGORY" IS '酒店一级分类 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."HOTEL_SUB_CATEGORY" IS '酒店二级分类 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."RATING" IS '酒店评分 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LNG_AUTONAVI" IS '高德经度 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."LAT_AUTONAVI" IS '高德维度 CtripV5.0新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHECKIN_LATE_TIME" IS '最晚入住时间，基础信息系统新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CHECKOUT_EARLY_TIME" IS '最早离店时间，基础信息系统新加字段';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."GREEN_HOTEL_RATING" IS '绿色旅游饭店等级 0-金树叶  1-银树叶 ';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CREATETIME" IS '创建时间';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."CREATOR" IS '创建人';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."MODIFYTIME" IS '修改时间';
COMMENT ON COLUMN "HTL_RFP"."T_HOTEL"."MODIFYBY" IS '修改人';

-- ----------------------------
-- Primary Key structure for table T_HOTEL
-- ----------------------------
ALTER TABLE "HTL_RFP"."T_HOTEL" ADD CONSTRAINT "PK_T_RFP_HOTEL" PRIMARY KEY ("RFP_HOTELID");

-- ----------------------------
-- Checks structure for table T_HOTEL
-- ----------------------------
ALTER TABLE "HTL_INFO"."T_HOTEL" ADD CONSTRAINT "SYS_C0011517" CHECK ("HOTELID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table T_HOTEL
-- ----------------------------
CREATE INDEX "HTL_INFO"."IDX_CTRIP_ID"
    ON "HTL_INFO"."T_HOTEL" ("ISACTIVE" ASC, "CTRIP_HOTELID" ASC)
    LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "HTL_INFO"."IDX_HTL_CITY"
    ON "HTL_INFO"."T_HOTEL" ("CITY" ASC)
    LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "HTL_INFO"."IDX_HTL_COUNTRY"
    ON "HTL_INFO"."T_HOTEL" ("COUNTRY" ASC)
    LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "HTL_INFO"."IDX_HTL_HINFO_MTIME"
    ON "HTL_INFO"."T_HOTEL" ("MODIFYTIME" ASC)
    LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);
CREATE INDEX "HTL_INFO"."IDX_HTL_HOTEL_ISACTIVE"
    ON "HTL_INFO"."T_HOTEL" ("ISACTIVE" ASC)
    LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
);

-- ----------------------------
-- Triggers structure for table T_HOTEL
-- ----------------------------
CREATE TRIGGER "HTL_INFO"."HOTELCODE_GENERATION_TRG" BEFORE INSERT ON "HTL_INFO"."T_HOTEL" REFERENCING OLD AS "OLD" NEW AS "NEW" FOR EACH ROW
begin
  if inserting then
select fun_gen_code(1) into :new.hotelcd from dual;
end if;
end;
/
CREATE TRIGGER "HTL_INFO"."TGI_SYN_HOTEL_LOG" AFTER DELETE OR INSERT OR UPDATE ON "HTL_INFO"."T_HOTEL" REFERENCING OLD AS "OLD" NEW AS "NEW" FOR EACH ROW
begin
  if inserting or updating then
     insert into t_hotel_log values (:new.HOTELID,:new.HOTELCD,:new.CHN_NAME,:new.ENG_NAME,:new.COUNTRY,:new.PROVINCE,:new.CITY,:new.BUSINESSZONE,
     :new.DISTRICT,:new.LAYER_HIGH,:new.LAYER_COUNT,:new.HOTEL_STAR,:new.WEBSITE,:new.CHN_ADDRESS,:new.ENG_ADDRESS,:new.PRACICE_DATE,:new.FITMENT_DATE,
     :new.TELEPHONE,:new.POST_CODE,:new.FAX,:new.LANGUAGE,:new.CREDIT_CARD,:new.HOTEL_TYPE,:new.ACCEP_CUSTOM,:new.INTRODUCE,:new.DESCRIPTION,:new.TRAVEL_CHECK,
     :new.HOTEL_GROUP,:new.TAX_PER,:new.CARD_NEED_TAX,:new.CHECKIN_TIME,:new.CHECKOUT_TIME,:new.ALERT_MESSAGE,:new.HOTELBRAND,:new.HOTEL_THEME,:new.NOSMOKINGFLOOR,
     :new.ISALLNOSMOKING,:new.LONGITUDE,:new.LATITUDE,:new.ISACTIVE,:new.CREATETIME,:new.CREATOR,:new.MODIFYTIME,:new.MODIFYBY,:new.HOTELINFOKEY,SYSDATE
     ,:OLD.CHN_NAME,:OLD.ENG_NAME,:old.Alert_Message);
end if;
  if deleting then
     insert into t_hotel_log values (:old.HOTELID,:old.HOTELCD,:old.CHN_NAME,:old.ENG_NAME,:old.COUNTRY,:old.PROVINCE,:old.CITY,:old.BUSINESSZONE,

     :old.DISTRICT,:old.LAYER_HIGH,:old.LAYER_COUNT,:old.HOTEL_STAR,:old.WEBSITE,:old.CHN_ADDRESS,:old.ENG_ADDRESS,:old.PRACICE_DATE,:old.FITMENT_DATE,
     :old.TELEPHONE,:old.POST_CODE,:old.FAX,:old.LANGUAGE,:old.CREDIT_CARD,:old.HOTEL_TYPE,:old.ACCEP_CUSTOM,:old.INTRODUCE,:old.DESCRIPTION,:old.TRAVEL_CHECK,
     :old.HOTEL_GROUP,:old.TAX_PER,:old.CARD_NEED_TAX,:old.CHECKIN_TIME,:old.CHECKOUT_TIME,:old.ALERT_MESSAGE,:old.HOTELBRAND,:old.HOTEL_THEME,:old.NOSMOKINGFLOOR,
     :old.ISALLNOSMOKING,:old.LONGITUDE,:old.LATITUDE,'0',:old.CREATETIME,:old.CREATOR,:old.MODIFYTIME,:old.MODIFYBY,:old.HOTELINFOKEY,SYSDATE
     ,:OLD.CHN_NAME,:OLD.ENG_NAME,:old.Alert_Message);
end if;
end TGI_SYN_HOTEL_LOG;
/
