package com.fangcang.rfp;

public class Test {

    public static void main(String[] args) {


        String sb =
                "<!DOCTYPE html>\n" +
                        "<html lang=\"en\">\n" +
                        "\n" +
                        "<head>\n" +
                        "    <meta charset=\"UTF-8\">\n" +
                        "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
                        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                        "    <title></title>\n" +
                        "</head>\n" +

                        "\n" +
                        "<body style=\"background: rgb(232, 232, 237);\">\n" +
                        "    <div style=\"background: rgb(232, 232, 237);width: 100% ;padding: 10px;\">\n" +
                        "        <div class=\"content\" style=\"width: 1040px;border-radius: 5px;margin: 20px auto;\">\n" +
                        "            <div class=\"headInfo\"\n" +
                        "                style=\"height: 64px;background-color: rgba(13, 82, 156, 1);display: flex;align-items: center;padding-left: 40px;border-radius: 5px 5px 0 0;\">\n" +
                        "                <div class=\"tit1\" style=\"font-weight: 900;font-style: normal;font-size: 24px;letter-spacing: 1px;color: #FFFFFF;\">\n" +
                        "                    协议通 通知\n" +
                        "                </div>\n" +
                        "            </div>\n" +
                        "            <div class=\"titInfo\"\n" +
                        "                style=\"height: 94px;background-color: rgba(235, 244, 255, 1);padding: 20px 40px;font-size: 13px;box-sizing: border-box;\">\n" +
                        "                <div style=\"display: flex; align-items: center; justify-content: space-between;\">\n" +
                        "                    <div style=\"color: #444444;\">\n" +
                        "\t\t\t\t\t尊敬的 \n" +
                        "\t\t\t\t\t<span style=\"color: #0D529C;\">hotelName：</span>\n" +
                        "                        <div style=\"margin-top:0px;text-indent: 26px;\">\n" +
                        "                            您参加\n" +
                        "\t\t\t\t\t\t\t<span\n" +
                        "                                style=\"color: #FF4200;\">projectName</span>报价"+ 2+"，目前报价状态为：\n" +
                        "\t\t\t\t\t\t\t\t<span\n" +
                        "                                style=\"color:#FF4200; font-size:20px\" >bidStateValue</span>   请及时跟进\n" +
                        "                        </div>\n" +
                        "                    </div>\n" +
                        "                    <div style=\"display: flex; align-items: center;\">\n" +
                        "               \n" +
                        "                        <div>\n" +
                        "                           <a href=\"http://www.fangcang.com/RFP/login.html#/login\" style=\"width: 140px;text-decoration:none;height: 40px;line-height: 40px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);border-radius: 3px; cursor: pointer; border: 0px; display: block;\">登录协议通</a>" +
                        "\t\t\t\t\t\t\t</div>\n" +
                        "                    </div>\n" +
                        "                </div>\n" +
                        "            </div>\n" +
                        "            <div class=\"contactInfo\"\n" +
                        "                style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                <div style=\"font-size: 18px;\">\n" +
                        "                    招标机构简介\n" +
                        "                </div>\n" +
                        "                <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "                    <div style=\"font-size: 13px;\">\n" +
                        "                        <div style=\"display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;;\">机构名称</div>\n" +
                        "                            <div>orgName</div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">简介</div>\n" +
                        "                            <div style=\"width:525px;\">\n" +
                        "                                companyProfile\n" +
                        "                            </div>\n" +
                        "                        </div>\n" +
                        "                    </div>\n" +
                        "                    <div style=\"width: 237px;height:147px; border:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                        <img src=\"logoUrl\" alt=\"\"\n" +
                        "                            style=\"width: 100%;height:145px;object-fit: contain;\">\n" +
                        "                    </div>\n" +
                        "                </div>\n" +
                        "            </div>\n" +
                        "            <div class=\"contactInfo\"\n" +
                        "                style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                <div style=\"font-size: 18px;\">\n" +
                        "                    招标项目基本信息\n" +
                        "                </div>\n" +
                        "                <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "                    <div style=\"font-size: 13px;\">\n" +
                        "                        <div style=\"display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;;\">招标项目名称*</div>\n" +
                        "                            <div style=\"display: flex;align-items: center;\">\n" +
                        "                                <span>projectName</span>\n" +
                        "                              <div\n" +
                        "                                    style=\"width:56px;height:20px;line-height: 20px;text-align: center;color: rgb(161, 161, 161);border:1px solid rgba(222, 224, 229, 1);margin: 0 5px 0 10px;\">\n" +
                        "                                    projectTypeValue招标</div>\n" +
                        "                                <div\n" +
                        "                                    style=\"width:45px;height:20px;line-height: 20px;text-align: center;color: #fff;background-color: rgb(251, 126, 51);\">\n" +
                        "                                    projectStateValue</div>\n" +
                        "                            </div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">机构名称*</div>\n" +
                        "                            <div>orgName</div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">招标方式*</div>\n" +
                        "                            <div>公开招标</div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">报名起止时间*</div>\n" +
                        "                            <div>\n" +
                        "                                <span>enrollStartTime</span> —— <span>enrollEndTime</span>\n" +
                        "                            </div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">公司差标范围*</div>\n" +
                        "                            <div>\n" +
                        "                                <span>diffMinAmount</span> —— <span>diffMaxAmount</span>\n" +
                        "                            </div>\n" +
                        "                        </div>\n" +
                        "                    </div>\n" +
                        "                </div>\n" +
                        "            </div>\n" +

                        "            <div class=\"contactInfo\"\n" +
                        "                style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                <div style=\"font-size: 18px;\">\n" +
                        "                    招标机构简介\n" +
                        "                </div>\n" +
                        "                <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "                    <div style=\"font-size: 13px;\">\n" +
                        "                        <div style=\"display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;;\">机构名称</div>\n" +
                        "                            <div>orgName</div>\n" +
                        "                        </div>\n" +
                        "                        <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                            <div style=\"width:130px;color: #A1A1A1;\">简介</div>\n" +
                        "                            <div style=\"width:525px;\">\n" +
                        "                                companyProfile\n" +
                        "                            </div>\n" +
                        "                        </div>\n" +
                        "                    </div>\n" +
                        "                    <div style=\"width: 237px;height:147px; border:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                        <img src=\"logoUrl\" alt=\"\"\n" +
                        "                            style=\"width: 100%;height:145px;object-fit: contain;\">\n" +
                        "                    </div>\n" +
                        "                </div>\n" +
                        "            </div>\n" +
                        "        <div class=\"contactInfo\" \n" +
                        "               style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\"> \n" +
                        "               <div style=\"font-size: 18px;\"> \n" +
                        "                       酒店报价人信息 \n" +
                        "                       </div> \n" +
                        "               <div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\"> \n" +
                        "                 <div style=\"font-size: 13px;\"> \n" +
                        "                   <div style=\"display:flex;\"> \n" +
                        "                     <div style=\"width:130px;color: #A1A1A1;\">酒店报价人</div> \n" +
                        "                     <div>bidContactName</div> \n" +
                        "                   </div> \n" +
                        "                   <div style=\"margin-top:20px;display:flex;\"> \n" +
                        "                     <div style=\"width:130px;color: #A1A1A1;\">联系电话</div> \n" +
                        "                     <div style=\"width:525px;\"> \n" +
                        "                       bidContactMobile \n" +
                        "                       </div> \n" +
                        "                  </div>\n" +
                        "                   <div style=\"margin-top:20px;display:flex;\">\n" +
                        "                    <div style=\"width:130px;color: #A1A1A1;\">邮箱</div>\n" +
                        "                     <div style=\"width:525px;\">\n" +
                        "                       bidContactEmail\n" +
                        "                    </div>\n" +
                        "                   </div>\n" +
                        "                 </div>\n" +
                        "               </div>\n" +
                        "             </div>\n" +
                        "<div class=\"quotation-table\"> \n" +
                        "       <div class=\"head\">\n" +
                        "       <div class=\"name\">报价日期</div> \n" +
                        "       <p>旺季 / 淡季 / 产品不适用三者日期不得重叠，淡/旺季价与基础协议价重叠时，以淡/旺季价为准</p> \n" +
                        "    </div>\n" +
                        "<div class=\"table-box\">\n" +
                        "<div class=\"row\">\n" +
                        "<p class=\"label\">基础协议价日期 <span class=\"star\">*</span></p>\n" +
                        "<p class=\"val\">baseApplicableDays</p>\n" +
                        "</div>\n" +
                        "<div class=\"row\">\n" +
                        "<p class=\"label\">Season 1日期</p>\n" +
                        "<p class=\"val\">season1ApplicableDays</p>\n" +
                        "</div>\n" +
                        "<div class=\"row\">\n" +
                        "<p class=\"label\">Season 2日期</p>\n" +
                        "<p class=\"val\">season2ApplicableDays</p>\n" +
                        "</div>\n" +
                        "<div class=\"row\">\n" +
                        "<p class=\"label\">不适用日期</p>\n" +
                        "<p class=\"val\">unApplicableDays</p>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +

                        "<div class=\"quotation-table quotation-detail\"> \n" +
                        "<h3>房型报价明细</h3>\n" +
                        "<div class=\"head\">\n" +
                        "<div class=\"name\">第一档房型及报价：</div>\n" +
                        "   <div class=\"img-room\">\n" +
                        "<img src=\"http://test02.fangcang.com/RFP/FTP/upload/RFP/ORG/SUBJECT/LICENSE/9d351dc9-c7e5-4492-8ac6-bba274083d58.png\" alt=\"\">  \n" +
                        "<p>大床房</p> \n" +
                        "</div> \n" +
                        "<p>该房档大床房<span class=\"c-orange\">120</span>间，双床房<span class=\"c-orange\">120</span>间，合计总可用<span class=\"c-orange\">120</span>间房</p>\n" +
                        "</div> \n" +
                        "<table class=\"table-box\" border=\"1px solid #e6ebf1\"> \n" +
                        "<thead> \n" +
                        "<tr>\n" +
                        "<th>早餐</th>\n" +
                        "<th>基础协议价</th>\n" +
                        "<th class=\"w-150\">Season 1</th>\n" +
                        "<th class=\"w-150\">Season 2</th>\n" +
                        "<th>适用星期</th>\n" +
                        "<th class=\"w-150\">退款条款</th>\n" +
                        "<th>LRA承诺</th>\n" +
                        "<th class=\"w-150\">备注</th>\n" +
                        "</tr>\n" +
                        "</thead>\n" +
                        "<tbody>\n" +
                        "<tr>\n" +
                        "<td>无早</td>\n" +
                        "<td class=\"price\">￥119</td>\n" +
                        "<td class=\"w-150\"><div class=\"price-box up\"><p class=\"price\">￥219</p></div></td>\n" +
                        "<td class=\"w-150\"><div class=\"price-box down\"><p class=\"price\">￥219</p></div></td>\n" +
                        "<td><div class=\"week-box\"><p>五</p><p>六</p></div></td>\n" +
                        "<td class=\"w-150\">当天18:00后不可退改</td>\n" +
                        "<td>LRA</td>\n" +
                        "<td class=\"w-150\"> 房屋仅保留至17:30，谨慎预订！ </td>\n" +
                        "</tr>\n" +
                        "</tbody>\n" +
                        "</table>\n" +
                        "</div>\n" +
                        "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\"> \n" +
                        " <div style=\"font-size: 18px;\">酒店承诺情况 </div>\n" +
                        "<div class=\"shrink-box\" style=\"margin-top:20px;\">\n" +
                        "<div class=\"item\">\n" +
                        "       <p>承诺: 支持公司统一支付 </p> \n" +
                        "       <p>承诺: 支持公司统一支付 </p> \n" +
                        "       <p class=\"no-shrink\"> 不承诺: 提供税率6%增值税专用发票</p> \n" +
                        "</div>\n" +
                        "<div class=\"item\">\n" +
                        "<p>承诺: 支持公司统一支付 </p>\n" +
                        "<p class=\"no-shrink\"> 不承诺: 提供税率6%增值税专用发票</p>\n" +
                        "<p class=\"no-shrink\"> 不承诺: 提供税率6%增值税专用发票</p>\n" +
                        "</div> \n" +
                        "</div>\n" +
                        "</div>\n" +

                        "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "<div style=\"font-size: 18px;\">发票信息</div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:130px;color: #A1A1A1;\">发票类型</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:130px;color: #A1A1A1;\">发票税点</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +



                        "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "<div style=\"font-size: 18px;\">预订条件 </div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:200px;color: #A1A1A1;\">最早入住时间</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:200px;color: #A1A1A1;\">最晚离店时间</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:200px;color: #A1A1A1;\">到店付免担保最晚保留时间</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\">\n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:200px;color: #A1A1A1;\">超出最晚保留时间情况</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +

                        "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "<div style=\"font-size: 18px;\">其他 </div>\n" +
                        "<div style=\"margin-top:30px;display: flex;align-items: center;justify-content: space-between;\"> \n" +
                        "<div style=\"font-size: 13px;\">\n" +
                        "<div style=\"display:flex;\">\n" +
                        "<div style=\"width:130px;color: #A1A1A1;\">员工权益</div>\n" +
                        "<div>-</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +
                        "</div>\n" +

                        "<div class=\"contactInfo\" style=\"padding:30px 40px;box-sizing: border-box;background-color: #fff;border-bottom:1px solid rgba(222, 224, 229, 1);\">\n" +
                        "                <div style=\"font-size: 18px;\">\n" +
                        "                    项目介绍\n" +
                        "                </div>\n" +
                        "                <div id=\"content\" style=\"margin-top:20px;\">\n" +
                        "\n" +         "introduction" +
                        "                </div>\n" +
                        "\n" +
                        "            </div>\n" +
                        "            <div class=\"footer\"\n" +
                        "                style=\"height: 60px;line-height: 60px;background-color: rgba(235, 244, 255, 1);;font-size: 13px;padding-left: 40px;color: #A0A7BB;border-radius:0 0 5px 5px;\">\n" +
                        "                <span style=\"cursor: pointer;margin-right:20px;\">想成为它的差旅酒店供应商么？\n" +
                        "\t\t\t\t<a href=\"http://www.fangcang.com/RFP/login.html#/login\"   style=\"color: rgb(2, 167, 240);text-decoration:underline;\">登录协议通</a>可参与投标报价\n" +
                        "\t\t\t\t</span>\n" +
                        "\t\t\t\t\t\t\n" +
                        "                <span>无账号请联系运营开通: <span style=\"color: rgb(255, 96, 0);\">400-1866-919</span></span>\n" +
                        "            </div>\n" +
                        "        </div>\n" +
                        "    </div>\n" +
                        "</div>\n" +
                        "</body>\n" +
                        "\n" +
                        "<script type=\"text/javascript\">\n" +
                        "\t\n" +
                        "\t   let content = document.getElementById('content');\n" +
                        "    content.innerHTML =  `introduction` " +
                        "</script>\n" +

                        "  <style>\n" +
                        " .quotation-table {\n" +
                        "          padding: 20px 40px 0;\n" +
                        "           box-sizing: border-box;\n" +
                        "           background-color: #fff;\n" +
                        "           font-size: 14px;\n" +
                        "       }\n" +

                        ".quotation-table .c-orange {\n" +
                        "           color: #ff6000;\n" +
                        "        }\n" +


                        ".quotation-table .head {\n" +
                        "         display: flex;\n" +
                        "           color: #222;\n" +
                        "           align-items: center;\n" +
                        "           justify-content: flex-start;\n" +
                        "       }\n" +

                        ".quotation-table .head p {\n" +
                        "           color: #898377;\n" +
                        "       }\n" +

                        ".quotation-table .head .name {\n" +
                        "           font-size: 18px;\n" +
                        "           margin-right: 10px;\n" +
                        "       }\n" +

                        ".table-box {\n" +
                        "           border: 1px solid #e6ebf1;\n" +
                        "       }\n" +

                        ".table-box .row {\n" +
                        "           display: flex;\n" +
                        "           align-items: center;\n" +
                        "           justify-content: flex-start;\n" +
                        "           box-sizing: border-box;\n" +
                        "           height: 40px;\n" +
                        "            line-height: 40px;\n" +
                        "       }\n" +

                        ".table-box .row .label {\n" +
                        "           width: 155px;\n" +
                        "           height: 40px;\n" +
                        "           line-height: 40px;\n" +
                        "           padding-left: 10px;\n" +
                        "           background-color: #f5f5f5;\n" +
                        "           margin: 0;\n" +
                        "          color: #777777;\n" +
                        "          box-sizing: border-box;\n" +
                        "          font-size: 13px;\n" +
                        "           border-bottom: 1px solid #e6ebf1;\n" +
                        "           border-right: 1px solid #e6ebf1;\n" +
                        "        }\n" +

                        ".table-box .row:last-child .label {\n" +
                        "          border-bottom: 0;\n" +
                        "       }\n" +

                        ".table-box .row .label .star {\n" +
                        "           color: #FD5A60;\n" +
                        "       }\n" +

                        ".table-box .row .val {\n" +
                        "           width: 100%;\n" +
                        "           color: #1B1B1B;\n" +
                        "           font-size: 14px;\n" +
                        "           border-bottom: 1px solid #e6ebf1;\n" +
                        "            padding-left: 10px;\n" +
                        "       }\n" +

                        ".table-box .row:last-child .val {\n" +
                        "           border: none;\n" +
                        "       }\n" +

                        ".quotation-detail h3 {\n" +
                        "           font-weight: normal;\n" +
                        "           font-size: 18px;\n" +
                        "       }\n" +

                        ".quotation-detail .img-room {\n" +
                        "           display: flex;\n" +
                        "           align-items: center;\n" +
                        "           width: 140px;\n" +
                        "           height: 40px;\n" +
                        "           background-color: #fff;\n" +
                        "           box-shadow: 1px 1px 6px #ccc;\n" +
                        "           margin-right: 10px;\n" +
                        "       }\n" +

                        ".quotation-detail .img-room img {\n" +
                        "           display: inline-block;\n" +
                        "           width: 50px;\n" +
                        "           height: 40px;\n" +
                        "       }\n" +


                        ".quotation-detail .table-box {\n" +
                        "           margin-top: 10px;\n" +
                        "           border-spacing: 0;\n" +
                        "           border-collapse: collapse;\n" +
                        "          border-color: #e6ebf1;\n" +

                        "      }\n" +

                        ".quotation-detail .head {\n" +
                        "           margin-top: 20px;\n" +
                        "       }\n" +

                        ".table-box th,\n" +
                        ".table-box td {\n" +
                        "          width: 75px;\n" +
                        "           height: 30px;\n" +
                        "           padding: 0 10px;\n" +
                        "           text-align: left;\n" +
                        "           background-color: #f5f5f5;\n" +
                        "           font-size: 13px;\n" +
                        "          font-weight: normal;\n" +
                        "          margin: 0;\n" +
                        "          border: 1px solid #e6ebf1;\n" +
                        "           color: #777777;\n" +
                        "       }\n" +

                        ".table-box th {\n" +
                        "           border: none;\n" +
                        "       }\n" +

                        ".table-box td {\n" +
                        "           background: #fff;\n" +
                        "           color: #1B1B1B;\n" +
                        "      }\n" +

                        ".w-150 {\n" +
                        "           width: 150px !important;\n" +
                        "       }\n" +

                        ".table-box td .price,\n" +
                        ".table-box td.price {\n" +
                        "           color: #333333;\n" +
                        "           font-weight: bold;\n" +
                        "      }\n" +

                        ".price-box p {\n" +
                        "           margin: 0;\n" +
                        "       }\n" +

                        ".price-box.up {\n" +
                        "           color: #FF6600;\n" +
                        "       }\n" +

                        ".price-box.down {\n" +
                        "           color: #02A460;\n" +
                        "      }\n" +

                        ".week-box {\n" +
                        "           display: flex;\n" +
                        "           align-items: center;\n" +
                        "       }\n" +

                        ".week-box p {\n" +
                        "          width: 20px;\n" +
                        "           height: 20px;\n" +
                        "           text-align: center;\n" +
                        "           color: #6A84A2;\n" +
                        "           border: 1px solid rgba(106, 132, 162, 1);\n" +
                        "          border-radius: 2px;\n" +
                        "          margin-right: 2px;\n" +
                        "       }\n" +

                        ".shrink-box .item {\n" +
                        "          display: flex;\n" +
                        "           align-items: center;\n" +
                        "           justify-content: space-between;\n" +
                        "       }\n" +

                        ".shrink-box .item p {\n" +
                        "           font-size: 14px;\n" +
                        "           flex: 1;\n" +
                        "      }\n" +

                        ".shrink-box .item img {\n" +
                        "          display: inline-block;\n" +
                        "           width: 13px;\n" +
                        "           height: 13px;\n" +
                        "       }\n" +

                        ".no-shrink {\n" +
                        "          color: #D9001B;\n" +
                        "       }\n" +
                        "</style>\n" +

                        "</html>";
        System.out.println(sb);

    }
}
