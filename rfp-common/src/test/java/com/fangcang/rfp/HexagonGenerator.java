package com.fangcang.rfp;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class HexagonGenerator {

    // 六边形的边长
    private static final double SIZE = 0.009;
    // 递归深度
    private static final int DEPTH = 6;

    public static void main(String[] args) {
        // 初始百度经纬度
        double initialBdLng = 114.0;
        double initialBdLat = 30.0;

        Set<String> uniqueHexagons = new HashSet<>();
        long l = System.currentTimeMillis();
        generateHexagons(new double[]{initialBdLng, initialBdLat}, DEPTH, uniqueHexagons);
        System.out.println((System.currentTimeMillis() - l));

        // 输出唯一六边形的坐标
        System.out.println("最终生成的唯一六边形 BD-09 坐标:");
        System.out.println(uniqueHexagons);
       /* for (String hexagon : uniqueHexagons) {
            System.out.println("六边形顶点坐标: " + hexagon);
        }*/
    }

    // 生成六边形
    private static void generateHexagons(double[] center, int depth, Set<String> uniqueHexagons) {
        if (depth == 0) {
            List<double[]> hexagonCoords = calculateHexagon(center);  // 获取六边形坐标
            uniqueHexagons.add(hexagonToString(hexagonCoords));  // 将当前六边形的字符串形式添加到集合中
            return;
        }

        List<double[]> hexagonCoords = calculateHexagon(center);
        List<double[]> newCenters = getNewHexagonCenters(hexagonCoords);

        for (double[] newCenter : newCenters) {
            generateHexagons(newCenter, depth - 1, uniqueHexagons);
        }
    }

    // 计算六边形的顶点并返回其坐标
    private static List<double[]> calculateHexagon(double[] center) {
        List<double[]> hexagon = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            double angle = Math.PI / 3 * i;
            double x = center[0] + SIZE * Math.cos(angle); // 经度
            double y = center[1] + SIZE * Math.sin(angle); // 纬度
            hexagon.add(new double[]{x, y});
        }
        return hexagon;
    }

    // 将六边形坐标转换为字符串表示
    private static String hexagonToString(List<double[]> hexagonCoords) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < hexagonCoords.size(); i++) {
            double[] coord = hexagonCoords.get(i);
            sb.append(String.format("(%.6f, %.6f)", coord[0], coord[1]));
            if (i < hexagonCoords.size() - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    // 根据六边形的顶点返回新六边形的中心坐标
    private static List<double[]> getNewHexagonCenters(List<double[]> hexagonCoords) {
        List<double[]> newCenters = new ArrayList<>();

        for (int i = 0; i < 6; i++) {
            double[] startPoint = hexagonCoords.get(i);
            double[] endPoint = hexagonCoords.get((i + 1) % 6);

            // 中点
            double midX = (startPoint[0] + endPoint[0]) / 2;
            double midY = (startPoint[1] + endPoint[1]) / 2;

            // 计算偏移量
            double offsetX = (endPoint[0] - startPoint[0]) /
                    Math.sqrt(Math.pow(endPoint[0] - startPoint[0], 2) + Math.pow(endPoint[1] - startPoint[1], 2)) * SIZE;
            double offsetY = (endPoint[1] - startPoint[1]) /
                    Math.sqrt(Math.pow(endPoint[0] - startPoint[0], 2) + Math.pow(endPoint[1] - startPoint[1], 2)) * SIZE;

            double[] newCenter = {midX + offsetY, midY - offsetX};
            newCenters.add(newCenter);
        }
        return newCenters;
    }
}
