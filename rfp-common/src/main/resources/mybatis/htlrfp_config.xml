<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <properties>
        <property name="dialect" value="oracle"/>
    </properties>
    <settings>
        <setting name="cacheEnabled" value="true"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="defaultStatementTimeout" value="300"/>
    </settings>

    <plugins>
        <!-- com.github.pagehelper为PageHelper类所在包名 -->
        <plugin interceptor="com.github.pagehelper.PageHelper">
            <!-- 该参数默认为false -->
            <!-- 设置为true时，会将RowBounds第一个参数offset当成pageNum页码使用 -->
            <!-- 和startPage中的pageNum效果一样 -->
            <property name="offsetAsPageNum" value="true"/>
            <!-- 该参数默认为false -->
            <!-- 设置为true时，使用RowBounds分页会进行count查询 -->
            <property name="rowBoundsWithCount" value="true"/>
            <!-- 设置为true时，如果 pageSize=0 或者 RowBounds.limit = 0 就会查询出全部的结果 -->
            <!-- （相当于没有执行分页查询，但是返回结果仍然是Page类型） -->
            <property name="pageSizeZero" value="false"/>
            <!-- 3.3.0版本可用 - 分页参数合理化，默认false禁用 -->
            <!-- 启用合理化时，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页 -->
            <!-- 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据 -->
            <property name="reasonable" value="false"/>
            <!-- always总是返回PageInfo类型,check检查返回类型是否为PageInfo,none返回Page -->
            <property name="returnPageInfo" value="none"/>
        </plugin>
    </plugins>
    <!--**************************************** 分页插件    end ****************************************-->

    <mappers>
        <mapper resource="mybatis/mapper/OrgDao.xml"></mapper>
        <mapper resource="mybatis/mapper/UPermissionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/URoleDao.xml"></mapper>
        <mapper resource="mybatis/mapper/URolePermissionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/UUserDao.xml"></mapper>
        <mapper resource="mybatis/mapper/UUserRoleDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgSubjectDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgPoiDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ContractTemplateDao.xml"></mapper>

        <mapper resource="mybatis/mapper/RecommendHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/AttachmentDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectPoiDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectIntentHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelTendStrategyMapper.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelTendWeightMapper.xml"></mapper>

        <mapper resource="mybatis/mapper/PriceApplicableDayDao.xml"></mapper>
        <mapper resource="mybatis/mapper/PriceApplicableRoomDao.xml"></mapper>
        <mapper resource="mybatis/mapper/PriceUnapplicableDayDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelPriceDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelBidStrategyDao.xml"></mapper>

        <mapper resource="mybatis/mapper/ContractDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ContractExternalDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgSubjectFaddAccountDao.xml"></mapper>
        <mapper resource="mybatis/mapper/RfpContractSignRecordDao.xml"></mapper>
        <mapper resource="mybatis/mapper/RfpSubjectAuthRecordDao.xml"></mapper>

        <mapper resource="mybatis/mapper/OrderMonitorConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/PriceMonitorConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/DisHotelDailyOrderDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelPriceMonitorDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelPriceMonitorSumDao.xml"></mapper>
        <mapper resource="mybatis/mapper/PoiHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/DateWorkDayDao.xml"></mapper>

        <mapper resource="mybatis/mapper/DepartmentDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelApplyOnlineDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelApplyOnlineLogDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectCustomTendStrategyDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelWhiteDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectCustomBidStrategyDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectIntentHotelGroupDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelGroupDefaultCustomStrategyDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelGroupDefaultUnapplicableDayDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelViolationsConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ViolationsRemindConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelViolationsMonitorDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelViolationsDailyPriceDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelViolationOrderDetailDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ViolationsGsoConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgRelatedHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ViolationsFullMonitorLogDao.xml"></mapper>
        <mapper resource="mybatis/mapper/MonitorCallTmchubErrorDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelViolationsDailyCheckDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelPriceGroupDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelBidTempInfoDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelGroupDefaultApplicableDayDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelPriceMonitorRoomDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ConvertPriceFailedRecordDao.xml"></mapper>
        <mapper resource="mybatis/mapper/LanyonImportColumnDao.xml"></mapper>
        <mapper resource="mybatis/mapper/LanyonImportDataDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectLanyonViewKeysDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelHistoryDataDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelHexagonLngLatDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectLastYearCityStatDao.xml"></mapper>
        <mapper resource="mybatis/mapper/PartnerConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/BidOperateLogDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectInviteHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgHotelGroupBrandDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelRemarkDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrderExcludeSupConfigDao.xml"></mapper>
        <mapper resource="mybatis/mapper/DisHotelOrderMonitorDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ChannelPartnerDao.xml"></mapper>
        <mapper resource="mybatis/mapper/EmployeeOrgDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgFollowHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectAddedHotelDao.xml"></mapper>
        <mapper resource="mybatis/mapper/AreadataDao.xml"></mapper>
        <mapper resource="mybatis/mapper/BidTaskImportColumnDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ImportBidTaskDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OtaHotelDailyMinPriceDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHistoryRecommendDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectRecommendStatLogDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgHotelGroupContactUserDao.xml"></mapper>
        <mapper resource="mybatis/mapper/OrgHotelGroupUserLogDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelRelatedCertDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectCustomStrategyOptionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectCustomBidStrategyOptionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectCustomBidStrategyOptionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/HotelGroupDefaultCustomStrategyOptionDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectWeightDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectWeightHotelGroupDao.xml"></mapper>
        <mapper resource="mybatis/mapper/ProjectHotelWeightDao.xml"></mapper>

    </mappers>

</configuration>
