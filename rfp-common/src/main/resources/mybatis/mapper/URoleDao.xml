<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.URoleDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.RoleDTO" >
    <id column="role_id" property="roleId" jdbcType="INTEGER" />
    <result column="org_id" property="orgId" jdbcType="INTEGER" />
    <result column="role_name" property="roleName" jdbcType="VARCHAR" />
    <result column="role_code" property="roleCode" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="role_type" property="roleType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    role_id, org_id, role_name, role_code, state, role_type
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_role
    where role_id = #{roleId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from htl_rfp.t_role
    where role_id = #{roleId,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.URole" >

    <selectKey keyProperty="roleId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_ROLE_ID.nextval from dual
    </selectKey>

    insert into htl_rfp.t_role
    <trim prefix="(" suffix=")" suffixOverrides="," >
        role_id,
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="roleName != null" >
        role_name,
      </if>
      <if test="roleCode != null" >
        role_code,
      </if>
      <if test="state != null" >
        state,
      </if>
      <if test="roleType != null" >
        role_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        #{roleId,jdbcType=INTEGER},
      <if test="orgId != null" >
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="roleName != null" >
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null" >
        #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="roleType != null" >
        #{roleType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.URole" >
    update htl_rfp.t_role
    <set >
      <if test="roleName != null" >
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null" >
        role_code = #{roleCode,jdbcType=VARCHAR},
      </if>

    </set>
    where role_id = #{roleId,jdbcType=INTEGER} and role_type = 1
  </update>

  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.URole" >
    update htl_rfp.t_role
    set org_id = #{orgId,jdbcType=INTEGER},
      role_name = #{roleName,jdbcType=VARCHAR},
      role_code = #{roleCode,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      role_type = #{roleType,jdbcType=INTEGER}
    where role_id = #{roleId,jdbcType=INTEGER}
  </update>

  <select id="findRoleByUserId" resultType="java.lang.String">
  		select r.role_code from htl_rfp.t_role r,htl_rfp.t_user_role ur where ur.role_id = r.role_id and ur.user_id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getSystemRoleCodeByUserId" resultType="java.lang.String">
  		select r.role_code from htl_rfp.t_role r,htl_rfp.t_user_role ur where  r.role_type = 1 and ur.role_id = r.role_id and ur.user_id = #{userId,jdbcType=INTEGER}
  </select>

	<!-- ==================================== 初始化数据（调用存储过程） ========================================= -->
	<insert id="initData">
		<!-- {call init_shiro_demo()} -->
	</insert>
	<!-- ==================================== 初始化数据（调用存储过程） ========================================= -->
  
  
  <!-- Mybatis 一对多 Demo  begin -->
  <!-- 查询用户全部的role & permission -->
  <resultMap id="findNowAllPermission_resultMap" extends="BaseResultMap" type="com.fangcang.rfp.common.dto.RoleDTO" >
  	<collection property="permissions" javaType="com.fangcang.rfp.common.dto.Permission">
  	 	<id column="pid" property="permissionId" jdbcType="INTEGER" />
    	<result column="pname" property="name" jdbcType="VARCHAR" />
    	<result column="url" property="url" jdbcType="VARCHAR" />
  	</collection>
  </resultMap>

  <!-- 根据用户ID，查询所有权限 -->
  <select id="findNowAllPermission" resultMap="findNowAllPermission_resultMap">
		select ur.role_id,ur.role_name,ur.role_type ,up.permission_id pid,up.url,up.name pname 
			from (select role_id from htl_rfp.t_user_role where user_id = #{userId,jdbcType=INTEGER}) uur
				left join htl_rfp.t_role ur on uur.role_id = ur.role_id
				left join htl_rfp.t_role_permission urp on urp.role_id = ur.role_id
				left join htl_rfp.t_permission up on up.permission_id = urp.permission_id
  </select>
   <!-- Mybatis 一对多 Demo  end -->
   
   
   <sql id="where" >
    <trim prefix="where" prefixOverrides="and |or">  
        <if test="roleName != null and roleName != ''">  
            and role_name = #{roleName,jdbcType=VARCHAR}  
        </if> 
        <if test="state != null and state != ''">  
            and state = #{state,jdbcType=INTEGER}
        </if>  
        <if test="orgId != null and orgId != ''">  
            and org_id = #{orgId,jdbcType=INTEGER}
        </if>  
        <if test="roleType != null and roleType != ''">  
            and role_type = #{roleType,jdbcType=INTEGER}
        </if>  
    </trim>
  </sql>
   <select id="selectByCondition" resultMap="BaseResultMap" parameterType="java.util.Map" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_role
    <include refid="where" />
  </select>

  <select id="queryAdminCountByOrgId" parameterType="long" resultType="int">
    select count(1)
    from htl_rfp.t_role
    where org_id = #{orgId} and role_code = 'ADMIN' and role_name = '管理员'
  </select>
   
   <!-- 根据用户Id查询角色列表 -->
   <select id="selectRoleByUserId" resultMap="BaseResultMap">
  		select r.role_id, r.org_id, r.role_name, r.role_code, r.state, r.role_type 
  		from htl_rfp.t_role r,htl_rfp.t_user_role ur where ur.role_id = r.role_id and ur.user_id = #{userId,jdbcType=BIGINT}
  </select>
  
  <select id="selectByCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_role
    where STATE = 1 and ROLE_TYPE = 1 and role_code = #{roleCode,jdbcType=VARCHAR}
  </select>
  
  <resultMap id="URoleResultMap" type="com.fangcang.rfp.common.dto.RoleDTO" extends="BaseResultMap">
  		<id column="user_id" property="userId" jdbcType="INTEGER" />
  </resultMap>
  <select id="selectUserRoleByUserIds" resultMap="URoleResultMap" parameterType="java.util.List" >
    select r.role_id, r.org_id, r.role_name, r.role_code, r.state, r.role_type,ur.user_id 
		from htl_rfp.t_user_role ur,htl_rfp.t_role r
	where r.role_id = ur.role_id and ur.user_id in 
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
    	#{item}
    </foreach>
  </select>

  <select id="querySystemRoleList" resultType="com.fangcang.rfp.common.entity.URole">
    select ROLE_ID roleId,
           ROLE_CODE roleCode,
           ROLE_NAME roleName
    from htl_rfp.t_role
    where STATE = 1 and ROLE_TYPE = 1
  </select>
   
</mapper>