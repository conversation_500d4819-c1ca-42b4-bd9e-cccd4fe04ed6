<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrgHotelGroupContactUserDao">

    <select id="queryOrgHotelGroupContactUserList" parameterType="com.fangcang.rfp.common.dto.request.OrgHotelGroupContactUserQueryRequest" resultType="com.fangcang.rfp.common.dto.response.OrgHotelGroupContactUserQueryResponse">
        SELECT
            t1.ENTERPRISE_ORG_ID AS enterpriseOrgId,
            t1.HOTEL_GROUP_ORG_ID AS hotelGroupOrgId,
            t1.HOTEL_GROUP_USER_ID AS hotelGroupUserId,
            t2.ORG_NAME AS enterpriseOrgName,
            t3.ORG_NAME AS hotelGroupOrgName,
            t4.USER_NAME AS userName,
            t4.MOBILE AS userContactMobile,
            t4.EMAIL AS userContactEmail,
            t1.CREATOR AS creator,
            t1.CREATE_TIME AS createTime,
            t1.MODIFY_TIME AS modifyTime,
            t1.MODIFIER AS modifier
        FROM htl_rfp.T_ORG_HOTEL_GROUP_CONTACT_USER t1
        LEFT JOIN htl_rfp.T_ORG t2 ON t1.ENTERPRISE_ORG_ID = t2.ORG_ID AND t2.ORG_TYPE = 3
        LEFT JOIN htl_rfp.T_ORG t3 ON t1.HOTEL_GROUP_ORG_ID = t3.ORG_ID
        LEFT JOIN htl_rfp.T_USER t4 ON t1.HOTEL_GROUP_USER_ID = t4.USER_ID
        WHERE 1= 1
        <if test="enterpriseOrgId != null and enterpriseOrgId > 0">
            AND t1.ENTERPRISE_ORG_ID = #{enterpriseOrgId}
        </if>
        <if test="hotelGroupOrgId != null and hotelGroupOrgId > 0">
            AND t1.HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId}
        </if>
        <if test="userName != null and userName !=''">
            AND  t4.USER_NAME LIKE CONCAT(CONCAT('%',#{userName}),'%')
        </if>
        <if test="userContactMobile != null and userContactMobile !=''">
            AND  t4.MOBILE = #{userContactMobile}
        </if>
        <if test="relatedOrgIdList != null and relatedOrgIdList.size() > 0">
            AND t1.ENTERPRISE_ORG_ID IN
            <foreach collection="relatedOrgIdList" item="relatedOrgId" open="(" close=")" separator=",">
                #{relatedOrgId}
            </foreach>
        </if>
        <if test="hotelGroupOrgIdList != null and hotelGroupOrgIdList.size() > 0">
            AND t1.HOTEL_GROUP_ORG_ID IN
            <foreach collection="hotelGroupOrgIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.CREATE_TIME DESC
    </select>

    <delete id="deleteByOrgId" >
        DELETE FROM htl_rfp.T_ORG_HOTEL_GROUP_CONTACT_USER
        WHERE ENTERPRISE_ORG_ID = #{enterpriseOrgId} AND HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId}
    </delete>

    <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.OrgHotelGroupContactUser">
        INSERT INTO htl_rfp.T_ORG_HOTEL_GROUP_CONTACT_USER(
            ENTERPRISE_ORG_ID,
            HOTEL_GROUP_ORG_ID,
            HOTEL_GROUP_USER_ID,
            CREATOR,
            CREATE_TIME
        ) VALUES (#{enterpriseOrgId},
                  #{hotelGroupOrgId},
                  #{hotelGroupUserId},
                  #{creator},
                  SYSDATE)
    </insert>

    <update id="updateHotelGroupUserId" parameterType="com.fangcang.rfp.common.entity.OrgHotelGroupContactUser">
        UPDATE htl_rfp.T_ORG_HOTEL_GROUP_CONTACT_USER
        SET HOTEL_GROUP_USER_ID = #{hotelGroupUserId},
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE ENTERPRISE_ORG_ID = #{enterpriseOrgId} AND HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId}
    </update>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM htl_rfp.T_ORG_HOTEL_GROUP_CONTACT_USER
        WHERE ENTERPRISE_ORG_ID = #{enterpriseOrgId} AND HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId} AND HOTEL_GROUP_USER_ID = #{userId}
    </select>

</mapper>