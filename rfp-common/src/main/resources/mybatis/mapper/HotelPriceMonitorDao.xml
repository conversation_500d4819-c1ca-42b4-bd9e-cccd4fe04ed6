<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelPriceMonitorDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelPriceMonitor">
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId" />
    <result column="ROOM_TYPE_NAME" jdbcType="VARCHAR" property="roomTypeName" />
    <result column="RATEPLAN_ID" jdbcType="VARCHAR" property="rateplanId" />
    <result column="RATEPLAN_NAME" jdbcType="VARCHAR" property="rateplanName" />
    <result column="SALE_DATE" jdbcType="TIMESTAMP" property="saleDate" />
    <result column="BREAKFAST_NUM" jdbcType="INTEGER" property="breakfastNum" />
    <result column="SALE_PRICE" jdbcType="DOUBLE" property="salePrice" />
    <result column="ROOM_STATUS" jdbcType="INTEGER" property="roomStatus" />
    <result column="COMPARE_RESULT" jdbcType="INTEGER" property="compareResult" />
    <result column="CANCEL_RESTRICTION_TYPE" jdbcType="INTEGER" property="cancelRestrictionType" />
    <result column="CANCEL_RESTRICTION_DAY" jdbcType="INTEGER" property="cancelRestrictionDay" />
    <result column="CANCEL_RESTRICTION_TIME" jdbcType="VARCHAR" property="cancelRestrictionTime" />
    <result column="PRICE_CODE" jdbcType="BIGINT" property="priceCode" />
    <result column="PRICE_YEAR" jdbcType="INTEGER" property="priceYear" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="DIF_PRICE" jdbcType="DOUBLE" property="difPrice" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="MONITOR_DAY_PRICE" jdbcType="DOUBLE" property="monitorDayPrice" />
    <result column="MONITOR_DAY_PRICE_TYPE" jdbcType="INTEGER" property="monitorDayPriceType" />
    <result column="LRA" jdbcType="INTEGER" property="lra" />
  </resultMap>

  <sql id="Base_Column_List">
    ORG_ID,PROJECT_ID,HOTEL_ID,ROOM_TYPE_ID,ROOM_TYPE_NAME,RATEPLAN_ID,RATEPLAN_NAME,SALE_DATE,
    BREAKFAST_NUM,SALE_PRICE,ROOM_STATUS,COMPARE_RESULT,CANCEL_RESTRICTION_TYPE,CANCEL_RESTRICTION_DAY,CANCEL_RESTRICTION_TIME,
    PRICE_CODE,PRICE_YEAR,CREATOR,CREATE_TIME,MODIFY_TIME,DIF_PRICE,CITY_CODE,MONITOR_DAY_PRICE,MONITOR_DAY_PRICE_TYPE,LRA
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor">
    insert into htl_rfp.T_HOTEL_PRICE_MONITOR (ORG_ID, PROJECT_ID, HOTEL_ID,
      ROOM_TYPE_ID, ROOM_TYPE_NAME, RATEPLAN_ID, 
      RATEPLAN_NAME, SALE_DATE, BREAKFAST_NUM, 
      SALE_PRICE, ROOM_STATUS, COMPARE_RESULT, 
      CANCEL_RESTRICTION_TYPE, CANCEL_RESTRICTION_DAY, 
      CANCEL_RESTRICTION_TIME, PRICE_CODE, PRICE_YEAR, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME, DIF_PRICE, CITY_CODE, MONITOR_DAY_PRICE, MONITOR_DAY_PRICE_TYPE,LRA
      )
    values (#{orgId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, 
      #{roomTypeId,jdbcType=DECIMAL}, #{roomTypeName,jdbcType=VARCHAR}, #{rateplanId,jdbcType=VARCHAR}, 
      #{rateplanName,jdbcType=VARCHAR}, #{saleDate,jdbcType=TIMESTAMP}, #{breakfastNum,jdbcType=DECIMAL}, 
      #{salePrice,jdbcType=DECIMAL}, #{roomStatus,jdbcType=DECIMAL}, #{compareResult,jdbcType=DECIMAL}, 
      #{cancelRestrictionType,jdbcType=DECIMAL}, #{cancelRestrictionDay,jdbcType=DECIMAL}, 
      #{cancelRestrictionTime,jdbcType=VARCHAR}, #{priceCode,jdbcType=DECIMAL}, #{priceYear,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
      SYSDATE, #{difPrice,jdbcType=DECIMAL}, #{cityCode,jdbcType=VARCHAR},#{monitorDayPrice,jdbcType=DECIMAL},
            #{monitorDayPriceType,jdbcType=INTEGER},#{lra,jdbcType=INTEGER}
      )
  </insert>
  <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.ProjectPoi">
      insert into htl_rfp.T_HOTEL_PRICE_MONITOR (ORG_ID, PROJECT_ID, HOTEL_ID,
      ROOM_TYPE_ID, ROOM_TYPE_NAME, RATEPLAN_ID,
      RATEPLAN_NAME, SALE_DATE, BREAKFAST_NUM,
      SALE_PRICE, ROOM_STATUS, COMPARE_RESULT,
      CANCEL_RESTRICTION_TYPE, CANCEL_RESTRICTION_DAY,
      CANCEL_RESTRICTION_TIME, PRICE_CODE, PRICE_YEAR,
      CREATOR, CREATE_TIME, MODIFIER,
      MODIFY_TIME, DIF_PRICE, CITY_CODE, MONITOR_DAY_PRICE, MONITOR_DAY_PRICE_TYPE,LRA
      )
    <foreach collection="list" item="item" index="index" separator="union all">
        (select #{item.orgId,jdbcType=DECIMAL}, #{item.projectId,jdbcType=DECIMAL}, #{item.hotelId,jdbcType=DECIMAL},
        #{item.roomTypeId,jdbcType=DECIMAL}, #{item.roomTypeName,jdbcType=VARCHAR}, #{item.rateplanId,jdbcType=VARCHAR},
        #{item.rateplanName,jdbcType=VARCHAR}, #{item.saleDate,jdbcType=TIMESTAMP}, #{item.breakfastNum,jdbcType=DECIMAL},
        #{item.salePrice,jdbcType=DECIMAL}, #{item.roomStatus,jdbcType=DECIMAL}, #{item.compareResult,jdbcType=DECIMAL},
        #{item.cancelRestrictionType,jdbcType=DECIMAL}, #{item.cancelRestrictionDay,jdbcType=DECIMAL},
        #{item.cancelRestrictionTime,jdbcType=VARCHAR}, #{item.priceCode,jdbcType=DECIMAL}, #{item.priceYear,jdbcType=DECIMAL},
        #{item.creator,jdbcType=VARCHAR}, SYSDATE, #{item.modifier,jdbcType=VARCHAR},
        SYSDATE, #{item.difPrice,jdbcType=DECIMAL}, #{item.cityCode,jdbcType=VARCHAR},#{item.monitorDayPrice,jdbcType=DECIMAL},
        #{item.monitorDayPriceType,jdbcType=INTEGER},#{item.lra,jdbcType=INTEGER} from dual
        )
    </foreach>
  </insert>
  <select id="selectHotelPriceMonitorByUnique" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM htl_rfp.T_HOTEL_PRICE_MONITOR t
        WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId} AND t.SALE_DATE = #{saleDate} AND t.ROOM_TYPE_ID = #{roomTypeId} AND t.BREAKFAST_NUM  = #{breakfastNum}
  </select>

  <select id="selectHotelPriceMonitorViolationDay" resultType="java.util.Date" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor">
      SELECT DISTINCT(SALE_DATE) AS saleDate
      FROM htl_rfp.T_HOTEL_PRICE_MONITOR t
      WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId}
      AND (COMPARE_RESULT = 1 OR  COMPARE_RESULT = 2)
      <if test="saleDate != null   ">
          AND t.SALE_DATE >= #{saleDate}
      </if>
      ORDER BY saleDate
  </select>
    <select id="selectHotelPriceMonitorList" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM htl_rfp.T_HOTEL_PRICE_MONITOR t
        WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId}
        <if test="orgId != null">
            AND t.ORG_ID = #{orgId}
        </if>
        <if test="saleDate != null   ">
            AND t.SALE_DATE >= #{saleDate}
        </if>
        <if test="compareResult != null">
            AND t.COMPARE_RESULT = #{compareResult}
        </if>
        <if test="roomTypeId != null">
            AND t.ROOM_TYPE_ID = #{roomTypeId}
        </if>
        <if test="breakfastNum != null">
            AND t.BREAKFAST_NUM = #{breakfastNum}
        </if>
        ORDER BY t.SALE_DATE
    </select>

    <select id="selectHotelPriceLast15MonitorList"  parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor" resultMap="BaseResultMap">
        SELECT * FROM (
            select <include refid="Base_Column_List"/>
                from htl_rfp.T_HOTEL_PRICE_MONITOR t
                where t.PROJECT_ID = #{projectId} and t.HOTEL_ID = #{hotelId}
                <if test="orgId != null">
                    and t.ORG_ID = #{orgId}
                </if>
                <if test="saleDate != null   ">
                    and t.SALE_DATE >= #{saleDate}
                </if>
                <if test="compareResult != null">
                    and t.COMPARE_RESULT = #{compareResult}
                </if>
                order by t.SALE_DATE DESC
        )  WHERE ROWNUM &lt;= 15

    </select>

    <update id="saveOrUpdateHotelPriceMonitor" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor">
        MERGE INTO htl_rfp.T_HOTEL_PRICE_MONITOR a
        USING (
        SELECT
            #{projectId} AS projectId, #{hotelId} AS hotelId,#{saleDate} AS saleDate, #{roomTypeId} AS roomTypeId, #{breakfastNum} AS breakfastNum
        FROM dual
        ) b
        ON (a.PROJECT_ID = #{projectId} and a.HOTEL_ID = #{hotelId} and a.SALE_DATE = #{saleDate} and a.ROOM_TYPE_ID = #{roomTypeId} and a.BREAKFAST_NUM = #{breakfastNum})
        when matched then
        update set
            COMPARE_RESULT = #{compareResult}
            ,PRICE_YEAR = #{priceYear}
            ,MODIFY_TIME = SYSDATE
            ,ROOM_TYPE_NAME = #{roomTypeName,jdbcType=VARCHAR}
            ,RATEPLAN_ID = #{rateplanId,jdbcType=VARCHAR}
            ,RATEPLAN_NAME = #{rateplanName,jdbcType=VARCHAR}
            ,SALE_PRICE = #{salePrice,jdbcType=DECIMAL}
            ,ROOM_STATUS = #{roomStatus,jdbcType=INTEGER}
            ,CANCEL_RESTRICTION_TYPE = #{cancelRestrictionType,jdbcType=INTEGER}
            ,CANCEL_RESTRICTION_DAY = #{cancelRestrictionDay,jdbcType=INTEGER}
            ,CANCEL_RESTRICTION_TIME = #{cancelRestrictionTime,jdbcType=VARCHAR}
            ,PRICE_CODE = #{priceCode,jdbcType=INTEGER}
            ,MODIFIER = #{modifier,jdbcType=VARCHAR}
            ,DIF_PRICE = #{difPrice,jdbcType=VARCHAR}
            ,MONITOR_DAY_PRICE = #{monitorDayPrice,jdbcType=DECIMAL}
            ,MONITOR_DAY_PRICE_TYPE = #{monitorDayPriceType,jdbcType=INTEGER}
            ,LRA = #{lra,jdbcType=INTEGER}
        where PROJECT_ID = #{projectId} and HOTEL_ID = #{hotelId} and SALE_DATE = #{saleDate} and ROOM_TYPE_ID = #{roomTypeId} AND BREAKFAST_NUM = #{breakfastNum}
        when not matched then
        insert(
            ORG_ID, PROJECT_ID, HOTEL_ID,
            ROOM_TYPE_ID, ROOM_TYPE_NAME, RATEPLAN_ID,
            RATEPLAN_NAME, SALE_DATE, BREAKFAST_NUM,
            SALE_PRICE, ROOM_STATUS, COMPARE_RESULT,
            CANCEL_RESTRICTION_TYPE, CANCEL_RESTRICTION_DAY,
            CANCEL_RESTRICTION_TIME, PRICE_CODE, PRICE_YEAR,
            CREATOR, CREATE_TIME, MODIFIER,
            MODIFY_TIME, DIF_PRICE, CITY_CODE, MONITOR_DAY_PRICE, MONITOR_DAY_PRICE_TYPE,LRA
        )values(
            #{orgId}, #{projectId}, #{hotelId},
            #{roomTypeId}, #{roomTypeName,jdbcType=VARCHAR}, #{rateplanId,jdbcType=VARCHAR},
            #{rateplanName,jdbcType=VARCHAR}, #{saleDate}, #{breakfastNum,jdbcType=INTEGER},
            #{salePrice,jdbcType=DECIMAL}, #{roomStatus,jdbcType=INTEGER}, #{compareResult},
            #{cancelRestrictionType,jdbcType=INTEGER}, #{cancelRestrictionDay,jdbcType=INTEGER},
            #{cancelRestrictionTime,jdbcType=VARCHAR}, #{priceCode,jdbcType=INTEGER}, #{priceYear},
            #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
            SYSDATE, #{difPrice,jdbcType=DECIMAL}, #{cityCode,jdbcType=VARCHAR},
            #{monitorDayPrice,jdbcType=DECIMAL}, #{monitorDayPriceType,jdbcType=INTEGER}, #{lra,jdbcType=INTEGER}
        )
    </update>

    <select id="queryHotelIdsByProject" resultType="java.lang.String" parameterType="java.util.List">
        SELECT  DISTINCT (t.project_id || '_' || t.hotel_id) AS project_hotel_id
        FROM htl_rfp.T_HOTEL_PRICE_MONITOR t WHERE t.project_id IN
        <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
    </select>

    <select id="selectMonitorRoomTypeId" resultType="java.lang.Long">
        SELECT max(ROOM_TYPE_ID)
        FROM htl_rfp.T_HOTEL_PRICE_MONITOR t
        WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId} AND t.SALE_DATE =
                                                                          (SELECT MAX(SALE_DATE) FROM htl_rfp.T_HOTEL_PRICE_MONITOR
                                                                           WHERE PROJECT_ID = #{projectId} AND HOTEL_ID = #{hotelId})               )
    </select>

    <insert id="backupData" parameterType="java.util.Date">
        INSERT INTO htl_rfp.T_HOTEL_PRICE_MONITOR_BACKUP (
            ORG_ID, PROJECT_ID, HOTEL_ID,
            ROOM_TYPE_ID, ROOM_TYPE_NAME, RATEPLAN_ID,
            RATEPLAN_NAME, SALE_DATE, BREAKFAST_NUM,
            SALE_PRICE, ROOM_STATUS, COMPARE_RESULT,
            CANCEL_RESTRICTION_TYPE, CANCEL_RESTRICTION_DAY,
            CANCEL_RESTRICTION_TIME, PRICE_CODE, PRICE_YEAR,
            CREATOR, CREATE_TIME, MODIFIER,
            MODIFY_TIME, DIF_PRICE, CITY_CODE, MONITOR_DAY_PRICE, MONITOR_DAY_PRICE_TYPE,LRA
        )(
         SELECT
            ORG_ID, PROJECT_ID, HOTEL_ID,
            ROOM_TYPE_ID, ROOM_TYPE_NAME, RATEPLAN_ID,
            RATEPLAN_NAME, SALE_DATE, BREAKFAST_NUM,
            SALE_PRICE, ROOM_STATUS, COMPARE_RESULT,
            CANCEL_RESTRICTION_TYPE, CANCEL_RESTRICTION_DAY,
            CANCEL_RESTRICTION_TIME, PRICE_CODE, PRICE_YEAR,
            CREATOR, CREATE_TIME, MODIFIER,
            MODIFY_TIME, DIF_PRICE, CITY_CODE, MONITOR_DAY_PRICE, MONITOR_DAY_PRICE_TYPE,LRA
        FROM htl_rfp.T_HOTEL_PRICE_MONITOR WHERE SALE_DATE &lt; #{saleDateBefore}
        )
    </insert>

    <delete id="deleteBySaleDateBeforeAfterBackup"  parameterType="java.util.Date">
        DELETE
        FROM
            htl_rfp.T_HOTEL_PRICE_MONITOR
        WHERE
            SALE_DATE &lt; #{saleDateBefore}
    </delete>

    <delete id="deleteBySaleDateAfter" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitor">
        DELETE
        FROM
            htl_rfp.T_HOTEL_PRICE_MONITOR
        WHERE
            PROJECT_ID = #{projectId,jdbcType=BIGINT}
          AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
          AND SALE_DATE >= #{saleDate}
    </delete>
</mapper>