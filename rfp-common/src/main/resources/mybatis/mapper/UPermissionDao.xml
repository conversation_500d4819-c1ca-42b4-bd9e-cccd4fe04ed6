<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.UPermissionDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.Permission" >
    <id column="permission_id" property="permissionId" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="pid" property="pid" jdbcType="INTEGER" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="is_platform" property="isPlatform" jdbcType="INTEGER" />
    <result column="is_supplier" property="isSupplier" jdbcType="INTEGER" />
    <result column="is_distributor" property="isDistributor" jdbcType="INTEGER" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    permission_id, name, type, pid, url, is_platform, is_supplier, is_distributor, 
    state, description
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_permission
    where permission_id = #{permissionId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from htl_rfp.t_permission
    where permission_id = #{permissionId,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.UPermission" >
    <selectKey keyProperty="permissionId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_PERMISSION_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.t_permission
    <trim prefix="(" suffix=")" suffixOverrides="," >
        permission_id,
      <if test="name != null" >
        name,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="pid != null" >
        pid,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="isPlatform != null" >
        is_platform,
      </if>
      <if test="isSupplier != null" >
        is_supplier,
      </if>
      <if test="isDistributor != null" >
        is_distributor,
      </if>
      <if test="state != null" >
        state,
      </if>
      <if test="description != null" >
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        #{permissionId,jdbcType=INTEGER},
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="pid != null" >
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="isPlatform != null" >
        #{isPlatform,jdbcType=INTEGER},
      </if>
      <if test="isSupplier != null" >
        #{isSupplier,jdbcType=INTEGER},
      </if>
      <if test="isDistributor != null" >
        #{isDistributor,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.UPermission" >
    update htl_rfp.t_permission
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="pid != null" >
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="isPlatform != null" >
        is_platform = #{isPlatform,jdbcType=INTEGER},
      </if>
      <if test="isSupplier != null" >
        is_supplier = #{isSupplier,jdbcType=INTEGER},
      </if>
      <if test="isDistributor != null" >
        is_distributor = #{isDistributor,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where permission_id = #{permissionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.UPermission" >
    update htl_rfp.t_permission
    set name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      pid = #{pid,jdbcType=INTEGER},
      url = #{url,jdbcType=VARCHAR},
      is_platform = #{isPlatform,jdbcType=INTEGER},
      is_supplier = #{isSupplier,jdbcType=INTEGER},
      is_distributor = #{isDistributor,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR}
    where permission_id = #{permissionId,jdbcType=INTEGER}
  </update>
  
  <select id="selectPermissionById" resultType="com.fangcang.rfp.common.dto.PermissionBo">
		select up.permission_id permissionId,ur.role_id roleId,up.name,up.url,ifnull(ur.role_id,0) marker 
		from htl_rfp.t_permission up
		left join htl_rfp.t_role_permission urp
				 on urp.permission_id = up.permission_id
		left join (select role_id from t_role where role_id = #{id,jdbcType=BIGINT}) ur 
				 on ur.role_id = urp.role_id
		group by up.permission_id
  </select>
  
  <!-- 根据用ID查询permission -->
  <select id="findPermissionByUserId" resultType="java.lang.String" parameterType="java.util.Map">
		select p.url from htl_rfp.t_permission p , htl_rfp.t_role_permission rp, htl_rfp.t_user_role ur
		where p.permission_id = rp.permission_id and rp.role_id = ur.role_id 
			and ur.user_id = #{userId,jdbcType=INTEGER}
			and p.state = 1 
		  <if test="isPlatform != null and isPlatform != ''" >
	       	and p.is_platform = #{isPlatform,jdbcType=VARCHAR}
	      </if>
	      <if test="isSupplier != null and isSupplier != ''" >
	       	and p.is_supplier = #{isSupplier,jdbcType=VARCHAR},
	      </if>
	      <if test="isDistributor != null and isDistributor != ''" >
	       	and p.is_distributor = #{isDistributor,jdbcType=VARCHAR},
	      </if>
  </select>
  
  
</mapper>