<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelPriceMonitorRoomDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
    <result column="PRICE_MONITOR_ROOM_ID" jdbcType="DECIMAL" property="priceMonitorRoomId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="DECIMAL" property="projectIntentHotelId" />
    <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId" />
    <result column="BREAKFAST_NUM" jdbcType="INTEGER" property="breakfastNum" />
    <result column="HOTEL_PRICE_GROUP_ID" jdbcType="BIGINT" property="hotelPriceGroupId" />
    <result column="IS_DELETED" jdbcType="BIGINT" property="isDeleted" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
      PRICE_MONITOR_ROOM_ID,PROJECT_ID,HOTEL_ID,PROJECT_INTENT_HOTEL_ID,ROOM_TYPE_ID,BREAKFAST_NUM,HOTEL_PRICE_GROUP_ID,IS_DELETED,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
      <selectKey keyProperty="priceMonitorRoomId" resultType="_long" order="BEFORE">
          select htl_rfp.SEQ_PRICE_MONITOR_ROOMID.nextval from dual
      </selectKey>
    insert into htl_rfp.T_HOTEL_PRICE_MONITOR_ROOM (
      PRICE_MONITOR_ROOM_ID, PROJECT_ID, HOTEL_ID,PROJECT_INTENT_HOTEL_ID,
      ROOM_TYPE_ID, BREAKFAST_NUM, HOTEL_PRICE_GROUP_ID,
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME
      )
    values (#{priceMonitorRoomId,jdbcType=BIGINT}, #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL},#{projectIntentHotelId,jdbcType=DECIMAL},
      #{roomTypeId,jdbcType=DECIMAL}, #{breakfastNum,jdbcType=INTEGER},#{hotelPriceGroupId,jdbcType=BIGINT},
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
      SYSDATE
      )
  </insert>

    <update id="delete" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
        UPDATE HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        SET IS_DELETED = PRICE_MONITOR_ROOM_ID,
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_ID = #{projectId,jdbcType=BIGINT}
          AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        <if test="hotelPriceGroupId != null">
            AND HOTEL_PRICE_GROUP_ID =  #{hotelPriceGroupId,jdbcType=BIGINT}
        </if>
        <if test="roomTypeId != null">
            AND ROOM_TYPE_ID =  #{roomTypeId,jdbcType=BIGINT}
        </if>
        <if test="breakfastNum != null">
            AND BREAKFAST_NUM =  #{breakfastNum,jdbcType=INTEGER}
        </if>
    </update>

    <delete id="deleteData" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
        DELETE FROM HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        <if test="hotelPriceGroupId != null">
            AND HOTEL_PRICE_GROUP_ID =  #{hotelPriceGroupId,jdbcType=BIGINT}
        </if>
        <if test="roomTypeId != null">
            AND ROOM_TYPE_ID =  #{roomTypeId,jdbcType=BIGINT}
        </if>
        <if test="breakfastNum != null">
            AND BREAKFAST_NUM =  #{breakfastNum,jdbcType=INTEGER}
        </if>
    </delete>

    <update id="updateNoDelete" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
        UPDATE HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        SET IS_DELETED =  0,
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
        PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        <if test="roomTypeId != null">
            AND ROOM_TYPE_ID =  #{roomTypeId,jdbcType=BIGINT}
        </if>
        <if test="breakfastNum != null">
            AND BREAKFAST_NUM =  #{breakfastNum,jdbcType=INTEGER}
        </if>
    </update>

    <select id="getMonitorRoom" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM
            HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        WHERE
            PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        AND ROOM_TYPE_ID = #{roomTypeId,jdbcType=BIGINT}
        AND BREAKFAST_NUM = #{breakfastNum,jdbcType=INTEGER}
    </select>

    <select id="selectMonitorRoomList" resultType="com.fangcang.rfp.common.entity.HotelPriceMonitorRoom" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
            HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        WHERE
            PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        AND IS_DELETED = 0
        ORDER BY CREATE_TIME ASC
    </select>

    <select id="selectMonitorRoomListByProjectIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
            HTL_RFP.T_HOTEL_PRICE_MONITOR_ROOM
        WHERE
            PROJECT_ID IN
           <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
               #{projectId,jdbcType=BIGINT}
           </foreach>
          AND HOTEL_ID IN
            <foreach collection="hotelIdList" item="hotelId" open="(" close=")" separator=",">
                #{hotelId,jdbcType=BIGINT}
            </foreach>
           AND IS_DELETED = 0
           ORDER BY CREATE_TIME ASC
    </select>
</mapper>