<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.UUserRoleDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.UUserRole" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, role_id
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from htl_rfp.t_user_role
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from htl_rfp.t_user_role
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.UUserRole" >
    <selectKey keyProperty="id" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_USER_ROLE_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.t_user_role (id, user_id, role_id
      )
    values (htl_rfp.SEQ_RFP_USER_ROLE_ID.nextval, #{userId,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.UUserRole" >
    update htl_rfp.t_user_role
    <set >
      <if test="roleId != null" >
        role_id = #{roleId,jdbcType=INTEGER}
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.UUserRole" >
    update htl_rfp.t_user_role
    set user_id = #{userId,jdbcType=INTEGER},
      role_id = #{roleId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

   <!-- 根据用户ID删除-->
  <delete id="deleteByUserId">
  	delete from htl_rfp.t_user_role where user_id =  #{id,jdbcType=INTEGER}
  </delete>
  
  <delete id="deleteRoleByUserIds">
  	delete from htl_rfp.t_user_role where user_id in(${userIds})
  </delete>

  <!-- 根据用户roleId查询用户ID -->
  <select id="findUserIdByRoleId" resultType="java.lang.Long">
  	select uid from htl_rfp.t_user_role where role_id =  #{id,jdbcType=INTEGER}
  </select>
  
  <insert id="insertBatch" parameterType="java.util.List" >
	insert into htl_rfp.t_user_role (user_id, role_id
	)
	values 
	<foreach collection ="list" item="ur" index= "index" separator =",">
	(
		#{ur.userId,jdbcType=INTEGER},
		#{ur.roleId,jdbcType=INTEGER}
	)
    </foreach >
  </insert>

</mapper>