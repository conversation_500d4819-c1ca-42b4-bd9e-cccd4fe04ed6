<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectCustomStrategyOptionDao">

    <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.ProjectCustomStrategyOption">
        insert into htl_rfp.T_PROJECT_C_STRATEGY_OPTION (
            OPTION_ID, CUSTOM_TEND_STRATEGY_ID, OPTION_NAME,
            DISPLAY_ORDER, WEIGHT_SCORE,
            CREATOR, CREATE_TIME
        )
        select htl_rfp.SEQ_RFP_STRATEGY_OPTION_ID.nextval AS optionId, t.* from(
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.customTendStrategyId,jdbcType=BIGINT} AS customTendStrategyId,
            #{item.optionName,jdbcType=VARCHAR} AS optionName,
            #{item.displayOrder,jdbcType=INTEGER} AS displayOrder,
            #{item.weightScore,jdbcType=DECIMAL} AS weightScore,
            #{item.creator,jdbcType=VARCHAR} AS creator,
            SYSDATE AS createTime
            from dual
        </foreach>)t
    </insert>

    <delete id="deleteByCustomStrategyId">
        DELETE FROM htl_rfp.T_PROJECT_C_STRATEGY_OPTION WHERE CUSTOM_TEND_STRATEGY_ID = #{customStrategyId}
    </delete>
    <select id="selectNextOptionId" resultType="java.lang.Long">
        select htl_rfp.SEQ_RFP_STRATEGY_OPTION_ID.nextval from dual
    </select>
    <select id="queryByCustomStrategyIds" resultType="com.fangcang.rfp.common.dto.CustomStrategyOptionVO">
        SELECT
            OPTION_ID AS optionId,
            CUSTOM_TEND_STRATEGY_ID AS strategyId,
            OPTION_NAME AS optionName,
            DISPLAY_ORDER AS displayOrder,
            WEIGHT_SCORE AS weightScore
         FROM htl_rfp.T_PROJECT_C_STRATEGY_OPTION
         WHERE CUSTOM_TEND_STRATEGY_ID IN
               <foreach collection="customStrategyIds" separator="," open="(" close=")" item="customStrategyId">
                   #{customStrategyId}
               </foreach>
        ORDER BY DISPLAY_ORDER ASC
    </select>

    <update id="updateCustomStrategyOption" parameterType="com.fangcang.rfp.common.entity.ProjectCustomStrategyOption">
        UPDATE htl_rfp.T_PROJECT_C_STRATEGY_OPTION
        SET WEIGHT_SCORE = #{weightScore},
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            CUSTOM_TEND_STRATEGY_ID = #{customTendStrategyId}
        AND OPTION_ID = #{optionId}
    </update>

</mapper>