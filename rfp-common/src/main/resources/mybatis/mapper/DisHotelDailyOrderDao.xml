<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.DisHotelDailyOrderDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.DisHotelDailyOrder">
    <result column="DISTRIBUTOR_CODE" jdbcType="VARCHAR" property="distributorCode" />
    <result column="HOTEL_ID" jdbcType="INTEGER" property="hotelId" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="BOOK_DATE" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="BOOK_COUNT" jdbcType="INTEGER" property="bookCount" />
    <result column="ROOM_NIGHT_COUNT" jdbcType="INTEGER" property="roomNightCount" />
    <result column="ORDER_AMOUNT" jdbcType="DECIMAL" property="orderAmount" />
    <result column="SAVED_AMOUNT" jdbcType="DECIMAL" property="savedAmount" />
    <result column="HAS_OTA_PRICE_ORDER_AMOUNT" jdbcType="DECIMAL" property="hasOtaPriceOrderAmount" />
    <result column="TOTAL_OTA_PRICE" jdbcType="DECIMAL" property="totalOtaPrice" />
    <result column="SAVED_AMOUNT_ORDER_COUNT" jdbcType="INTEGER" property="savedAmountOrderCount" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.DisHotelDailyOrder">
    insert into T_DIS_HOTEL_DAILY_ORDER (DISTRIBUTOR_CODE, HOTEL_ID, CITY_CODE, 
      BOOK_DATE, BOOK_COUNT, ROOM_NIGHT_COUNT, 
      ORDER_AMOUNT, SAVED_AMOUNT, HAS_OTA_PRICE_ORDER_AMOUNT,TOTAL_OTA_PRICE,SAVED_AMOUNT_ORDER_COUNT,
      CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
      )
    values (#{distributorCode,jdbcType=VARCHAR}, #{hotelId,jdbcType=INTEGER}, #{cityCode,jdbcType=VARCHAR},
      #{bookDate,jdbcType=TIMESTAMP}, #{bookCount,jdbcType=INTEGER}, #{roomNightCount,jdbcType=INTEGER},
      #{orderAmount,jdbcType=DECIMAL}, #{savedAmount,jdbcType=DECIMAL},
      #{hasOtaPriceOrderAmount,jdbcType=DECIMAL}, #{totalOtaPrice,jdbcType=DECIMAL}, #{savedAmountOrderCount,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>

    <select id="selectHotelSavedAmountStatList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
            resultType="com.fangcang.rfp.common.dto.response.DisHotelDailyOrderResponse">
        select
            t.savedAmount,
            t.hotelId,
            t.hasOtaPriceOrderAmount,
            t.totalOtaPrice,
            t.roomNightCount,
            t.orderAmount
        from (
            select o.hotel_id hotelId ,
                sum(o.SAVED_AMOUNT) savedAmount,
                sum(o.HAS_OTA_PRICE_ORDER_AMOUNT) hasOtaPriceOrderAmount,
                sum(o.TOTAL_OTA_PRICE) totalOtaPrice,
                sum(o.ROOM_NIGHT_COUNT) roomNightCount,
                sum(o.ORDER_AMOUNT) orderAmount
            from T_DIS_HOTEL_DAILY_ORDER o
            where 1 = 1
            and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
            AND  o.TOTAL_OTA_PRICE IS NOT NULL AND o.HAS_OTA_PRICE_ORDER_AMOUNT IS NOT NULL
            <if test="orgId != null and orgId > 0">
                and exists ( select 1
                        from T_ORDER_MONITOR_CONFIG c
                        where o.DISTRIBUTOR_CODE = c.distributor_code
                        and c.STATE = 1
                        and c.org_id = #{orgId}
                    )
            </if>
            <if test="hotelIdList != null and hotelIdList.size() > 0">
                and o.HOTEL_ID in
                <foreach item="item" index="index" collection="hotelIdList" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        group by o.hotel_id
        )t
        order by t.savedAmount desc
    </select>

  <select id="selectOrderMonitorHotelStatList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
          resultType="com.fangcang.rfp.common.dto.response.DisHotelDailyOrderStatResponse">
        SELECT
            o.BOOK_DATE AS bookDate,
            NVL(SUM(o.BOOK_COUNT),0) AS bookCount,
            NVL(SUM(o.ROOM_NIGHT_COUNT),0) AS roomNightCount,
            NVL(SUM(o.ORDER_AMOUNT),0) AS orderAmount,
            NVL(SUM(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END),0) AS savedAmount,
            NVL( SUM(o.HAS_OTA_PRICE_ORDER_AMOUNT),0) as hasOtaPriceOrderAmount,
            NVL(SUM(o.TOTAL_OTA_PRICE),0) AS totalOtaPrice,
            NVL(SUM(o.SAVED_ROOM_NIGHT_COUNT),0) AS savedRoomNightCount,
            NVL(SUM(o.SAVED_AMOUNT_ORDER_COUNT),0) AS savedAmountOrderCount
        FROM HTL_RFP.T_DIS_HOTEL_DAILY_ORDER o
        WHERE o.HOTEL_ID = #{hotelId}
          AND to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE AND o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
              AND exists ( select 1
                           from HTL_RFP.T_ORDER_MONITOR_CONFIG c
                           where o.DISTRIBUTOR_CODE = c.distributor_code
                             and c.STATE = 1
                            <if test="orgId != null">
                                and c.org_id = #{orgId}
                            </if>
                )

        GROUP BY o.HOTEL_ID, o.BOOK_DATE
        ORDER BY o.BOOK_DATE ASC
  </select>
  <select id="selectOrderMonitorHotelList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
          resultType="com.fangcang.rfp.common.dto.response.DisHotelDailyOrderResponse">
      select
            h.chn_name hotelName,
            t.roomNightCount,
            t.orderAmount,
            t.savedAmount,
            t.hotelId,
            t.hasOtaPriceOrderAmount,
            t.totalOtaPrice,
            t.orgId,
            t.isFollowHotel,
            CASE t.totalOtaPrice
                WHEN 0 THEN 0
                ELSE t.savedAmount/t.totalOtaPrice
                END AS savedAmountRate,
            CASE t.roomNightCount
                WHEN 0 THEN 0
                ELSE t.savedRoomNightCount/t.roomNightCount
                END AS savedRoomNightRate
      from (
      select
          o.hotel_id hotelId ,
          omc.ORG_ID orgId,
          NVL(sum(o.ROOM_NIGHT_COUNT),0) roomNightCount,
          NVL(sum(o.ORDER_AMOUNT),0) orderAmount,
          NVL(sum(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END) ,0) savedAmount,
          NVL(sum(o.HAS_OTA_PRICE_ORDER_AMOUNT),0) hasOtaPriceOrderAmount,
          NVL(sum(o.TOTAL_OTA_PRICE),0) totalOtaPrice,
          NVL(sum(o.SAVED_ROOM_NIGHT_COUNT),0) savedRoomNightCount,
          MAX(CASE WHEN ofh.HOTEL_ID IS NULL THEN 0 ELSE 1 END ) AS isFollowHotel
      from T_DIS_HOTEL_DAILY_ORDER o
      LEFT JOIN HTL_RFP.T_ORDER_MONITOR_CONFIG omc ON o.DISTRIBUTOR_CODE = omc.DISTRIBUTOR_CODE
      LEFT JOIN HTL_RFP.T_ORG_FOLLOW_HOTEL ofh ON ofh.ORG_ID = omc.ORG_ID AND ofh.HOTEL_ID = o.HOTEL_ID
      where omc.STATE = 1
          <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
              and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
          </if>
          <if test="orgId != null">
              and omc.org_id = #{orgId}
          </if>
          <if test="provinceId != null and provinceId > 0">
              AND o.CITY_CODE IN (
              SELECT DATACODE FROM HTL_BASE.T_AREADATA WHERE DATATYPE = 3 AND PARENTID  = #{provinceId}
              )
          </if>
        <if test="isFollowHotel != null and isFollowHotel > 0">
            AND ofh.HOTEL_ID IS NOT NULL
        </if>
          <if test="hotelId != null ">
              and o.hotel_id = #{hotelId}
          </if>
          <if test="cityCode != null and cityCode != ''">
              and o.CITY_CODE = #{cityCode}
          </if>
          <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
              AND omc.ORG_ID IN
              <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                  #{userRelatedOrgId}
              </foreach>
          </if>
          <if test="userIds != null">
              and exists(select 1
              from T_ORDER_MONITOR_CONFIG cg ,htl_rfp.t_project tp ,htl_rfp.t_project_intent_hotel tph
              where o.DISTRIBUTOR_CODE = cg.distributor_code and cg.org_id = tp.tender_org_id
              and tp.project_id = tph.project_id and o.hotel_id = tph.hotel_id
              and cg.STATE = 1 and cg.org_id = #{orgId} and tph.distributor_contact_uid IN
              <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                  #{userId}
              </foreach>
              <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                  AND tp.TENDER_ORG_ID IN
                  <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                      #{userRelatedOrgId}
                  </foreach>
              </if>
              )
          </if>
      group by o.HOTEL_ID,omc.ORG_ID
      )t
      left join HTL_INFO.T_HOTEL h  on t.hotelId = h.HOTELID
      <if test="sortColumn == 0 and sortType == 0">
          order by roomNightCount ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 0 and sortType == 1">
          order by roomNightCount DESC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 1 and sortType == 0">
          order by orderAmount ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 1 and sortType == 1">
          order by orderAmount DESC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 2 and sortType == 0">
          order by savedAmount ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 2 and sortType == 1">
          order by t.savedAmount DESC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 3 and sortType == 0">
          order by savedAmountRate ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 3 and sortType == 1">
          order by savedAmountRate DESC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 4 and sortType == 0">
          order by savedRoomNightRate ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 4 and sortType == 1">
          order by savedRoomNightRate DESC, orgId ASC, hotelId ASC
      </if>
  </select>

    <select id="selectHotelSavedAmount" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
        resultType="com.fangcang.rfp.common.dto.HotelSavedAmountDto">
        SELECT
            o.hotel_id hotelId ,
            o.DISTRIBUTOR_CODE distributorCode,
            NVL(sum(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END) ,0) savedAmount,
            NVL(sum(o.TOTAL_OTA_PRICE),0) totalOtaPrice
        FROM T_DIS_HOTEL_DAILY_ORDER o
        WHERE 1=1
        <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
            and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
        </if>
        <if test="hotelId != null ">
            and o.hotel_id = #{hotelId}
        </if>
        AND  o.TOTAL_OTA_PRICE IS NOT NULL AND o.HAS_OTA_PRICE_ORDER_AMOUNT IS NOT NULL
        group by o.HOTEL_ID,o.DISTRIBUTOR_CODE
    </select>

    <select id="selectHotelSavedAmountGroupByOrg" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
            resultType="com.fangcang.rfp.common.dto.HotelSavedAmountDto">
        SELECT
        o.hotel_id hotelId ,
        omc.ORG_ID orgId,
        NVL(sum(CASE WHEN o.TOTAL_OTA_PRICE IS NULL OR o.TOTAL_OTA_PRICE = 0 THEN 0 ELSE o.SAVED_AMOUNT END) ,0) savedAmount,
        NVL(sum(o.TOTAL_OTA_PRICE),0) totalOtaPrice
        FROM T_DIS_HOTEL_DAILY_ORDER o
        LEFT JOIN T_ORDER_MONITOR_CONFIG omc ON o.DISTRIBUTOR_CODE = omc.distributor_code
        WHERE 1=1
        <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
            and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
        </if>
        <if test="hotelId != null ">
            and o.hotel_id = #{hotelId}
        </if>
        <if test="hotelIdList != null and hotelIdList.size() > 0">
            AND o.hotel_id IN
            <foreach collection="hotelIdList" item="hotelId" separator="," open="（" close=")">
                #{hotelId}
            </foreach>
        </if>
        AND omc.STATE = 1
        group by o.HOTEL_ID, omc.ORG_ID
    </select>

  <select id="selectOrderMonitorCityList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
          resultType="com.fangcang.rfp.common.dto.response.DisHotelDailyOrderResponse">
        select a.DATANAME cityName,
               t.roomNightCount,
               t.orderAmount,
               t.savedAmount
        from (
          select
          o.CITY_CODE,
          sum( o.ROOM_NIGHT_COUNT) roomNightCount,
          sum( o.ORDER_AMOUNT) orderAmount,
          sum( o.SAVED_AMOUNT) savedAmount
          from T_DIS_HOTEL_DAILY_ORDER o
          where 1=1
              <if test=" startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
                  and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
              </if>
              and exists ( select 1
                           from T_ORDER_MONITOR_CONFIG c
                           where o.DISTRIBUTOR_CODE = c.distributor_code
                             and c.STATE = 1
                            <if test="orgId != null">
                                 and c.org_id = #{orgId}
                            </if>
                          <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                              AND c.org_id IN
                              <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                                  #{userRelatedOrgId}
                              </foreach>
                          </if>
                        )
              <if test="cityCode != null and cityCode != '' ">
                  and o.CITY_CODE = #{cityCode}
              </if>
              group by o.CITY_CODE
          )t
      left join HTL_BASE.T_AREADATA a on t.CITY_CODE = a.datacode and a.DATATYPE = 3 and a.COUNTRYCODE = 'CN'
      <if test="sortColumn == 0 and sortType == 0">
          order by t.roomNightCount
      </if>
      <if test="sortColumn == 0 and sortType == 1">
          order by t.roomNightCount desc
      </if>
      <if test="sortColumn == 1 and sortType == 0">
          order by t.orderAmount
      </if>
      <if test="sortColumn == 1 and sortType == 1">
          order by t.orderAmount desc
      </if>
      <if test="sortColumn == 2 and sortType == 0">
          order by t.savedAmount
      </if>
      <if test="sortColumn == 2 and sortType == 1">
          order by t.savedAmount desc
      </if>

  </select>

    <select id="selectSaveAndWateList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
            resultType="com.fangcang.rfp.common.dto.response.TransactionRankingMonitoringAmountResponse">

        select h.CHN_NAME hotelName,
               t.amount amount
        from (
            select
                o.HOTEL_ID,
                sum(o.SAVED_AMOUNT) amount
            from HTL_RFP.T_DIS_HOTEL_DAILY_ORDER o
            where 1=1
            <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
                and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
            </if>
            and exists ( select 1
                         from T_ORDER_MONITOR_CONFIG c
                         where o.DISTRIBUTOR_CODE = c.distributor_code
                           and c.STATE = 1
                        <if test="orgId != null">
                           and c.org_id = #{orgId}
                        </if>
                    )
            <if test="saveOrWasteType != null and saveOrWasteType == 1">
                and o.SAVED_AMOUNT &gt; 0
            </if>
            <if test="saveOrWasteType != null and saveOrWasteType == 2">
                and  o.SAVED_AMOUNT &lt; 0
            </if>
            group by o.HOTEL_ID
            <if test="saveOrWasteType != null and saveOrWasteType == 1">
                order by amount desc
            </if>
            <if test="saveOrWasteType != null and saveOrWasteType == 2">
                order by amount
            </if>
        )t
        left join  HTL_INFO.T_HOTEL h on  t.HOTEL_ID = h.HOTELID
        where rownum &lt;= 10

    </select>

    <select id="selectAverageAmountList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
            resultType="com.fangcang.rfp.common.dto.response.AverageAmountResponse">
        select
            o.BOOK_DATE bookDate,
            sum (o.ROOM_NIGHT_COUNT) roomNightCount,
            sum (o.ORDER_AMOUNT) orderAmount
        from T_DIS_HOTEL_DAILY_ORDER o
        where 1=1
        <if test="startTime != null and startTime != ''  and endTime != null and endTime != ''  ">
            and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.BOOK_DATE and  o.BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
        </if>
        and exists ( select 1
                     from T_ORDER_MONITOR_CONFIG c
                     where o.DISTRIBUTOR_CODE = c.distributor_code
                       and c.STATE = 1
                    <if test="orgId != null">
                       and c.org_id = #{orgId}
                    </if>
                )
        group by  o.BOOK_DATE
        order by o.BOOK_DATE
    </select>
    <select id="selectRoomNights" resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryRoomNightsDto">
      select sum(nvl(t.room_night_count,0)) from htl_rfp.T_DIS_HOTEL_DAILY_ORDER t
        where t.hotel_id = #{hotelId}
      <if test="distributorCodes != null and distributorCodes.size() > 0">
        and t.distributor_code in
        <foreach collection="distributorCodes" item="distributorCode" open="(" close=")" separator="," index="index">
            #{distributorCode}
        </foreach>
      </if>
        and to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> BOOK_DATE and  BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
    </select>
    
    <select id="selectHotelRoomNights" resultType="com.fangcang.rfp.common.dto.HotelRoomNightCountDto"
            parameterType="com.fangcang.rfp.common.dto.request.QueryRoomNightsDto">
        SELECT
            HOTEL_ID AS hotelId,
            sum(nvl(t.room_night_count,0)) AS roomNightCount
        FROM htl_rfp.T_DIS_HOTEL_DAILY_ORDER t
        WHERE t.hotel_id IN
         <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
             #{hotelId}
         </foreach>
        AND to_date(#{startTime},'yyyy-mm-dd') <![CDATA[   <=  ]]> BOOK_DATE and  BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endTime},'yyyy-mm-dd')
        GROUP BY HOTEL_ID
    </select>

    <select id="selectDistributorCodeList" parameterType="com.fangcang.rfp.common.dto.DisHotelDailySavedOrderRequest" resultType="java.lang.String">
        SELECT
            DISTINCT d.DISTRIBUTOR_CODE
        FROM htl_rfp.T_DIS_HOTEL_DAILY_ORDER d
        LEFT JOIN htl_rfp.T_ORDER_MONITOR_CONFIG o ON d.DISTRIBUTOR_CODE = o.DISTRIBUTOR_CODE
        WHERE
            d.HOTEL_ID = #{hotelId}
          AND d.BOOK_DATE IN
        <foreach collection="bookDateList" item="bookDate" separator="," open="(" close=")">
            #{bookDate}
        </foreach>
        <if test="orgId != null and orgId > 0">
           AND o.ORG_ID = #{orgId}
        </if>
    </select>

    <select id="selectOrderSupplierCodeCode" resultType="com.fangcang.rfp.common.dto.response.OrderSupplyCodeResponse">
        SELECT
            ORDERID as orderId,
            SUPPLIERCODE as supplierCode
        FROM
            htl_order.T_HOTELSUPPLYORDER
        WHERE
            ORDERID IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectDisHotelOrderList" parameterType="com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest"
            resultType="com.fangcang.rfp.common.dto.response.DisHotelOrderResponse">
        SELECT
            o.ID AS orderId,
            o.ORDERCODE AS orderCode,
            o.HOTELID AS hotelId,
            o.HOTELNAME AS hotelName,
            o.ROOMTYPEID AS roomTypeId,
            o.ROOMTYPENAME AS roomTypeName,
            o.ROOMNUM AS roomNum,
            o.CHECKINDATE AS checkInDate,
            o.CHECKOUTDATE AS checkOutDate,
            o.ORDERSUM AS orderSum,
            d.PAYTIME AS payTime
        FROM
            HTL_ORDER.T_HOTEL_ORDER o
        INNER JOIN
            HTL_ORDER.T_DISTRIBUTOR d ON o.ID = d.ORDERID
        WHERE
            o.HOTELID = #{hotelId}
          AND to_date(#{orderDate},'yyyy-mm-dd') <![CDATA[   <=  ]]> o.CREATETIME and  o.CREATETIME <![CDATA[   <  ]]> to_date(#{orderDate},'yyyy-mm-dd') + 1
          AND d.DISTRIBUTORCODE IN
        <foreach collection="distributorCodes" item="distributorCode" open="(" close=")" separator=",">
            #{distributorCode}
        </foreach>
        AND o.ORDERSTATE = 3
        ORDER BY o.ID ASC
    </select>

    <select id="queryOrderRoomRateDetailList" resultType="com.fangcang.rfp.common.dto.response.DisOrderRoomRateDetailsResponse">
        SELECT
            ORDERID AS orderId,
            ROOMDATE AS roomDate,
            ROOMINDEX AS roomIndex,
            ROOMPRICE AS roomPrice,
            BREAKFASTTYPE AS breakfastType,
            BREAKFASTNUM AS breakfastNum
        FROM
            HTL_ORDER.T_ROOMRATEDETAILS
        WHERE ORDERID IN
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
        AND roomPrice > 0
        ORDER BY ROOMDATE ASC
    </select>

    <select id="queryOtaOrderRoomRateDetailList" resultType="com.fangcang.rfp.common.dto.response.DisOtaOrderRoomRateDetailsResponse">
        SELECT
            ORDERID AS orderId,
            ROOMDATE AS roomDate,
            min(ROOMPRICE) AS roomPrice,
            BREAKFASTTYPE AS breakfastType,
            BREAKFASTNUM AS breakfastNum,
            min(CREATEDATE) AS createDate
        FROM
        HTL_ORDER.T_OTA_ROOMRATEDETAILS
        WHERE ORDERID IN
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
        GROUP BY ORDERID, ROOMDATE, BREAKFASTTYPE, BREAKFASTNUM
        ORDER BY ROOMDATE ASC
    </select>
</mapper>
