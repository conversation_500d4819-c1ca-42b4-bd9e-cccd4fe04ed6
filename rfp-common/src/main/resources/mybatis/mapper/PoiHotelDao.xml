<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PoiHotelDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PoiHotel">
    <result column="POI_ID" jdbcType="DECIMAL" property="poiId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
    POI_ID,HOTEL_ID,ORG_ID,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.PoiHotel">
    insert into T_POI_HOTEL (POI_ID, HOTEL_ID, ORG_ID, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME)
    values (#{poiId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{orgId,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>


  <select id="selectByPoiIdAndHotelId" resultType="com.fangcang.rfp.common.entity.PoiHotel" parameterType="com.fangcang.rfp.common.entity.PoiHotel">
    select <include refid="Base_Column_List"/>
    from HTL_RFP.T_POI_HOTEL t
    where t.POI_ID = #{poiId} and t.HOTEL_ID = #{hotelId}
  </select>

  <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.PoiHotel">
        insert into HTL_RFP.T_POI_HOTEL(POI_ID,HOTEL_ID,ORG_ID,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME)
        <foreach collection="list" index="index" item="item" separator="union all">
          (
              select #{item.poiId},#{item.hotelId},#{item.orgId},#{item.creator},SYSDATE,#{item.modifier},SYSDATE
              from dual
          )
        </foreach>
  </insert>

  <delete id="deletePoiHotel" parameterType="com.fangcang.rfp.common.entity.PoiHotel">
    delete HTL_RFP.T_POI_HOTEL t
    where t.POI_ID = #{poiId} and t.HOTEL_ID = #{hotelId}
  </delete>

  <select id="selectBingHotelList" parameterType="com.fangcang.rfp.common.dto.request.count.PoiBindHotelRequest"
          resultType="com.fangcang.rfp.common.dto.response.count.PoiBindHotelResponse">

    select  b.hotelId,
      b.CHN_NAME hotelName,
      b.CHN_ADDRESS hotelAddress,
      ph.poi_id poiId,
      <!-- nvl((select htl_info.get_earth_distance(#{lngBaiDu},#{latBaiDu},b.LNG_BAIDU, b.LAT_BAIDU) from dual),'') as distance -->
      nvl((select  ROUND(SQRT(POWER(#{lngBaiDu} - b.LNG_BAIDU, 2) + POWER(#{latBaiDu} -b.LAT_BAIDU, 2))*100, 4)  from dual),'') as distance
    from (
      select pih.hotel_id
      from HTL_RFP.T_PROJECT_INTENT_HOTEL pih, HTL_RFP.T_PROJECT p
      where pih.BID_STATE = 3 and p.project_id = pih.project_id
      and p.PRICE_MONITOR_START_DATE &lt;= to_date(#{now}, 'yyyy-MM-dd')
      and p.Price_Monitor_End_Date &gt;= to_date(#{now}, 'yyyy-MM-dd')
    )a
    left join (
      select h.hotelid, h.CHN_NAME, h.CHN_ADDRESS, h.LNG_BAIDU, h.LAT_BAIDU
      from HTL_INFO.T_HOTEL h
      where h.city = #{cityCode}
    )b
    on a.hotel_id = b.hotelId
    left join HTL_RFP.T_POI_HOTEL ph on ph.hotel_id = a.hotel_id
    where a.hotel_id in (select h.hotelid from HTL_INFO.T_HOTEL h  where h.city = #{cityCode})
    <if test="hotelName != null and hotelName != '' ">
      and b.CHN_NAME like concat('%',concat(#{hotelName},'%'))
    </if>
    group by b.hotelId,
            b.CHN_NAME,
            b.CHN_ADDRESS,
            ph.poi_id,
            b.LNG_BAIDU,
            b.LAT_BAIDU
    order by distance
  </select>


</mapper>