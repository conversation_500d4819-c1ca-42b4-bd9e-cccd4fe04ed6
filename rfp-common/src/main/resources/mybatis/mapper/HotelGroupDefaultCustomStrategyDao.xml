<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelGroupDefaultCustomStrategyDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelGroupDefaultCustomStrategy">
        <result column="CUSTOM_TEND_STRATEGY_ID" jdbcType="DECIMAL" property="customTendStrategyId"/>
        <result column="PROJECT_INTENT_HOTEL_GROUP_ID" jdbcType="DECIMAL" property="projectIntentHotelGroupId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="STRATEGY_NAME" jdbcType="VARCHAR" property="strategyName"/>
        <result column="SUPPORT_STRATEGY_NAME" jdbcType="DECIMAL" property="supportStrategyName"/>
        <result column="STRATEGY_TYPE" jdbcType="INTEGER" property="strategyType"/>
        <result column="SUPPORT_STRATEGY_TEXT" jdbcType="VARCHAR" property="supportStrategyText"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      CUSTOM_TEND_STRATEGY_ID, PROJECT_INTENT_HOTEL_GROUP_ID,PROJECT_ID, STRATEGY_NAME, SUPPORT_STRATEGY_NAME,STRATEGY_TYPE,SUPPORT_STRATEGY_TEXT,
      CREATOR, CREATE_TIME, MODIFIER,MODIFY_TIME
    </sql>
    <insert id="batchMergeHotelGroupDefaultCustomStrategy" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            MERGE INTO htl_rfp.T_H_GROUP_DEFAULT_CUS_STRATEGY t
            USING dual
            ON (t.custom_tend_strategy_id = #{item.customTendStrategyId} AND
            t.project_intent_hotel_group_id = #{item.projectIntentHotelGroupId} AND
            t.project_id = #{item.projectId})
            WHEN MATCHED THEN
            UPDATE SET
            t.support_strategy_name = #{item.supportStrategyName,jdbcType=DECIMAL},
            t.support_strategy_text = #{item.supportStrategyText,jdbcType=VARCHAR},
            t.modifier = #{item.modifier,jdbcType=VARCHAR},
            t.modify_time = sysdate
            WHEN NOT MATCHED THEN
            INSERT (custom_tend_strategy_id, project_intent_hotel_group_id, project_id, strategy_name,
            support_strategy_name,STRATEGY_TYPE, SUPPORT_STRATEGY_TEXT, creator, create_time, modifier, modify_time)
            VALUES (#{item.customTendStrategyId}, #{item.projectIntentHotelGroupId},
            #{item.projectId}, #{item.strategyName,jdbcType=VARCHAR}, #{item.supportStrategyName,jdbcType=DECIMAL},#{item.strategyType,jdbcType=INTEGER},
            #{item.supportStrategyText,jdbcType=VARCHAR},
            #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
        </foreach>
    </insert>

    <select id="queryHotelGroupDefaultCustomStrategy" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_H_GROUP_DEFAULT_CUS_STRATEGY where project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </select>

    <delete id="deleteHotelGroupDefaultCustomStrategy" parameterType="com.fangcang.rfp.common.dto.request.DeleteHotelGroupDefaultCustomStrategyDto">
         delete from htl_rfp.T_H_GROUP_DEFAULT_CUS_STRATEGY where
         project_intent_hotel_group_id = #{projectIntentHotelGroupId} and project_id = #{projectId}
         <if test="customTendStrategyIds != null">
         and custom_tend_strategy_id in
             <foreach collection="customTendStrategyIds" open="(" close=")" item="customTendStrategyId" separator="," index="index">
                 #{customTendStrategyId}
             </foreach>
         </if>
    </delete>
</mapper>