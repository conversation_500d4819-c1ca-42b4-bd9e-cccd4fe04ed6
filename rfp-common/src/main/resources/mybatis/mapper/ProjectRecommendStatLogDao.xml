<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectRecommendStatLogDao">

    <select id="queryLogList" resultType="com.fangcang.rfp.common.entity.ProjectRecommendStatLog">
        SELECT
            PROJECT_ID AS projectId,
            STAT_REFERENCE_NO AS statReferenceNo,
            STAT_NAME AS statName,
            BEGIN_TIME AS beginTime,
            END_TIME AS endTime,
            IS_FINISHED AS isFinished,
            RESULT AS result,
            RESULT_MSG AS resultMsg,
            CREATOR AS creator,
            CREATE_TIME AS createTime
        FROM htl_rfp.T_PROJECT_RECOMMEND_STAT_LOG
        WHERE PROJECT_ID = #{projectId}
          <if test="statReferenceNo != null and statReferenceNo != ''">
              AND STAT_REFERENCE_NO = #{statReferenceNo}
          </if>
        ORDER BY createTime ASC, STAT_NAME ASC
    </select>

    <select id="getProjectRecommendStatLog" resultType="com.fangcang.rfp.common.entity.ProjectRecommendStatLog">
        SELECT
            PROJECT_ID AS projectId,
            STAT_REFERENCE_NO AS statReferenceNo,
            STAT_NAME AS statName,
            BEGIN_TIME AS beginTime,
            END_TIME AS endTime,
            IS_FINISHED AS isFinished,
            RESULT AS result,
            RESULT_MSG AS resultMsg,
            CREATOR AS creator,
            CREATE_TIME AS createTime
            FROM htl_rfp.T_PROJECT_RECOMMEND_STAT_LOG
        WHERE PROJECT_ID = #{projectId}
        AND STAT_REFERENCE_NO = #{statReferenceNo}
        AND STAT_NAME = #{statName}
    </select>

    <insert id="insertLog" parameterType="com.fangcang.rfp.common.entity.ProjectRecommendStatLog">
        INSERT INTO htl_rfp.T_PROJECT_RECOMMEND_STAT_LOG
        (PROJECT_ID, STAT_REFERENCE_NO, STAT_NAME, BEGIN_TIME, IS_FINISHED, CREATOR, CREATE_TIME)
        VALUES
        (#{projectId}, #{statReferenceNo}, #{statName}, #{beginTime}, #{isFinished},  #{creator}, sysdate)
    </insert>

    <update id="finishRecord" parameterType="com.fangcang.rfp.common.entity.ProjectRecommendStatLog">
        UPDATE htl_rfp.T_PROJECT_RECOMMEND_STAT_LOG
        SET END_TIME = #{endTime,jdbcType=TIMESTAMP},
            IS_FINISHED = #{isFinished,jdbcType=INTEGER},
            RESULT = #{result,jdbcType=INTEGER},
            RESULT_MSG = #{resultMsg,jdbcType=VARCHAR}
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND STAT_REFERENCE_NO = #{statReferenceNo,jdbcType=VARCHAR}
        AND STAT_NAME = #{statName,jdbcType=VARCHAR}
    </update>
</mapper>