<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelTendWeightDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelTendWeight">
        <id column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="WHTF_ROOM_NIGHT" jdbcType="DECIMAL" property="whtfRoomNight"/>
        <result column="WHT_ROOM_NIGHT_STATE" jdbcType="INTEGER" property="whtRoomNightState"/>
        <result column="WHT_ROOM_NIGHT_EX" jdbcType="DECIMAL" property="whtRoomNightEx"/>
        <result column="WHT_ROOM_NIGHT_EX_STATE" jdbcType="INTEGER" property="whtRoomNightExState"/>
        <result column="WHT_CITY" jdbcType="DECIMAL" property="whtCity"/>
        <result column="WHT_CITY_STATE" jdbcType="INTEGER" property="whtCityState"/>
        <result column="WHT_LOCATION" jdbcType="DECIMAL" property="whtLocation"/>
        <result column="WHT_LOCATION_STATE" jdbcType="INTEGER" property="whtLocationState"/>
        <result column="WHT_PRICE_ADVANT" jdbcType="DECIMAL" property="whtPriceAdvant"/>
        <result column="WHT_PRICE_ADVANT_STATE" jdbcType="INTEGER" property="whtPriceAdvantState"/>
        <result column="WHT_PRICE_ADVANT_EX" jdbcType="DECIMAL" property="whtPriceAdvantEx"/>
        <result column="WHT_PRICE_ADVANT_EX_STATE" jdbcType="INTEGER" property="whtPriceAdvantExState"/>
        <result column="WHT_TRAVEL_STANDARD" jdbcType="DECIMAL" property="whtTravelStandard"/>
        <result column="WHT_TRAVEL_STANDARD_STATE" jdbcType="INTEGER" property="whtTravelStandardState"/>
        <result column="WHT_OTA_SCORE" jdbcType="DECIMAL" property="whtOtaScore"/>
        <result column="WHT_OTA_SCORE_STATE" jdbcType="INTEGER" property="whtOtaScoreState"/>
        <result column="WHT_INVOICE" jdbcType="DECIMAL" property="whtInvoice"/>
        <result column="WHT_INVOICE_STATE" jdbcType="INTEGER" property="whtInvoiceState"/>
        <result column="WHT_CO_PAY" jdbcType="DECIMAL" property="whtCoPay"/>
        <result column="WHT_CO_PAY_STATE" jdbcType="INTEGER" property="whtCoPayState"/>
        <result column="WHT_BREAKFAST" jdbcType="DECIMAL" property="whtBreakfast"/>
        <result column="WHT_BREAKFAST_STATE" jdbcType="INTEGER" property="whtBreakfastState"/>
        <result column="WHT_LRA" jdbcType="DECIMAL" property="whtLra"/>
        <result column="WHT_LRA_STATE" jdbcType="INTEGER" property="whtLraState"/>
        <result column="WHT_CANCEL" jdbcType="DECIMAL" property="whtCancel"/>
        <result column="WHT_CANCE_STATE" jdbcType="INTEGER" property="whtCanceState"/>
        <result column="WHT_BALANCE" jdbcType="DECIMAL" property="whtBalance"/>
        <result column="WHT_BALANCE_STATE" jdbcType="INTEGER" property="whtBalanceState"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="WHT_TOTAL_WEIGHT" jdbcType="DECIMAL" property="whtTotalWeight"/>
    </resultMap>
    <sql id="Base_Column_List">
    PROJECT_ID, WHTF_ROOM_NIGHT, WHT_ROOM_NIGHT_STATE, WHT_ROOM_NIGHT_EX, WHT_ROOM_NIGHT_EX_STATE, 
    WHT_CITY, WHT_CITY_STATE, WHT_LOCATION, WHT_LOCATION_STATE, WHT_PRICE_ADVANT, WHT_PRICE_ADVANT_STATE, 
    WHT_PRICE_ADVANT_EX, WHT_PRICE_ADVANT_EX_STATE, WHT_TRAVEL_STANDARD, WHT_TRAVEL_STANDARD_STATE, 
    WHT_OTA_SCORE, WHT_OTA_SCORE_STATE, WHT_INVOICE, WHT_INVOICE_STATE, WHT_CO_PAY, WHT_CO_PAY_STATE, 
    WHT_BREAKFAST, WHT_BREAKFAST_STATE, WHT_LRA, WHT_LRA_STATE, WHT_CANCEL, WHT_CANCE_STATE, 
    WHT_BALANCE, WHT_BALANCE_STATE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME,WHT_TOTAL_WEIGHT
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_HOTEL_TEND_WEIGHT
        where PROJECT_ID = #{projectId,jdbcType=DECIMAL}
    </select>


    <insert id="insertProjectHotelTendWeight" parameterType="com.fangcang.rfp.common.entity.ProjectHotelTendWeight">
    insert into htl_rfp.T_PROJECT_HOTEL_TEND_WEIGHT (PROJECT_ID, WHTF_ROOM_NIGHT, WHT_ROOM_NIGHT_STATE,
      WHT_ROOM_NIGHT_EX, WHT_ROOM_NIGHT_EX_STATE, WHT_CITY,
      WHT_CITY_STATE, WHT_LOCATION, WHT_LOCATION_STATE,
      WHT_PRICE_ADVANT, WHT_PRICE_ADVANT_STATE, WHT_PRICE_ADVANT_EX,
      WHT_PRICE_ADVANT_EX_STATE, WHT_TRAVEL_STANDARD,
      WHT_TRAVEL_STANDARD_STATE, WHT_OTA_SCORE, WHT_OTA_SCORE_STATE,
      WHT_INVOICE, WHT_INVOICE_STATE, WHT_CO_PAY,
      WHT_CO_PAY_STATE, WHT_BREAKFAST, WHT_BREAKFAST_STATE,
      WHT_LRA, WHT_LRA_STATE, WHT_CANCEL,
      WHT_CANCE_STATE, WHT_BALANCE, WHT_BALANCE_STATE,
      CREATOR, CREATE_TIME, MODIFIER,
      MODIFY_TIME,WHT_TOTAL_WEIGHT)
    values (#{projectId}, #{whtfRoomNight,jdbcType=DECIMAL}, #{whtRoomNightState},
      #{whtRoomNightEx,jdbcType=DECIMAL}, #{whtRoomNightExState}, #{whtCity,jdbcType=DECIMAL},
      #{whtCityState}, #{whtLocation,jdbcType=DECIMAL}, #{whtLocationState},
      #{whtPriceAdvant,jdbcType=DECIMAL}, #{whtPriceAdvantState}, #{whtPriceAdvantEx,jdbcType=DECIMAL},
      #{whtPriceAdvantExState}, #{whtTravelStandard,jdbcType=DECIMAL},
      #{whtTravelStandardState}, #{whtOtaScore,jdbcType=DECIMAL}, #{whtOtaScoreState},
      #{whtInvoice,jdbcType=DECIMAL}, #{whtInvoiceState}, #{whtCoPay,jdbcType=DECIMAL},
      #{whtCoPayState}, #{whtBreakfast,jdbcType=DECIMAL}, #{whtBreakfastState},
      #{whtLra,jdbcType=DECIMAL}, #{whtLraState}, #{whtCancel,jdbcType=DECIMAL},
      #{whtCanceState}, #{whtBalance,jdbcType=DECIMAL}, #{whtBalanceState},
      #{creator,jdbcType=VARCHAR}, sysdate, #{modifier,jdbcType=VARCHAR},sysdate,#{whtTotalWeight,jdbcType=DECIMAL})
  </insert>
    <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.ProjectHotelTendWeight">
    update htl_rfp.T_PROJECT_HOTEL_TEND_WEIGHT
    set WHTF_ROOM_NIGHT = #{whtfRoomNight,jdbcType=DECIMAL},
      WHT_ROOM_NIGHT_STATE = #{whtRoomNightState},
      WHT_ROOM_NIGHT_EX = #{whtRoomNightEx,jdbcType=DECIMAL},
      WHT_ROOM_NIGHT_EX_STATE = #{whtRoomNightExState},
      WHT_CITY = #{whtCity,jdbcType=DECIMAL},
      WHT_CITY_STATE = #{whtCityState},
      WHT_LOCATION = #{whtLocation,jdbcType=DECIMAL},
      WHT_LOCATION_STATE = #{whtLocationState},
      WHT_PRICE_ADVANT = #{whtPriceAdvant,jdbcType=DECIMAL},
      WHT_PRICE_ADVANT_STATE = #{whtPriceAdvantState},
      WHT_PRICE_ADVANT_EX = #{whtPriceAdvantEx,jdbcType=DECIMAL},
      WHT_PRICE_ADVANT_EX_STATE = #{whtPriceAdvantExState},
      WHT_TRAVEL_STANDARD = #{whtTravelStandard,jdbcType=DECIMAL},
      WHT_TRAVEL_STANDARD_STATE = #{whtTravelStandardState},
      WHT_OTA_SCORE = #{whtOtaScore,jdbcType=DECIMAL},
      WHT_OTA_SCORE_STATE = #{whtOtaScoreState},
      WHT_INVOICE = #{whtInvoice,jdbcType=DECIMAL},
      WHT_INVOICE_STATE = #{whtInvoiceState},
      WHT_CO_PAY = #{whtCoPay,jdbcType=DECIMAL},
      WHT_CO_PAY_STATE = #{whtCoPayState},
      WHT_BREAKFAST = #{whtBreakfast,jdbcType=DECIMAL},
      WHT_BREAKFAST_STATE = #{whtBreakfastState},
      WHT_LRA = #{whtLra,jdbcType=DECIMAL},
      WHT_LRA_STATE = #{whtLraState},
      WHT_CANCEL = #{whtCancel,jdbcType=DECIMAL},
      WHT_CANCE_STATE = #{whtCanceState},
      WHT_BALANCE = #{whtBalance,jdbcType=DECIMAL},
      WHT_BALANCE_STATE = #{whtBalanceState},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME =sysdate,
      WHT_TOTAL_WEIGHT = #{whtTotalWeight,jdbcType=DECIMAL}
    where PROJECT_ID = #{projectId}
  </update>

    <update id="updateWhtTotalWeight">
        update htl_rfp.T_PROJECT_HOTEL_TEND_WEIGHT
        set WHT_TOTAL_WEIGHT = #{whtTotalWeight,jdbcType=DECIMAL}
        where PROJECT_ID = #{projectId}
    </update>

</mapper>