<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.OrgSubjectDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.request.OrgSubjectDto" >
    <id column="subject_id" property="subjectId" jdbcType="BIGINT" />
    <result column="subject_name" property="subjectName" jdbcType="VARCHAR" />
    <result column="cert_code" property="certCode" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="bank" property="bank" jdbcType="VARCHAR" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="BIGINT" />
    <result column="is_default" property="isDefault" jdbcType="INTEGER" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="cert_url" property="certUrl" jdbcType="VARCHAR" />
    <result column="authorization_url" property="authorizationUrl" jdbcType="VARCHAR" />
    <result column="authorize_state" property="authorizeState" jdbcType="INTEGER" />
    <result column="cert_state" property="certState" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="ACCOUNT_TYPE" property="accountType" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List">
    SUBJECT_ID, SUBJECT_NAME, CERT_CODE, ORG_ID, BANK, ACCOUNT_NAME, ACCOUNT_NUMBER,
    IS_DEFAULT, STATE, CERT_URL, AUTHORIZATION_URL, AUTHORIZE_STATE, CERT_STATE, CREATOR,
    CREATE_TIME, MODIFIER, MODIFY_TIME,ACCOUNT_TYPE
  </sql>

  <select id="queryOrgSubjectPage" parameterType="long" resultMap="BaseResultMap">
    select *
    from htl_rfp.t_org_subject
    where ORG_ID = #{orgId} and STATE = 1
  </select>

  <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
    select *
    from htl_rfp.t_org_subject
    where SUBJECT_ID = #{subjectId}
  </select>

  <select id="selectPaySubjectList" resultType="com.fangcang.rfp.common.dto.response.OrgSubjectResponse">
    select o.ORG_ID orgId,
           o.ORG_NAME orgName,
           tos.SUBJECT_ID subjectId,
           tos.SUBJECT_NAME subjectName
    from htl_rfp.T_ORG o
    left join htl_rfp.T_ORG_SUBJECT tos on o.ORG_ID = tos.ORG_ID
    where o.CAN_PROVIDER_CO_PAY = 1 and o.STATE = 1 and tos.STATE = 1  and tos.is_default = 1
  </select>

  <select id="selectDefaultSubjectByOrgId" parameterType="list" resultMap="BaseResultMap">
    select *
    from  htl_rfp.T_ORG_SUBJECT tos
    where  tos.STATE = 1 and tos.IS_DEFAULT = 1 and tos.ORG_ID in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
  </select>

  <insert id="insertOrgSubject" parameterType="com.fangcang.rfp.common.entity.OrgSubject">
    <selectKey keyProperty="subjectId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_ORG_SUBJECT_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.t_org_subject (
    subject_id,subject_name,
    cert_code,org_id,
    is_default,state,
    cert_url,authorization_url,
    authorize_state,cert_state,
     creator,create_time,
     bank,account_name,account_number,ACCOUNT_TYPE)
    values (
      #{subjectId},#{subjectName},
      #{certCode},#{orgId},
      #{isDefault},#{state},
      #{certUrl},#{authorizationUrl,jdbcType=VARCHAR},
      #{authorizeState,jdbcType=INTEGER},#{certState,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, SYSDATE,
      #{bank,jdbcType=VARCHAR},#{accountName,jdbcType=VARCHAR},#{accountNumber},#{accountType,jdbcType=VARCHAR})
  </insert>

  <update id="updateOrgSubject" parameterType="com.fangcang.rfp.common.entity.OrgSubject">
    update htl_rfp.t_org_subject
    <set >
      MODIFIER = #{modifier},
      MODIFY_TIME = SYSDATE
      <if test="subjectName != null" >
        ,SUBJECT_NAME = #{subjectName}
      </if>
      <if test="certCode != null" >
        ,CERT_CODE = #{certCode}
      </if>
      <if test="bank != null" >
        ,BANK = #{bank}
      </if>
      <if test="accountType != null" >
        ,ACCOUNT_TYPE = #{accountType}
      </if>
      <if test="accountName != null" >
        ,account_name = #{accountName}
      </if>
      <if test="accountNumber != null" >
        ,account_number = #{accountNumber}
      </if>
      <if test="isDefault != null" >
        ,IS_DEFAULT = #{isDefault}
      </if>
      <if test="certUrl != null" >
        ,CERT_URL = #{certUrl}
      </if>
      <if test="authorizationUrl != null" >
        ,AUTHORIZATION_URL = #{authorizationUrl}
      </if>
      <if test="authorizeState != null">
        ,AUTHORIZE_STATE = #{authorizeState}
      </if>
      <if test="certState != null">
        ,CERT_STATE = #{certState}
      </if>

    </set>
    where SUBJECT_ID = #{subjectId}
  </update>

  <update id="deleteOrgSubject" parameterType="com.fangcang.rfp.common.entity.OrgSubject">
    update htl_rfp.t_org_subject set MODIFIER = #{modifier},
                             MODIFY_TIME = SYSDATE,
                             state = #{state}
                         where SUBJECT_ID = #{subjectId}
  </update>

  <select id="queryOrgSubjectByOrgIdAndCerCode" resultType="long" parameterType="com.fangcang.rfp.common.entity.OrgSubject">
    select SUBJECT_ID
    from htl_rfp.t_org_subject
    where ORG_ID = #{orgId} and CERT_CODE = #{certCode}
    <if test="state != null and state >= 0">
      AND state = #{state}
    </if>
  </select>

  <select id="selectOrgSubjectInfoBySubjectId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from htl_rfp.t_org_subject
    where subject_id = #{subjectId}
  </select>

  <select id="selectDefaultSubjectByHotelId" resultMap="BaseResultMap" parameterType="java.lang.Long">
      select tos.SUBJECT_ID, tos.SUBJECT_NAME, tos.CERT_CODE, tos.ORG_ID, tos.BANK, tos.ACCOUNT_NAME, tos.ACCOUNT_NUMBER,
    tos.IS_DEFAULT, tos.STATE, tos.CERT_URL, tos.AUTHORIZATION_URL, tos.AUTHORIZE_STATE, tos.CERT_STATE, tos.CREATOR,
    tos.CREATE_TIME, tos.MODIFIER, tos.MODIFY_TIME
    from  htl_rfp.T_ORG_SUBJECT tos , htl_rfp.t_org og
    where  tos.ORG_ID = og.org_id and tos.STATE = 1 and tos.IS_DEFAULT = 1 and og.state = 1 and
           og.org_id in (select org_id from t_org_related_hotel where hotel_id = #{hotelId})
  </select>

  <select id="queryUnAuthSubjectCount" resultType="integer">
    select count(1)
    from htl_rfp.t_org_subject t
    where t.AUTHORIZE_STATE != 1
  </select>

  <select id="queryAuthSubjectCount" resultType="integer" parameterType="integer">
    select count(1)
    from htl_rfp.t_org_subject os
    where  os.AUTHORIZE_STATE = 1 and os.STATE = 1
      and exists (
            select 1
            from htl_rfp.T_ORG o
            where o.ORG_TYPE =  #{orgType} and os.ORG_ID = o.ORG_ID and o.STATE = 1
      )

  </select>

</mapper>
