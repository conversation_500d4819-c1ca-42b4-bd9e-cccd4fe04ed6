<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelGroupDefaultUnapplicableDayDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelGroupDefaultUnapplicableDay">
        <id column="DEFAULT_UNAPPLICABLE_DAY_ID" jdbcType="DECIMAL" property="defaultUnapplicableDayId"/>
        <result column="PROJECT_INTENT_HOTEL_GROUP_ID" jdbcType="DECIMAL" property="projectIntentHotelGroupId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    DEFAULT_UNAPPLICABLE_DAY_ID, PROJECT_INTENT_HOTEL_GROUP_ID, PROJECT_ID, START_DATE, 
    END_DATE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>
    <select id="queryHotelGroupDefaultUnapplicableDay" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_HGRP_DEFAULT_UNAPPLICABLEDAY
        where project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </select>
    <delete id="deleteHotelGroupDefaultUnapplicableDay">
    delete from T_HGRP_DEFAULT_UNAPPLICABLEDAY
    where project_intent_hotel_group_id = #{projectIntentHotelGroupId} and project_id = #{projectId}
    </delete>
    <!-- 定义insertBatch方法 -->
    <insert id="insertBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO htl_rfp.T_HGRP_DEFAULT_UNAPPLICABLEDAY (default_unapplicable_day_id,
            project_intent_hotel_group_id,
            project_id, start_date, end_date, creator, create_time, modifier, modify_time)
            VALUES (htl_rfp.seq_rfp_hg_unapplicableday_id.nextval, #{item.projectIntentHotelGroupId}, #{item.projectId},
            #{item.startDate},
            #{item.endDate}, #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
        </foreach>
    </insert>
</mapper>