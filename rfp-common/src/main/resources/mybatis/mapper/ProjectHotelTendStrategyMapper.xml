<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelTendStrategyDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelTendStrategy">
        <id column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="ONLY_POI_CITY" jdbcType="INTEGER" property="onlyPoiCity"/>
        <result column="ONLY_POI_DISTANCE" jdbcType="INTEGER" property="onlyPoiDistance"/>
        <result column="POI_DISTANCE" jdbcType="DECIMAL" property="poiDistance"/>
        <result column="ONLY_HOTEL_STAR" jdbcType="INTEGER" property="onlyHotelStar"/>
        <result column="HOTEL_STAR" jdbcType="VARCHAR" property="hotelStar"/>
        <result column="SUPPORT_PAY_AT_HOTEL" jdbcType="INTEGER" property="supportPayAtHotel"/>
        <result column="SUPPORT_CO_PAY" jdbcType="INTEGER" property="supportCoPay"/>
        <result column="SUPPORT_NO_GUARANTEE" jdbcType="INTEGER" property="supportNoGuarantee"/>
        <result column="SUPPORT_CHECKIN_INFO" jdbcType="INTEGER" property="supportCheckinInfo"/>
        <result column="SUPPORT_PAY_EARLY_CHECKOUT" jdbcType="INTEGER" property="supportPayEarlyCheckout"/>
        <result column="SUPPORT_EARLY_CHECKIN" jdbcType="INTEGER" property="supportEarlyCheckin"/>
        <result column="SUPPORT_EARLY_CHECKIN_TIME" jdbcType="VARCHAR" property="supportEarlyCheckinTime"/>
        <result column="SUPPORT_LATE_CHECKOUT" jdbcType="INTEGER" property="supportLateCheckout"/>
        <result column="SUPPORT_LATE_CHECKOUT_TIME" jdbcType="VARCHAR" property="supportLateCheckoutTime"/>
        <result column="SUPPORT_MONTHLY_BALANCE" jdbcType="INTEGER" property="supportMonthlyBalance"/>
        <result column="SUPPORT_INCLUDE_COMMISSION" jdbcType="INTEGER" property="supportIncludeCommission"/>
        <result column="TEND_COMMISSION" jdbcType="DECIMAL" property="tendCommission"/>
        <result column="PROVIDE_INVOICE" jdbcType="INTEGER" property="provideInvoice"/>
        <result column="SUPPORT_VAT_INVOICE" jdbcType="INTEGER" property="supportVatInvoice"/>
        <result column="SUPPORT_PRICE_LIMIT" jdbcType="INTEGER" property="supportPriceLimit"/>
        <result column="LIMIT_MIN_PRICE" jdbcType="DECIMAL" property="limitMinPrice"/>
        <result column="LIMIT_MAX_PRICE" jdbcType="DECIMAL" property="limitMaxPrice"/>
        <result column="SUPPORT_FREE_CANCEL" jdbcType="INTEGER" property="supportFreeCancel"/>
        <result column="SUPPORT_LRA" jdbcType="INTEGER" property="supportLra"/>
        <result column="INCLUDE_BREAKFAST" jdbcType="INTEGER" property="includeBreakfast"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="PRICE_LIMIT_COUNT" jdbcType="INTEGER" property="priceLimitCount"/>
        <result column="FREE_CANCEL_COUNT" jdbcType="INTEGER" property="freeCancelCount"/>
        <result column="SUPPORT_LRA_COUNT" jdbcType="INTEGER" property="supportLraCount"/>
        <result column="INCLUDE_BREAKFAST_COUNT" jdbcType="INTEGER" property="includeBreakfastCount"/>
        <result column="MAX_UNAPPLICABLE_DAY" jdbcType="INTEGER" property="maxUnapplicableDay"/>
        <result column="SUPPORT_MAX_UNAPPLICABLE_DAY" jdbcType="INTEGER" property="supportMaxUnapplicableDay"/>
        <result column="MAX_ROOMTYPE_COUNT" jdbcType="INTEGER" property="maxRoomTypeCount"/>
        <result column="SUPPORT_MAX_ROOMTYPE_COUNT" jdbcType="INTEGER" property="supportMaxRoomTypeCount"/>
        <result column="SUPPORT_1ST_CANCEL_COUNT" jdbcType="INTEGER" property="support1stCancelCount"/>
        <result column="FREE_EXCLUDE_1ST_CANCEL_COUNT" jdbcType="INTEGER" property="freeExclude1stCancelCount"/>
        <result column="IS_SUGGEST_NO_BREAKFAST" jdbcType="INTEGER" property="isSuggestNoBreakfast"/>
        <result column="IS_INCLUDE_NO_BREAKFAST" jdbcType="INTEGER" property="isIncludeNoBreakfast"/>
        <result column="IS_SUGGEST_ONE_BREAKFAST" jdbcType="INTEGER" property="isSuggestOneBreakfast"/>
        <result column="IS_INCLUDE_ONE_BREAKFAST" jdbcType="INTEGER" property="isIncludeOneBreakfast"/>
        <result column="IS_SUGGEST_TWO_BREAKFAST" jdbcType="INTEGER" property="isSuggestTwoBreakfast"/>
        <result column="IS_INCLUDE_TWO_BREAKFAST" jdbcType="INTEGER" property="isIncludeTwoBreakfast"/>
        <result column="IS_INCLUDE_ALL_WEEKLY_DAY" jdbcType="INTEGER" property="isIncludeAllWeeklyDay"/>
        <result column="SUPPORT_INCLUDE_TAX_SERVICE" jdbcType="INTEGER" property="supportIncludeTaxService"/>
        <result column="SUPPORT_WIFI" jdbcType="INTEGER" property="supportWifi"/>
        <result column="SUPPORT_SEASON_DAY_LIMIT" jdbcType="INTEGER" property="supportSeasonDayLimit"/>
        <result column="MAX_SEASON_DAY" jdbcType="INTEGER" property="maxSeasonDay"/>
        <result column="IS_INCLUDE_LEVEL_ROOM_COUNT" jdbcType="INTEGER" property="isIncludeLevelRoomCount"/>
        <result column="SUPPORT_CANCEL_DAY" jdbcType="INTEGER" property="supportCancelDay"/>
        <result column="SUPPORT_CANCEL_TIME" jdbcType="VARCHAR" property="supportCancelTime"/>
        <result column="IS_INCLUDE_SUBJECT_CERT" jdbcType="INTEGER" property="isIncludeSubjectCert"/>
        <result column="IS_INCLUDE_SPECIAL_TRADES_CERT" jdbcType="INTEGER" property="isIncludeSpecialTradesCert"/>
        <result column="IS_INCLUDE_HYGIENE_CERT" jdbcType="INTEGER" property="isIncludeHygieneCert"/>
        <result column="IS_INCLUDE_FIRE_SAFETY_CERT" jdbcType="INTEGER" property="isIncludeFireSafetyCert"/>
    </resultMap>
    <sql id="Base_Column_List">
    PROJECT_ID, ONLY_POI_CITY, ONLY_POI_DISTANCE, POI_DISTANCE, ONLY_HOTEL_STAR, HOTEL_STAR, 
    SUPPORT_PAY_AT_HOTEL, SUPPORT_CO_PAY, SUPPORT_NO_GUARANTEE, SUPPORT_CHECKIN_INFO, 
    SUPPORT_PAY_EARLY_CHECKOUT, SUPPORT_EARLY_CHECKIN, SUPPORT_EARLY_CHECKIN_TIME, SUPPORT_LATE_CHECKOUT, 
    SUPPORT_LATE_CHECKOUT_TIME, SUPPORT_MONTHLY_BALANCE, SUPPORT_INCLUDE_COMMISSION, 
    TEND_COMMISSION, PROVIDE_INVOICE, SUPPORT_VAT_INVOICE, SUPPORT_PRICE_LIMIT, LIMIT_MIN_PRICE, 
    LIMIT_MAX_PRICE, SUPPORT_FREE_CANCEL, SUPPORT_LRA, INCLUDE_BREAKFAST, CREATOR, CREATE_TIME, 
    MODIFIER, MODIFY_TIME,PRICE_LIMIT_COUNT,FREE_CANCEL_COUNT,SUPPORT_LRA_COUNT,INCLUDE_BREAKFAST_COUNT,
    MAX_UNAPPLICABLE_DAY,SUPPORT_MAX_UNAPPLICABLE_DAY,MAX_ROOMTYPE_COUNT,SUPPORT_MAX_ROOMTYPE_COUNT,
    SUPPORT_1ST_CANCEL_COUNT,FREE_EXCLUDE_1ST_CANCEL_COUNT,IS_SUGGEST_NO_BREAKFAST,IS_INCLUDE_NO_BREAKFAST,IS_SUGGEST_ONE_BREAKFAST,
    IS_INCLUDE_ONE_BREAKFAST,IS_SUGGEST_TWO_BREAKFAST, IS_INCLUDE_TWO_BREAKFAST,IS_INCLUDE_ALL_WEEKLY_DAY,
    SUPPORT_INCLUDE_TAX_SERVICE,SUPPORT_WIFI,SUPPORT_SEASON_DAY_LIMIT,MAX_SEASON_DAY,IS_INCLUDE_LEVEL_ROOM_COUNT,SUPPORT_CANCEL_DAY,SUPPORT_CANCEL_TIME,
    IS_INCLUDE_SUBJECT_CERT,IS_INCLUDE_SPECIAL_TRADES_CERT,IS_INCLUDE_HYGIENE_CERT,IS_INCLUDE_FIRE_SAFETY_CERT
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_HOTEL_TEND_STRATEGY
        where PROJECT_ID = #{projectId,jdbcType=DECIMAL}
    </select>

    <insert id="insertProjectHotelTendStrategy" parameterType="com.fangcang.rfp.common.entity.ProjectHotelTendStrategy">
        insert into htl_rfp.T_PROJECT_HOTEL_TEND_STRATEGY (PROJECT_ID, ONLY_POI_CITY, ONLY_POI_DISTANCE,
      POI_DISTANCE, ONLY_HOTEL_STAR, HOTEL_STAR,
      SUPPORT_PAY_AT_HOTEL, SUPPORT_CO_PAY, SUPPORT_NO_GUARANTEE,
      SUPPORT_CHECKIN_INFO, SUPPORT_PAY_EARLY_CHECKOUT,
      SUPPORT_EARLY_CHECKIN, SUPPORT_EARLY_CHECKIN_TIME,
      SUPPORT_LATE_CHECKOUT, SUPPORT_LATE_CHECKOUT_TIME,
      SUPPORT_MONTHLY_BALANCE, SUPPORT_INCLUDE_COMMISSION,
      TEND_COMMISSION, PROVIDE_INVOICE, SUPPORT_VAT_INVOICE,
      SUPPORT_PRICE_LIMIT, LIMIT_MIN_PRICE, LIMIT_MAX_PRICE,
      SUPPORT_FREE_CANCEL, SUPPORT_LRA, INCLUDE_BREAKFAST,
      CREATOR, CREATE_TIME, MODIFIER,
      MODIFY_TIME,PRICE_LIMIT_COUNT,FREE_CANCEL_COUNT,SUPPORT_LRA_COUNT,INCLUDE_BREAKFAST_COUNT,
      MAX_UNAPPLICABLE_DAY,SUPPORT_MAX_UNAPPLICABLE_DAY,MAX_ROOMTYPE_COUNT,SUPPORT_MAX_ROOMTYPE_COUNT,SUPPORT_1ST_CANCEL_COUNT,
      FREE_EXCLUDE_1ST_CANCEL_COUNT,IS_SUGGEST_NO_BREAKFAST,IS_INCLUDE_NO_BREAKFAST,IS_SUGGEST_ONE_BREAKFAST,
      IS_INCLUDE_ONE_BREAKFAST,IS_SUGGEST_TWO_BREAKFAST, IS_INCLUDE_TWO_BREAKFAST,IS_INCLUDE_ALL_WEEKLY_DAY,
      SUPPORT_INCLUDE_TAX_SERVICE,SUPPORT_WIFI,SUPPORT_SEASON_DAY_LIMIT,MAX_SEASON_DAY, IS_INCLUDE_LEVEL_ROOM_COUNT,SUPPORT_CANCEL_DAY,SUPPORT_CANCEL_TIME,
    IS_INCLUDE_SUBJECT_CERT,IS_INCLUDE_SPECIAL_TRADES_CERT,IS_INCLUDE_HYGIENE_CERT,IS_INCLUDE_FIRE_SAFETY_CERT)
    values (#{projectId}, #{onlyPoiCity}, #{onlyPoiDistance},
      #{poiDistance,jdbcType=DECIMAL}, #{onlyHotelStar}, #{hotelStar,jdbcType=VARCHAR},
      #{supportPayAtHotel}, #{supportCoPay}, #{supportNoGuarantee},
      #{supportCheckinInfo}, #{supportPayEarlyCheckout},
      #{supportEarlyCheckin}, #{supportEarlyCheckinTime,jdbcType=VARCHAR},
      #{supportLateCheckout}, #{supportLateCheckoutTime,jdbcType=VARCHAR},
      #{supportMonthlyBalance}, #{supportIncludeCommission},
      #{tendCommission,jdbcType=DECIMAL}, #{provideInvoice}, #{supportVatInvoice},
      #{supportPriceLimit}, #{limitMinPrice,jdbcType=DECIMAL}, #{limitMaxPrice,jdbcType=DECIMAL},
      #{supportFreeCancel}, #{supportLra}, #{includeBreakfast},
      #{creator,jdbcType=DECIMAL}, sysdate, #{modifier,jdbcType=DECIMAL},sysdate,#{priceLimitCount,jdbcType=DECIMAL},
      #{freeCancelCount,jdbcType=DECIMAL},#{supportLraCount,jdbcType=DECIMAL},#{includeBreakfastCount,jdbcType=DECIMAL},
      #{maxUnapplicableDay,jdbcType=DECIMAL},#{supportMaxUnapplicableDay,jdbcType=DECIMAL},#{maxRoomTypeCount,jdbcType=DECIMAL},
      #{supportMaxRoomTypeCount,jdbcType=DECIMAL},#{support1stCancelCount,jdbcType=INTEGER},
      #{freeExclude1stCancelCount,jdbcType=DECIMAL},#{isSuggestNoBreakfast,jdbcType=DECIMAL},#{isIncludeNoBreakfast,jdbcType=DECIMAL},
      #{isSuggestOneBreakfast,jdbcType=DECIMAL},#{isIncludeOneBreakfast,jdbcType=DECIMAL},
      #{isSuggestTwoBreakfast,jdbcType=DECIMAL},#{isIncludeTwoBreakfast,jdbcType=DECIMAL},
      #{isIncludeAllWeeklyDay,jdbcType=DECIMAL},#{supportIncludeTaxService,jdbcType=DECIMAL},#{supportWifi,jdbcType=DECIMAL},
      #{supportSeasonDayLimit,jdbcType=DECIMAL}, #{maxSeasonDay,jdbcType=DECIMAL}, #{isIncludeLevelRoomCount,jdbcType=DECIMAL},
      #{supportCancelDay,jdbcType=BIGINT},#{supportCancelTime,jdbcType=VARCHAR},
      #{isIncludeSubjectCert,jdbcType=DECIMAL},#{isIncludeSpecialTradesCert,jdbcType=DECIMAL},#{isIncludeHygieneCert,jdbcType=DECIMAL},#{isIncludeFireSafetyCert,jdbcType=DECIMAL}
        )
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.ProjectHotelTendStrategy">
    update htl_rfp.T_PROJECT_HOTEL_TEND_STRATEGY t set
        t.ONLY_POI_CITY = #{onlyPoiCity},
        t.ONLY_POI_DISTANCE = #{onlyPoiDistance},
        t.POI_DISTANCE = #{poiDistance,jdbcType=DECIMAL},
        t.ONLY_HOTEL_STAR = #{onlyHotelStar},
        t.HOTEL_STAR = #{hotelStar,jdbcType=VARCHAR},
        t.SUPPORT_PAY_AT_HOTEL = #{supportPayAtHotel},
        t.SUPPORT_CO_PAY = #{supportCoPay},
        t.SUPPORT_NO_GUARANTEE = #{supportNoGuarantee},
        t.SUPPORT_CHECKIN_INFO = #{supportCheckinInfo},
        t.SUPPORT_PAY_EARLY_CHECKOUT = #{supportPayEarlyCheckout},
        t.SUPPORT_EARLY_CHECKIN = #{supportEarlyCheckin},
        t.SUPPORT_EARLY_CHECKIN_TIME = #{supportEarlyCheckinTime,jdbcType=VARCHAR},
        t.SUPPORT_LATE_CHECKOUT = #{supportLateCheckout},
        t.SUPPORT_LATE_CHECKOUT_TIME = #{supportLateCheckoutTime,jdbcType=VARCHAR},
        t.SUPPORT_MONTHLY_BALANCE = #{supportMonthlyBalance},
        t.SUPPORT_INCLUDE_COMMISSION = #{supportIncludeCommission},
        t.TEND_COMMISSION = #{tendCommission,jdbcType=DECIMAL},
        t.PROVIDE_INVOICE = #{provideInvoice},
        t.SUPPORT_VAT_INVOICE = #{supportVatInvoice},
        t.SUPPORT_PRICE_LIMIT = #{supportPriceLimit},
        t.LIMIT_MIN_PRICE = #{limitMinPrice,jdbcType=DECIMAL},
        t.LIMIT_MAX_PRICE = #{limitMaxPrice,jdbcType=DECIMAL},
        t.SUPPORT_FREE_CANCEL = #{supportFreeCancel},
        t.SUPPORT_LRA = #{supportLra},
        t.INCLUDE_BREAKFAST = #{includeBreakfast},
        t.MODIFIER = #{modifier,jdbcType=VARCHAR},
        t.MODIFY_TIME =sysdate,
        t.PRICE_LIMIT_COUNT = #{priceLimitCount,jdbcType=DECIMAL},
        t.FREE_CANCEL_COUNT = #{freeCancelCount,jdbcType=DECIMAL},
        t.SUPPORT_LRA_COUNT = #{supportLraCount,jdbcType=DECIMAL},
        t.INCLUDE_BREAKFAST_COUNT = #{includeBreakfastCount,jdbcType=DECIMAL},
        t.MAX_UNAPPLICABLE_DAY = #{maxUnapplicableDay,jdbcType=DECIMAL},
        t.SUPPORT_MAX_UNAPPLICABLE_DAY = #{supportMaxUnapplicableDay,jdbcType=DECIMAL},
        t.MAX_ROOMTYPE_COUNT = #{maxRoomTypeCount,jdbcType=DECIMAL},
        t.SUPPORT_MAX_ROOMTYPE_COUNT = #{supportMaxRoomTypeCount,jdbcType=DECIMAL},
        t.SUPPORT_1ST_CANCEL_COUNT = #{support1stCancelCount,jdbcType=DECIMAL},
        t.FREE_EXCLUDE_1ST_CANCEL_COUNT = #{freeExclude1stCancelCount,jdbcType=DECIMAL},
        t.IS_SUGGEST_NO_BREAKFAST = #{isSuggestNoBreakfast,jdbcType=DECIMAL},
        t.IS_INCLUDE_NO_BREAKFAST = #{isIncludeNoBreakfast,jdbcType=DECIMAL},
        t.IS_SUGGEST_ONE_BREAKFAST = #{isSuggestOneBreakfast,jdbcType=DECIMAL},
        t.IS_INCLUDE_ONE_BREAKFAST = #{isIncludeOneBreakfast,jdbcType=DECIMAL},
        t.IS_SUGGEST_TWO_BREAKFAST = #{isSuggestTwoBreakfast,jdbcType=DECIMAL},
        t.IS_INCLUDE_TWO_BREAKFAST = #{isIncludeTwoBreakfast,jdbcType=DECIMAL},
        t.IS_INCLUDE_ALL_WEEKLY_DAY = #{isIncludeAllWeeklyDay,jdbcType=DECIMAL},
        t.SUPPORT_INCLUDE_TAX_SERVICE = #{supportIncludeTaxService,jdbcType=DECIMAL},
        t.SUPPORT_WIFI = #{supportWifi,jdbcType=DECIMAL},
        t.SUPPORT_SEASON_DAY_LIMIT = #{supportSeasonDayLimit,jdbcType=DECIMAL},
        t.MAX_SEASON_DAY = #{maxSeasonDay,jdbcType=DECIMAL},
        t.IS_INCLUDE_LEVEL_ROOM_COUNT = #{isIncludeLevelRoomCount,jdbcType=DECIMAL},
        t.SUPPORT_CANCEL_DAY = #{supportCancelDay,jdbcType=DECIMAL},
        t.SUPPORT_CANCEL_TIME = #{supportCancelTime,jdbcType=VARCHAR},
        t.IS_INCLUDE_SUBJECT_CERT = #{isIncludeSubjectCert,jdbcType=DECIMAL},
        t.IS_INCLUDE_SPECIAL_TRADES_CERT = #{isIncludeSpecialTradesCert,jdbcType=DECIMAL},
        t.IS_INCLUDE_HYGIENE_CERT = #{isIncludeHygieneCert,jdbcType=DECIMAL},
        t.IS_INCLUDE_FIRE_SAFETY_CERT = #{isIncludeFireSafetyCert,jdbcType=DECIMAL}
    where PROJECT_ID = #{projectId}
  </update>
</mapper>