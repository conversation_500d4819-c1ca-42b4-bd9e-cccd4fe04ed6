<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectCustomBidStrategyDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectCustomBidStrategy">
    <id column="CUSTOM_TEND_STRATEGY_ID" jdbcType="DECIMAL" property="customTendStrategyId" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="DECIMAL" property="projectIntentHotelId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="STRATEGY_NAME" jdbcType="VARCHAR" property="strategyName" />
    <result column="SUPPORT_STRATEGY_NAME" jdbcType="DECIMAL" property="supportStrategyName" />
    <result column="STRATEGY_TYPE" jdbcType="INTEGER" property="strategyType" />
    <result column="SUPPORT_STRATEGY_TEXT" jdbcType="VARCHAR" property="supportStrategyText" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    CUSTOM_TEND_STRATEGY_ID, PROJECT_INTENT_HOTEL_ID, HOTEL_ID, PROJECT_ID, STRATEGY_NAME, STRATEGY_TYPE,SUPPORT_STRATEGY_TEXT,
    SUPPORT_STRATEGY_NAME, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>
  <delete id="batchDeleteProjectCustomBidStrategy" parameterType="java.util.List">
    DELETE FROM T_PROJECT_CUSTOM_BID_STRATEGY
    WHERE custom_tend_strategy_id IN
    <foreach collection="customTendStrategyIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    AND PROJECT_ID = #{projectId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByProjectHotelId">
    DELETE FROM T_PROJECT_CUSTOM_BID_STRATEGY
    WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
    AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </delete>

  <select id="queryProjectCustomBidStrategy"
          parameterType="com.fangcang.rfp.common.dto.request.QueryProjectCustomBidStrategyDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from htl_rfp.T_PROJECT_CUSTOM_BID_STRATEGY
    where
    PROJECT_ID = #{projectId}
    <if test="hotelId != null">
      and HOTEL_ID = #{hotelId}
    </if>
    <if test="projectIntentHotelId != null">
      and PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </if>
  </select>

  <select id="queryProjectCustomBidStrategyByDisplayOrderAsc"  parameterType="com.fangcang.rfp.common.dto.request.QueryProjectCustomBidStrategyDto"
          resultType="com.fangcang.rfp.common.entity.ProjectCustomBidStrategy">
    SELECT
        t1.CUSTOM_TEND_STRATEGY_ID AS customTendStrategyId,
        t1.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
        t1.HOTEL_ID AS hotelId,
        t1.PROJECT_ID AS projectId,
        t1.STRATEGY_NAME AS strategyName,
        t1.STRATEGY_TYPE AS strategyType,
        t1.SUPPORT_STRATEGY_TEXT AS supportStrategyText,
        t1.SUPPORT_STRATEGY_NAME AS supportStrategyName,
        t1.CREATOR AS creator,
        t1.CREATE_TIME AS createTime,
        t1.MODIFIER AS modifier,
        t1.MODIFY_TIME AS modifyTime
    FROM htl_rfp.T_PROJECT_CUSTOM_BID_STRATEGY t1
    LEFT JOIN htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY t2 ON t1.CUSTOM_TEND_STRATEGY_ID = t2.CUSTOM_TEND_STRATEGY_ID
    WHERE
        t1.PROJECT_ID = #{projectId}
    <if test="hotelId != null">
      and t1.HOTEL_ID = #{hotelId}
    </if>
    <if test="projectIntentHotelId != null">
      and t1.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </if>
    ORDER BY t2.DISPLAY_ORDER ASC
  </select>

  <insert id="batchMergeProjectCustomBidStrategy" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
      MERGE INTO htl_rfp.T_PROJECT_CUSTOM_BID_STRATEGY t
      USING dual
      ON (t.custom_tend_strategy_id = #{item.customTendStrategyId} AND
      t.project_intent_hotel_id = #{item.projectIntentHotelId,jdbcType=DECIMAL} AND
      t.hotel_id = #{item.hotelId,jdbcType=DECIMAL} AND
      t.project_id = #{item.projectId,jdbcType=DECIMAL})
      WHEN MATCHED THEN
      UPDATE SET
      t.support_strategy_name = #{item.supportStrategyName,jdbcType=INTEGER},
      t.STRATEGY_TYPE = #{item.strategyType,jdbcType=INTEGER},
      t.SUPPORT_STRATEGY_TEXT = #{item.supportStrategyText,jdbcType=VARCHAR},
      t.modifier = #{item.modifier,jdbcType=VARCHAR},
      t.modify_time = sysdate
      WHEN NOT MATCHED THEN
      INSERT (custom_tend_strategy_id, project_intent_hotel_id, hotel_id, project_id, strategy_name,STRATEGY_TYPE,SUPPORT_STRATEGY_TEXT,
      support_strategy_name, creator, create_time, modifier, modify_time)
      VALUES (#{item.customTendStrategyId,jdbcType=DECIMAL}, #{item.projectIntentHotelId,jdbcType=DECIMAL}, #{item.hotelId,jdbcType=DECIMAL},
      #{item.projectId,jdbcType=DECIMAL}, #{item.strategyName,jdbcType=VARCHAR},  #{item.strategyType,jdbcType=INTEGER},#{item.supportStrategyText,jdbcType=VARCHAR},
      #{item.supportStrategyName,jdbcType=INTEGER},#{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
    </foreach>
  </insert>
</mapper>