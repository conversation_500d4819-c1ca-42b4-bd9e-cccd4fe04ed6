<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.RecommendHotelDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.RecommendHotel">
        <id column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="BREAKFAST_NUM" jdbcType="INTEGER" property="breakfastNum"/>
        <result column="REFERENCE_PRICE" jdbcType="DECIMAL" property="referencePrice"/>
        <result column="RECOMMEND_SCORE" jdbcType="BIGINT" property="recommendScore"/>
        <result column="BRIGHT_SPOT" jdbcType="VARCHAR" property="brightSpot"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="CONTACT_EMAIL" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="LAST_ROOM_AVAILABLE" jdbcType="INTEGER" property="lastRoomAvailable"/>
        <result column="REQUIRED_ROOM_NIGHT" jdbcType="VARCHAR" property="requiredRoomNight"/>
        <result column="PLATFORM_CONTACT_UID" jdbcType="BIGINT" property="platformContactUid"/>
        <result column="PLATFORM_CONTACT_NAME" jdbcType="VARCHAR" property="platformContactName"/>
        <result column="STATE" jdbcType="INTEGER" property="state"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    HOTEL_ID, BREAKFAST_NUM, REFERENCE_PRICE, RECOMMEND_SCORE, BRIGHT_SPOT, CONTACT_NAME, 
    CONTACT_MOBILE, CONTACT_EMAIL, LAST_ROOM_AVAILABLE, REQUIRED_ROOM_NIGHT, PLATFORM_CONTACT_UID, 
    PLATFORM_CONTACT_NAME, STATE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>
    <select id="selectRecommendHotelByHotelId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_RECOMMEND_HOTEL
        where HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        <if test="state != null">
           and STATE = #{state}
        </if>
    </select>

    <insert id="saveRecommendHotel" parameterType="com.fangcang.rfp.common.entity.RecommendHotel">
        insert into htl_rfp.T_RECOMMEND_HOTEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotelId != null">
                HOTEL_ID,
            </if>
            <if test="breakfastNum != null">
                BREAKFAST_NUM,
            </if>
            <if test="referencePrice != null">
                REFERENCE_PRICE,
            </if>
            <if test="recommendScore != null">
                RECOMMEND_SCORE,
            </if>
            <if test="brightSpot != null">
                BRIGHT_SPOT,
            </if>
            <if test="contactName != null">
                CONTACT_NAME,
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE,
            </if>
            <if test="contactEmail != null">
                CONTACT_EMAIL,
            </if>
            <if test="lastRoomAvailable != null">
                LAST_ROOM_AVAILABLE,
            </if>
            <if test="requiredRoomNight != null">
                REQUIRED_ROOM_NIGHT,
            </if>
            <if test="platformContactUid != null">
                PLATFORM_CONTACT_UID,
            </if>
            <if test="platformContactName != null">
                PLATFORM_CONTACT_NAME,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            CREATE_TIME,
            <if test="modifier != null">
                MODIFIER,
            </if>
            MODIFY_TIME,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotelId != null">
                #{hotelId,jdbcType=BIGINT},
            </if>
            <if test="breakfastNum != null">
                #{breakfastNum,jdbcType=INTEGER},
            </if>
            <if test="referencePrice != null">
                #{referencePrice,jdbcType=DECIMAL},
            </if>
            <if test="recommendScore != null">
                #{recommendScore,jdbcType=BIGINT},
            </if>
            <if test="brightSpot != null">
                #{brightSpot,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null">
                #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null">
                #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="lastRoomAvailable != null">
                #{lastRoomAvailable,jdbcType=INTEGER},
            </if>
            <if test="requiredRoomNight != null">
                #{requiredRoomNight,jdbcType=VARCHAR},
            </if>
            <if test="platformContactUid != null">
                #{platformContactUid,jdbcType=BIGINT},
            </if>
            <if test="platformContactName != null">
                #{platformContactName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            sysdate,
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            sysdate,
        </trim>
    </insert>
    <update id="updateRecommendHotelByHotelId" parameterType="com.fangcang.rfp.common.entity.RecommendHotel">
        update htl_rfp.T_RECOMMEND_HOTEL
        <set>
            <if test="breakfastNum != null">
                BREAKFAST_NUM = #{breakfastNum,jdbcType=INTEGER},
            </if>
            <if test="referencePrice != null">
                REFERENCE_PRICE = #{referencePrice,jdbcType=DECIMAL},
            </if>
            RECOMMEND_SCORE = #{recommendScore,jdbcType=BIGINT},
            BRIGHT_SPOT = #{brightSpot,jdbcType=VARCHAR},
            <if test="contactName != null">
                CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null">
                CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="lastRoomAvailable != null">
                LAST_ROOM_AVAILABLE = #{lastRoomAvailable,jdbcType=INTEGER},
            </if>
            REQUIRED_ROOM_NIGHT = #{requiredRoomNight,jdbcType=VARCHAR},
            <if test="platformContactUid != null">
                PLATFORM_CONTACT_UID = #{platformContactUid,jdbcType=BIGINT},
            </if>
            <if test="platformContactName != null">
                PLATFORM_CONTACT_NAME = #{platformContactName,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = sysdate,
        </set>
        where HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </update>

    <update id="removeRecommendHotelByHotelId" parameterType="com.fangcang.rfp.common.entity.RecommendHotel">
        update htl_rfp.T_RECOMMEND_HOTEL
        <set>
            STATE = #{state,jdbcType=INTEGER},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = sysdate,
        </set>
        where HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </update>

    <select id="queryRecommendHotelInfo" resultType="com.fangcang.rfp.common.dto.response.QueryRecommendHotelResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryRecommendHotelParam">
        select rh.hotel_id as hotelId , ih.chn_name as hotelName ,ih.hotel_star as hotelStar,
        ih.chn_address as hotelAddress ,bc.dataname as cityName ,ig.brandname as hotelGroupName ,ib.brandname as
        hotelBrandName,
        ih.pracice_date as praciceDate,ih.fitment_date as fitmentDate,ih.layer_count as layerCount,ih.rating as rating,
        rh.breakfast_num as breakfastNum ,rh.reference_price as referencePrice ,rh.bright_spot as brightSpot,
        rh.last_room_available as lastRoomAvailable,
        rh.contact_name as contactName,rh.contact_mobile as contactMobile,rh.contact_email as
        contactEmail,rh.platform_contact_name as platformContactName,
        rh.recommend_score as recommendScore,rh.required_room_night as requiredRoomNight,rh.platform_contact_uid as
        platformContactUid
        from htl_rfp.t_recommend_hotel rh
        left join htl_info.t_hotel ih
        on rh.hotel_id = ih.hotelid
        left join htl_info.t_brand ig
        on ih.hotel_group = ig.brandid
        left join htl_info.t_brand ib
        on ih.hotelbrand = ib.brandid
--         left join htl_info.t_image im
--         on ih.hotelid = im.hotelid
--         and im.isactiive = 1
--         and im.ismainimg = 1
        left join htl_base.t_areadata bc
        on ih.city = bc.datacode and bc.datatype = 3
        and bc.countrycode = 'CN'
        where rh.state = 1 and ih.isactive = 1 and ih.country = 'CN'
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName !=''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and rh.hotel_id = #{hotelId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId !=''">
            and ih.hotel_group = #{hotelGroupId}
        </if>
        <if test="plateID != null and plateID !=''">
            and ih.hotelbrand = #{plateID}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and ih.city = #{cityCode}
        </if>
        <if test="hotelStars != null ">
            and ih.hotel_star in
            <foreach collection="hotelStars" open="(" close=")" item="hotelStar" separator="," index="index">
                #{hotelStar}
            </foreach>
        </if>
        <if test="minReferencePrice != null and maxReferencePrice != null">
            and  <![CDATA[ (rh.reference_price >= #{minReferencePrice} and rh.reference_price < #{maxReferencePrice})]]>
        </if>
        <if test="platformContactUid != null">
            and rh.platform_contact_uid = #{platformContactUid}
        </if>
        <if test="platformContactName != null and platformContactName !=''">
            and rh.platform_contact_name like concat(concat('%',#{platformContactName}),'%')
        </if>
        <if test="inviteStatus != null and inviteStatus== 1">
            and  EXISTS (select 1 from  htl_rfp.t_project_intent_hotel ph where rh.hotel_id = ph.hotel_id and ph.project_id = #{projectId})
        </if>
        <if test="inviteStatus != null and inviteStatus== 0">
            and NOT EXISTS (select 1 from  htl_rfp.t_project_intent_hotel ph where rh.hotel_id = ph.hotel_id and ph.project_id = #{projectId})
        </if>
        <if test="otaSort == null">
            order by rh.recommend_score desc, rh.modify_time desc
        </if>
        <if test="otaSort != null">
            <if test="otaSort == 1">
                order by nvl(ih.rating,-1) desc, rh.recommend_score desc, rh.modify_time desc
            </if>
            <if test="otaSort == 0">
                order by nvl(ih.rating,-1) asc , rh.recommend_score desc, rh.modify_time desc
            </if>
        </if>
    </select>

    <select id="queryHotelGroupInvitationHotel" resultType="com.fangcang.rfp.common.dto.response.QueryInvitationHotelResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryInvitationHotelGroupHotelRequest">
        select ih.hotelid as hotelId , ih.chn_name as hotelName ,ih.hotel_star as hotelStar,
        ih.chn_address as hotelAddress ,ih.CITY_NAME as cityName ,ig.brandname as hotelGroupName ,ib.brandname as
        hotelBrandName, pih.creator creator,  pih.CREATE_TIME createTime,
        ih.pracice_date as praciceDate,ih.fitment_date as fitmentDate,ih.layer_count as layerCount,ih.rating as rating,
        rh.breakfast_num as breakfastNum ,rh.reference_price as referencePrice ,rh.bright_spot as brightSpot,
        rh.last_room_available as lastRoomAvailable,
        rh.contact_name as contactName,rh.contact_mobile as contactMobile,rh.contact_email as
        contactEmail,rh.platform_contact_name as platformContactName,
        rh.recommend_score as recommendScore ,rh.required_room_night as requiredRoomNight,rh.platform_contact_uid as
        platformContactUid,rh.state as recommendHotelState ,
        ih.telephone as telephone,ih.lng_baidu as lngBaidu ,ih.lat_baidu as latBaidu
        from  htl_rfp.T_PROJECT_INVITE_HOTEL pih
        left join htl_info.t_hotel ih
        on ih.hotelid = pih.hotel_id and pih.project_id = #{projectId}
        left join htl_rfp.t_recommend_hotel rh
        on ih.hotelid = rh.hotel_id and rh.state = 1
        left join htl_info.t_brand ig
        on ih.hotel_group = TO_CHAR(ig.brandid)
        left join htl_info.t_brand ib
        on ih.hotelbrand = TO_CHAR(ib.brandid)
        where ih.isactive = 1 and ih.country = 'CN'
        <if test="hotelId != null">
            and ih.hotelid = #{hotelId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId != ''">
            and ih.hotel_group = #{hotelGroupId}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and ih.city = #{cityCode}
        </if>
        ORDER BY pih.CREATE_TIME DESC
    </select>
    <select id="queryInvitationHotel" resultType="com.fangcang.rfp.common.dto.response.QueryInvitationHotelResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryInvitationHotelParam">

        select ih.hotelid as hotelId , ih.chn_name as hotelName ,ih.hotel_star as hotelStar,
        ih.chn_address as hotelAddress ,bc.dataname as cityName ,ig.brandname as hotelGroupName ,ib.brandname as
        hotelBrandName,
        ih.pracice_date as praciceDate,ih.fitment_date as fitmentDate,ih.layer_count as layerCount,ih.rating as rating,
        rh.breakfast_num as breakfastNum ,rh.reference_price as referencePrice ,rh.bright_spot as brightSpot,
        rh.last_room_available as lastRoomAvailable,
        rh.contact_name as contactName,rh.contact_mobile as contactMobile,rh.contact_email as
        contactEmail,rh.platform_contact_name as platformContactName,
        rh.recommend_score as recommendScore ,rh.required_room_night as requiredRoomNight,rh.platform_contact_uid as
        platformContactUid,rh.state as recommendHotelState ,ph.invite_status as inviteStatus,ph.project_intent_hotel_id as projectIntentHotelId,
        ih.telephone as telephone,ih.lng_baidu as lngBaidu ,ih.lat_baidu as latBaidu
        from htl_info.t_hotel ih
        left join htl_rfp.t_recommend_hotel rh
        on ih.hotelid = rh.hotel_id and rh.state = 1
        left join htl_info.t_brand ig
        on ih.hotel_group = TO_CHAR(ig.brandid)
        left join htl_info.t_brand ib
        on ih.hotelbrand = TO_CHAR(ib.brandid)
        left join htl_base.t_areadata bc
        on ih.city = bc.datacode and bc.datatype = 3
        and bc.countrycode = 'CN'
        left join htl_rfp.t_project_intent_hotel ph
        on ih.hotelid = ph.hotel_id and ph.project_id = #{projectId}
        where ih.isactive = 1 and ih.country = 'CN'
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and ih.hotelid = #{hotelId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId != ''">
            and ih.hotel_group = #{hotelGroupId}
        </if>
        <if test="plateID != null and plateID != ''">
            and ih.hotelbrand = #{plateID}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and ih.city = #{cityCode}
        </if>
        <if test="hotelStars != null">
            and ih.hotel_star in
            <foreach collection="hotelStars" open="(" close=")" item="hotelStar" separator="," index="index">
                #{hotelStar}
            </foreach>
        </if>
        <if test="minReferencePrice != null and maxReferencePrice != null">
            and  <![CDATA[ (rh.reference_price >= #{minReferencePrice} and rh.reference_price < #{maxReferencePrice})]]>
        </if>
        <if test="platformContactUid != null">
            and rh.platform_contact_uid = #{platformContactUid}
        </if>
        <if test="platformContactName != null and platformContactName !=''">
            and rh.platform_contact_name like concat(concat('%',#{platformContactName}),'%')
        </if>
        <if test="inviteStatus != null and inviteStatus ==1">
            and ph.invite_status = 1
        </if>
        <if test="inviteStatus != null and inviteStatus ==0">
             and (ph.invite_status = 0 or ph.invite_status is null)
        </if>
        <if test="lngBaiDu != null and latBaiDu != null">
           and <![CDATA[ (ih.lng_baidu >= #{minLon} and ih.lng_baidu <= #{maxLon} and ih.lat_baidu >= #{minLat} and ih.lat_baidu <= #{maxLat})]]>
        </if>
        <if test="otaSort == null">
            order by nvl(rh.recommend_score,-1) desc, rh.modify_time desc
        </if>
        <if test="otaSort != null">
            <if test="otaSort == 1">
                order by nvl(ih.rating,-1) desc, nvl(rh.recommend_score,-1) desc, rh.modify_time desc
            </if>
            <if test="otaSort == 0">
                order by nvl(ih.rating,-1) asc , nvl(rh.recommend_score,-1) desc, rh.modify_time desc
            </if>
        </if>
    </select>

    <select id="selectRecommendHotelByHotelIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_RECOMMEND_HOTEL
        where STATE=1 and HOTEL_ID in
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </select>

    <select id="selectGroupOrBrandInfo" resultType="com.fangcang.rfp.common.dto.response.GroupOrBrandInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryGroupOrBrandInfoParam">
        select t.brandid as brandId,t.brandname as brandName from htl_info.T_BRAND t
        where
        <if test="queryType==0">
            t.groupid=0
            <if test="groupId != null">
                and t.brandid = #{groupId}
            </if>
        </if>
        <if test="queryType==1">
            t.groupid !=0
            <if test="groupId != null">
                and t.groupid = #{groupId}
            </if>
        </if>
        <if test="groupOrBrandName != null and groupOrBrandName !=''">
            and t.brandname like concat(concat('%',#{groupOrBrandName}),'%')
        </if>
    </select>

    <select id="selectGroupByIdList" resultType="com.fangcang.rfp.common.dto.response.GroupOrBrandInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryGroupOrBrandInfoParam">
        select t.brandid as brandId,t.brandname as brandName from htl_info.T_BRAND t
        where
            t.groupid =0
            and t.brandid IN
            <foreach collection="hotelGroupIdList" open="(" close=")" separator="," item="hotelGroupId">
                #{hotelGroupId}
            </foreach>
    </select>

    <select id="selectBrandByIdList" resultType="com.fangcang.rfp.common.dto.response.GroupOrBrandInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryGroupOrBrandInfoParam">
        select t.brandid as brandId,t.brandname as brandName from htl_info.T_BRAND t
        where
        t.groupid !=0
        and t.brandid IN
        <foreach collection="brandIdList" open="(" close=")" separator="," item="brandId">
            #{brandId}
        </foreach>
    </select>

    <select id="selectHotelGroupBrandInfo" resultType="com.fangcang.rfp.common.dto.response.GroupOrBrandInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBrandRequest">
        SELECT t.brandid AS brandId,t.brandname AS brandName FROM htl_info.T_BRAND t
        WHERE t.groupid !=0 AND t.groupid = #{groupId}
    </select>
</mapper>