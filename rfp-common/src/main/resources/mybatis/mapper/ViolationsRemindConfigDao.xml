<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ViolationsRemindConfigDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ViolationsRemindConfig">
        <id column="REMIND_CONFIG_ID" jdbcType="DECIMAL" property="remindConfigId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="MINIMUM_NIGHTS" jdbcType="DECIMAL" property="minimumNights"/>
        <result column="MAXIMUM_NIGHTS" jdbcType="DECIMAL" property="maximumNights"/>
        <result column="NEED_SYSTEM_REMINDER_COUNT" jdbcType="DECIMAL" property="needSystemReminderCount"/>
        <result column="NEED_PRIORITY_REMINDER_COUNT" jdbcType="DECIMAL" property="needPriorityReminderCount"/>
        <result column="NEED_JIALI_FOLLOW_UP_COUNT" jdbcType="DECIMAL" property="needJialiFollowUpCount"/>
        <result column="NEED_BUSINESS_FOLLOW_UP_COUNT" jdbcType="DECIMAL" property="needBusinessFollowUpCount"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    REMIND_CONFIG_ID, PROJECT_ID, MINIMUM_NIGHTS, MAXIMUM_NIGHTS, NEED_SYSTEM_REMINDER_COUNT,
    NEED_PRIORITY_REMINDER_COUNT, NEED_JIALI_FOLLOW_UP_COUNT,NEED_BUSINESS_FOLLOW_UP_COUNT,
    CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VIOLATIONS_REMIND_CONFIG
        where REMIND_CONFIG_ID = #{remindConfigId,jdbcType=DECIMAL}
    </select>
    <select id="queryViolationsRemindConfig"
            resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from T_VIOLATIONS_REMIND_CONFIG where PROJECT_ID = #{projectId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_VIOLATIONS_REMIND_CONFIG
    where REMIND_CONFIG_ID = #{remindConfigId,jdbcType=DECIMAL}
  </delete>

    <insert id="batchViolationsRemindConfig" parameterType="java.util.List">
        MERGE INTO htl_rfp.T_VIOLATIONS_REMIND_CONFIG dest
        USING (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.remindConfigId,jdbcType=DECIMAL} AS remindConfigId,
            #{item.projectId,jdbcType=DECIMAL} AS projectId,
            #{item.minimumNights,jdbcType=DECIMAL} AS minimumNights,
            #{item.maximumNights,jdbcType=DECIMAL} AS maximumNights,
            #{item.needSystemReminderCount,jdbcType=DECIMAL} AS needSystemReminderCount,
            #{item.needPriorityReminderCount,jdbcType=DECIMAL} AS needPriorityReminderCount,
            #{item.needJialiFollowUpCount,jdbcType=DECIMAL} AS needJialiFollowUpCount,
            #{item.needBusinessFollowUpCount,jdbcType=DECIMAL} AS needBusinessFollowUpCount,
            #{item.creator,jdbcType=VARCHAR} AS creator,
            #{item.modifier,jdbcType=VARCHAR} AS modifier
            FROM dual
        </foreach>
        ) src
        ON (dest.remind_config_id = src.remindConfigId AND dest.project_id = src.projectId)
        WHEN MATCHED THEN
        UPDATE SET
        dest.minimum_nights = src.minimumNights,
        dest.maximum_nights = src.maximumNights,
        dest.need_system_reminder_count = src.needSystemReminderCount,
        dest.need_priority_reminder_count = src.needPriorityReminderCount,
        dest.need_jiali_follow_up_count = src.needJialiFollowUpCount,
        dest.need_business_follow_up_count = src.needBusinessFollowUpCount,
        dest.modifier = src.modifier,
        dest.modify_time = SYSDATE
        WHEN NOT MATCHED THEN
        INSERT (remind_config_id, project_id, minimum_nights, maximum_nights,
        need_system_reminder_count, need_priority_reminder_count,
        need_jiali_follow_up_count, need_business_follow_up_count,
        creator, create_time, modifier, modify_time)
        VALUES (SEQ_VIOLATIONS_REMIND_CONFIG.NEXTVAL, src.projectId, src.minimumNights, src.maximumNights,
        src.needSystemReminderCount, src.needPriorityReminderCount,
        src.needJialiFollowUpCount, src.needBusinessFollowUpCount,
        src.creator, SYSDATE, src.modifier, SYSDATE)
    </insert>

</mapper>