<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ChannelPartnerDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ChannelPartner">
        <result column="CHANNEL_PARTNER_ID" jdbcType="DECIMAL" property="channelPartnerId"/>
        <result column="PARTNER_NAME" jdbcType="VARCHAR" property="partnerName"/>
        <result column="PARTNER_CODE" jdbcType="VARCHAR" property="partnerCode"/>
        <result column="LOGIN_DOMAIN_URL" jdbcType="VARCHAR" property="loginDomainUrl"/>
        <result column="BACKGROUND_IMAGE_URL" jdbcType="VARCHAR" property="backgroundImageUrl"/>
        <result column="LOGO_IMAGE_URL" jdbcType="VARCHAR" property="logoImageUrl"/>
        <result column="AGREEMENT_URL" jdbcType="VARCHAR" property="agreementUrl"/>
        <result column="LOGIN_SKIN" jdbcType="VARCHAR" property="loginSkin"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHANNEL_PARTNER_ID, PARTNER_NAME, PARTNER_CODE,LOGIN_DOMAIN_URL, BACKGROUND_IMAGE_URL,
      LOGO_IMAGE_URL, AGREEMENT_URL, LOGIN_SKIN, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <select id="queryNextId" resultType="java.lang.Long">
        SELECT htl_rfp.SEQ_CHANNEL_PARTNER_ID.nextval FROM dual
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.dto.request.AddChannelPartnerDto">
        <selectKey keyProperty="channelPartnerId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_CHANNEL_PARTNER_ID.nextval from dual
        </selectKey>
        INSERT INTO htl_rfp.T_CHANNEL_PARTNER (CHANNEL_PARTNER_ID, PARTNER_NAME, PARTNER_CODE,
        LOGIN_DOMAIN_URL, BACKGROUND_IMAGE_URL,LOGO_IMAGE_URL,AGREEMENT_URL, LOGIN_SKIN,
        CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME)
        VALUES (#{channelPartnerId}, #{partnerName,jdbcType=VARCHAR}, #{partnerCode,jdbcType=VARCHAR},
        #{loginDomainUrl,jdbcType=VARCHAR}, #{backgroundImageUrl,jdbcType=VARCHAR},
        #{logoImageUrl,jdbcType=VARCHAR}, #{agreementUrl,jdbcType=VARCHAR}, #{loginSkin,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},  sysdate, #{modifier,jdbcType=VARCHAR},  sysdate)
    </insert>

    <select id="queryChannelById" resultType="com.fangcang.rfp.common.dto.response.QueryChannelPartnerResponse">
        SELECT
            CHANNEL_PARTNER_ID as channelPartnerId,
            PARTNER_NAME as partnerName,
            PARTNER_CODE as partnerCode,
            LOGIN_DOMAIN_URL as loginDomainUrl,
            BACKGROUND_IMAGE_URL as backgroundImageUrl,
            LOGO_IMAGE_URL as logoImageUrl,
            AGREEMENT_URL as agreementUrl,
            LOGIN_SKIN as loginSkin,
            CREATOR as creator,
            CREATE_TIME as createTime,
            MODIFIER as modifier,
            MODIFY_TIME as modifyTime
        FROM
            htl_rfp.T_CHANNEL_PARTNER
        WHERE
            CHANNEL_PARTNER_ID = #{channelPartnerId}
    </select>

    <select id="queryChannelPartnerByName" parameterType="com.fangcang.rfp.common.dto.request.QueryChannelPartnerByNameRequest" resultType="com.fangcang.rfp.common.dto.response.QueryChannelPartnerByNameResponse">
        SELECT
            CHANNEL_PARTNER_ID as channelPartnerId,
            PARTNER_NAME as partnerName,
            PARTNER_CODE as partnerCode
        FROM
            htl_rfp.T_CHANNEL_PARTNER
        WHERE
            PARTNER_NAME LIKE concat('%', CONCAT(#{partnerName},'%'))
    </select>

    <select id="queryChannelList"
            resultType="com.fangcang.rfp.common.dto.response.QueryChannelPartnerResponse"
            parameterType="com.fangcang.rfp.common.dto.request.ChannelPartnerRequest">
        SELECT
            CHANNEL_PARTNER_ID as channelPartnerId,
            PARTNER_NAME as partnerName,
            PARTNER_CODE as partnerCode,
            LOGIN_DOMAIN_URL as loginDomainUrl,
            BACKGROUND_IMAGE_URL as backgroundImageUrl,
            LOGO_IMAGE_URL as logoImageUrl,
            AGREEMENT_URL as agreementUrl,
            LOGIN_SKIN as loginSkin,
            CREATOR as creator,
            CREATE_TIME as createTime,
            MODIFIER as modifier,
            MODIFY_TIME as modifyTime
        FROM htl_rfp.T_CHANNEL_PARTNER
        WHERE
            1=1
        <if test="partnerName != null and partnerName != ''">
            AND PARTNER_NAME = #{partnerName}
        </if>
        <if test="partnerCode != null and partnerCode != ''">
            AND PARTNER_CODE = #{partnerCode}
        </if>
        <if test="loginDomainUrl != null and loginDomainUrl != ''">
            AND LOGIN_DOMAIN_URL = #{loginDomainUrl}
        </if>
        order by CREATE_TIME desc
    </select>
    <update id="updateByChannelPartnerId" parameterType="com.fangcang.rfp.common.dto.request.UpdateChannelPartnerDto">
      UPDATE
          htl_rfp.T_CHANNEL_PARTNER
      SET
          PARTNER_NAME = #{partnerName,jdbcType=VARCHAR},
          PARTNER_CODE = #{partnerCode,jdbcType=VARCHAR},
          LOGIN_DOMAIN_URL = #{loginDomainUrl,jdbcType=VARCHAR},
          BACKGROUND_IMAGE_URL = #{backgroundImageUrl,jdbcType=VARCHAR},
          LOGO_IMAGE_URL = #{logoImageUrl, jdbcType=VARCHAR},
          AGREEMENT_URL = #{agreementUrl,jdbcType=VARCHAR},
          LOGIN_SKIN = #{loginSkin,jdbcType=VARCHAR},
          MODIFIER = #{modifier,jdbcType=VARCHAR},
          MODIFY_TIME = sysDate
      WHERE CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=DECIMAL}
    </update>

</mapper>