<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHistoryRecommendDao">
    <update id="insertOrUpdate" parameterType="com.fangcang.rfp.common.entity.ProjectHistoryRecommend">
        MERGE INTO htl_rfp.T_PROJECT_HISTORY_RECOMMEND dest
            USING (
                SELECT  #{projectId,jdbcType=BIGINT} AS projectId,
                        #{hotelId,jdbcType=BIGINT} AS hotelId,
                        #{isFrequency,jdbcType=INTEGER} AS isFrequency,
                        #{isFrequencyRecommend,jdbcType=INTEGER} AS isFrequencyRecommend,
                        #{frequencyRecommends,jdbcType=VARCHAR} AS frequencyRecommends,
                        #{isSameLevelRreq,jdbcType=INTEGER} AS isSameLevelRreq,
                        #{isSameLevelRreqRecommend,jdbcType=INTEGER} AS isSameLevelRreqRecommend,
                        #{sameLevelRreqHotelId,jdbcType=BIGINT} AS sameLevelRreqHotelId,
                        #{sameLevelRreqHotelDistance,jdbcType=DECIMAL} AS sameLevelRreqHotelDistance,
                        #{sameLevelRreqHotelInfo,jdbcType=VARCHAR} AS sameLevelRreqHotelInfo,
                        #{sameLevelRreqRecommends,jdbcType=VARCHAR} AS sameLevelRreqRecommends,
                        #{isPoiNearHotel,jdbcType=INTEGER} AS isPoiNearHotel,
                        #{isPoiNearHotelRecommend,jdbcType=INTEGER} AS isPoiNearHotelRecommend,
                        #{poiNearHotelRecommends,jdbcType=VARCHAR} AS poiNearHotelRecommends,
                        #{poiNearHotelPoiId,jdbcType=BIGINT} AS poiNearHotelPoiId,
                        #{isNoPoiHotArea,jdbcType=INTEGER} AS isNoPoiHotArea,
                        #{isNoPoiHotAreaRecommend,jdbcType=INTEGER} AS isNoPoiHotAreaRecommend,
                        #{noPoiHotAreaRecommends,jdbcType=VARCHAR} AS noPoiHotAreaRecommends,
                        #{noPoiHotelId,jdbcType=BIGINT} AS noPoiHotelId,
                        #{noPoiHotelInfo,jdbcType=VARCHAR} AS noPoiHotelInfo,
                        #{isAreaGather,jdbcType=INTEGER} AS isAreaGather,
                        #{isAreaGatherRecommend,jdbcType=INTEGER} AS isAreaGatherRecommend,
                        #{areaGatherHotelId,jdbcType=BIGINT} AS areaGatherHotelId,
                        #{areaGatherHotelInfo,jdbcType=VARCHAR} AS areaGatherHotelInfo,
                        #{areaGatherRecommends,jdbcType=VARCHAR} AS areaGatherRecommends,
                        #{isHighQuality,jdbcType=INTEGER} AS isHighQuality,
                        #{isHighQualityRecommend,jdbcType=INTEGER} AS isHighQualityRecommend,
                        #{isSavedHotel,jdbcType=INTEGER} AS isSavedHotel,
                        #{isSavedHotelRecommend,jdbcType=INTEGER} AS isSavedHotelRecommend,
                        #{priceLevelRoomNight,jdbcType=INTEGER} AS priceLevelRoomNight,
                        #{creator,jdbcType=VARCHAR} AS creator,
                        #{modifier,jdbcType=VARCHAR} AS modifier
                FROM dual
            ) src
            ON (dest.PROJECT_ID = src.projectId AND dest.HOTEL_ID = src.hotelId)
            WHEN MATCHED THEN
                UPDATE SET
                    <if test="isFrequency != null">
                        dest.IS_FREQUENCY = #{isFrequency,jdbcType=INTEGER},
                    </if>
                    <if test="isFrequencyRecommend != null">
                        dest.IS_FREQUENCY_RECOMMEND = #{isFrequencyRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="frequencyRecommends != null and frequencyRecommends != ''">
                        dest.FREQUENCY_RECOMMENDS = #{frequencyRecommends,jdbcType=VARCHAR},
                    </if>
                    <if test="isSameLevelRreq != null ">
                        dest.IS_SAME_LEVEL_FREQ = #{isSameLevelRreq,jdbcType=INTEGER},
                    </if>
                    <if test="isSameLevelRreqRecommend != null ">
                        dest.IS_SAME_LEVEL_FREQ_RECOMMEND = #{isSameLevelRreqRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="sameLevelRreqHotelId != null ">
                        dest.SAME_LEVEL_FREQ_HOTEL_ID = #{sameLevelRreqHotelId,jdbcType=BIGINT},
                    </if>
                    <if test="sameLevelRreqHotelDistance != null ">
                        dest.SAME_LEVEL_FREQ_HOTEL_DISTANCE = #{sameLevelRreqHotelDistance,jdbcType=DECIMAL},
                    </if>
                    <if test="sameLevelRreqHotelInfo != null and sameLevelRreqHotelInfo !='' ">
                        dest.SAME_LEVEL_FREQ_HOTEL_INFO = #{sameLevelRreqHotelInfo,jdbcType=VARCHAR},
                    </if>
                    <if test="sameLevelRreqRecommends != null and sameLevelRreqRecommends !='' ">
                        dest.SAME_LEVEL_FREQ_RECOMMENDS = #{sameLevelRreqRecommends,jdbcType=VARCHAR},
                    </if>
                    <if test="isPoiNearHotel != null ">
                        dest.IS_POI_NEAR_HOTEL = #{isPoiNearHotel,jdbcType=INTEGER},
                    </if>
                    <if test="isPoiNearHotelRecommend != null ">
                        dest.IS_POI_NEAR_HOTEL_RECOMMEND = #{isPoiNearHotelRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="poiNearHotelRecommends != null and poiNearHotelRecommends !='' ">
                        dest.POI_NEAR_HOTEL_RECOMMENDS = #{poiNearHotelRecommends,jdbcType=VARCHAR},
                    </if>
                    <if test="poiNearHotelPoiId != null ">
                        dest.POI_NEAR_HOTEL_POI_ID = #{poiNearHotelPoiId,jdbcType=BIGINT},
                    </if>
                    <if test="isNoPoiHotArea != null">
                        dest.IS_NO_POI_HOT_AREA = #{isNoPoiHotArea,jdbcType=INTEGER},
                    </if>
                    <if test="isNoPoiHotAreaRecommend != null">
                        dest.IS_NO_POI_HOT_AREA_RECOMMEND = #{isNoPoiHotAreaRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="noPoiHotAreaRecommends != null and noPoiHotAreaRecommends !='' ">
                        dest.NO_POI_HOT_AREA_RECOMMENDS = #{noPoiHotAreaRecommends,jdbcType=VARCHAR},
                    </if>
                    <if test="noPoiHotelId != null ">
                        dest.NO_POI_HOTEL_ID = #{noPoiHotelId,jdbcType=BIGINT},
                    </if>
                    <if test="noPoiHotelInfo != null and noPoiHotelInfo != ''">
                        dest.NO_POI_HOTEL_INFO = #{noPoiHotelInfo,jdbcType=VARCHAR},
                    </if>
                    <if test="isAreaGather != null">
                        dest.IS_AREA_GATHER = #{isAreaGather,jdbcType=INTEGER},
                    </if>
                    <if test="isAreaGatherRecommend != null">
                        dest.IS_AREA_GATHER_RECOMMEND = #{isAreaGatherRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="areaGatherHotelId != null">
                        dest.AREA_GATHER_HOTEL_ID = #{areaGatherHotelId,jdbcType=BIGINT},
                    </if>
                    <if test="areaGatherHotelInfo != null and areaGatherHotelInfo != ''">
                        dest.AREA_GATHER_HOTEL_INFO = #{areaGatherHotelInfo,jdbcType=VARCHAR},
                    </if>
                    <if test="areaGatherRecommends != null and areaGatherRecommends != ''">
                        dest.AREA_GATHER_RECOMMENDS = #{areaGatherRecommends,jdbcType=VARCHAR},
                    </if>
                    <if test="isHighQuality != null">
                        dest.IS_HIGH_QUALITY = #{isHighQuality,jdbcType=INTEGER},
                    </if>
                    <if test="isHighQualityRecommend != null">
                        dest.IS_HIGH_QUALITY_RECOMMEND = #{isHighQualityRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="isSavedHotel != null">
                        dest.IS_SAVED_HOTEL = #{isSavedHotel,jdbcType=INTEGER},
                    </if>
                    <if test="isSavedHotelRecommend != null">
                        dest.IS_SAVED_HOTEL_RECOMMEND = #{isSavedHotelRecommend,jdbcType=INTEGER},
                    </if>
                    <if test="priceLevelRoomNight != null">
                        dest.PRICE_LEVEL_ROOM_NIGHT = #{priceLevelRoomNight,jdbcType=INTEGER},
                    </if>
                    dest.MODIFIER = #{modifier,jdbcType=VARCHAR},
                    dest.MODIFY_TIME = SYSDATE
            WHEN NOT MATCHED THEN
                INSERT (PROJECT_ID, HOTEL_ID,
                        IS_FREQUENCY,IS_FREQUENCY_RECOMMEND,FREQUENCY_RECOMMENDS,
                        IS_SAME_LEVEL_FREQ,IS_SAME_LEVEL_FREQ_RECOMMEND,SAME_LEVEL_FREQ_HOTEL_ID,SAME_LEVEL_FREQ_HOTEL_DISTANCE,SAME_LEVEL_FREQ_HOTEL_INFO,SAME_LEVEL_FREQ_RECOMMENDS,
                        IS_POI_NEAR_HOTEL,IS_POI_NEAR_HOTEL_RECOMMEND,POI_NEAR_HOTEL_RECOMMENDS,POI_NEAR_HOTEL_POI_ID,
                        IS_NO_POI_HOT_AREA,IS_NO_POI_HOT_AREA_RECOMMEND,NO_POI_HOT_AREA_RECOMMENDS,NO_POI_HOTEL_ID,NO_POI_HOTEL_INFO,
                        IS_AREA_GATHER,IS_AREA_GATHER_RECOMMEND,AREA_GATHER_HOTEL_ID,AREA_GATHER_HOTEL_INFO, AREA_GATHER_RECOMMENDS,
                        IS_HIGH_QUALITY, IS_HIGH_QUALITY_RECOMMEND,
                        IS_SAVED_HOTEL,IS_SAVED_HOTEL_RECOMMEND, PRICE_LEVEL_ROOM_NIGHT,
                        CREATOR, CREATE_TIME)
                    VALUES (src.projectId, src.hotelId,
                            src.isFrequency,src.isFrequencyRecommend,src.frequencyRecommends,
                            src.isSameLevelRreq,src.isSameLevelRreqRecommend,src.sameLevelRreqHotelId, src.sameLevelRreqHotelDistance, src.sameLevelRreqHotelInfo,src.sameLevelRreqRecommends,
                            src.isPoiNearHotel,src.isPoiNearHotelRecommend,src.poiNearHotelRecommends,src.poiNearHotelPoiId,
                            src.isNoPoiHotArea, src.isNoPoiHotAreaRecommend, src.noPoiHotAreaRecommends,src.noPoiHotelId, src.noPoiHotelInfo,
                            src.isAreaGather,src.isAreaGatherRecommend,src.areaGatherHotelId,src.areaGatherHotelInfo,src.areaGatherRecommends,
                            src.isHighQuality, src.isHighQualityRecommend,
                            src.isSavedHotel,src.isSavedHotelRecommend,src.priceLevelRoomNight,
                            src.creator, sysdate)
    </update>

    <update id="resetRecommendFrequencySameLevelInfo" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_SAME_LEVEL_FREQ = 0,
            IS_SAME_LEVEL_FREQ_RECOMMEND = 0,
            SAME_LEVEL_FREQ_HOTEL_ID = null,
            SAME_LEVEL_FREQ_HOTEL_INFO = null,
            SAME_LEVEL_FREQ_RECOMMENDS = null
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendFrequency" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_FREQUENCY = 0,
            IS_FREQUENCY_RECOMMEND = 0,
            FREQUENCY_RECOMMENDS = null
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendPoiNearHotelInfo" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_POI_NEAR_HOTEL = 0,
            IS_POI_NEAR_HOTEL_RECOMMEND = 0,
            POI_NEAR_HOTEL_RECOMMENDS = null,
            POI_NEAR_HOTEL_POI_ID = null
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendNoPoiHotArea" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_NO_POI_HOT_AREA = 0,
            IS_NO_POI_HOT_AREA_RECOMMEND = 0,
            NO_POI_HOT_AREA_RECOMMENDS = null,
            NO_POI_HOTEL_ID = null,
            NO_POI_HOTEL_INFO = null
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetRecommendAreaGather" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_AREA_GATHER = 0,
            IS_AREA_GATHER_RECOMMEND = 0,
            AREA_GATHER_HOTEL_ID = null,
            AREA_GATHER_HOTEL_INFO = null,
            AREA_GATHER_RECOMMENDS = null
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetHighQuality" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_HIGH_QUALITY = 0,
            IS_HIGH_QUALITY_RECOMMEND = 0,
            PRICE_LEVEL_ROOM_NIGHT = 0
        WHERE
            PROJECT_ID = #{projectId}
    </update>

    <update id="resetSavedHotel" parameterType="java.lang.Long">
        UPDATE
            htl_rfp.T_PROJECT_HISTORY_RECOMMEND
        SET
            IS_SAVED_HOTEL = 0,
            IS_SAVED_HOTEL_RECOMMEND = 0,
            PRICE_LEVEL_ROOM_NIGHT = 0
        WHERE
            PROJECT_ID = #{projectId}
    </update>

</mapper>
