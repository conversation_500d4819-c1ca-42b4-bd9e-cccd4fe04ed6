<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelWhiteDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelWhite">
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="HOTEL_WHITE_TYPE" jdbcType="INTEGER" property="hotelWhiteType"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        HOTEL_ID, PROJECT_ID, HOTEL_WHITE_TYPE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <insert id="mergeProjectHotelWhite" parameterType="com.fangcang.rfp.common.entity.ProjectHotelWhite">
        MERGE INTO htl_rfp.T_PROJECT_HOTEL_WHITE t
        USING (SELECT #{hotelId} AS hotel_id,
                      #{projectId} AS project_id,
                      #{hotelWhiteType} AS HOTEL_WHITE_TYPE,
                      #{creator,jdbcType=VARCHAR} AS creator,
                      #{modifier,jdbcType=VARCHAR} AS modifier
               FROM dual) s
        ON (t.hotel_id = s.hotel_id AND t.project_id = s.project_id AND t.HOTEL_WHITE_TYPE = s.HOTEL_WHITE_TYPE)
        WHEN MATCHED THEN
            UPDATE SET
                t.modifier = s.modifier,
                t.modify_time = sysdate
        WHEN NOT MATCHED THEN
            INSERT (hotel_id, project_id, HOTEL_WHITE_TYPE, creator, create_time, modifier, modify_time)
            VALUES (s.hotel_id, s.project_id, s.HOTEL_WHITE_TYPE, s.creator, sysdate, s.modifier, sysdate)
    </insert>

    <select id="queryMonitorProjectHotelWhiteDetailList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
         FROM
            htl_rfp.T_PROJECT_HOTEL_WHITE
         WHERE
             PROJECT_ID  IN
         <foreach collection="projectIdList" item="projectId" open="（" close="）" separator=",">
             #{projectId}
         </foreach>
         AND HOTEL_WHITE_TYPE IN
        <foreach collection="hotelWhiteTypes" item="hotelWhiteType" open="（" close="）" separator=",">
            #{hotelWhiteType}
        </foreach>
    </select>
    <select id="queryProjectHotelWhiteDetailList"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectHotelWhiteResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectHotelWhiteRequest">
        select ih.hotelid as hotelId,
        ih.chn_name as hotelName,
        t.project_id as projectId,
        t.HOTEL_WHITE_TYPE as hotelWhiteType,
        ih.city_name as cityName,
        t.creator as creator,
        t.create_time as createTime
        from htl_rfp.T_PROJECT_HOTEL_WHITE t ,htl_info.t_hotel ih
        where t.hotel_id = ih.hotelid and ih.isactive=1 and ih.country='CN'
        AND T.PROJECT_ID =#{projectId}
        <if test="hotelId != null">
            AND t.hotel_id= #{hotelId}
        </if>
        <if test="hotelName != null and hotelName!=''">
            and ih.chn_name like concat(concat('%',#{hotelName}),'%')
        </if>
        <if test="cityCode != null and cityCode !=''">
            and ih.city = #{cityCode}
        </if>
        <if test="hotelWhiteType != null and hotelWhiteType !=''">
            AND t.HOTEL_WHITE_TYPE= #{hotelWhiteType}
        </if>
    </select>

    <delete id="deleteProjectHotelWhite">
        DELETE FROM htl_rfp.T_PROJECT_HOTEL_WHITE
        WHERE hotel_id = #{hotelId} AND project_id = #{projectId} AND HOTEL_WHITE_TYPE = #{hotelWhiteType}
    </delete>

</mapper>