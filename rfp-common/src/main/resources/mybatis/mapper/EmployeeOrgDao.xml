<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.EmployeeOrgDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.EmployeeOrg">
        <result column="CHANNEL_PARTNER_ID" jdbcType="DECIMAL" property="channelPartnerId"/>
        <result column="USER_ID" jdbcType="DECIMAL" property="userId"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHANNEL_PARTNER_ID, USER_ID, ORG_ID, CREATOR, CREATE_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.dto.request.AddEmployeeOrgDto">
        INSERT INTO htl_rfp.T_EMPLOYEE_ORG (CHANNEL_PARTNER_ID, USER_ID, ORG_ID,CREATOR, CREATE_TIME)
        VALUES (#{channelPartnerId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR},  sysdate)
    </insert>

    <delete id="delete" parameterType="com.fangcang.rfp.common.dto.request.DeleteEmployeeOrgDto">
        DELETE FROM
            htl_rfp.T_EMPLOYEE_ORG
        WHERE USER_ID = #{userId,jdbcType=BIGINT}
          AND ORG_ID = #{orgId,jdbcType=BIGINT}
        <if test="channelPartnerId != null and channelPartnerId > 0">
            AND CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=BIGINT}
        </if>
    </delete>

    <select id="queryEmployeeOrgResponseList" parameterType="com.fangcang.rfp.common.dto.request.QueryEmployeeOrgRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryEmployeeOrgResponse">
        SELECT
            c.CHANNEL_PARTNER_ID AS channelPartnerId,
            eo.USER_ID as userId,
            eo.ORG_ID as orgId,
            c.PARTNER_NAME as channelPartnerName,
            u.USER_NAME as userName,
            o.ORG_NAME as orgName,
            eo.CREATOR as creator,
            eo.CREATE_TIME as createTime
        FROM
            htl_rfp.T_EMPLOYEE_ORG eo
        LEFT JOIN htl_rfp.T_ORG o ON eo.ORG_ID = o.ORG_ID
        LEFT JOIN htl_rfp.T_USER u ON  eo.USER_ID =  u.USER_ID
        LEFT JOIN htl_rfp.T_CHANNEL_PARTNER c ON u.CHANNEL_PARTNER_ID = c.CHANNEL_PARTNER_ID
        WHERE 1=1
            <if test="channelPartnerName != null and channelPartnerName != ''">
                AND c.PARTNER_NAME LIKE concat('%', CONCAT(#{channelPartnerName},'%'))
            </if>
            <if test="channelPartnerId != null and channelPartnerId > 0">
                AND c.CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=BIGINT}
            </if>
            <if test="userName != null and userName != ''">
                AND u.USER_NAME LIKE concat('%', CONCAT(#{userName},'%'))
            </if>
            <if test="orgName != null and orgName != ''">
                AND o.ORG_NAME  LIKE concat('%', CONCAT(#{orgName},'%'))
            </if>
        ORDER BY eo.CREATE_TIME DESC
    </select>

    <select id="getEmployOrg" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM
            htl_rfp.T_EMPLOYEE_ORG
        WHERE
            USER_ID = #{userId}
        AND
            ORG_ID = #{orgId}
    </select>

    <select id="queryUserEmployOrgList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
            htl_rfp.T_EMPLOYEE_ORG
        WHERE
            USER_ID = #{userId}
    </select>

</mapper>