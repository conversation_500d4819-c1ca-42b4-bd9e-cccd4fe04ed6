<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelHexagonLngLatDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelHexagonLngLat">
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="HEXAGON_LNG_LAT_10" jdbcType="VARCHAR" property="hexagonLngLat10"/>
        <result column="LNG_BAIDU" jdbcType="DOUBLE" property="lngBaidu"/>
        <result column="LAT_BAIDU" jdbcType="DOUBLE" property="latBaidu"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        HOTEL_ID, HEXAGON_LNG_LAT_10, LNG_BAIDU, LAT_BAIDU, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <select id="selectByHotelId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM htl_rfp.T_HOTEL_HEXAGON_LNG_LAT
        WHERE HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </select>

    <select id="selectLngLatByHotelId"  resultMap="BaseResultMap">
        SELECT
        HOTEL_ID, LNG_BAIDU, LAT_BAIDU, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
        FROM htl_rfp.T_HOTEL_HEXAGON_LNG_LAT
        WHERE HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </select>
    <select id="selectLngLatByHotelIds" resultMap="BaseResultMap">
        SELECT
            HOTEL_ID, LNG_BAIDU, LAT_BAIDU, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
        FROM htl_rfp.T_HOTEL_HEXAGON_LNG_LAT
        WHERE HOTEL_ID IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateByHotelId" parameterType="com.fangcang.rfp.common.entity.HotelHexagonLngLat" >
        UPDATE
            htl_rfp.T_HOTEL_HEXAGON_LNG_LAT
        SET HEXAGON_LNG_LAT_10 = #{hexagonLngLat10,jdbcType=VARCHAR},
            LNG_BAIDU = #{lngBaidu,jdbcType=DOUBLE},
            LAT_BAIDU = #{latBaidu,jdbcType=DOUBLE},
            MODIFY_TIME = SYSDATE
        WHERE
            HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </update>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelHexagonLngLat" >
        INSERT INTO
            htl_rfp.T_HOTEL_HEXAGON_LNG_LAT
        (
         HOTEL_ID,
         HEXAGON_LNG_LAT_10,
         LNG_BAIDU,
         LAT_BAIDU,
         CREATOR,
         CREATE_TIME
        ) VALUES (
                  #{hotelId,jdbcType=BIGINT},
                  #{hexagonLngLat10,jdbcType=VARCHAR},
                  #{lngBaidu,jdbcType=DOUBLE},
                  #{latBaidu,jdbcType=DOUBLE},
                  #{creator,jdbcType=VARCHAR},
                  SYSDATE)
    </insert>

</mapper>