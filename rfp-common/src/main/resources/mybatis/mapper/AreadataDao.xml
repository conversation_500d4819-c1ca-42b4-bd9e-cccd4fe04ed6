<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.AreadataDao">

    <select id="queryListByName" resultType="com.fangcang.rfp.common.dto.AreadataDto">
        SELECT
            DATATYPE AS dataType,
            DATANAME AS dataName,
            DATACODE AS dataCode
        FROM htl_base.T_AREADATA
        WHERE DATATYPE = #{dataType}
          AND DATANAME LIKE CONCAT(CONCAT('%', #{dataName}),'%')
        ORDER BY dataName
    </select>

    <select id="queryByCode" resultType="com.fangcang.rfp.common.dto.AreadataDto">
        SELECT
            DATATYPE AS dataType,
            DATANAME AS dataName,
            DATACODE AS dataCode
        FROM htl_base.T_AREADATA
        WHERE DATATYPE = #{dataType}
          AND DATACODE  = #{dataCode}
        ORDER BY dataName
    </select>

    <select id="queryByCodes" resultType="com.fangcang.rfp.common.dto.AreadataDto">
        SELECT
            DATATYPE AS dataType,
            DATANAME AS dataName,
            DATACODE AS dataCode
        FROM htl_base.T_AREADATA
        WHERE DATATYPE = #{dataType}
          AND DATACODE  IN
        <foreach collection="dataCodes" separator="," open="(" close=")" item="dataCode">
            #{dataCode}
        </foreach>

    </select>

</mapper>