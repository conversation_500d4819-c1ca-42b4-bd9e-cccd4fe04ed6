<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectCustomBidStrategyOptionDao">

    <insert id="batchMergeProjectCustomBidStrategyOption" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            MERGE INTO htl_rfp.T_CUSTOM_BID_STRATEGY_OPTION t
            USING dual
            ON (t.OPTION_ID = #{item.optionId} AND
            t.custom_tend_strategy_id = #{item.customTendStrategyId} AND
            t.PROJECT_INTENT_HOTEL_ID = #{item.projectIntentHotelId} AND
            t.project_id = #{item.projectId} AND
            t.hotel_id = #{item.hotelId})
            WHEN MATCHED THEN
            UPDATE SET
            t.OPTION_NAME = #{item.optionName,jdbcType=VARCHAR},
            t.IS_SUPPORT = #{item.isSupport,jdbcType=INTEGER},
            t.modifier = #{item.modifier,jdbcType=VARCHAR},
            t.modify_time = sysdate
            WHEN NOT MATCHED THEN
            INSERT (OPTION_ID, CUSTOM_TEND_STRATEGY_ID, PROJECT_INTENT_HOTEL_ID,
            PROJECT_ID, hotel_id, OPTION_NAME, IS_SUPPORT, CREATOR,CREATE_TIME, modifier, modify_time)
            VALUES (#{item.optionId}, #{item.customTendStrategyId}, #{item.projectIntentHotelId}, #{item.projectId},
            #{item.hotelId},#{item.optionName}, #{item.isSupport,jdbcType=INTEGER},
            #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
        </foreach>
    </insert>

    <select id="queryProjectCustomBidStrategyOptions" resultType="com.fangcang.rfp.common.entity.ProjectCustomBidStrategyOption">
        SELECT
            t.OPTION_ID AS optionId,
            t.CUSTOM_TEND_STRATEGY_ID AS customTendStrategyId,
            t.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            t.PROJECT_ID AS projectId,
            t.hotel_id AS hotelId,
            t.OPTION_NAME AS optionName,
            t.IS_SUPPORT AS isSupport,
            t.CREATE_TIME AS creator,
            t.CREATE_TIME AS createTime,
            t.modifier AS modifier,
            t.modify_time AS modifyTime
        FROM
            htl_rfp.T_CUSTOM_BID_STRATEGY_OPTION t
        LEFT JOIN
            htl_rfp.T_PROJECT_C_STRATEGY_OPTION t1
        ON t.CUSTOM_TEND_STRATEGY_ID = t1.CUSTOM_TEND_STRATEGY_ID AND t.OPTION_ID = t1.OPTION_ID
        WHERE
            t.PROJECT_ID = #{projectId}
        AND
            t.hotel_id = #{hotelId}
        ORDER BY t1.DISPLAY_ORDER ASC
    </select>

    <delete id="batchDelete">
        DELETE FROM htl_rfp.T_CUSTOM_BID_STRATEGY_OPTION
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
        AND CUSTOM_TEND_STRATEGY_ID IN
        <foreach collection="list" item="strategyId" open="(" close=")" separator=",">
            #{strategyId}
        </foreach>
    </delete>
</mapper>