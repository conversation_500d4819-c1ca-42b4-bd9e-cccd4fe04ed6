<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PriceMonitorConfigDao">

  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PriceMonitorConfig">
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="PARTNER_CODE" jdbcType="VARCHAR" property="partnerCode" />
    <result column="IS_MONITOR_AGREEMENT" jdbcType="INTEGER" property="isMonitorAgreement" />
    <result column="STATE" jdbcType="INTEGER" property="state" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Coumn_List">
    ORG_ID,PARTNER_CODE,STATE,IS_MONITOR_AGREEMENT,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.PriceMonitorConfig">
    insert into T_PRICE_MONITOR_CONFIG (ORG_ID, PARTNER_CODE, STATE, IS_MONITOR_AGREEMENT,
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME)
    values (#{orgId,jdbcType=BIGINT}, #{partnerCode,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{isMonitorAgreement,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
            SYSDATE)
  </insert>

  <delete id="deletePriceMonitorConfig" parameterType="long">
    delete from htl_rfp.T_PRICE_MONITOR_CONFIG where ORG_ID = #{orgId}
  </delete>

  <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
    select <include refid="Base_Coumn_List"/> from htl_rfp.T_PRICE_MONITOR_CONFIG where ORG_ID = #{orgId}
  </select>

  <update id="updatePriceMonitorConfigState" parameterType="com.fangcang.rfp.common.dto.request.PriceMonitorConfigDto">
    update htl_rfp.T_PRICE_MONITOR_CONFIG
    set
      STATE = #{state},
      MODIFIER = #{modifier},
      MODIFY_TIME = SYSDATE
    where ORG_ID = #{orgId}
  </update>

  <update id="updatePriceMonitorAgreement" parameterType="com.fangcang.rfp.common.dto.request.PriceMonitorAgreementConfigDto">
    update htl_rfp.T_PRICE_MONITOR_CONFIG
    set
      IS_MONITOR_AGREEMENT = #{isMonitorAgreement},
      MODIFIER = #{modifier},
      MODIFY_TIME = SYSDATE
    where ORG_ID = #{orgId}
  </update>

  <select id="selectPriceMonitorConfigList" parameterType="com.fangcang.rfp.common.dto.request.PriceMonitorConfigRequest" resultType="com.fangcang.rfp.common.dto.request.PriceMonitorConfigResponse">
    select mc.ORG_ID orgId,
           o.ORG_NAME orgName,
           mc.PARTNER_CODE partnerCode,
           mc.STATE state,
           mc.IS_MONITOR_AGREEMENT isMonitorAgreement
    from htl_rfp.T_PRICE_MONITOR_CONFIG mc
    left join htl_rfp.T_ORG o on mc.ORG_ID = o.ORG_ID
    where 1 = 1
    <if test="orgName != null and orgName != '' ">
        and o.ORG_NAME like concat(concat('%',#{orgName,jdbcType=VARCHAR}),'%')
    </if>
  </select>
  <select id="selectPriceMonitorConfigByProject"
          resultType="com.fangcang.rfp.common.entity.PriceMonitorConfig" >
    select c.ORG_ID as orgId,
    c.PARTNER_CODE as partnerCode,
    c.STATE as state,
    c.IS_MONITOR_AGREEMENT isMonitorAgreement,
    c.CREATOR as creator,
    c.CREATE_TIME as createTime,
    c.MODIFIER as modifier,
    c.MODIFY_TIME as modifyTime
    from htl_rfp.T_PRICE_MONITOR_CONFIG c,
    htl_rfp.t_project p where c.ORG_ID = p.tender_org_id
    and p.project_id = #{projectId}
  </select>

</mapper>