<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ImportBidTaskDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ImportBidTask">
        <result column="IMPORT_BID_TASK_ID" jdbcType="BIGINT" property="importBidTaskId"/>
        <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="PROP_CODE" jdbcType="VARCHAR" property="propCode"/>
        <result column="AMADEUS_CHAIN_CODE" jdbcType="VARCHAR" property="amadeusChainCode"/>
        <result column="AMADEUS_HOTEL_CODE" jdbcType="VARCHAR" property="amadeusHotelCode"/>
        <result column="PROP_NAME" jdbcType="VARCHAR" property="propName"/>
        <result column="PROP_PHONE" jdbcType="VARCHAR" property="propPhone"/>
        <result column="PROP_ADD" jdbcType="VARCHAR" property="propAdd"/>
        <result column="IS_VALIDATION_ERROR" jdbcType="INTEGER" property="isValidationError"/>
        <result column="VALIDATION_ERROR_MSG" jdbcType="VARCHAR" property="validationErrorMsg"/>
        <result column="GENERATED_BID_STATUS_ID" jdbcType="INTEGER" property="generatedBidStatusId"/>
        <result column="DATA_DETAIL" jdbcType="VARCHAR" property="dataDetail"/>
        <result column="FILE_ID" jdbcType="BIGINT" property="fileId"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="queryWithNoDataDetail" resultType="com.fangcang.rfp.common.entity.ImportBidTask">
        SELECT
            i.IMPORT_BID_TASK_ID AS importBidTaskId,
            i.TASK_ID AS taskId,
            i.PROJECT_ID AS projectId,
            i.HOTEL_ID AS hotelId,
            i.PROP_CODE AS propCode,
            i.AMADEUS_CHAIN_CODE AS amadeusChainCode,
            i.AMADEUS_HOTEL_CODE AS amadeusHotelCode,
            i.PROP_NAME AS propName,
            i.PROP_PHONE AS propPhone,
            i.PROP_ADD AS propAdd,
            i.IS_VALIDATION_ERROR AS isValidationError,
            i.VALIDATION_ERROR_MSG AS validationErrorMsg,
            i.GENERATED_BID_STATUS_ID AS generatedBidStatusId,
            i.FILE_ID AS fileId,
            i.CREATOR AS creator,
            i.CREATE_TIME AS createTime,
            i.MODIFIER AS modifier,
            i.MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_IMPORT_BID_TASK i
        WHERE
          i.IMPORT_BID_TASK_ID  IN
            <foreach collection="importBidTaskIds" item="importBidTaskId" open="(" close=")" separator=",">
                #{importBidTaskId}
            </foreach>
    </select>
    <select id="getWithDataDetail" resultType="com.fangcang.rfp.common.dto.ImportBidTaskDto">
        SELECT
            i.IMPORT_BID_TASK_ID AS importBidTaskId,
            i.TASK_ID AS taskId,
            i.PROJECT_ID AS projectId,
            i.HOTEL_ID AS hotelId,
            i.PROP_CODE AS propCode,
            i.AMADEUS_CHAIN_CODE AS amadeusChainCode,
            i.AMADEUS_HOTEL_CODE AS amadeusHotelCode,
            i.PROP_NAME AS propName,
            i.PROP_PHONE AS propPhone,
            i.PROP_ADD AS propAdd,
            i.IS_VALIDATION_ERROR AS isValidationError,
            i.VALIDATION_ERROR_MSG AS validationErrorMsg,
            i.GENERATED_BID_STATUS_ID AS generatedBidStatusId,
            i.DATA_DETAIL AS dataDetail,
            i.FILE_ID AS fileId,
            i.CREATOR AS creator,
            i.CREATE_TIME AS createTime,
            i.MODIFIER AS modifier,
            i.MODIFY_TIME AS modifyTime,
            h.CITY_NAME AS fcPropCity,
            h.CHN_NAME AS fcPropName,
            h.TELEPHONE AS fcPropPhone,
            h.CHN_ADDRESS AS fcPropAdd,
            i.HOTEL_ID AS fcPropCode,
            a.FILE_ORIGINAL_NAME AS fileName,
            a.FILE_URL AS fileUrl
        FROM htl_rfp.T_IMPORT_BID_TASK i
        LEFT JOIN htl_info.T_HOTEL h ON i.HOTEL_ID = h.HOTELID
        LEFT JOIN htl_rfp.T_ATTACHMENT a ON i.FILE_ID = a.FILE_ID
        WHERE IMPORT_BID_TASK_ID = #{importBidTaskId}
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ImportBidTask">
        <selectKey keyProperty="importBidTaskId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_IMPORT_BID_TASK.nextval from dual
        </selectKey>
        INSERT INTO htl_rfp.T_IMPORT_BID_TASK (IMPORT_BID_TASK_ID, TASK_ID, PROJECT_ID,
        HOTEL_ID, PROP_CODE,AMADEUS_CHAIN_CODE,AMADEUS_HOTEL_CODE, PROP_NAME,
        PROP_PHONE, PROP_ADD,IS_VALIDATION_ERROR,VALIDATION_ERROR_MSG,GENERATED_BID_STATUS_ID,DATA_DETAIL,FILE_ID,
        CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME)
        VALUES (#{importBidTaskId}, #{taskId,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT},
        #{hotelId,jdbcType=BIGINT},  #{propCode,jdbcType=VARCHAR},
        #{amadeusChainCode,jdbcType=VARCHAR}, #{amadeusHotelCode,jdbcType=VARCHAR},
        #{propName,jdbcType=VARCHAR},  #{propPhone,jdbcType=VARCHAR}, #{propAdd,jdbcType=VARCHAR},
        #{isValidationError,jdbcType=INTEGER},#{validationErrorMsg,jdbcType=VARCHAR},
        #{generatedBidStatusId,jdbcType=INTEGER},#{dataDetail,jdbcType=VARCHAR},#{fileId,jdbcType=BIGINT},
        #{creator,jdbcType=VARCHAR},  sysdate, #{modifier,jdbcType=VARCHAR},  sysdate)
    </insert>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE
        FROM htl_rfp.T_IMPORT_BID_TASK
        WHERE IMPORT_BID_TASK_ID = #{importBidTaskId}
    </delete>

    <update id="update" parameterType="com.fangcang.rfp.common.entity.ImportBidTask">
        UPDATE htl_rfp.T_IMPORT_BID_TASK
        SET
            TASK_ID = #{taskId,jdbcType=VARCHAR},
            HOTEL_ID = #{hotelId,jdbcType=BIGINT},
            PROP_CODE = #{propCode,jdbcType=VARCHAR},
            AMADEUS_CHAIN_CODE = #{amadeusChainCode,jdbcType=VARCHAR},
            AMADEUS_HOTEL_CODE = #{amadeusHotelCode,jdbcType=VARCHAR},
            PROP_NAME = #{propName,jdbcType=VARCHAR},
            PROP_PHONE = #{propPhone,jdbcType=VARCHAR},
            PROP_ADD = #{propAdd,jdbcType=VARCHAR},
            IS_VALIDATION_ERROR = #{isValidationError,jdbcType=INTEGER},
            VALIDATION_ERROR_MSG = #{validationErrorMsg,jdbcType=VARCHAR},
            GENERATED_BID_STATUS_ID = #{generatedBidStatusId,jdbcType=INTEGER},
            DATA_DETAIL = #{dataDetail,jdbcType=CLOB},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE
        WHERE
            IMPORT_BID_TASK_ID = #{importBidTaskId,jdbcType=BIGINT}
    </update>

    <update id="updateGeneratedStatus" parameterType="com.fangcang.rfp.common.entity.ImportBidTask">
        UPDATE htl_rfp.T_IMPORT_BID_TASK
        SET
            GENERATED_BID_STATUS_ID = #{generatedBidStatusId,jdbcType=INTEGER},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE
        WHERE
            IMPORT_BID_TASK_ID = #{importBidTaskId,jdbcType=BIGINT}
    </update>

    <select id="queryList" resultType="com.fangcang.rfp.common.dto.ImportBidTaskDto"
            parameterType="com.fangcang.rfp.common.dto.request.ImportBidTaskRequest">
        SELECT
            i.IMPORT_BID_TASK_ID AS importBidTaskId,
            i.TASK_ID AS taskId,
            i.PROJECT_ID AS projectId,
            i.HOTEL_ID AS hotelId,
            i.PROP_CODE AS propCode,
            i.AMADEUS_CHAIN_CODE AS amadeusChainCode,
            i.AMADEUS_HOTEL_CODE AS amadeusHotelCode,
            i.PROP_NAME AS propName,
            i.PROP_PHONE AS propPhone,
            i.PROP_ADD AS propAdd,
            i.IS_VALIDATION_ERROR AS isValidationError,
            i.VALIDATION_ERROR_MSG AS validationErrorMsg,
            i.GENERATED_BID_STATUS_ID AS generatedBidStatusId,
          <if test="isIncludeDataDetail">
              i.DATA_DETAIL AS dataDetail,
          </if>
            i.CREATOR AS creator,
            i.CREATE_TIME AS createTime,
            i.MODIFIER AS modifier,
            i.MODIFY_TIME AS modifyTime,
            h.CITY_NAME AS fcPropCity,
            h.CHN_NAME AS fcPropName,
            h.TELEPHONE AS fcPropPhone,
            h.CHN_ADDRESS AS fcPropAdd,
            i.HOTEL_ID AS fcPropCode,
            p.PROJECT_NAME AS projectName,
            p.TENDER_ORG_ID AS tenderOrgId
        FROM htl_rfp.T_IMPORT_BID_TASK i
        LEFT JOIN htl_info.T_HOTEL h ON i.HOTEL_ID = h.HOTELID
        LEFT JOIN htl_rfp.T_PROJECT p ON i.PROJECT_ID = p.PROJECT_ID
        WHERE
            1=1
        <if test="projectName != null and projectName != ''">
            AND i.PROJECT_ID IN (SELECT PROJECT_ID FROM htl_rfp.T_PROJECT WHERE PROJECT_NAME like CONCAT('%', CONCAT(#{projectName},'%')))
        </if>
        <if test="projectId != null and projectId > 0">
            AND i.PROJECT_ID = #{projectId}
        </if>
        <if test="hotelId != null and hotelId > 0">
            AND i.HOTEL_ID = #{hotelId}
        </if>
        <if test="city != null and city != ''">
            AND h.CITY = #{city}
        </if>
        <if test="propName != null and propName != ''">
            AND i.PROP_NAME LIKE CONCAT('%', CONCAT(#{propName}, '%'))
        </if>
        <if test="createDateFrom != null and createDateFrom != ''">
            AND i.CREATE_TIME >= to_date(#{createDateFrom}, 'yyyy-mm-dd')
        </if>
        <if test="createDateTo != null and createDateTo != ''">
            AND i.CREATE_TIME &lt; to_date(#{createDateTo}, 'yyyy-mm-dd') + 1
        </if>
        <if test="isValidationError != null">
            AND i.IS_VALIDATION_ERROR = #{isValidationError}
        </if>
        <if test="generatedBidStatusId != null">
            AND i.GENERATED_BID_STATUS_ID = #{generatedBidStatusId}
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="importBidTaskIds != null and importBidTaskIds.size() > 0">
            AND i.IMPORT_BID_TASK_ID IN
            <foreach collection="importBidTaskIds" item="importBidTaskId" open="(" close=")" separator=",">
                #{importBidTaskId}
            </foreach>
        </if>
        ORDER BY i.CREATE_TIME DESC
    </select>


</mapper>