<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.AttachmentDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.Attachment">
        <result column="FILE_ID" jdbcType="BIGINT" property="fileId"/>
        <result column="FILE_ORIGINAL_NAME" jdbcType="VARCHAR" property="fileOriginalName"/>
        <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName"/>
        <result column="FILE_FTP_PATH" jdbcType="VARCHAR" property="fileFtpPath"/>
        <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType"/>
        <result column="IS_ACTIVE" jdbcType="INTEGER" property="isActive"/>
        <result column="EXTERNAL_ID" jdbcType="BIGINT" property="externalId"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="FILE_STREAM" jdbcType="BLOB" property="fileStream"/>
        <result column="FILE_SIZE" jdbcType="BIGINT" property="fileSize"/>
    </resultMap>

    <sql id="Base_Column_List">
        FILE_ID, FILE_ORIGINAL_NAME, FILE_NAME, FILE_FTP_PATH, FILE_URL, BUSINESS_TYPE,
        IS_ACTIVE, EXTERNAL_ID, CREATOR, CREATE_DATE, MODIFIER, MODIFY_TIME, FILE_STREAM,FILE_SIZE
    </sql>

    <insert id="addAttachment" parameterType="com.fangcang.rfp.common.entity.Attachment" keyProperty="fileId"
            useGeneratedKeys="true">
        <selectKey keyProperty="fileId" resultType="_long" order="BEFORE">
            select htl_rfp.seq_rfp_attachment_file_id.nextval from dual
        </selectKey>
        insert into htl_rfp.T_ATTACHMENT (FILE_ID, FILE_ORIGINAL_NAME, FILE_NAME,
        FILE_FTP_PATH, FILE_URL, BUSINESS_TYPE,
        IS_ACTIVE, EXTERNAL_ID, CREATOR,
        CREATE_DATE, MODIFIER, MODIFY_TIME, FILE_STREAM,FILE_SIZE
        )
        values (#{fileId}, #{fileOriginalName,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
        #{fileFtpPath,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER},
        1, #{externalId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR},
        sysdate, #{modifier,jdbcType=VARCHAR}, sysdate,#{fileStream,jdbcType=BLOB},#{fileSize}
        )
    </insert>

    <select id="queryAttachment" resultMap="BaseResultMap"
            parameterType="com.fangcang.rfp.common.dto.request.QueryAttachmentInfoParam">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_ATTACHMENT
        where IS_ACTIVE = 1
        <if test="fileId != null">
            and FILE_ID = #{fileId}
        </if>
        <if test="externalId != null and businessType != null">
            and EXTERNAL_ID = #{externalId} and BUSINESS_TYPE = #{businessType}
        </if>
        <if test="businessType != null">
            and BUSINESS_TYPE = #{businessType}
        </if>
    </select>

    <update id="updateAttachmentExternalIdByFileId" parameterType="com.fangcang.rfp.common.entity.Attachment">
        update htl_rfp.T_ATTACHMENT set MODIFY_TIME=sysdate
        <if test="externalId != null">
            ,EXTERNAL_ID = #{externalId}
        </if>
        <if test="isActive != null">
            ,IS_ACTIVE = #{isActive}
        </if>
        <if test="modifier != null">
            ,MODIFIER = #{modifier}
        </if>
        where FILE_ID = #{fileId}
    </update>

    <update id="deleteAttachmentByFileId" parameterType="com.fangcang.rfp.common.entity.Attachment">
        update htl_rfp.T_ATTACHMENT set IS_ACTIVE = 0 ,MODIFY_TIME=sysdate
        <if test="modifier != null">
            ,MODIFIER = #{modifier}
        </if>
        where FILE_ID = #{fileId}
    </update>
</mapper>