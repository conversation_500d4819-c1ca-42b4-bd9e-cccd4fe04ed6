<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelApplyOnlineDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelApplyOnline">
        <result column="HOTEL_ONLINE_ID" jdbcType="DECIMAL" property="hotelOnlineId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="ONLINE_STATE" jdbcType="DECIMAL" property="onlineState"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
     HOTEL_ONLINE_ID, HOTEL_ID, PROJECT_ID, ONLINE_STATE, CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_APPLY_ONLINE
        where HOTEL_ONLINE_ID = #{hotelOnlineId}
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelApplyOnline">
        <selectKey keyProperty="hotelOnlineId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_APPLY_ONLINE.nextval from dual
        </selectKey>
        insert into htl_rfp.T_HOTEL_APPLY_ONLINE (HOTEL_ONLINE_ID, HOTEL_ID, PROJECT_ID,
        ONLINE_STATE, CREATOR, CREATE_TIME,
        MODIFIER, MODIFY_TIME)
        values (#{hotelOnlineId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL},
        #{onlineState,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},sysdate, #{modifier,jdbcType=VARCHAR}, sysdate)
    </insert>

    <update id="updateHotelApplyOnline" parameterType="com.fangcang.rfp.common.dto.request.UpdateHotelApplyOnlineDto">
        update htl_rfp.T_HOTEL_APPLY_ONLINE set ONLINE_STATE=#{updateOnlineState} ,MODIFY_TIME=SYSDATE
        <if test="operator != null and operator !=''">
            ,MODIFIER=#{operator}
        </if>
        WHERE HOTEL_ONLINE_ID=#{hotelOnlineId}
    </update>

    <select id="queryHotelApplyOnline" parameterType="com.fangcang.rfp.common.dto.request.QueryHotelApplyOnlineRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryHotelApplyOnlineResponse">
        select th.hotelid AS hotelId,th.chn_name AS hotelName,
        th.hotel_star AS hotelStar,
        th.chn_address AS hotelAddress,
        th.rating AS rating,
        th.city_name AS cityName,
        ph.BID_CONTACT_NAME AS contactName,
        ph.BID_CONTACT_MOBILE AS contactMobile,
        ph.BID_CONTACT_EMAIL AS contactEmail,
        p.project_name AS projectName,
        ha.online_state AS onlineState,
        ph.distributor_contact_uid AS distributorContactUid,
        ph.distributor_contact_name AS distributorContactName,
        ha.modifier AS modifier,
        ha.modify_time AS modifyTime,
        p.project_id AS projectId,
        ha.hotel_online_id as hotelOnlineId,
        p.IS_AUTO_CONFIG_GO_ONLINE as isAutoConfigGoOnline,
        p.ONLINE_DISTRIBUTOR_CODE as onlineDistributorCode,
        p.ONLINE_INVOICE_SET as onlineInvoiceSet,
        p.ONLINE_SERVICE_FEE_SET as onlineServiceFeeSet,
        p.ONLINE_SERVICE_FEE_TYPE as onlineServiceFeeType,
        p.ONLINE_SERVICE_FEE as onlineServiceFee,
        ph.RECEIVE_ORDER_METHOD as receiveOrderMethod,
        ph.TAX_DIFF_INCREASE_RATE as taxDiffIncreaseRate
        from htl_rfp.T_PROJECT_INTENT_HOTEL ph,
        htl_rfp.t_hotel_apply_online ha,
        htl_info.t_hotel th,
        htl_rfp.t_project p
        where ph.project_id = ha.project_id
        and ph.hotel_id = ha.hotel_id
        and ph.hotel_id = th.hotelid
        and ph.project_id = p.project_id
        and p.project_state != 3
        and th.isactive=1
        and th.country='CN'
        <if test="onlineState != null and onlineState == 0">
          and ha.online_state in (0,5)
        </if>
        <if test="onlineState != null and onlineState == 1">
            and ha.online_state = 1
        </if>
        <if test="onlineState != null and onlineState == 2">
            and ha.online_state in (2,6)
        </if>
        <if test="onlineState != null and onlineState == 3">
            and ha.online_state in 3
        </if>
        <if test="onlineState != null and onlineState == 4">
            and ha.online_state in (4,7)
        </if>
        <if test="orgId != null">
            and p.tender_org_id = #{orgId}
        </if>
        <if test="userIds != null">
            and ph.distributor_contact_uid in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode !=''">
            and th.city = #{cityCode}
        </if>
        <if test="hotelName != null and hotelName !=''">
            and th.chn_name like concat(concat('%',#{hotelName}),'%')
        </if>
        <if test="hotelId != null ">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="distributorContactUid != null ">
            and ph.distributor_contact_uid = #{distributorContactUid}
        </if>
        <if test="distributorContactName != null and distributorContactName !='' ">
            and ph.distributor_contact_name like concat(concat('%',#{distributorContactName}),'%')
        </if>
        <if test="projectName != null and projectName !=''">
            and p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="isOnline != null">
            and p.IS_AUTO_CONFIG_GO_ONLINE = #{isOnline}
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>

        order by ha.create_time desc
    </select>

    <select id="queryHotelApplyOnlineDetail"
            resultType="com.fangcang.rfp.common.dto.response.QueryHotelApplyOnlineDetailResponse">
       select th.hotelid AS hotelId,
        th.chn_name AS hotelName,
        th.hotel_star AS hotelStar,
        ph.BID_CONTACT_NAME AS contactName,
        ph.BID_CONTACT_MOBILE AS contactMobile,
        ph.BID_CONTACT_EMAIL AS contactEmail,
        ph.HOTEL_GROUP_BID_CONTACT_NAME AS hotelGroupContactName,
        ph.HOTEL_GROUP_BID_CONTACT_MOBILE AS hotelGroupContactMobile,
        ph.HOTEL_GROUP_BID_CONTACT_EMAIL AS hotelGroupContactEmail,
        ph.EMPLOYEE_RIGHT AS employeeRight,
        ph.EMPLOYEE_RIGHT_FILE_URL AS employeeRightFileUrl,
        ph.BID_ORG_ID AS bidOrgId,
        ph.BID_ORG_TYPE AS bidOrgType,
        p.project_id as projectId,
        p.project_name AS projectName,
        p.IS_AUTO_CONFIG_GO_ONLINE as isAutoConfigGoOnline,
        p.ONLINE_DISTRIBUTOR_CODE as onlineDistributorCode,
        p.ONLINE_INVOICE_SET as onlineInvoiceSet,
        p.ONLINE_SERVICE_FEE_SET as onlineServiceFeeSet,
        p.ONLINE_SERVICE_FEE_TYPE as onlineServiceFeeType,
        p.ONLINE_SERVICE_FEE as onlineServiceFee,
        ph.RECEIVE_ORDER_METHOD as receiveOrderMethod,
        ph.TAX_DIFF_INCREASE_RATE as taxDiffIncreaseRate,
        ph.distributor_contact_name as distributorContactName,
        u.mobile as distributorContactMobile,
        ph.bid_weight as bidWeight,
        b.brandname as brandName,
        th.country as country,
        th.city_name as cityName,
        bc.dataname as provinceName,
        th.chn_address as hotelAddress,
        th.pracice_date as praciceDate,
        th.fitment_date as fitmentDate,
        th.rating as rating,
        th.layer_count as layerCount,
        th.lng_baidu as lngBaiDu,
        th.lat_baidu as latBaiDu,
        ph.hotel_org_id as hotelOrgId,
        ph.hotel_subject_id as hotelSubjectId,
        ph.project_intent_hotel_id as projectIntentHotelId,
        th.city as cityCode
        from  htl_rfp.t_hotel_apply_online ha
        left join htl_rfp.T_PROJECT_INTENT_HOTEL ph on  ha.project_id =ph.project_id and  ha.hotel_id = ph.hotel_id
        left join htl_info.t_hotel th on ph.hotel_id = th.hotelid   and th.isactive=1   and th.country='CN'
        left join htl_rfp.t_project p on ph.project_id = p.project_id and p.project_state != 3
        left join htl_rfp.t_user u on ph.distributor_contact_uid = u.user_id
        left join htl_info.t_brand b on th.hotelbrand = b.brandid
        left join htl_base.t_areadata bc on th.province = bc.datacode and bc.datatype = 2
        where ha.hotel_online_id = #{hotelOnlineId}
    </select>

    <select id="statisticalHotelApplyOnlineCount" parameterType="com.fangcang.rfp.common.dto.request.QueryHotelApplyOnlineRequest"
    resultType="com.fangcang.rfp.common.dto.response.StatisticalHotelApplyOnlineResponse">
        select ha.online_state as onlineState,count(ha.online_state) as count
        from htl_rfp.T_PROJECT_INTENT_HOTEL ph,
        htl_rfp.t_hotel_apply_online ha,
        htl_info.t_hotel th,
        htl_rfp.t_project p
        where ph.project_id = ha.project_id
        and ph.hotel_id = ha.hotel_id
        and ph.hotel_id = th.hotelid
        and ph.project_id = p.project_id
        and p.project_state != 3
        and th.isactive=1
        and th.country='CN'
        <if test="orgId != null">
            and p.tender_org_id = #{orgId}
        </if>
        <if test="userIds != null">
            and ph.distributor_contact_uid in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        group by ha.online_state
    </select>

    <select id="queryByProjectIdAndHotelId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_APPLY_ONLINE
        where project_id = #{projectId} AND hotel_id=#{hotelId}
    </select>
</mapper>