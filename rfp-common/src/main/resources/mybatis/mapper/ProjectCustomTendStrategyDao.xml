<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectCustomTendStrategyDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectCustomTendStrategy">
        <id column="CUSTOM_TEND_STRATEGY_ID" jdbcType="DECIMAL" property="customTendStrategyId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="STRATEGY_NAME" jdbcType="VARCHAR" property="strategyName"/>
        <result column="SUPPORT_STRATEGY_NAME" jdbcType="DECIMAL" property="supportStrategyName"/>
        <result column="STRATEGY_TYPE" jdbcType="INTEGER" property="strategyType"/>
        <result column="WHT_STRATEGY_NAME" jdbcType="DECIMAL" property="whtStrategyName"/>
        <result column="WHT_STRATEGY_NAME_STATE" jdbcType="DECIMAL" property="whtStrategyNameState"/>
        <result column="DISPLAY_ORDER" jdbcType="DECIMAL" property="displayOrder"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    CUSTOM_TEND_STRATEGY_ID, PROJECT_ID, STRATEGY_NAME, STRATEGY_TYPE,SUPPORT_STRATEGY_NAME, WHT_STRATEGY_NAME,WHT_STRATEGY_NAME_STATE,CREATOR,DISPLAY_ORDER,
    CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        where CUSTOM_TEND_STRATEGY_ID = #{customTendStrategyId,jdbcType=DECIMAL}
    </select>

    <select id="selectCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        WHERE PROJECT_ID = #{projectId}
    </select>

    <select id="selectMaxDisplayOrder" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT NVL(MAX(DISPLAY_ORDER),0)
        FROM htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        WHERE PROJECT_ID = #{projectId}
    </select>
    <select id="existProjectCustomTendStrategy"
            resultType="com.fangcang.rfp.common.entity.ProjectCustomTendStrategy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        where PROJECT_ID=#{projectId} and STRATEGY_NAME = #{strategyName}
    </select>
    <select id="queryProjectCustomTendStrategy" resultType="com.fangcang.rfp.common.dto.response.QueryCustomTendStrategyResponse"
    parameterType="com.fangcang.rfp.common.dto.request.QueryCustomTendStrategyRequest">
        select
        CUSTOM_TEND_STRATEGY_ID as customTendStrategyId,
        PROJECT_ID as projectId,
        STRATEGY_NAME as strategyName,
        SUPPORT_STRATEGY_NAME as supportStrategyName,
        STRATEGY_TYPE AS strategyType,
        WHT_STRATEGY_NAME as whtStrategyName,
        WHT_STRATEGY_NAME_STATE as whtStrategyNameState,
        MIN_WEIGHT as minWeight,
        DISPLAY_ORDER as displayOrder,
        CREATOR as creator,
        CREATE_TIME as createTime
        from htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        where PROJECT_ID=#{projectId}
        <if test="strategyName != null and strategyName != ''">
            and STRATEGY_NAME like concat(concat('%',#{strategyName}),'%')
        </if>
        <if test="strategyType != null">
            AND STRATEGY_TYPE = #{strategyType}
        </if>
        order by displayOrder ASC, createTime ASC
    </select>
    <delete id="deleteProjectCustomTendStrategy" >
    delete from htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
    where CUSTOM_TEND_STRATEGY_ID = #{customTendStrategyId} and PROJECT_ID=#{projectId}
    </delete>
    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectCustomTendStrategy">
        <selectKey keyProperty="customTendStrategyId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_CUSTOM_TEND_STRATEGY.nextval from dual
        </selectKey>
        insert into htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY (CUSTOM_TEND_STRATEGY_ID, PROJECT_ID,
        STRATEGY_NAME, SUPPORT_STRATEGY_NAME, STRATEGY_TYPE, WHT_STRATEGY_NAME,WHT_STRATEGY_NAME_STATE,
        DISPLAY_ORDER, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
        )
        values (#{customTendStrategyId}, #{projectId,jdbcType=DECIMAL},
        #{strategyName,jdbcType=VARCHAR},0,  #{strategyType,jdbcType=INTEGER}, 0,1,
        #{displayOrder}, #{creator,jdbcType=VARCHAR},
        sysdate, #{modifier,jdbcType=VARCHAR},sysdate
        )
    </insert>

    <update id="batchUpdateCustomTendStrategy" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            update htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY set
            MODIFY_TIME = sysdate
            <if test="item.supportStrategyName != null">
                ,SUPPORT_STRATEGY_NAME = #{item.supportStrategyName,jdbcType=DECIMAL}
            </if>
            <if test="item.whtStrategyName != null">
                ,WHT_STRATEGY_NAME = #{item.whtStrategyName,jdbcType=DECIMAL}
            </if>
            <if test="item.whtStrategyNameState != null">
                ,WHT_STRATEGY_NAME_STATE = #{item.whtStrategyNameState,jdbcType=DECIMAL}
            </if>
            <if test="item.minWeight != null">
                ,MIN_WEIGHT = #{item.minWeight,jdbcType=DECIMAL}
            </if>
            <if test="item.strategyType != null">
                ,STRATEGY_TYPE = #{item.strategyType,jdbcType=INTEGER}
            </if>
            <if test="item.modifier != null and item.modifier !=''">
                ,MODIFIER = #{item.modifier,jdbcType=VARCHAR}
            </if>
            where CUSTOM_TEND_STRATEGY_ID = #{item.customTendStrategyId,jdbcType=DECIMAL}
            and PROJECT_ID=#{item.projectId}
        </foreach>
    </update>

    <select id="selectTotalWeight" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT
            SUM(WHT_STRATEGY_NAME)
        FROM
            T_PROJECT_CUSTOM_TEND_STRATEGY
        WHERE
            PROJECT_ID= #{projectId,jdbcType=BIGINT}
        AND WHT_STRATEGY_NAME_STATE = 1
    </select>

    <update id="updateDisplayOrder" parameterType="com.fangcang.rfp.common.entity.ProjectCustomTendStrategy">
        UPDATE
            htl_rfp.T_PROJECT_CUSTOM_TEND_STRATEGY
        SET DISPLAY_ORDER = #{displayOrder},
            MODIFY_TIME = sysdate,
            MODIFIER = #{modifier,jdbcType=VARCHAR}
        WHERE
            CUSTOM_TEND_STRATEGY_ID = #{customTendStrategyId}
    </update>
</mapper>