<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelDao">

    <select id="selectHotelInfo" resultType="com.fangcang.rfp.common.dto.response.HotelResponse">
        SELECT
            h.HOTELID AS hotelId,
            h.CHN_NAME AS hotelName,
            h.COUNTRY AS country,
            h.PROVINCE AS province,
            h.CITY AS city,
            h.CITY_NAME AS cityName,
            h.HOTEL_STAR AS hotelStar,
            h.CHN_ADDRESS AS chnAddress,
            h.LNG_BAIDU AS lngBaidu,
            h.LAT_BAIDU AS latBaidu,
            h.LONGITUDE AS longitude,
            h.LATITUDE AS latitude,
            h.RATING AS ratting,
            rh.BRIGHT_SPOT AS brightSpot,
            h.HOTEL_GROUP AS hotelGroup,
            h.HOTELBRAND AS hotelBrand,
            h.LAYER_COUNT AS roomCount,
            h.FITMENT_DATE AS fitmentDate,
            h.PRACICE_DATE AS praciceDate,
            h.TELEPHONE AS telephone,
            h.ISACTIVE as isActive
        FROM htl_info.T_HOTEL h
        LEFT JOIN htl_rfp.T_RECOMMEND_HOTEL rh ON h.HOTELID = rh.HOTEL_ID
        WHERE h.HOTELID = #{hotelId,jdbcType=BIGINT}
    </select>

    <select id="selectHotelInfoByIds" resultType="com.fangcang.rfp.common.dto.response.HotelResponse">
        SELECT
            h.HOTELID AS hotelId,
            h.CHN_NAME AS hotelName,
            h.COUNTRY AS country,
            h.PROVINCE AS province,
            h.CITY AS city,
            h.CITY_NAME AS cityName,
            h.HOTEL_STAR AS hotelStar,
            h.CHN_ADDRESS AS chnAddress,
            h.LNG_BAIDU AS lngBaidu,
            h.LAT_BAIDU AS latBaidu,
            h.LONGITUDE AS longitude,
            h.LATITUDE AS latitude,
            h.RATING AS ratting,
            rh.BRIGHT_SPOT AS brightSpot,
            h.HOTEL_GROUP AS hotelGroup,
            h.HOTELBRAND AS hotelBrand
        FROM htl_info.T_HOTEL h
                 LEFT JOIN htl_rfp.T_RECOMMEND_HOTEL rh ON h.HOTELID = rh.HOTEL_ID
        WHERE HOTELID IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectHotelInfoByCityAndRating" resultType="com.fangcang.rfp.common.dto.response.HotelResponse">
        SELECT
            h.HOTELID AS hotelId,
            h.CHN_NAME AS hotelName,
            h.COUNTRY AS country,
            h.PROVINCE AS province,
            h.CITY AS city,
            h.CITY_NAME AS cityName,
            h.HOTEL_STAR AS hotelStar,
            h.CHN_ADDRESS AS chnAddress,
            h.LNG_BAIDU AS lngBaidu,
            h.LAT_BAIDU AS latBaidu,
            h.LONGITUDE AS longitude,
            h.LATITUDE AS latitude,
            h.RATING AS ratting,
            h.PRACICE_DATE AS praciceDate,
            h.FITMENT_DATE AS fitmentDate
        FROM htl_info.T_HOTEL h
        WHERE h.CITY = #{city}
          <if test="rating != null and rating != ''">
              AND h.RATING IS NOT NULL AND h.RATING >= #{rating}
          </if>
    </select>

    <select id="selectHotelRoomInfoList" resultType="com.fangcang.rfp.common.dto.response.RoomInfoResponse">
        SELECT
            ROOMTYPEID AS roomId,
            ROOMTYPENAME AS roomName
        FROM
            htl_info.T_ROOMTYPE
        WHERE
            HOTELID = #{hotelId,jdbcType=BIGINT}
        AND ISACTIVE = 1
    </select>

    <select id="selectHotelWithGroupBrandInfoList" resultType="com.fangcang.rfp.common.dto.response.HotelWithGroupBrandInfoResponse">
        SELECT
            th.hotelId,
            th.chn_name AS hotelName,--酒店名称
            th.city AS cityCode,
            bc.dataName AS cityName,--城市
            ig.groupId AS groupId,
            ig.brandname as hotelGroupName,-- 酒店集团
            ib.brandId AS brandId,
            ib.brandname as brandName, -- 品牌
            th.province AS province, -- 省份
            pro.dataName AS provinceName -- 省份名称
        FROM
            htl_info.t_hotel th
            left join htl_info.t_brand ig on th.hotel_group = ig.brandid
            left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
            left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
            left join htl_base.t_areadata pro ON th.province = pro.datacode AND pro.datatype = 2
        WHERE
            th.hotelId IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
    </select>
    
    <select id="selectCityDataList" resultType="com.fangcang.rfp.common.dto.AreadataDto">
        SELECT
            DATATYPE AS dataType,
            DATANAME AS dataName,
            DATACODE AS dataCode
        FROM
            htl_base.t_areadata
        WHERE
            DATATYPE = 3
        AND
            DATACODE IN
        <foreach collection="cityCodeList" item="cityCode" open="(" close=")" separator=",">
            #{cityCode}
        </foreach>
    </select>

    <select id="selectProvinceIdByCode" resultType="java.lang.Long" parameterType="java.lang.String">
        SELECT
            ID
        FROM
            HTL_BASE.T_AREADATA
        WHERE DATACODE = #{province}
        AND DATATYPE = 2
    </select>

    <select id="selectHotelRoomInfoByHotelIds" resultType="com.fangcang.rfp.common.dto.response.RoomInfoResponse">
        SELECT
        ROOMTYPEID AS roomId,
        ROOMTYPENAME AS roomName
        FROM
        htl_info.T_ROOMTYPE
        WHERE
        HOTELID IN
        <foreach collection="hotelIds" item="hotelId" separator="," open="(" close=")">
            #{hotelId,jdbcType=BIGINT}
        </foreach>
        AND ISACTIVE = 1
    </select>
</mapper>