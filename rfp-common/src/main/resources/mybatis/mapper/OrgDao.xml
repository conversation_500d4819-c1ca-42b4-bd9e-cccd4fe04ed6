<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.OrgDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.common.OrgDTO" >
    <id column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
    <result column="org_type" property="orgType" jdbcType="INTEGER" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="contact_mobile" property="contactMobile" jdbcType="VARCHAR" />
    <result column="address_detail" property="addressDetail" jdbcType="VARCHAR" />
    <result column="company_profile" property="companyProfile" jdbcType="VARCHAR" />
    <result column="logo_url" property="logoUrl" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="client_org_code" property="clientOrgCode" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="contact_email" property="contactEmail" jdbcType="VARCHAR" />
    <result column="can_provider_co_pay" property="canProviderCoPay" jdbcType="INTEGER" />
    <result column="balance_contact_name" property="balanceContactName" jdbcType="VARCHAR" />
    <result column="balance_contact_mobile" property="balanceContactMobile" jdbcType="VARCHAR" />
    <result column="balance_contact_email" property="balanceContactEmail" jdbcType="VARCHAR" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="PARTNER_CHANNEL_CODE" property="partnerChannelCode" jdbcType="VARCHAR" />
    <result column="CHANNEL_PARTNER_ID" property="channelPartnerId" jdbcType="BIGINT" />
  </resultMap>

  <sql id="Base_Column_List" >
    org_id, org_name, org_type, contact_name, contact_mobile,
    address_detail, company_profile,
    logo_url, state, client_org_code, creator, create_time,
    modifier, modify_time,
    contact_email,can_provider_co_pay,
    balance_contact_name,balance_contact_mobile,balance_contact_email,group_id,PARTNER_CHANNEL_CODE,CHANNEL_PARTNER_ID
  </sql>
  
  <sql id="Org_Column_List" >
       t.org_id,
       t.org_name,
       t.org_type,
       t.contact_name,
       t.contact_mobile,
       t.address_detail,
       t.company_profile,
       t.logo_url,
       t.state,
       t.client_org_code,
       t.creator,
       t.create_time,
       t.modifier,
       t.modify_time
       t.contact_email,
       t.can_provider_co_pay,
       t.balance_contact_name,
       t.balance_contact_mobile,
       t.balance_contact_email,
       t. group_id,
       t.CHANNEL_PARTNER_ID
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="long" >
    select 
    <include refid="Base_Column_List" />
    from t_org
    where org_id = #{orgId}
  </select>

    <select id="selectByName" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from t_org
        where org_name = #{orgName} and state = 1
    </select>

    <select id="selectDisOrgByName" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from t_org
        where org_name = #{orgName} and state = 1 AND ORG_TYPE = 3
    </select>


    <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.Org" >
    <selectKey keyProperty="orgId" resultType="_long" order="BEFORE">
      select htl_rfp.seq_rfp_org_id.nextval from dual
    </selectKey>
    insert into t_org
    <trim prefix="(" suffix=")" suffixOverrides="," >
        org_id,
        org_name,
        org_type,
        contact_name,
        contact_mobile,
        address_detail,
        company_profile,
        logo_url,
        state,
      <if test="clientOrgCode != null" >
        client_org_code,
      </if>
        creator,
        create_time,
        PARTNER_CHANNEL_CODE,
        contact_email,
        can_provider_co_pay,
        balance_contact_name,
        balance_contact_mobile,
        balance_contact_email,
        CHANNEL_PARTNER_ID
        <if test="groupId != null">
           , group_id
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

        #{orgId},
        #{orgName,jdbcType=VARCHAR},
        #{orgType,jdbcType=INTEGER},
        #{contactName,jdbcType=VARCHAR},
        #{contactMobile,jdbcType=VARCHAR},
        #{addressDetail,jdbcType=VARCHAR},
        #{companyProfile,jdbcType=VARCHAR},
        #{logoUrl,jdbcType=VARCHAR},
        #{state,jdbcType=INTEGER},

      <if test="clientOrgCode != null" >
        #{clientOrgCode,jdbcType=VARCHAR},
      </if>
        #{creator,jdbcType=VARCHAR},
        SYSDATE,
        #{partnerChannelCode,jdbcType=VARCHAR},
        #{contactEmail,jdbcType=VARCHAR},
        #{canProviderCoPay,jdbcType=INTEGER},
        #{balanceContactName,jdbcType=VARCHAR},
        #{balanceContactMobile,jdbcType=VARCHAR},
        #{balanceContactEmail,jdbcType=VARCHAR},
        #{channelPartnerId,jdbcType=BIGINT}
        <if test="groupId != null">
            , #{groupId}
        </if>
    </trim>
  </insert>

  <update id="deleteOrgInfoById">
    update htl_rfp.t_org set modifier = #{modifier,jdbcType=VARCHAR},
                             modify_time = SYSDATE,
                             state = #{state}
                             where ORG_ID = #{orgId}
  </update>

  <select id="selectOrgCountByOrgName" resultType="int" parameterType="com.fangcang.rfp.common.entity.Org">
    select count(1)
    from htl_rfp.t_org
    where ORG_NAME = #{orgName} and state = 1
  </select>

  <select id="selectOrgCountByOrgTypeAndHotelId" resultType="int" parameterType="java.lang.Long">
    select count(1)
    from htl_rfp.t_org t1,
         htl_rfp.t_org_related_hotel t2
    where t1.ORG_ID = t2.ORG_ID AND t1.ORG_TYPE = 2 and t2.HOTEL_ID = #{hotelId} and t1.state = 1
  </select>

  <update id="updatePartnerInfo" parameterType="com.fangcang.rfp.common.dto.common.OrgDTO" >
    UPDATE htl_rfp.t_org
    SET PARTNER_CHANNEL_CODE = #{partnerChannelCode,jdbcType=VARCHAR},
        CLIENT_ORG_CODE = #{clientOrgCode,jdbcType=VARCHAR}
    WHERE
        ORG_ID = #{orgId,jdbcType=BIGINT}
  </update>

  <select id="getByPartner" resultMap="BaseResultMap">
      SELECT
          <include refid="Base_Column_List"></include>
      FROM
         htl_rfp.t_org
      WHERE
          PARTNER_CHANNEL_CODE = #{partnerChannelCode, jdbcType=VARCHAR}
      AND CLIENT_ORG_CODE = #{clientOrgCode,jdbcType=VARCHAR}
  </select>

  <update id="updateByOrgId" parameterType="com.fangcang.rfp.common.dto.common.OrgDTO" >
    update htl_rfp.t_org
    <trim prefix="SET" suffixOverrides=",">
      modifier = #{modifier,jdbcType=VARCHAR},
      modify_time = SYSDATE,
        <if test="contactName != null ">
          contact_name = #{contactName,jdbcType=VARCHAR},
        </if>
        <if test="contactMobile != null ">
            contact_mobile = #{contactMobile,jdbcType=VARCHAR},
        </if>
        <if test="contactEmail != null ">
          contact_email = #{contactEmail,jdbcType=VARCHAR},
        </if>
        <if test="balanceContactName != null ">
            balance_contact_name = #{balanceContactName,jdbcType=VARCHAR},
        </if>
        <if test="balanceContactMobile != null ">
            balance_contact_mobile = #{balanceContactMobile,jdbcType=VARCHAR},
        </if>
        <if test="balanceContactEmail != null ">
            balance_contact_email = #{balanceContactEmail,jdbcType=VARCHAR},
        </if>
          CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=BIGINT},
          address_detail = #{addressDetail,jdbcType=VARCHAR},
          company_profile = #{companyProfile,jdbcType=VARCHAR},
          logo_url = #{logoUrl,jdbcType=VARCHAR},
          group_id = #{groupId,jdbcType=BIGINT},
          ORG_NAME = #{orgName},
    </trim>
    where org_id = #{orgId,jdbcType=INTEGER}
  </update>

  <select id="queryOrgInfoByParams"  resultType="com.fangcang.rfp.common.dto.common.OrgDTO"
          parameterType="com.fangcang.rfp.common.dto.request.OrgRequest">
    select t1.org_id orgId,
          t1.org_name orgName,
          t1.org_type orgType,
          t1.contact_name contactName,
          t1.contact_mobile contactMobile,
          t1.CONTACT_EMAIL contactEmail,
          t1.company_profile companyProfile,
          t1.logo_url logoUrl,
          t1.can_provider_co_pay canProviderCoPay,
          t1.state state,
          t1.balance_contact_name balanceContactName,
          t1.balance_contact_mobile balanceContactMobile,
          t1.balance_contact_email balanceContactEmail,
          t1.address_detail addressDetail,
          t2.SUBJECT_NAME defaultSubjectName,
          t2.AUTHORIZE_STATE  authorizeState,
          t2.CERT_STATE certState,
          t1.group_id groupId,
          t1.CHANNEL_PARTNER_ID channelPartnerId,
          cp.PARTNER_NAME AS channelPartnerName
    from htl_rfp.t_org t1
    left join htl_rfp.t_org_subject t2 on t1.ORG_ID = t2.ORG_ID and t2.state = 1 and t2.is_default = 1
    left join htl_rfp.T_CHANNEL_PARTNER cp ON t1.CHANNEL_PARTNER_ID = cp.CHANNEL_PARTNER_ID
    where  t1.org_type != 1
    <if test="orgName != null and orgName != '' ">
      and t1.org_name like CONCAT(CONCAT('%',#{orgName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="subjectName != null and subjectName != '' ">
      and t2.SUBJECT_NAME like  CONCAT(CONCAT('%',#{subjectName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="orgType != null and orgType != '' ">
      and t1.org_type = #{orgType}
    </if>
    <if test="authorizeState != null and authorizeState == 1 ">
      and t2.AUTHORIZE_STATE = 1
    </if>
    <if test="authorizeState != null and authorizeState == 0 ">
      and (t2.AUTHORIZE_STATE = 0 OR t2.AUTHORIZE_STATE IS NULL)
    </if>
    <if test="certState != null and certState == 1">
      and t2.CERT_STATE = 1
    </if>
   <if test="certState != null and certState == 0">
      and (t2.CERT_STATE = 0 OR t2.CERT_STATE IS NULL)
   </if>
   <if test="hotelName != null and hotelName != '' ">
          AND t1.org_id IN (SELECT t11.org_id FROM htl_rfp.t_org_related_hotel t11, htl_info.t_hotel t12 WHERE t11.hotel_id = t12.hotelid and  t12.CHN_NAME like CONCAT(CONCAT('%',#{hotelName,jdbcType=VARCHAR}),'%'))
    </if>
  <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
      AND t1.org_id IN
      <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
          #{userRelatedOrgId}
      </foreach>
  </if>
      order by t1.create_time desc ,t1.org_id desc
  </select>

  <select id="queryOrgListByOrgName" parameterType="com.fangcang.rfp.common.dto.request.OrgRequest"
          resultType="com.fangcang.rfp.common.entity.Org">
    select orgId,orgName,orgType from (
      select org_id orgId,
           org_name orgName,
           org_type orgType
    from htl_rfp.t_org
    where 1=1 and ORG_TYPE != 1 and state = 1
    <if test="orgId != null ">
     and org_id = #{orgId}
    </if>
      <if test="channelPartnerId != null and channelPartnerId > 0">
          AND CHANNEL_PARTNER_ID = #{channelPartnerId}
      </if>
      <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
          AND org_id IN
          <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
              #{userRelatedOrgId}
          </foreach>
      </if>
    <if test="orgName != null  and orgName != ''">
        and org_name like CONCAT(CONCAT('%',#{orgName}),'%')
        order by UTL_MATCH.edit_distance_similarity(LTRIM(rTRIM(#{orgName}))  ,org_name) desc
    </if>
      )temp
      where rownum &lt;= 10

  </select>



    <select id="queryPlatAndDisOrgListByOrgName" parameterType="com.fangcang.rfp.common.dto.request.OrgRequest"
            resultType="com.fangcang.rfp.common.entity.Org">
        select orgId,orgName,channelPartnerId from (select org_id orgId,
        org_name orgName,CHANNEL_PARTNER_ID  channelPartnerId
        from htl_rfp.t_org
        where 1=1 and ORG_TYPE in (1,3) and state = 1
        <if test="orgName != null  ">
            and org_name like CONCAT(CONCAT('%',#{orgName}),'%')
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND org_id IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="channelPartnerId != null and channelPartnerId > 0">
            AND CHANNEL_PARTNER_ID = #{channelPartnerId}
        </if>
        )temp
        where rownum &lt;= 10

    </select>

    <select id="queryDisOrgListByOrgName" parameterType="com.fangcang.rfp.common.dto.request.OrgRequest"
            resultType="com.fangcang.rfp.common.entity.Org">
        select orgId,orgName, channelPartnerId from (select org_id orgId,
        org_name orgName,CHANNEL_PARTNER_ID  channelPartnerId
        from htl_rfp.t_org
        where 1=1 and ORG_TYPE = 3 and state = 1
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND org_id IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="orgName != null  ">
            and org_name like CONCAT(CONCAT('%',#{orgName}),'%')
        </if>
        <if test="channelPartnerId != null and channelPartnerId > 0">
          AND CHANNEL_PARTNER_ID = #{channelPartnerId}
        </if>
        )temp
        where rownum &lt;= 10
    </select>

    <select id="queryHotelGroupOrgListByOrgName" parameterType="com.fangcang.rfp.common.dto.request.OrgRequest"
            resultType="com.fangcang.rfp.common.entity.Org">
        select orgId,orgName, channelPartnerId from (select org_id orgId,
        org_name orgName,CHANNEL_PARTNER_ID  channelPartnerId
        from htl_rfp.t_org
        where 1=1 and ORG_TYPE = 4 and state = 1
        <if test="orgName != null  ">
            and org_name like CONCAT(CONCAT('%',#{orgName}),'%')
        </if>
        )temp
        where rownum &lt;= 10
    </select>

    <select id="queryInfoByOrgName" parameterType="com.fangcang.rfp.common.dto.request.OrgRequest"
            resultType="com.fangcang.rfp.common.entity.Org">
        select orgId,orgName,orgType, channelPartnerId from (select org_id orgId,
            org_name orgName,
            ORG_TYPE orgType,
            CHANNEL_PARTNER_ID  channelPartnerId
        from htl_rfp.t_org
        where 1=1  and state = 1
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND org_id IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="channelPartnerId != null and channelPartnerId > 0">
            AND CHANNEL_PARTNER_ID = #{channelPartnerId}
        </if>
        <if test="orgName != null and orgName != '' ">
        and org_name like CONCAT(CONCAT('%',#{orgName}),'%')
        order by UTL_MATCH.edit_distance_similarity(LTRIM(rTRIM(#{orgName}))  ,org_name) desc
        </if>
        ) temp  where  rownum &lt;= 10
    </select>

  <sql id="where" >
    <trim prefix="where" prefixOverrides="and |or">
        <if test="state != null">
            and state = #{state,jdbcType=INTEGER}
        </if>
        <if test="orgName != null and orgName !=''">
            and org_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test="clientOrgCode != null and clientOrgCode !=''">  
            and client_org_code = #{clientOrgCode,jdbcType=VARCHAR}
        </if>

    </trim>
  </sql>

  <!-- 查询所有有效的机构id集合 -->
  <select id="selectAllOrgIdForHotelPage" resultType="java.lang.Long" parameterType="java.util.Map">
       select org_id from htl_rfp.t_org
       where state= 1
       <if test="businessType != null">
          <if test="businessType == 1">
          	and is_hotel_supplier = 1
          </if>
       </if>
  </select>

    <!-- 根据酒店ID获取机构信息 -->
    <select id="getOrgByHotelId" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.Org" >
        select
        <include refid="Base_Column_List" />
        from htl_rfp.t_org
        where state = 1 and org_id in (
            SELECT org_id FROM T_ORG_RELATED_HOTEL WHERE hotel_id = #{hotelId,jdbcType=INTEGER}
        )
    </select>

    <!-- 根据酒店集团ID获取机构信息 -->
    <select id="getHotelGroupOrgByGroupId" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.Org" >
        SELECT
        <include refid="Base_Column_List" />
        FROM htl_rfp.t_org
        WHERE GROUP_ID = #{grouplId,jdbcType=INTEGER} AND state = 1 AND ORG_TYPE = 4
    </select>

    <!-- 根据酒店ID获取机构信息 -->
    <select id="getOrgByHotelIds" resultMap="BaseResultMap" parameterType="java.util.List" >
        select
        <include refid="Base_Column_List" />
        from htl_rfp.t_org
        where state = 1 and org_id in (
            SELECT org_id FROM T_ORG_RELATED_HOTEL WHERE hotel_id IN
            <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
                #{hotelId}
            </foreach>
        )
    </select>

    <!-- 统计各类型机构数 -->
    <select id="getOrgCount" resultType="com.fangcang.rfp.common.dto.response.count.OrgCountResponse">
        select o.org_type as orgType, count(1) as orgCount
          from htl_rfp.t_org o
         where o.state = 1
         group by o.org_type
    </select>

    <select id="getOrgByOrgIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_org
        where org_id IN
        <foreach collection="orgIds" open="(" close=")" item="orgId" separator="," index="index">
            #{orgId}
        </foreach>
    </select>

    <select id="checkOrgNameRepeat" parameterType="com.fangcang.rfp.common.dto.common.OrgDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_org
        WHERE state = 1 AND org_name = #{orgName} AND org_id != #{orgId}
    </select>

    <select id="queryChannelPartnerRelatedDistributorOrgList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_org
        WHERE state = 1 AND org_type = 3
        AND CHANNEL_PARTNER_ID = #{channelPartnerId}
    </select>
</mapper>
