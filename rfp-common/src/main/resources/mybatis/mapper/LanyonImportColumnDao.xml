<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.LanyonImportColumnDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.LanyonImportColumn">
        <result column="COLUMN_INDEX" jdbcType="BIGINT" property="columnIndex"/>
        <result column="DATA_TYPE" jdbcType="VARCHAR" property="dataType"/>
        <result column="COLUMN_CODE" jdbcType="VARCHAR" property="columnCode"/>
        <result column="COLUMN_NAME" jdbcType="VARCHAR" property="columnName"/>
        <result column="DISPLAY_ORDER" jdbcType="BIGINT" property="displayOrder"/>
        <result column="DATA_CATEGORY" jdbcType="VARCHAR" property="dataCategory"/>
    </resultMap>

    <sql id="Base_Column_List">
        COLUMN_INDEX, DATA_TYPE, COLUMN_CODE, COLUMN_NAME,DISPLAY_ORDER,DATA_CATEGORY
    </sql>


    <select id="queryAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_LANYON_IMPORT_COLUMN
       ORDER BY COLUMN_INDEX ASC
    </select>

    <select id="queryByDisplayOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_LANYON_IMPORT_COLUMN
        ORDER BY DISPLAY_ORDER ASC
    </select>

</mapper>