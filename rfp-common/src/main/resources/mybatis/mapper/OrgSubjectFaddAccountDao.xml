<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrgSubjectFaddAccountDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.OrgSubjectFaddAccount">
    <id column="SUBJECT_ID" jdbcType="DECIMAL" property="subjectId" />
    <result column="STATE" jdbcType="DECIMAL" property="state" />
    <result column="FADD_ACCOUNT_TYPE" jdbcType="DECIMAL" property="faddAccountType" />
    <result column="FADD_AUTHORIZE_STATE" jdbcType="DECIMAL" property="faddAuthorizeState" />
    <result column="FADD_VERIFY_STATE" jdbcType="DECIMAL" property="faddVerifyState" />
    <result column="FADD_CUSTOMER_ID" jdbcType="VARCHAR" property="faddCustomerId" />
    <result column="FADD_VERIFY_URL" jdbcType="VARCHAR" property="faddVerifyUrl" />
    <result column="FADD_TRANSACTION_NO" jdbcType="VARCHAR" property="faddTransactionNo" />
    <result column="FADD_AUTHORIZE_REMARK" jdbcType="VARCHAR" property="faddAuthorizeRemark" />
    <result column="FADD_VERIFY_REMARK" jdbcType="VARCHAR" property="faddVerifyRemark" />
    <result column="FADD_CERT_STATUS" jdbcType="DECIMAL" property="faddCertStatus" />
    <result column="FADD_CERT_REMARK" jdbcType="VARCHAR" property="faddCertRemark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    SUBJECT_ID, STATE, FADD_ACCOUNT_TYPE, FADD_AUTHORIZE_STATE, FADD_VERIFY_STATE, FADD_CUSTOMER_ID, 
    FADD_VERIFY_URL, FADD_TRANSACTION_NO, FADD_AUTHORIZE_REMARK, FADD_VERIFY_REMARK, 
    FADD_CERT_STATUS, FADD_CERT_REMARK, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT
    where SUBJECT_ID = #{subjectId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT
    where SUBJECT_ID = #{subjectId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.OrgSubjectFaddAccount">
    insert into htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT (SUBJECT_ID, STATE, FADD_ACCOUNT_TYPE,
      FADD_AUTHORIZE_STATE, FADD_VERIFY_STATE, FADD_CUSTOMER_ID, 
      FADD_VERIFY_URL, FADD_TRANSACTION_NO, FADD_AUTHORIZE_REMARK, 
      FADD_VERIFY_REMARK, FADD_CERT_STATUS, FADD_CERT_REMARK, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME)
    values (#{subjectId,jdbcType=DECIMAL}, #{state,jdbcType=DECIMAL}, #{faddAccountType,jdbcType=DECIMAL}, 
      #{faddAuthorizeState,jdbcType=DECIMAL}, #{faddVerifyState,jdbcType=DECIMAL}, #{faddCustomerId,jdbcType=VARCHAR}, 
      #{faddVerifyUrl,jdbcType=VARCHAR}, #{faddTransactionNo,jdbcType=VARCHAR}, #{faddAuthorizeRemark,jdbcType=VARCHAR}, 
      #{faddVerifyRemark,jdbcType=VARCHAR}, #{faddCertStatus,jdbcType=DECIMAL}, #{faddCertRemark,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
      SYSDATE)
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.OrgSubjectFaddAccount">
    update htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT
    <set>
      <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="faddAccountType != null">
        FADD_ACCOUNT_TYPE = #{faddAccountType,jdbcType=DECIMAL},
      </if>
      <if test="faddAuthorizeState != null">
        FADD_AUTHORIZE_STATE = #{faddAuthorizeState,jdbcType=DECIMAL},
      </if>
      <if test="faddVerifyState != null">
        FADD_VERIFY_STATE = #{faddVerifyState,jdbcType=DECIMAL},
      </if>
      <if test="faddCustomerId != null">
        FADD_CUSTOMER_ID = #{faddCustomerId,jdbcType=VARCHAR},
      </if>
      <if test="faddVerifyUrl != null">
        FADD_VERIFY_URL = #{faddVerifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="faddTransactionNo != null">
        FADD_TRANSACTION_NO = #{faddTransactionNo,jdbcType=VARCHAR},
      </if>
      <if test="faddAuthorizeRemark != null">
        FADD_AUTHORIZE_REMARK = #{faddAuthorizeRemark,jdbcType=VARCHAR},
      </if>
      <if test="faddVerifyRemark != null">
        FADD_VERIFY_REMARK = #{faddVerifyRemark,jdbcType=VARCHAR},
      </if>
      <if test="faddCertStatus != null">
        FADD_CERT_STATUS = #{faddCertStatus,jdbcType=DECIMAL},
      </if>
      <if test="faddCertRemark != null">
        FADD_CERT_REMARK = #{faddCertRemark,jdbcType=VARCHAR},
      </if>

      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
        MODIFY_TIME = SYSDATE
    </set>
    where SUBJECT_ID = #{subjectId}
  </update>

  <update id="updateByCustomerId" parameterType="com.fangcang.rfp.common.entity.OrgSubjectFaddAccount">
    update htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT
    <set>
      <if test="faddAuthorizeState != null">
        FADD_AUTHORIZE_STATE = #{faddAuthorizeState,jdbcType=INTEGER},
      </if>
      <if test="faddVerifyState != null">
        FADD_VERIFY_STATE = #{faddVerifyState,jdbcType=INTEGER},
      </if>
      <if test="faddAuthorizeRemark != null">
        FADD_AUTHORIZE_REMARK = #{faddAuthorizeRemark,jdbcType=VARCHAR},
      </if>
      <if test="faddVerifyRemark != null">
        FADD_VERIFY_REMARK = #{faddVerifyRemark,jdbcType=VARCHAR},
      </if>
      <if test="faddCertStatus != null">
        FADD_CERT_STATUS = #{faddCertStatus,jdbcType=INTEGER},
      </if>
      <if test="faddCertRemark != null">
        FADD_CERT_REMARK = #{faddCertRemark,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      MODIFY_TIME = SYSDATE
    </set>
    where  FADD_CUSTOMER_ID = #{faddCustomerId}
  </update>

  <select id="selectSubjectIdByCustomerId" parameterType="string" resultType="long">
    select SUBJECT_ID
    from htl_rfp.T_ORG_SUBJECT_FADD_ACCOUNT
    where FADD_CUSTOMER_ID = #{customerId}
  </select>

</mapper>