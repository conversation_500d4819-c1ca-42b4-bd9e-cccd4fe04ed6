<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectIntentHotelDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId"/>
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="HOTEL_CONTACT_UID" jdbcType="BIGINT" property="hotelContactUid"/>
        <result column="HOTEL_CONTACT_NAME" jdbcType="VARCHAR" property="hotelContactName"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="CONTACT_EMAIL" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="BID_CONTACT_NAME" jdbcType="VARCHAR" property="bidContactName"/>
        <result column="BID_CONTACT_MOBILE" jdbcType="VARCHAR" property="bidContactMobile"/>
        <result column="BID_CONTACT_EMAIL" jdbcType="VARCHAR" property="bidContactEmail"/>
        <result column="HOTEL_GROUP_BID_CONTACT_NAME" jdbcType="VARCHAR" property="hotelGroupBidContactName"/>
        <result column="HOTEL_GROUP_BID_CONTACT_MOBILE" jdbcType="VARCHAR" property="hotelGroupBidContactMobile"/>
        <result column="HOTEL_GROUP_BID_CONTACT_EMAIL" jdbcType="VARCHAR" property="hotelGroupBidContactEmail"/>
        <result column="DISTRIBUTOR_CONTACT_UID" jdbcType="BIGINT" property="distributorContactUid"/>
        <result column="DISTRIBUTOR_CONTACT_NAME" jdbcType="VARCHAR" property="distributorContactName"/>
        <result column="SEND_MAIL_STATUS" jdbcType="INTEGER" property="sendMailStatus"/>
        <result column="BID_STATE" jdbcType="INTEGER" property="bidState"/>
        <result column="LATEST_YEAR_ROOM_NIGHT" jdbcType="BIGINT" property="latestYearRoomNight"/>
        <result column="TENDER_AVG_PRICE" jdbcType="DECIMAL" property="tenderAvgPrice"/>
        <result column="BID_WEIGHT" jdbcType="DECIMAL" property="bidWeight"/>
        <result column="CO_PAYER_SUBJECT_ID" jdbcType="BIGINT" property="coPayerSubjectId"/>
        <result column="DISTRIBUTOR_SUBJECT_ID" jdbcType="BIGINT" property="distributorSubjectId"/>
        <result column="HOTEL_SUBJECT_ID" jdbcType="BIGINT" property="hotelSubjectId"/>
        <result column="HOTEL_BANK" jdbcType="VARCHAR" property="hotelBank"/>
        <result column="HOTEL_ACCOUNT_NAME" jdbcType="VARCHAR" property="hotelAccountName"/>
        <result column="HOTEL_ACCOUNT_NUMBER" jdbcType="VARCHAR" property="hotelAccountNumber"/>
        <result column="DISTRIBUTOR_BANK" jdbcType="VARCHAR" property="distributorBank"/>
        <result column="DISTRIBUTOR_ACCOUNT_NAME" jdbcType="VARCHAR" property="distributorAccountName"/>
        <result column="DISTRIBUTOR_ACCOUNT_NUMBER" jdbcType="VARCHAR" property="distributorAccountNumber"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="INVITE_STATUS" jdbcType="INTEGER" property="inviteStatus"/>
        <result column="HOTEL_ORG_ID" jdbcType="BIGINT" property="hotelOrgId"/>
        <result column="CO_PAYER_BANK" jdbcType="VARCHAR" property="coPayerBank" />
        <result column="CO_PAYER_ORG_ID" jdbcType="BIGINT" property="coPayerOrgId" />
        <result column="CO_PAYER_ACCOUNT_NAME" jdbcType="VARCHAR" property="coPayerAccountName" />
        <result column="CO_PAYER_ACCOUNT_NUMBER" jdbcType="VARCHAR" property="coPayerAccountNumber" />
        <result column="UN_PRICE_REMARK" jdbcType="VARCHAR" property="unPriceRemark" />
        <result column="PLATFORM_CONTACT_UID" jdbcType="BIGINT" property="platformContactUid" />
        <result column="PLATFORM_CONTACT_NAME" jdbcType="VARCHAR" property="platformContactName" />
        <result column="HOTEL_SERVICE_POINTS" jdbcType="DECIMAL" property="hotelServicePoints"/>
        <result column="TOTAL_ROOM_NIGHT" jdbcType="DECIMAL" property="totalRoomNight"/>
        <result column="TOTAL_VIOLATION_COUNT" jdbcType="DECIMAL" property="totalViolationCount"/>
        <result column="LAST_REMINDER_LEVEL" jdbcType="VARCHAR" property="lastReminderLevel"/>
        <result column="VIOLATION_STAT_PROCESS_STATUS" jdbcType="DECIMAL" property="violationStatProcessStatus"/>
        <result column="ONLINE_FOLLOW_NAME" jdbcType="VARCHAR" property="onlineFollowName" />
        <result column="MONITOR_FOLLOW_NAME" jdbcType="VARCHAR" property="monitorFollowName" />
        <result column="HOTEL_PRICE_FOLLOW_NAME" jdbcType="VARCHAR" property="hotelPriceFollowName" />
        <result column="EMPLOYEE_RIGHT" jdbcType="VARCHAR" property="employeeRight" />
        <result column="EMPLOYEE_RIGHT_FILE_URL" jdbcType="VARCHAR" property="employeeRightFileUrl" />
        <result column="IS_UPLOAD" jdbcType="INTEGER" property="isUpload" />
        <result column="BID_UPLOAD_SOURCE" jdbcType="INTEGER" property="bidUploadSource" />
        <result column="LAST_VIOLATION_TIME" jdbcType="TIMESTAMP" property="lastViolationTime" />
        <result column="CERTS_URL" jdbcType="VARCHAR" property="certsUrl" />
        <result column="REJECT_NEGOTIATION_REMARK" jdbcType="VARCHAR" property="rejectNegotiationRemark" />
        <result column="NOTIFY_STATUS" jdbcType="INTEGER" property="notifyStatus" />
        <result column="NOTIFY_OPERATOR" jdbcType="VARCHAR" property="notifyOperator" />
        <result column="NOTIFY_TIME" jdbcType="TIMESTAMP" property="notifyTime" />
        <result column="HOTEL_BANK_ACCOUNT_TYPE" jdbcType="INTEGER" property="hotelBankAccountType" />
        <result column="RECEIVE_ORDER_METHOD" jdbcType="INTEGER" property="receiveOrderMethod" />
        <result column="TAX_DIFF_INCREASE_RATE" jdbcType="DECIMAL" property="taxDiffIncreaseRate" />
        <result column="BID_ORG_ID" jdbcType="DECIMAL" property="bidOrgId" />
        <result column="BID_ORG_TYPE" jdbcType="INTEGER" property="bidOrgType" />
        <result column="REJECT_APPROVE_REMARK" jdbcType="VARCHAR" property="rejectApproveRemark" />
        <result column="HOTEL_GROUP_APPROVE_STATUS" jdbcType="INTEGER" property="hotelGroupApproveStatus" />
        <result column="LAST_INVITE_TIME" jdbcType="TIMESTAMP" property="lastInviteTime"/>
        <result column="SPECIAL_TRADES_CERT_URL" jdbcType="VARCHAR" property="specialTradesCertUrl"/>
        <result column="HYGIENE_LICENSE_CERT_URL" jdbcType="VARCHAR" property="hygieneLicenseCertUrl"/>
        <result column="FIRE_SAFETY_CERT_URL" jdbcType="VARCHAR" property="fireSafetyCertUrl"/>
        <result column="SPECIAL_TRADES_CERT_DATE" jdbcType="TIMESTAMP" property="specialTradesCertDate"/>
        <result column="HYGIENE_LICENSE_CERT_DATE" jdbcType="TIMESTAMP" property="hygieneLicenseCertDate"/>
        <result column="FIRE_SAFETY_CERT_DATE" jdbcType="TIMESTAMP" property="fireSafetyCertDate"/>
    </resultMap>

    <sql id="Base_Column_List">
      PROJECT_INTENT_HOTEL_ID,PROJECT_ID, HOTEL_ID, HOTEL_CONTACT_UID,
      HOTEL_CONTACT_NAME, CONTACT_NAME, CONTACT_MOBILE,
      CONTACT_EMAIL, BID_CONTACT_NAME, BID_CONTACT_MOBILE,
      BID_CONTACT_EMAIL, HOTEL_GROUP_BID_CONTACT_NAME, HOTEL_GROUP_BID_CONTACT_MOBILE,
      HOTEL_GROUP_BID_CONTACT_EMAIL, DISTRIBUTOR_CONTACT_UID,
      DISTRIBUTOR_CONTACT_NAME, SEND_MAIL_STATUS,
      BID_STATE, LATEST_YEAR_ROOM_NIGHT, TENDER_AVG_PRICE,
      BID_WEIGHT, CO_PAYER_SUBJECT_ID, DISTRIBUTOR_SUBJECT_ID,
      HOTEL_SUBJECT_ID, HOTEL_BANK, HOTEL_ACCOUNT_NAME,
      HOTEL_ACCOUNT_NUMBER, DISTRIBUTOR_BANK, DISTRIBUTOR_ACCOUNT_NAME,
      DISTRIBUTOR_ACCOUNT_NUMBER, CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME,INVITE_STATUS,HOTEL_ORG_ID, CO_PAYER_BANK, CO_PAYER_ORG_ID,
      CO_PAYER_ACCOUNT_NAME, CO_PAYER_ACCOUNT_NUMBER,UN_PRICE_REMARK,PLATFORM_CONTACT_UID,PLATFORM_CONTACT_NAME,
      HOTEL_SERVICE_POINTS,TOTAL_ROOM_NIGHT,TOTAL_VIOLATION_COUNT,LAST_REMINDER_LEVEL,VIOLATION_STAT_PROCESS_STATUS,
      ONLINE_FOLLOW_NAME,MONITOR_FOLLOW_NAME,HOTEL_PRICE_FOLLOW_NAME,EMPLOYEE_RIGHT,EMPLOYEE_RIGHT_FILE_URL,IS_UPLOAD,BID_UPLOAD_SOURCE,LAST_VIOLATION_TIME,CERTS_URL,REJECT_NEGOTIATION_REMARK,
      NOTIFY_STATUS,NOTIFY_OPERATOR,NOTIFY_TIME,
      HOTEL_BANK_ACCOUNT_TYPE,RECEIVE_ORDER_METHOD,TAX_DIFF_INCREASE_RATE,BID_ORG_ID,BID_ORG_TYPE,REJECT_APPROVE_REMARK,HOTEL_GROUP_APPROVE_STATUS,LAST_INVITE_TIME,
       SPECIAL_TRADES_CERT_URL,HYGIENE_LICENSE_CERT_URL,FIRE_SAFETY_CERT_URL,
       SPECIAL_TRADES_CERT_DATE, HYGIENE_LICENSE_CERT_DATE, FIRE_SAFETY_CERT_DATE
  </sql>

    <insert id="insertBatchProjectIntentHotel" parameterType="java.util.List" >
        insert into htl_rfp.T_PROJECT_INTENT_HOTEL (PROJECT_INTENT_HOTEL_ID,PROJECT_ID, HOTEL_ID, HOTEL_CONTACT_UID,
        HOTEL_CONTACT_NAME, CONTACT_NAME, CONTACT_MOBILE,
        CONTACT_EMAIL, BID_CONTACT_NAME, BID_CONTACT_MOBILE,
        BID_CONTACT_EMAIL, HOTEL_GROUP_BID_CONTACT_NAME, HOTEL_GROUP_BID_CONTACT_MOBILE,
        HOTEL_GROUP_BID_CONTACT_EMAIL, DISTRIBUTOR_CONTACT_UID,
        DISTRIBUTOR_CONTACT_NAME, SEND_MAIL_STATUS,
        BID_STATE, LATEST_YEAR_ROOM_NIGHT, TENDER_AVG_PRICE,
        BID_WEIGHT, CO_PAYER_SUBJECT_ID, DISTRIBUTOR_SUBJECT_ID,
        HOTEL_SUBJECT_ID, HOTEL_BANK, HOTEL_ACCOUNT_NAME,
        HOTEL_ACCOUNT_NUMBER, DISTRIBUTOR_BANK, DISTRIBUTOR_ACCOUNT_NAME,
        DISTRIBUTOR_ACCOUNT_NUMBER, CREATOR, CREATE_TIME,
        MODIFIER, MODIFY_TIME,INVITE_STATUS,HOTEL_ORG_ID, CO_PAYER_BANK, CO_PAYER_ORG_ID,
        CO_PAYER_ACCOUNT_NAME, CO_PAYER_ACCOUNT_NUMBER,HOTEL_BANK_ACCOUNT_TYPE,RECEIVE_ORDER_METHOD,TAX_DIFF_INCREASE_RATE,BID_ORG_ID,BID_ORG_TYPE,REJECT_APPROVE_REMARK,HOTEL_GROUP_APPROVE_STATUS,LAST_INVITE_TIME)
        select htl_rfp.SEQ_RFP_PROJ_INTENT_HOTEL_ID.nextval as projectIntentHotelId, t.* from(
        <foreach collection="list" item="item" index="index" separator="union all">
            select #{item.projectId} as projectId, #{item.hotelId} as hotelId, #{item.hotelContactUid,jdbcType=DECIMAL}
            as hotelContactUid,#{item.hotelContactName,jdbcType=VARCHAR} as hotelContactName,
            #{item.contactName,jdbcType=VARCHAR} as contactName,#{item.contactMobile,jdbcType=VARCHAR} as contactMobile,
            #{item.contactEmail,jdbcType=VARCHAR} as contactEmail, #{item.bidContactName,jdbcType=VARCHAR} as
            bidContactName, #{item.bidContactMobile,jdbcType=VARCHAR} as bidContactMobile,
            #{item.bidContactEmail,jdbcType=VARCHAR} as bidContactEmail,
            #{item.hotelGroupBidContactName,jdbcType=VARCHAR} as hotelGroupBidContactName,
            #{item.hotelGroupBidContactMobile,jdbcType=VARCHAR} as hotelGroupBidContactMobile,
            #{item.hotelGroupBidContactEmail,jdbcType=VARCHAR} as hotelGroupBidContactEmail,
            #{item.distributorContactUid,jdbcType=DECIMAL}
            as distributorContactUid,
            #{item.distributorContactName,jdbcType=VARCHAR} as distributorContactName,
            #{item.sendMailStatus,jdbcType=INTEGER} as sendMailStatus,
            #{item.bidState,jdbcType=INTEGER} as bidState, #{item.latestYearRoomNight,jdbcType=INTEGER} as
            latestYearRoomNight, #{item.tenderAvgPrice,jdbcType=DECIMAL} as tenderAvgPrice,
            #{item.bidWeight,jdbcType=DECIMAL} as bidWeight, #{item.coPayerSubjectId,jdbcType=DECIMAL} as
            coPayerSubjectId, #{item.distributorSubjectId,jdbcType=DECIMAL} as distributorSubjectId,
            #{item.hotelSubjectId,jdbcType=DECIMAL} as hotelSubjectId, #{item.hotelBank,jdbcType=VARCHAR} as hotelBank,
            #{item.hotelAccountName,jdbcType=VARCHAR} as hotelAccountName,
            #{item.hotelAccountNumber,jdbcType=VARCHAR} as hotelAccountNumber, #{item.distributorBank,jdbcType=VARCHAR}
            as distributorBank, #{item.distributorAccountName,jdbcType=VARCHAR} as distributorAccountName,
            #{item.distributorAccountNumber,jdbcType=VARCHAR} as distributorAccountNumber,
            #{item.creator,jdbcType=VARCHAR} as creator, sysdate as createTime,
            #{item.modifier,jdbcType=VARCHAR} as modifier, sysdate as modifyTime,#{item.inviteStatus,jdbcType=INTEGER}
            as inviteStatus ,#{item.hotelOrgId,jdbcType=BIGINT} as hotelOrgId, #{item.coPayerBank,jdbcType=VARCHAR} as coPayerBank
            , #{item.coPayerOrgId,jdbcType=BIGINT} as coPayerOrgId, #{item.coPayerAccountName,jdbcType=VARCHAR} as coPayerAccountName ,
              #{item.coPayerAccountNumber,jdbcType=VARCHAR} as coPayerAccountNumber,
             #{item.hotelBankAccountType,jdbcType=INTEGER} as hotelBankAccountType,
            #{item.receiveOrderMethod,jdbcType=INTEGER} as receiveOrderMethod,
            #{item.taxDiffIncreaseRate,jdbcType=DECIMAL} as taxDiffIncreaseRate,
            #{item.bidOrgId,jdbcType=DECIMAL} as bidOrgId,
            #{item.bidOrgType,jdbcType=INTEGER} as bidOrgType,
            #{item.rejectApproveRemark,jdbcType=VARCHAR} as rejectApproveRemark,
            #{item.hotelGroupApproveStatus,jdbcType=INTEGER} as hotelGroupApproveStatus,
            #{item.lastInviteTime,jdbcType=TIMESTAMP} as lastInviteTime
              from dual
        </foreach>)t
    </insert>

    <insert id="insertProjectIntentHotel" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        <selectKey keyProperty="projectIntentHotelId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_RFP_PROJ_INTENT_HOTEL_ID.nextval from dual
        </selectKey>
        insert into htl_rfp.T_PROJECT_INTENT_HOTEL (PROJECT_INTENT_HOTEL_ID,PROJECT_ID, HOTEL_ID, HOTEL_CONTACT_UID,
        HOTEL_CONTACT_NAME, CONTACT_NAME, CONTACT_MOBILE,
        CONTACT_EMAIL, BID_CONTACT_NAME, BID_CONTACT_MOBILE,
        BID_CONTACT_EMAIL, HOTEL_GROUP_BID_CONTACT_NAME, HOTEL_GROUP_BID_CONTACT_MOBILE,
        HOTEL_GROUP_BID_CONTACT_EMAIL, DISTRIBUTOR_CONTACT_UID,
        DISTRIBUTOR_CONTACT_NAME, SEND_MAIL_STATUS,
        BID_STATE, LATEST_YEAR_ROOM_NIGHT, TENDER_AVG_PRICE,
        BID_WEIGHT, CO_PAYER_SUBJECT_ID, DISTRIBUTOR_SUBJECT_ID,
        HOTEL_SUBJECT_ID, HOTEL_BANK, HOTEL_ACCOUNT_NAME,
        HOTEL_ACCOUNT_NUMBER, DISTRIBUTOR_BANK, DISTRIBUTOR_ACCOUNT_NAME,
        DISTRIBUTOR_ACCOUNT_NUMBER, CREATOR, CREATE_TIME,
        MODIFIER, MODIFY_TIME,INVITE_STATUS,HOTEL_ORG_ID, CO_PAYER_BANK, CO_PAYER_ORG_ID,
        CO_PAYER_ACCOUNT_NAME, CO_PAYER_ACCOUNT_NUMBER, IS_UPLOAD, BID_UPLOAD_SOURCE,
        HOTEL_BANK_ACCOUNT_TYPE,RECEIVE_ORDER_METHOD,TAX_DIFF_INCREASE_RATE,BID_ORG_Id,BID_ORG_TYPE,
        REJECT_APPROVE_REMARK,HOTEL_GROUP_APPROVE_STATUS,LAST_INVITE_TIME,
        SPECIAL_TRADES_CERT_URL,HYGIENE_LICENSE_CERT_URL,FIRE_SAFETY_CERT_URL)
        values (
        #{projectIntentHotelId},#{projectId} , #{hotelId} , #{hotelContactUid,jdbcType=DECIMAL} ,#{hotelContactName,jdbcType=VARCHAR} ,
        #{contactName,jdbcType=VARCHAR} ,#{contactMobile,jdbcType=VARCHAR} ,
        #{contactEmail,jdbcType=VARCHAR} , #{bidContactName,jdbcType=VARCHAR} , #{bidContactMobile,jdbcType=VARCHAR} ,
        #{bidContactEmail,jdbcType=VARCHAR} , #{hotelGroupBidContactName,jdbcType=VARCHAR} , #{hotelGroupBidContactMobile,jdbcType=VARCHAR} ,
        #{hotelGroupBidContactEmail,jdbcType=VARCHAR} ,
        #{distributorContactUid,jdbcType=DECIMAL} ,
        #{distributorContactName,jdbcType=VARCHAR} ,
        #{sendMailStatus,jdbcType=INTEGER} ,
        #{bidState,jdbcType=INTEGER} , #{latestYearRoomNight,jdbcType=INTEGER} , #{tenderAvgPrice,jdbcType=DECIMAL} ,
        #{bidWeight,jdbcType=DECIMAL} , #{coPayerSubjectId,jdbcType=DECIMAL} ,
        #{distributorSubjectId,jdbcType=DECIMAL} ,
        #{hotelSubjectId,jdbcType=DECIMAL} , #{hotelBank,jdbcType=VARCHAR},
        #{hotelAccountName,jdbcType=VARCHAR} ,
        #{hotelAccountNumber,jdbcType=VARCHAR} , #{distributorBank,jdbcType=VARCHAR} , #{distributorAccountName,jdbcType=VARCHAR} ,
        #{distributorAccountNumber,jdbcType=VARCHAR} , #{creator,jdbcType=VARCHAR}, sysdate ,
        #{modifier,jdbcType=VARCHAR} , sysdate ,#{inviteStatus,jdbcType=INTEGER},#{hotelOrgId,jdbcType=BIGINT}, #{coPayerBank,jdbcType=VARCHAR}
        , #{coPayerOrgId,jdbcType=BIGINT} , #{coPayerAccountName,jdbcType=VARCHAR} ,  #{coPayerAccountNumber,jdbcType=VARCHAR},
        #{isUpload, jdbcType=INTEGER}, #{bidUploadSource, jdbcType=INTEGER},
        #{hotelBankAccountType, jdbcType=INTEGER},
        #{receiveOrderMethod, jdbcType=INTEGER},
        #{taxDiffIncreaseRate, jdbcType=DECIMAL},
        #{bidOrgId, jdbcType=DECIMAL},
        #{bidOrgType, jdbcType=INTEGER},
        #{rejectApproveRemark, jdbcType=VARCHAR},
        #{hotelGroupApproveStatus, jdbcType=INTEGER},
        #{lastInviteTime, jdbcType=TIMESTAMP},
        #{specialTradesCertUrl, jdbcType=VARCHAR},
        #{hygieneLicenseCertUrl, jdbcType=VARCHAR},
        #{fireSafetyCertUrl, jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectByProjectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectByProjectIdAndBidStates"  resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT_INTENT_HOTEL
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
          <if test="bidStates != null and bidStates.size() > 0">
              AND BID_STATE IN
                <foreach collection="bidStates" item="bidState" separator="," open="(" close=")">
                    #{bidState,jdbcType=INTEGER}
                </foreach>
          </if>

    </select>

    <select id="selectHotelTentOtherInfo" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.ProjectHotelTentResponse">
        select
                h.HOTEL_CONTACT_NAME hotelContactName,
                min(BASE_PRICE) basePrice,
                hbs.SUPPORT_INCLUDE_COMMISSION supportIncludeCommission,
                hbs.TEND_COMMISSION tendCommission,
                hbs.PROVIDE_INVOICE_TYPE provideInvoiceType,
                hbs.PROVIDE_INVOICE_TAX_RATE provideInvoiceTaxRate
        from htl_rfp.T_PROJECT_INTENT_HOTEL h
        left join htl_rfp.T_PROJECT_HOTEL_PRICE hp on h.PROJECT_INTENT_HOTEL_ID = hp.PROJECT_INTENT_HOTEL_ID
        left join htl_rfp.T_PROJECT_HOTEL_BID_STRATEGY hbs on h.PROJECT_INTENT_HOTEL_ID = hbs.PROJECT_INTENT_HOTEL_ID
        where
            h.PROJECT_ID = #{projectId}
            <if test="hotelId != null">
                and h.HOTEL_ID = #{hotelId}
            </if>
          <if test="loopFilterHotelId != null">
              and h.HOTEL_ID = #{loopFilterHotelId}
          </if>
            and (h.hotel_org_id = #{supplyOrgId} or h.hotel_org_id is null)
        group by h.project_intent_hotel_id,
                h.HOTEL_CONTACT_NAME ,
                hbs.SUPPORT_INCLUDE_COMMISSION ,
                hbs.TEND_COMMISSION ,
                hbs.PROVIDE_INVOICE_TYPE ,
                hbs.PROVIDE_INVOICE_TAX_RATE
    </select>

    <select id="selectHotelTentList" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.ProjectHotelTentResponse">
        select p.project_id projectId,
        p.project_name projectName,
        p.project_type projectType,
        p.tender_type tenderType,
        o.ORG_NAME orgName,
        p.TENDER_COUNT tenderCount,
        p.DIFF_MIN_AMOUNT diffMinAmount,
        p.DIFF_MAX_AMOUNT diffMaxAmount,
        p.PROJECT_STATE projectState,
        p.ENROLL_START_TIME enrollStartTime,
        p.FIRST_BID_END_TIME firstBidEndTime,
        p.SECOND_BID_END_TIME secondBidEndTime,
        p.THIRD_BID_END_TIME thirdBidEndTime,
        p.NEED_ONLINE_CONTRACTS needOnlineContracts,
        P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
        p.PRICE_MONITOR_END_DATE as priceMonitorEndDate
        from HTL_RFP.T_PROJECT p
        left join HTL_RFP.T_ORG o on p.TENDER_ORG_ID = o.ORG_ID
        where p.CREATE_TIME >= #{lastYearTime} and p.PROJECT_STATE not in(0,3)
        and p.project_type = 1 and(
        (
        p.TENDER_TYPE = 1
        <if test="bidState != null and bidState == 0">
            and p.project_id not in (
            select project_id from htl_rfp.T_PROJECT_INTENT_HOTEL h where h.hotel_id = #{hotelId} and
            h.BID_STATE != #{bidState}
            and (h.hotel_org_id = #{supplyOrgId} or h.hotel_org_id is null)
            <if test="userId != null ">
                and p.project_id in (
                select project_id from htl_rfp.T_PROJECT_INTENT_HOTEL h where h.HOTEL_CONTACT_UID =
                #{userId}
                )
            </if>
            )
        </if>
        <if test="bidState != null and bidState != 0">
            and exists
            (select 1
            from htl_rfp.T_PROJECT_INTENT_HOTEL h
            where h.project_id = p.project_id
            and h.hotel_id = #{hotelId}
            and h.BID_STATE  = #{bidState}
            and (h.hotel_org_id = #{supplyOrgId} or h.hotel_org_id is null)
            <if test="userId != null ">
                and h.HOTEL_CONTACT_UID = #{userId}
            </if>)
        </if>
        )
        or
        ( p.TENDER_TYPE = 2
        and exists
        (select 1
        from htl_rfp.T_PROJECT_INTENT_HOTEL h
        where h.project_id = p.project_id
        and h.hotel_id = #{hotelId}
        and h.BID_STATE = #{bidState}
        and (h.hotel_org_id = #{supplyOrgId} or h.hotel_org_id is null)
        <if test="userId != null ">
            and h.HOTEL_CONTACT_UID = #{userId}
        </if>)
        ))
        <if test="projectName != null and projectName != '' ">
            and p.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="projectState != null">
            and p.PROJECT_STATE = #{projectState}
        </if>
        <if test="orgName != null and orgName != '' ">
            and  exists ( select ORG_ID from T_ORG o where p.TENDER_ORG_ID = o.org_id and  ORG_NAME like CONCAT(CONCAT('%',#{orgName,jdbcType=VARCHAR}),'%')  )
        </if>
        order by p.CREATE_TIME desc
    </select>

    <select id="selectProjectIntentHotelList" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest"
            resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT_INTENT_HOTEL
        WHERE HOTEL_ID IN (
             SELECT HOTEL_ID FROM HTL_RFP.T_ORG_RELATED_HOTEL WHERE ORG_ID = #{supplyOrgId}
        )
        AND (hotel_org_id = #{supplyOrgId} or hotel_org_id is null)
        <if test="bidState != null  and bidState > 0 ">
            AND BID_STATE = #{bidState}
        </if>
        <if test="bidState !=0 and userId != null ">
            and HOTEL_CONTACT_UID = #{userId}
        </if>
    </select>

    <select id="selectYesterdayProjectIntentHotel" resultType="com.fangcang.rfp.common.dto.response.YesterdayProjectHotelResponse">
        SELECT
            t1.PROJECT_ID AS projectId,
            t2.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            t2.HOTEL_ID AS hotelId,
            t3.DISTRIBUTOR_CODE AS distributorCode,
            t1.PRICE_MONITOR_START_DATE AS priceMonitorStartDate,
            t1.PRICE_MONITOR_END_DATE AS priceMonitorEndDate
        FROM htl_rfp.T_PROJECT t1
                 INNER JOIN htl_rfp.T_PROJECT_INTENT_HOTEL t2 ON t1.PROJECT_ID = t2.PROJECT_ID
                 INNER JOIN htl_rfp.T_ORDER_MONITOR_CONFIG t3 ON t1.TENDER_ORG_ID = t3.ORG_ID
        WHERE t1.PRICE_MONITOR_START_DATE &lt;=  to_date(to_char(sysdate-1,'yyyy-mm-dd'),'yyyy-mm-dd')
          AND t1.PRICE_MONITOR_END_DATE &gt;=  to_date(to_char(sysdate-1,'yyyy-mm-dd'),'yyyy-mm-dd')
          AND t2.BID_STATE = 3
          AND t3.STATE = 1
    </select>

    <update id="updateTotalRoomNight" >
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET TOTAL_ROOM_NIGHT = #{totalRoomNight,jdbcType=INTEGER},
            MODIFY_TIME = SYSDATE,
            MODIFIER = 'SYSTEM'
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="addViolationsCount">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET TOTAL_VIOLATION_COUNT = TOTAL_VIOLATION_COUNT + #{addCount,jdbcType=INTEGER},
            MODIFY_TIME = SYSDATE,
            MODIFIER = 'SYSTEM'
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateLastReminderLevel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET LAST_REMINDER_LEVEL = #{lastReminderLevel,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE,
            MODIFIER = 'SYSTEM'
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateViolationStatProcessStatusAndMsg">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET VIOLATION_STAT_PROCESS_STATUS = #{violationStatProcessStatus,jdbcType=INTEGER},
            VIOLATION_STAT_PROCESS_MSG = #{violationStatProcessStatusMsg,jdbcType=VARCHAR},
            STAT_PROCESS_ORG_TYPE = #{statProcessOrgType,jdbcType=INTEGER},
            MODIFY_TIME = SYSDATE,
            MODIFIER = 'SYSTEM'
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <select id="selectHotelOrgTentList" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.ProjectHotelTentResponse">
    SELECT
        projectId,
        projectName,
        projectType,
        tenderType,
        tenderOrgId,
        orgName,
        hotelId,
        hotelName,
        cityName,
        tenderCount,
        diffMinAmount,
        diffMaxAmount,
        projectState,
        enrollStartTime,
        firstBidEndTime,
        secondBidEndTime,
        thirdBidEndTime,
        needOnlineContracts,
        priceMonitorStartDate,
        priceMonitorEndDate,
        createTime,
        displayOrder
        FROM (
            SELECT
                p.project_id projectId,
                p.project_name projectName,
                p.project_type projectType,
                p.tender_type tenderType,
                p.tender_org_id tenderOrgId,
                tog.ORG_NAME orgName,
                o.HOTEL_ID hotelId,
                o.CHN_NAME hotelName,
                o.cityName cityName,
                p.TENDER_COUNT tenderCount,
                p.DIFF_MIN_AMOUNT diffMinAmount,
                p.DIFF_MAX_AMOUNT diffMaxAmount,
                p.PROJECT_STATE projectState,
                p.ENROLL_START_TIME enrollStartTime,
                p.FIRST_BID_END_TIME firstBidEndTime,
                p.SECOND_BID_END_TIME secondBidEndTime,
                p.THIRD_BID_END_TIME thirdBidEndTime,
                p.NEED_ONLINE_CONTRACTS needOnlineContracts,
                P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
                p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
                p.CREATE_TIME createTime,
                p.DISPLAY_ORDER displayOrder
            FROM HTL_RFP.T_PROJECT p LEFT JOIN HTL_RFP.T_ORG tog ON p.TENDER_ORG_ID = tog.ORG_ID,
            (
                SELECT
                t1.ORG_ID,
                t1.ORG_NAME,
                t2.HOTEL_ID,
                t3.CHN_NAME,
                t4.dataname AS cityName
                FROM HTL_RFP.T_ORG t1
                INNER JOIN HTL_RFP.T_ORG_RELATED_HOTEL t2 ON t1.ORG_ID = t2.ORG_ID
                LEFT JOIN HTL_INFO.T_HOTEL t3 ON t2.HOTEL_ID = t3.HOTELID
                LEFT JOIN htl_base.t_areadata t4 ON t3.city = t4.datacode AND t4.datatype = 3 AND t4.countrycode = 'CN'
                WHERE t1.ORG_ID = #{supplyOrgId}
                <if test="hotelId != null and hotelId > 0">
                   AND t2.hotel_id = #{hotelId}
                </if>
            ) o
            where p.CREATE_TIME >= #{lastYearTime} and p.PROJECT_STATE not in(0,3)
            and p.project_type = 1 AND p.TENDER_TYPE = 1
            <if test="projectName != null and projectName != '' ">
                and p.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')
            </if>
            <if test="projectState != null">
                and p.PROJECT_STATE = #{projectState}
            </if>
        )
        UNION
        (
        SELECT
            p.project_id projectId,
            p.project_name projectName,
            p.project_type projectType,
            p.tender_type tenderType,
            p.tender_org_id tenderOrgId,
            tog.ORG_NAME orgName,
            o.HOTEL_ID hotelId,
            o.CHN_NAME hotelName,
            o.cityName cityName,
            p.TENDER_COUNT tenderCount,
            p.DIFF_MIN_AMOUNT diffMinAmount,
            p.DIFF_MAX_AMOUNT diffMaxAmount,
            p.PROJECT_STATE projectState,
            p.ENROLL_START_TIME enrollStartTime,
            p.FIRST_BID_END_TIME firstBidEndTime,
            p.SECOND_BID_END_TIME secondBidEndTime,
            p.THIRD_BID_END_TIME thirdBidEndTime,
            p.NEED_ONLINE_CONTRACTS needOnlineContracts,
            P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
            p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
            p.CREATE_TIME createTime,
            p.DISPLAY_ORDER displayOrder
        FROM HTL_RFP.T_PROJECT p
        INNER JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON p.PROJECT_ID = pih.PROJECT_ID
        INNER JOIN
        (
            SELECT
            t1.ORG_ID,
            t1.ORG_NAME,
            t2.HOTEL_ID,
            t3.CHN_NAME,
            t4.dataname AS cityName
            FROM HTL_RFP.T_ORG t1
            INNER JOIN HTL_RFP.T_ORG_RELATED_HOTEL t2 ON t1.ORG_ID = t2.ORG_ID
            LEFT JOIN HTL_INFO.T_HOTEL t3 ON t2.HOTEL_ID = t3.HOTELID
            LEFT JOIN htl_base.t_areadata t4 ON t3.city = t4.datacode AND t4.datatype = 3 AND t4.countrycode = 'CN'
            WHERE t1.ORG_ID = #{supplyOrgId}
        ) o ON pih.HOTEL_ID = o.HOTEL_ID
         LEFT JOIN HTL_RFP.T_ORG tog ON p.TENDER_ORG_ID = tog.ORG_ID
            where p.CREATE_TIME >= #{lastYearTime} and p.PROJECT_STATE not in(0,3)
            and p.project_type = 1 AND p.TENDER_TYPE = 2
        <if test="bidState != null and bidState > -1">
           AND pih.BID_STATE = #{bidState,jdbcType=INTEGER}
        </if>
        <if test="hotelId != null and hotelId > 0">
           AND pih.hotel_id = #{hotelId}
        </if>
        and (pih.hotel_org_id = #{supplyOrgId} or pih.hotel_org_id is null)
        <if test="userId != null ">
            and pih.HOTEL_CONTACT_UID = #{userId}
        </if>
        <if test="projectName != null and projectName != '' ">
            and p.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="projectState != null">
            and p.PROJECT_STATE = #{projectState}
        </if>
        )
        order by createTime desc
    </select>

    <update id="appointHotelConcatInfo" parameterType="com.fangcang.rfp.common.dto.request.AppointHotelContactDto">
            update HTL_RFP.T_PROJECT_INTENT_HOTEL
            set HOTEL_CONTACT_UID = #{hotelContactUid},
                HOTEL_CONTACT_NAME = #{hotelContactName}
            where PROJECT_ID = #{projectId} and
                  HOTEL_ID = #{hotelId} and (hotel_org_id = #{supplyOrgId} or hotel_org_id is null)
    </update>

    <select id="selectInfoByProjectIdAndHotelId" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.ConfirmAgreeProtocolResponse">
        select pih.PROJECT_INTENT_HOTEL_ID projectIntentHotelId,
               pih.PROJECT_ID projectId,
               pih.HOTEL_ID hotelId,
               pih.HOTEL_CONTACT_NAME hotelContactName,
               pih.BID_STATE bidState,
               pih.BID_CONTACT_NAME bidContactName,
               pih.BID_CONTACT_MOBILE bidContactMobile,
               pih.BID_CONTACT_EMAIL bidContactEmail,
               pih.HOTEL_GROUP_BID_CONTACT_NAME hotelGroupBidContactName,
               pih.HOTEL_GROUP_BID_CONTACT_MOBILE hotelGroupBidContactMobile,
               pih.HOTEL_GROUP_BID_CONTACT_EMAIL hotelGroupBidContactEmail,
               pih.BID_WEIGHT bidWeight,
               pih.REMARK remark,
               pih.EMPLOYEE_RIGHT employeeRight,
               pih.EMPLOYEE_RIGHT_FILE_URL employeeRightFileUrl,
               pih.IS_UPLOAD isUpload,
               pih.BID_UPLOAD_SOURCE bidUploadSource,
               rh.BRIGHT_SPOT brightSpot,
               pih.hotel_org_id as hotelOrgId,
               pih.bid_org_id as bidOrgId,
               pih.BID_ORG_TYPE as bidOrgType,
               pih.RECEIVE_ORDER_METHOD as receiveOrderMethod,
                pih.SPECIAL_TRADES_CERT_URL as specialTradesCertUrl,
                pih.HYGIENE_LICENSE_CERT_URL as hygieneLicenseCertUrl,
                pih.FIRE_SAFETY_CERT_URL as fireSafetyCertUrl,
                pih.SPECIAL_TRADES_CERT_DATE as specialTradesCertDate,
                pih.HYGIENE_LICENSE_CERT_DATE as hygieneLicenseCertDate,
                pih.FIRE_SAFETY_CERT_DATE as fireSafetyCertDate
        from HTL_RFP.T_PROJECT_INTENT_HOTEL pih
        left join HTL_RFP.T_RECOMMEND_HOTEL  rh on pih.HOTEL_ID = rh.HOTEL_ID
        where pih.PROJECT_ID = #{projectId}
          <if test="hotelId != null">
              and pih.HOTEL_ID = #{hotelId}
          </if>
          <if test="loopFilterHotelId != null">
              and pih.HOTEL_ID = #{loopFilterHotelId}
          </if>
        <if test="supplyOrgId != null">
          and (pih.hotel_org_id = #{supplyOrgId} or pih.hotel_org_id is null)
        </if>
    </select>

    <update id="updateBidContactInfo" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        update htl_rfp.T_PROJECT_INTENT_HOTEL set MODIFY_TIME = sysdate
        <if test="bidWeight != null ">
            ,BID_WEIGHT = #{bidWeight}
        </if>
        <if test="bidState != null ">
            ,BID_STATE = #{bidState}
        </if>
        <if test="bidContactName != null and bidContactName != '' ">
            ,BID_CONTACT_NAME = #{bidContactName}
        </if>
        <if test="bidContactMobile != null and bidContactMobile != '' ">
            ,BID_CONTACT_MOBILE = #{bidContactMobile}
        </if>
        <if test="bidContactEmail != null and bidContactEmail != '' ">
            ,BID_CONTACT_EMAIL = #{bidContactEmail}
        </if>
        <if test="hotelGroupBidContactName != null and hotelGroupBidContactName != '' ">
            ,HOTEL_GROUP_BID_CONTACT_NAME = #{hotelGroupBidContactName}
        </if>
        <if test="hotelGroupBidContactMobile != null and hotelGroupBidContactMobile != '' ">
            ,HOTEL_GROUP_BID_CONTACT_MOBILE = #{hotelGroupBidContactMobile}
        </if>
        <if test="hotelGroupBidContactEmail != null and hotelGroupBidContactEmail != '' ">
            ,HOTEL_GROUP_BID_CONTACT_EMAIL = #{hotelGroupBidContactEmail}
        </if>
        <if test="coPayerSubjectId != null and coPayerSubjectId != '' ">
            ,CO_PAYER_SUBJECT_ID = #{coPayerSubjectId}
        </if>
        <if test="distributorSubjectId != null and distributorSubjectId != '' ">
            ,DISTRIBUTOR_SUBJECT_ID = #{distributorSubjectId}
        </if>
        <if test="distributorBank != null and distributorBank != '' ">
            ,DISTRIBUTOR_BANK = #{distributorBank}
        </if>
        <if test="distributorAccountName != null and distributorAccountName != '' ">
            ,DISTRIBUTOR_ACCOUNT_NAME = #{distributorAccountName}
        </if>
        <if test="distributorAccountNumber != null and distributorAccountNumber != '' ">
            ,DISTRIBUTOR_ACCOUNT_NUMBER = #{distributorAccountNumber}
        </if>
        <if test="coPayerBank != null and coPayerBank != '' ">
            ,CO_PAYER_BANK = #{coPayerBank}
        </if>
        <if test="coPayerOrgId != null ">
            ,CO_PAYER_ORG_ID = #{coPayerOrgId}
        </if>
        <if test="coPayerAccountName != null and coPayerAccountName != '' ">
            ,CO_PAYER_ACCOUNT_NAME = #{coPayerAccountName}
        </if>
        <if test="coPayerAccountNumber != null and coPayerAccountNumber != '' ">
            ,CO_PAYER_ACCOUNT_NUMBER = #{coPayerAccountNumber}
        </if>
        <if test="employeeRight != null and employeeRight != '' ">
            ,EMPLOYEE_RIGHT = #{employeeRight}
        </if>
        <if test="employeeRightFileUrl != null and employeeRightFileUrl != '' ">
            ,EMPLOYEE_RIGHT_FILE_URL = #{employeeRightFileUrl}
        </if>
        <if test="hotelOrgId != null and hotelOrgId != '' ">
            ,HOTEL_ORG_ID = #{hotelOrgId}
        </if>
        <if test="modifier != null and modifier != '' ">
            ,MODIFIER = #{modifier}
        </if>
        <if test="taxDiffIncreaseRate != null ">
            ,TAX_DIFF_INCREASE_RATE = #{taxDiffIncreaseRate}
        </if>
        <if test="bidOrgId != null ">
            ,BID_ORG_ID = #{bidOrgId}
        </if>
        <if test="bidOrgType != null ">
            ,BID_ORG_TYPE = #{bidOrgType}
        </if>
        <if test="hotelSubjectId != null ">
            ,HOTEL_SUBJECT_ID = #{hotelSubjectId}
        </if>
        <if test="receiveOrderMethod != null ">
            ,RECEIVE_ORDER_METHOD = #{receiveOrderMethod}
        </if>
        <if test="hotelGroupApproveStatus != null ">
            ,HOTEL_GROUP_APPROVE_STATUS = #{hotelGroupApproveStatus}
        </if>
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>

    <update id="updateContactInfoOnly" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE
            htl_rfp.T_PROJECT_INTENT_HOTEL
        SET MODIFY_TIME = sysdate,
            CONTACT_NAME = #{contactName},
            CONTACT_MOBILE = #{contactMobile},
            CONTACT_EMAIL = #{contactEmail},
            MODIFIER = #{modifier}
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}

    </update>
    <update id="updateBidContactInfoOnly" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE
            htl_rfp.T_PROJECT_INTENT_HOTEL
        SET MODIFY_TIME = sysdate,
            BID_CONTACT_NAME = #{bidContactName},
            BID_CONTACT_MOBILE = #{bidContactMobile},
            BID_CONTACT_EMAIL = #{bidContactEmail},
            MODIFIER = #{modifier}
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>


    <update id="updateHotelAndHotelGroupBidContactInfo" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE
            htl_rfp.T_PROJECT_INTENT_HOTEL
        SET MODIFY_TIME = sysdate,
            BID_CONTACT_NAME = #{bidContactName,jdbcType=VARCHAR},
            BID_CONTACT_MOBILE = #{bidContactMobile,jdbcType=VARCHAR},
            BID_CONTACT_EMAIL = #{bidContactEmail,jdbcType=VARCHAR},
            HOTEL_GROUP_BID_CONTACT_NAME = #{hotelGroupBidContactName,jdbcType=VARCHAR},
            HOTEL_GROUP_BID_CONTACT_MOBILE = #{hotelGroupBidContactMobile,jdbcType=VARCHAR},
            HOTEL_GROUP_BID_CONTACT_EMAIL = #{hotelGroupBidContactEmail,jdbcType=VARCHAR},
            MODIFIER = #{modifier,jdbcType=VARCHAR}
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>


    <update id="updateHotelGroupBidContactInfoOnly" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE
            htl_rfp.T_PROJECT_INTENT_HOTEL
        SET MODIFY_TIME = sysdate,
            HOTEL_GROUP_BID_CONTACT_NAME = #{hotelGroupBidContactName},
            HOTEL_GROUP_BID_CONTACT_MOBILE = #{hotelGroupBidContactMobile},
            HOTEL_GROUP_BID_CONTACT_EMAIL = #{hotelGroupBidContactEmail},
            MODIFIER = #{modifier}
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>

    <update id="updateBidOrgInfo" parameterType="com.fangcang.rfp.common.dto.request.UpdateBidOrgInfoRequest">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL SET MODIFY_TIME = sysdate
        <if test="operator != null">
            ,MODIFIER = #{operator,jdbcType=VARCHAR}
        </if>
        <if test="bidOrgId != null">
            ,BID_ORG_ID = #{bidOrgId,jdbcType=BIGINT}
        </if>
        <if test="bidOrgType != null">
            ,BID_ORG_TYPE = #{bidOrgType,jdbcType=INTEGER}
        </if>
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </update>
    <update id="updateProjectIntentHotel"
            parameterType="com.fangcang.rfp.common.dto.request.UpdateProjectIntentHotelDto">
        update htl_rfp.T_PROJECT_INTENT_HOTEL set MODIFY_TIME = sysdate
        <if test="operator != null">
            ,modifier = #{operator,jdbcType=VARCHAR}
        </if>
        <if test="operateType == 1">
            ,contact_name = #{contactName ,jdbcType=VARCHAR}, contact_mobile
            =#{contactMobile ,jdbcType=VARCHAR},contact_email=#{contactEmail ,jdbcType=VARCHAR}
        </if>
        <if test="operateType == 2">
            ,latest_year_room_night = #{latestYearRoomNight,jdbcType=BIGINT},tender_avg_price =
            #{tenderAvgPrice,jdbcType=DECIMAL}
        </if>
        <if test="operateType == 3">
            ,distributor_contact_uid = #{distributorContactUid,jdbcType=BIGINT},distributor_contact_name =
            #{distributorContactName,jdbcType=VARCHAR}
        </if>
        <if test="operateType == 4">
            ,invite_status = #{inviteStatus,jdbcType=INTEGER}
        </if>
        <if test="operateType == 5">
            ,send_mail_status = #{sendMailStatus,jdbcType=INTEGER}
        </if>
        <if test="operateType == 6">
            ,distributor_subject_id = #{distributorSubjectId,jdbcType=BIGINT}
            ,distributor_bank = #{distributorBank,jdbcType=VARCHAR}
            ,distributor_account_name = #{distributorAccountName,jdbcType=VARCHAR}
            ,distributor_account_number = #{distributorAccountNumber,jdbcType=VARCHAR}
        </if>
        <if test="operateType == 7">
            ,co_payer_subject_id = #{coPayerSubjectId,jdbcType=BIGINT}
            ,co_payer_bank = #{coPayerBank,jdbcType=VARCHAR}
            ,co_payer_org_id = #{coPayerOrgId,jdbcType=BIGINT}
            ,co_payer_account_name = #{coPayerAccountName,jdbcType=VARCHAR}
            ,co_payer_account_number = #{coPayerAccountNumber,jdbcType=VARCHAR}
        </if>
        <if test="operateType == 8">
            ,bid_state = #{bidState,jdbcType=INTEGER}
            <if test="bidState == 2 and remark != '' ">
              ,remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="bidState == 4 and remark != '' ">
                ,remark = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="bidState == 7 and rejectNegotiationRemark != null and rejectNegotiationRemark != '' ">
                ,REJECT_NEGOTIATION_REMARK = #{rejectNegotiationRemark,jdbcType=VARCHAR}
            </if>
            <if test="hotelOrgId != null and hotelOrgId > 0">
                ,HOTEL_ORG_ID = #{hotelOrgId,jdbcType=BIGINT}
            </if>
            <if test="hotelBankAccountType != null and hotelBankAccountType > 0">
                ,HOTEL_BANK_ACCOUNT_TYPE = #{hotelBankAccountType,jdbcType=INTEGER}
            </if>
            <if test="hotelSubjectId != null and hotelSubjectId > 0">
                ,HOTEL_SUBJECT_ID = #{hotelSubjectId,jdbcType=BIGINT}
            </if>
            <if test="hotelBank != null and hotelBank != ''">
                ,HOTEL_BANK = #{hotelBank,jdbcType=VARCHAR}
            </if>
            <if test="hotelAccountName != null and hotelAccountName != ''">
                ,HOTEL_ACCOUNT_NAME = #{hotelAccountName,jdbcType=VARCHAR}
            </if>
            <if test="hotelAccountNumber != null and hotelAccountNumber != ''">
                ,HOTEL_ACCOUNT_NUMBER = #{hotelAccountNumber,jdbcType=VARCHAR}
            </if>
        </if>
        where project_id = #{projectId,jdbcType=BIGINT}
        <if test="hotelIds != null and hotelIds.size() > 0">
        and hotel_id in
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
        </if>
    </update>

    <select id="selectProjectIntentHotel"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectIntentHotelResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectIntentHotelParam">
        select ph.project_intent_hotel_id as projectIntentHotelId,ph.project_id as projectId , ph.hotel_id as
        hotelId,ih.chn_name as hotelName ,ih.hotel_star as hotelStar,bc.dataname as cityName,ih.rating as rating,
        ih.chn_address as hotelAddress , rh.breakfast_num as breakfastNum ,rh.reference_price as referencePrice
        ,rh.bright_spot as brightSpot,
        rh.last_room_available as lastRoomAvailable,rh.required_room_night as requiredRoomNight,ph.contact_name as
        contactName, ph.contact_mobile as contactMobile,
        ph.contact_email as contactEmail,ph.distributor_contact_uid as distributorContactUid,ph.distributor_contact_name
        as distributorContactName,
        ph.ONLINE_FOLLOW_NAME AS onlineFollowName, ph.MONITOR_FOLLOW_NAME AS monitorFollowName, ph.HOTEL_PRICE_FOLLOW_NAME AS hotelPriceFollowName,
        ph.bid_state as bidState,ph.latest_year_room_night as latestYearRoomNight,ph.tender_avg_price as
        tenderAvgPrice,ph.send_mail_status as sendMailStatus,ph.invite_status as inviteStatus ,rh.state as
        recommendHotelState,ih.telephone as telephone
        from htl_rfp.t_project_intent_hotel ph
        left join htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        left join htl_rfp.t_recommend_hotel rh on ph.hotel_id=rh.hotel_id and rh.state = 1
--         left join htl_info.t_image im on ph.hotel_id = im.hotelid and im.isactiive = 1 and im.ismainimg = 1
        left join htl_base.t_areadata bc on ih.city = bc.datacode and bc.datatype = 3 and bc.countrycode = 'CN'
        where ph.project_id = #{projectId} and ph.invite_status = 1
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and ih.city = #{cityCode}
        </if>
        <if test="sendMailStatus != null">
            and ph.send_mail_status = #{sendMailStatus}
        </if>
        <if test="userIds != null">
            and ph.distributor_contact_uid in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="queryByProjectIdAndHotelIds" resultMap="BaseResultMap"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectIntentHotelDetailDto">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.t_project_intent_hotel
        where project_id = #{projectId}
        <if test="hotelIds != null and hotelIds.size() > 0">
            and hotel_id in
            <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
                #{hotelId}
            </foreach>
        </if>
        <if test="projectIntentHotelIds != null and projectIntentHotelIds.size() > 0">
            and PROJECT_INTENT_HOTEL_ID in
            <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
                #{projectIntentHotelId}
            </foreach>
        </if>
        <if test="inviteStatus != null">
            and invite_status = #{inviteStatus}
        </if>
        <if test="bidState != null">
            and bid_state = #{bidState}
        </if>
    </select>

    <select id="queryBidHotelCount" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelCountResponse" parameterType="java.util.List">
        select
        project_id AS projectId ,nvl(count(1),0) AS hotelCount
        from htl_rfp.t_project_intent_hotel
        where project_id in
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
            #{projectId}
        </foreach>
        and bid_state != 0 and bid_state != 5 group by project_id
    </select>

    <select id="queryByProjectIdAndGroupOrgId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.t_project_intent_hotel
        WHERE project_id = #{projectId,jdbcType=BIGINT}
        <if test="hotelGroupOrgId != null">
            AND HOTEL_ORG_ID = #{hotelGroupOrgId,jdbcType=BIGINT}
        </if>
        <if test="bidState != null">
            AND BID_STATE = #{bidState,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryInviteHotelCount" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelCountResponse" parameterType="java.util.List">
        select
        project_id AS projectId,nvl(count(1),0) AS hotelCount
        from htl_rfp.t_project_intent_hotel
        where project_id in
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
            #{projectId}
        </foreach>
        and invite_status = 1 group by project_id
    </select>

    <select id="queryGroupByHotelBidState" resultType="com.fangcang.rfp.common.dto.response.HotelBidStateResponse" parameterType="com.fangcang.rfp.common.dto.request.BidHotelInfoQueryRequest">
        SELECT
            ph.BID_STATE AS hotelBidState,
            COUNT(ph.BID_STATE) AS count
        FROM htl_rfp.t_project_intent_hotel ph
        LEFT JOIN htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        WHERE ph.project_id = #{projectId}
        AND ph.BID_STATE != 0
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and ih.city = #{cityCode}
        </if>
        <if test="hotelGroup != null and hotelGroup != ''">
            and ih.HOTEL_GROUP = #{hotelGroup}
        </if>
        <if test="plateID != null and plateID != ''">
            and ih.hotelbrand = #{plateID}
        </if>
        <if test="orgType == 3">
            <if test="roleCodeType != null and roleCodeType != 'ADMIN'">
                and ph.distributor_contact_uid in
                <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                    #{userId}
                </foreach>
            </if>
        </if>
         GROUP BY ph.BID_STATE
    </select>

    <select id="queryBidHotelInfoStat"
            resultType="com.fangcang.rfp.common.dto.ProjectBidBrandStatInfoVO"
            parameterType="com.fangcang.rfp.common.dto.request.QueryMapBidHotelInfoStatRequest">
        select
            ih.city as cityCode,
            count(ih.city) as cityBidCount,
            ih.hotel_group as hotelGroup,
            count(ih.hotel_group) as hotelGroupBidCount,
            ih.HOTELBRAND as hotelBrand,
            count(ih.HOTELBRAND) as hotelBrandBidCount
        from htl_rfp.t_project_intent_hotel ph
        left join htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        where ph.project_id = #{projectId}
        and (ph.bid_state IN (1,2,3,4,6,7))
        and ih.city is not null
        group by ih.city, ih.hotel_group, ih.HOTELBRAND
    </select>
    <select id="queryBidHotelInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.BidHotelInfoQueryRequest">
        select
            ph.project_intent_hotel_id as projectIntentHotelId,
            ph.project_id as projectId ,
            ph.hotel_id as hotelId,
            ib.brandname as hotelBrandName,
            ih.city as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.chn_name as hotelName ,
            ih.hotel_star as hotelStar,
            bc.dataname as cityName,
            ih.rating as rating,
            ih.chn_address as hotelAddress ,
            ih.pracice_date as praciceDate,
            ih.fitment_date as fitmentDate,
            ih.layer_count as layerCount,
            ih.hotel_group as hotelGroup,
            rh.bright_spot as brightSpot,
            ph.distributor_contact_uid as distributorContactUid,
            ph.distributor_contact_name as distributorContactName,
            ph.bid_weight as bidWeight,
            bs.provide_invoice_type as invoiceType,
            bs.provide_invoice_tax_rate as provideInvoiceTaxRate,
            bs.support_include_commission as supportIncludeCommission,
            bs.tend_commission as tendCommission,
            ph.bid_state as bidState,
            ph.latest_year_room_night as latestYearRoomNight,
            ph.tender_avg_price as tenderAvgPrice,
            ph.BID_ORG_TYPE as bidOrgType,
            ph.BID_ORG_ID as bidOrgId,
            org.ORG_NAME as bidOrgName,
            rh.state as recommendHotelState,
            rc.contract_state as contractState,
            ph.un_price_remark as unPriceRemark,
            ph.ONLINE_FOLLOW_NAME AS onlineFollowName,
            ph.MONITOR_FOLLOW_NAME AS monitorFollowName,
            ph.HOTEL_PRICE_FOLLOW_NAME AS hotelPriceFollowName,
            ph.REJECT_NEGOTIATION_REMARK AS rejectNegotiationRemark,
            ph.NOTIFY_STATUS AS notifyStatus,
            ph.NOTIFY_OPERATOR AS notifyOperator,
            ph.NOTIFY_TIME AS notifyTime
        from htl_rfp.t_project_intent_hotel ph
        left join htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        left join htl_rfp.t_recommend_hotel rh on ph.hotel_id=rh.hotel_id and rh.state = 1
        left join htl_rfp.T_ORG org on ph.BID_ORG_ID = org.ORG_ID
--         left join htl_info.t_image im on ph.hotel_id = im.hotelid and im.isactiive = 1 and im.ismainimg = 1
        left join htl_base.t_areadata bc on ih.city = bc.datacode and bc.datatype = 3 and bc.countrycode = 'CN'
        left join htl_info.t_brand ib on ih.hotelbrand = ib.brandid AND ib.groupid is not null
        left join htl_rfp.t_project_hotel_bid_strategy bs on ph.project_intent_hotel_id = bs.project_intent_hotel_id
        left join htl_rfp.t_rfp_contract rc on ph.project_id = rc.project_id and ph.project_intent_hotel_id = rc.project_bussiness_id and rc.contract_biz_type = 1
        where ph.project_id = #{projectId}
        <if test="notifyStatus != null">
            and ph.NOTIFY_STATUS = #{notifyStatus,jdbcType=INTEGER}
        </if>
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="hotelPriceFollowName != null and hotelPriceFollowName != ''">
            and ph.HOTEL_PRICE_FOLLOW_NAME like CONCAT(CONCAT('%',#{hotelPriceFollowName}),'%')
        </if>
        <if test="cityCode != null and cityCode != ''">
            and ih.city = #{cityCode}
        </if>
        <if test="hotelGroup != null and hotelGroup != ''">
            and ih.HOTEL_GROUP = #{hotelGroup}
        </if>
        <if test="plateID != null and plateID != ''">
            and ih.hotelbrand = #{plateID}
        </if>
        <if test="hotelStars != null">
            and ih.hotel_star in
            <foreach collection="hotelStars" open="(" close=")" item="hotelStar" separator="," index="index">
                #{hotelStar}
            </foreach>
        </if>
        <if test="bidState != null">
            and ph.bid_state = #{bidState}
        </if>
        <if test="invoiceType != null">
            and bs.provide_invoice_type = #{invoiceType}
        </if>
        <if test="contractState != null">
            and rc.contract_state = #{contractState}
        </if>
        <if test="orgType == 3">
            <if test="roleCodeType != null and roleCodeType != 'ADMIN'">
                and ph.distributor_contact_uid in
                <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                    #{userId}
                </foreach>
            </if>
        </if>
        <if test="bidOrgType != null">
            and ph.BID_ORG_TYPE = #{bidOrgType} AND BID_ORG_ID IS NOT NULL
        </if>
    </select>

    <select id="queryRecommendHotelInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidRecommendHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.BidHotelInfoQueryRequest">
        SELECT
            phhd.project_id as projectId,
            phhd.hotel_id as hotelId,
            ih.city as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.chn_name as hotelName,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            phhd.RECOMMEND_LEVEL as recommendLevel,
            rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
            rh.LAST_ROOM_AVAILABLE as lra,
            rh.BREAKFAST_NUM as breakfastNum,
            rh.REFERENCE_PRICE as referencePrice,
            pih.BID_STATE  AS bidState,
            CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL THEN 0 ELSE NVL(pih.INVITE_STATUS,0) END AS isInvited,
            CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA phhd
        LEFT JOIN htl_info.t_hotel ih on phhd.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        LEFT JOIN htl_rfp.t_recommend_hotel rh on phhd.hotel_id=rh.hotel_id and rh.state = 1
        LEFT JOIN htl_info.t_brand ib on ih.hotelbrand = ib.brandid AND ib.groupid is not null
        LEFT JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phhd.project_id = pih.project_id and phhd.hotel_id = pih.hotel_id
        WHERE phhd.project_id = #{projectId}
        AND phhd.RECOMMEND_LEVEL IS NOT NULL
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and phhd.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and phhd.CITY_CODE = #{cityCode}
        </if>
        <if test="hotelGroup != null and hotelGroup != ''">
            and ih.HOTEL_GROUP = #{hotelGroup}
        </if>
        <if test="plateID != null and plateID != ''">
            and ih.hotelbrand = #{plateID}
        </if>
        ORDER BY isBid ASC, isInvited ASC, recommendLevel ASC
    </select>

    <select id="queryHotelGroupRecommendHotelInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidRecommendHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBidMapHotelListRequest">
        SELECT
            phhd.project_id as projectId,
            phhd.hotel_id as hotelId,
            ih.city as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.chn_name as hotelName,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            phhd.RECOMMEND_LEVEL as recommendLevel,
            rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
            rh.LAST_ROOM_AVAILABLE as lra,
            rh.BREAKFAST_NUM as breakfastNum,
            rh.REFERENCE_PRICE as referencePrice,
            pih.BID_STATE as bidState,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL THEN 0 ELSE NVL(pih.INVITE_STATUS,0) END AS isInvited,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA phhd
        LEFT JOIN htl_info.t_hotel ih on phhd.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        LEFT JOIN htl_rfp.t_recommend_hotel rh on phhd.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phhd.project_id = pih.project_id and phhd.hotel_id = pih.hotel_id
        WHERE phhd.project_id = #{projectId}
        <if test="hotelId == null">
            AND phhd.RECOMMEND_LEVEL IS NOT NULL
        </if>
        AND ((pih.BID_ORG_ID = #{orgId} AND pih.BID_ORG_TYPE = 4)
            OR (ih.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND (pih.BID_ORG_TYPE = 2 OR pih.BID_STATE = 0 OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
            )
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and phhd.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and phhd.CITY_CODE = #{cityCode}
        </if>
        ORDER BY isBid ASC, isInvited ASC, recommendLevel ASC NULLS LAST
    </select>

    <select id="queryHotelGroupBidMapInvitedHotelList"
            resultType="com.fangcang.rfp.common.dto.response.BidRecommendHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBidMapHotelListRequest">
        SELECT
            ph.project_id as projectId,
            ph.hotel_id as hotelId,
            ih.city as cityCode,
            ih.lng_baidu as lngBaiDu,
            ih.lat_baidu as latBaiDu,
            ih.chn_name as hotelName,
            ih.hotel_star as hotelStar,
            ih.rating as rating,
            phhd.RECOMMEND_LEVEL as recommendLevel,
            rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
            rh.LAST_ROOM_AVAILABLE as lra,
            rh.BREAKFAST_NUM as breakfastNum,
            rh.REFERENCE_PRICE as referencePrice,
            pih.BID_STATE  AS bidState,
            CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM htl_rfp.T_PROJECT_INVITE_HOTEL ph
        LEFT JOIN  htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA phhd ON ph.hotel_id = phhd.hotel_id and ph.project_id = phhd.project_id
        LEFT JOIN htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        LEFT JOIN htl_rfp.t_recommend_hotel rh on ph.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON ph.project_id = pih.project_id and ph.hotel_id = pih.hotel_id
        WHERE ph.project_id = #{projectId}
        AND ((pih.BID_ORG_ID = #{orgId} AND pih.BID_ORG_TYPE = 4)
            OR (ih.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND (pih.BID_ORG_TYPE = 2 OR pih.BID_STATE = 0 OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
        )
        <if test="hotelId == null">
            <if test="hotelName != null and hotelName != ''">
                and ih.chn_name like concat(concat('%',#{hotelName}),'%')
            </if>
        </if>
        <if test="hotelId != null">
            and phhd.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and phhd.CITY_CODE = #{cityCode}
        </if>
        ORDER BY isBid ASC, recommendLevel ASC NULLS LAST
    </select>


    <select id="queryHotelGroupBidMapInvitedCityStat" resultType="com.fangcang.rfp.common.dto.ProjectBidBrandStatInfoVO"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBidMapHotelListRequest">
        SELECT
            ih.city as cityCode,
            count(ih.city) as cityBidCount
        FROM htl_rfp.T_PROJECT_INVITE_HOTEL ph
        LEFT JOIN  htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA phhd ON ph.hotel_id = phhd.hotel_id and ph.project_id = phhd.project_id
        LEFT JOIN htl_info.t_hotel ih on ph.hotel_id = ih.hotelid and ih.isactive = 1 and ih.country = 'CN'
        LEFT JOIN htl_rfp.t_recommend_hotel rh on ph.hotel_id = rh.hotel_id and rh.state = 1
        LEFT JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON ph.project_id = pih.project_id and ph.hotel_id = pih.hotel_id
        WHERE ph.project_id = #{projectId}
        AND ((pih.BID_ORG_ID = #{orgId} AND pih.BID_ORG_TYPE = 4)
        OR (ih.HOTELBRAND IN
        <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
            #{hotelGroupBrandId}
        </foreach> AND (pih.BID_ORG_TYPE = 2 OR pih.BID_STATE = 0 OR pih.PROJECT_INTENT_HOTEL_ID IS NULL))
        )
       GROUP BY ih.city

    </select>

    <select id="queryNoRecommendHotelInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidRecommendHotelInfoQueryResponse">
        SELECT
        ih.HOTELID as hotelId,
        ih.city as cityCode,
        ih.lng_baidu as lngBaiDu,
        ih.lat_baidu as latBaiDu,
        ih.chn_name as hotelName,
        ih.hotel_star as hotelStar,
        ih.rating as rating,
        NULL as recommendLevel,
        rh.REQUIRED_ROOM_NIGHT as requiredRoomNight,
        rh.LAST_ROOM_AVAILABLE as lra,
        rh.BREAKFAST_NUM as breakfastNum,
        rh.REFERENCE_PRICE as referencePrice,
        pih.BID_STATE  AS bidState,
        CASE WHEN pih.PROJECT_INTENT_HOTEL_ID IS NULL OR pih.BID_STATE = 0 THEN 0 ELSE 1 END AS isBid
        FROM
        htl_info.t_hotel ih
        LEFT JOIN htl_rfp.t_recommend_hotel rh on ih.HOTELID = rh.hotel_id and rh.state = 1
        LEFT JOIN htl_rfp.T_PROJECT_INTENT_HOTEL pih ON pih.project_id = #{projectId} and ih.HOTELID = pih.hotel_id
        WHERE
            ih.HOTELID = #{hotelId} AND ih.isactive = 1 AND ih.country = 'CN'
    </select>

    <select id="queryProjectBidHotelInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryMapPoiBidHotelInfoRequest">
        SELECT
            ph.project_intent_hotel_id AS projectIntentHotelId,
            ph.project_id AS projectId ,
            ph.hotel_id AS hotelId,
            ph.BID_STATE AS bidState,
            ph.HOTEL_SERVICE_POINTS AS hotelServicePoints,
            ih.city AS cityCode,
            ih.lng_baidu AS lngBaiDu,
            ih.lat_baidu AS latBaiDu,
            ih.chn_name AS hotelName,
            ih.hotel_star AS hotelStar,
            ih.rating AS rating,
            ih.chn_address AS hotelAddress ,
            ih.pracice_date AS praciceDate,
            ih.fitment_date AS fitmentDate,
            ih.layer_count AS layerCount,
            ph.BID_WEIGHT AS bidWeight
        FROM htl_rfp.t_project_intent_hotel ph
        LEFT JOIN htl_info.t_hotel ih ON ph.hotel_id = ih.hotelid AND ih.isactive = 1 AND ih.country = 'CN'
        <if test="cityCode != '' and cityCode != null">
            AND ih.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        WHERE ph.project_id = #{projectId,jdbcType=BIGINT}
        AND ph.BID_STATE >= 1
        <if test="cityCode != '' and cityCode != null">
           AND ih.city = #{cityCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryProjectHotelBidInfo"
            resultType="com.fangcang.rfp.common.dto.response.BidHotelInfoQueryResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryMapPoiBidHotelInfoRequest">
        SELECT
        ph.project_intent_hotel_id AS projectIntentHotelId,
        ph.project_id AS projectId ,
        ph.hotel_id AS hotelId,
        ph.BID_STATE AS bidState,
        ph.HOTEL_SERVICE_POINTS AS hotelServicePoints,
        ih.city AS cityCode,
        ih.lng_baidu AS lngBaiDu,
        ih.lat_baidu AS latBaiDu,
        ih.chn_name AS hotelName,
        ih.hotel_star AS hotelStar,
        ih.rating AS rating,
        ih.chn_address AS hotelAddress ,
        ih.pracice_date AS praciceDate,
        ih.fitment_date AS fitmentDate,
        ih.layer_count AS layerCount,
        ph.BID_WEIGHT AS bidWeight
        FROM htl_rfp.t_project_intent_hotel ph
        LEFT JOIN htl_info.t_hotel ih ON ph.hotel_id = ih.hotelid AND ih.isactive = 1 AND ih.country = 'CN'
        AND ih.hotelid = #{hotelId,jdbcType=BIGINT}
        WHERE ph.project_id = #{projectId,jdbcType=BIGINT}
        AND ph.hotel_id = #{hotelId,jdbcType=BIGINT}

    </select>

    <select id="querySubjectInfo" resultType="com.fangcang.rfp.common.dto.response.ProjectSubjectInfoQueryResponse">
        select ph.hotel_id as hotelId, os.subject_id as subjectId, os.subject_name as
        subjectName, o.org_name as orgName
        from htl_rfp.t_project_intent_hotel ph,
        htl_rfp.t_org_subject os,
        htl_rfp.t_org o
        where
        <if test="queryType == 1">
            ph.co_payer_subject_id = os.subject_id
        </if>
        <if test="queryType == 2">
            ph.distributor_subject_id = os.subject_id
        </if>
        and os.org_id = o.org_id
        and ph.project_id = #{projectId}
        and ph.hotel_id in
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from htl_rfp.T_PROJECT_INTENT_HOTEL
    where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
  </delete>

    <select id="selectInBiddingHotel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
        and bid_state in (1,2)
    </select>

    <select id="selectContractNotCompletedCount" resultType="java.lang.Integer">
        select count(1)
        from htl_rfp.T_PROJECT_INTENT_HOTEL ph,htl_rfp.T_RFP_CONTRACT rc
        where ph.project_intent_hotel_id = rc.project_bussiness_id  and  ph.PROJECT_ID = #{projectId,jdbcType=BIGINT}
        and ph.bid_state = 3 and rc.contract_state != 1
    </select>

    <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL
        where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </select>
    <select id="selectByProjectHotelId" parameterType="long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL
        where PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
    </select>


    <select id="selectHotelNameByHotelId" parameterType="long" resultType="string">
        select CHN_NAME
        from htl_info.t_hotel t
        where t.HOTELID = #{hotelId}
    </select>

    <select id="selectProjectIntentHotelByIntentHotelIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL
        where  PROJECT_INTENT_HOTEL_ID in
        <foreach collection="hotelIds" open="(" close=")" item="intentHotelId" separator="," index="index">
            #{intentHotelId}
        </foreach>
    </select>

    <update id="updateInviteStatus">
        update htl_rfp.T_PROJECT_INTENT_HOTEL
        set SEND_MAIL_STATUS = 1,
            LAST_INVITE_TIME = sysdate
        where PROJECT_INTENT_HOTEL_ID in
        <foreach collection="hotelIds" item="intentHotelId" open="(" close=")" separator="," index="index">
            #{intentHotelId}
        </foreach>

    </update>
    <update id="updateHotelGroupApprove" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET
            BID_STATE = #{bidState,jdbcType=INTEGER}
        , HOTEL_GROUP_APPROVE_STATUS = #{hotelGroupApproveStatus,jdbcType=INTEGER}
        , REJECT_APPROVE_REMARK = #{rejectApproveRemark,jdbcType=VARCHAR}
        <if test="modifier != null and modifier != ''">
            , MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        ,MODIFY_TIME = SYSDATE
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>
    <update id="updateProjectIntentHotelBidState" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET BID_STATE = #{bidState,jdbcType=INTEGER}
            <if test="rejectNegotiationRemark != null and rejectNegotiationRemark != ''">
                , REJECT_NEGOTIATION_REMARK = #{rejectNegotiationRemark,jdbcType=VARCHAR}
            </if>
            <if test="modifier != null and modifier != ''">
                , MODIFIER = #{modifier,jdbcType=VARCHAR}
            </if>
          <if test="rejectApproveRemark != null and rejectApproveRemark != ''">
              , REJECT_APPROVE_REMARK = #{rejectApproveRemark,jdbcType=VARCHAR}
          </if>
            ,MODIFY_TIME = SYSDATE
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateNotify" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET
            NOTIFY_STATUS = #{notifyStatus,jdbcType=INTEGER},
            NOTIFY_OPERATOR = #{notifyOperator,jdbcType=VARCHAR},
            NOTIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
        <if test="modifier != null and modifier != ''">
            , MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        ,MODIFY_TIME = SYSDATE
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateToWaitNotify" parameterType="com.fangcang.rfp.common.dto.request.UpdateProjectIntentHotelDto">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET
        NOTIFY_STATUS = 0,
        NOTIFY_OPERATOR = null,
        NOTIFY_TIME = null
        <if test="modifier != null and modifier != ''">
            , MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        ,MODIFY_TIME = SYSDATE
        WHERE project_id = #{projectId,jdbcType=BIGINT}
        AND hotel_id IN
        <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </update>
    <select id="queryProjectBidHotelCount" parameterType="java.util.List" resultType="integer">
        select count(1)
        from htl_rfp.t_project_intent_hotel
        where BID_STATE != 0 and  project_id in
        <foreach collection="projectList" open="(" close=")" item="projectId" separator="," index="index">
            #{projectId}
        </foreach>
    </select>

    <select id="queryInviteHotelUnBidCount" resultType="integer">
        select count(1)
        from htl_rfp.t_project_intent_hotel tpih
        where tpih.INVITE_STATUS = 1
        and not exists (select 1 from htl_rfp.T_PROJECT_HOTEL_BID_STRATEGY phb where tpih.HOTEL_ID = phb.HOTEL_ID)
    </select>

    <select id="selectBidHotelListByIdAndTime" parameterType="long" resultType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        select t.PROJECT_INTENT_HOTEL_ID projectIntentHotelId,
                   t.HOTEL_ID hotelId
        from htl_rfp.t_project_intent_hotel t
        where t.BID_STATE = 3 and t.PROJECT_ID = #{projectId}
          and exists (
                select START_DATE,END_DATE
                from htl_rfp.T_PRICE_APPLICABLE_DAY p
                where p.PROJECT_ID = t.project_id and  p.HOTEL_ID = t.hotel_id and  to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.start_date and p.end_date
            )
    </select>

    <select id="selectHotelInfoByHotelId" resultType="com.fangcang.rfp.common.dto.response.QueryHotelInfoResponse" parameterType="java.lang.Long">
        select th.hotelid AS hotelId,th.chn_name hotelName,th.eng_name hotelEnName,th.chn_address hotelAddress,th.city_name AS cityName,th.hotel_star AS hotelStar,
        th.telephone AS telePhone,th.post_code AS postCode,th.city as cityCode,th.businesszone as businessZoneCode,th.district as districtCode,th.icon_content AS iconContent,
        th.fax,th.lng_gps lngGps,th.lat_gps latGps,th.lng_baidu AS lngBaidu,th.lat_baidu AS latBaidu,th.lng_google AS lngGoogle,th.lat_google AS latGoogle,
        th.country AS countryCode, th.isactive AS isactive, th.pracice_date AS praciceDate,th.fitment_date AS fitmentData,th.hotel_group as hotelGroup,th.hotelbrand as hotelBrand,
        b.brandname as brandName,bc.dataname as provinceName,th.rating as rating,th.layer_count as roomNum
        from htl_info.t_hotel th
        left join htl_info.t_brand b on th.hotelbrand = b.brandid
        left join htl_base.t_areadata bc on th.province = bc.datacode and bc.datatype = 2
        where th.hotelid=#{hotelId} and th.isactive = 1 and th.country = 'CN'
    </select>

    <!-- 批量Merge Into -->
    <insert id="batchMergeProjectIntentHotels" >
    MERGE INTO HTL_RFP.T_PROJECT_INTENT_HOTEL t
    USING (
    <foreach collection="importProjectIntentHotelDtos" item="item" separator="UNION ALL">
        SELECT
        #{item.projectId} AS project_id,
        #{item.hotelId} AS hotel_id,
        #{item.contactName,jdbcType=VARCHAR} AS contact_name,
        #{item.contactMobile,jdbcType=VARCHAR} AS contact_mobile,
        #{item.contactEmail,jdbcType=VARCHAR} AS contact_email,
        #{item.distributorContactUid,jdbcType=DECIMAL} AS distributor_contact_uid,
        #{item.distributorContactName,jdbcType=VARCHAR} AS distributor_contact_name,
        #{item.latestYearRoomNight,jdbcType=INTEGER} AS latest_year_room_night,
        #{item.tenderAvgPrice,jdbcType=DECIMAL} AS tender_avg_price,
        #{item.onlineFollowName,jdbcType=VARCHAR} AS ONLINE_FOLLOW_NAME,
        #{item.monitorFollowName,jdbcType=VARCHAR} AS MONITOR_FOLLOW_NAME,
        #{item.hotelPriceFollowName,jdbcType=VARCHAR} AS HOTEL_PRICE_FOLLOW_NAME,
        #{item.operator,jdbcType=VARCHAR} AS creator,
        #{item.operator,jdbcType=VARCHAR} AS modifier
        FROM dual
    </foreach>
    ) s
    ON (t.project_id = s.project_id AND t.hotel_id = s.hotel_id)
    WHEN MATCHED THEN
    UPDATE SET
    t.contact_name = nvl(s.contact_name,t.contact_name),
    t.contact_mobile = nvl(s.contact_mobile,t.contact_mobile),
    t.contact_email = nvl(s.contact_email,t.contact_email),
    t.distributor_contact_uid = nvl(s.distributor_contact_uid,t.distributor_contact_uid),
    t.distributor_contact_name = nvl(s.distributor_contact_name,t.distributor_contact_name),
    t.invite_status = 1,
    t.latest_year_room_night = nvl(s.latest_year_room_night,t.latest_year_room_night),
    t.tender_avg_price = nvl(s.tender_avg_price,t.tender_avg_price),
    t.HOTEL_PRICE_FOLLOW_NAME = nvl(s.HOTEL_PRICE_FOLLOW_NAME,t.HOTEL_PRICE_FOLLOW_NAME),
    t.ONLINE_FOLLOW_NAME = nvl(s.ONLINE_FOLLOW_NAME,t.ONLINE_FOLLOW_NAME),
    t.MONITOR_FOLLOW_NAME = nvl(s.MONITOR_FOLLOW_NAME,t.MONITOR_FOLLOW_NAME),
    t.modifier = s.modifier,
    t.modify_time = sysdate
    WHEN NOT MATCHED THEN
    INSERT (
    t.project_intent_hotel_id, t.project_id, t.hotel_id,
     t.contact_name, t.contact_mobile, t.contact_email,t.distributor_contact_uid,
    t.distributor_contact_name,t.send_mail_status,t.invite_status, t.bid_state,
    t.latest_year_room_night, t.tender_avg_price,  t.HOTEL_PRICE_FOLLOW_NAME, t.ONLINE_FOLLOW_NAME,t.MONITOR_FOLLOW_NAME, t.creator, t.create_time, t.modifier,
    t.modify_time
    )values(
    htl_rfp.SEQ_RFP_PROJ_INTENT_HOTEL_ID.nextval, s.project_id, s.hotel_id,
    s.contact_name, s.contact_mobile, s.contact_email,
     s.distributor_contact_uid,
    s.distributor_contact_name, 0, 1, 0,
    s.latest_year_room_night, s.tender_avg_price, s.HOTEL_PRICE_FOLLOW_NAME, s.ONLINE_FOLLOW_NAME,s.MONITOR_FOLLOW_NAME, s.creator, sysdate, s.modifier,sysdate)
    </insert>

    <select id="queryHotelGroupBidPriceCount" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelCountResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBidPriceCountRequest">
        select
        ph.project_id AS projectId ,nvl(count(ph.hotel_id),0) AS hotelCount
        from htl_rfp.t_project_intent_hotel ph
        left join htl_info.t_hotel h on ph.hotel_id = h.hotelid
        where ph.project_id in
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
            #{projectId}
        </foreach>
        <if test="queryType == 1">
            and ph.bid_state = 1
        </if>
        <if test="queryType == 2">
            and ph.bid_state = 2
        </if>
        <if test="queryType == 3">
            and ph.bid_state = 3
        </if>
        <if test="queryType > 0 and hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{hotelGroupOrgId} AND ph.BID_ORG_TYPE = 4)
            OR (h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        group by ph.project_id
    </select>

    <update id="updateContactInfo" parameterType="com.fangcang.rfp.common.dto.request.UpdateContactInfoDto">
        update htl_rfp.t_project_intent_hotel set MODIFIER = #{operatorName} , MODIFY_TIME=sysdate
        <if test="updateType == 1">
            ,HOTEL_CONTACT_UID = null ,HOTEL_CONTACT_NAME = null
        </if>
        <if test="updateType == 2">
            ,DISTRIBUTOR_CONTACT_UID = null,DISTRIBUTOR_CONTACT_NAME = null
        </if>
        where
        <if test="updateType == 1">
            HOTEL_CONTACT_UID = #{userId}
        </if>
        <if test="updateType == 2">
            DISTRIBUTOR_CONTACT_UID = #{userId}
        </if>
    </update>

    <update id="updateBidContact" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE HTL_RFP.T_PROJECT_INTENT_HOTEL
        SET BID_CONTACT_NAME = #{bidContactName,jdbcType=VARCHAR},
            BID_CONTACT_MOBILE = #{bidContactMobile,jdbcType=VARCHAR},
            BID_CONTACT_EMAIL = #{bidContactEmail,jdbcType=VARCHAR},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateBidHotelSubjectInfo" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE HTL_RFP.T_PROJECT_INTENT_HOTEL
        SET HOTEL_ORG_ID = #{hotelOrgId,jdbcType=BIGINT},
            HOTEL_SUBJECT_ID = #{hotelSubjectId,jdbcType=BIGINT},
            HOTEL_BANK = #{hotelBank,jdbcType=VARCHAR},
            HOTEL_ACCOUNT_NAME = #{hotelAccountName,jdbcType=VARCHAR},
            HOTEL_ACCOUNT_NUMBER = #{hotelAccountNumber,jdbcType=VARCHAR},
            HOTEL_BANK_ACCOUNT_TYPE = #{hotelBankAccountType,jdbcType=INTEGER},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <select id="queryUnpricedHotelManagement"
            resultType="com.fangcang.rfp.common.dto.response.QueryUnpricedManagementResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryUnpricedManagementRequest">
        select ph.project_intent_hotel_id as projectIntentHotelId,
        p.project_name as projectName,
        o.org_name as tenderOrgName,
        p.tender_type as tenderType,
        h.city_name as cityName,
        h.chn_name as hotelName,
        h.hotel_star as hotelStar,
        h.telephone as hotelPhone,
        h.chn_address as hotelAddress,
        ph.contact_name as contactName,
        ph.contact_mobile as contactMobile,
        ph.contact_email as contactEmail,
        ph.bid_state as bidState,
        ph.un_price_remark as unPriceRemark,
        ph.invite_status as inviteStatus,
        ph.create_time as createTime,
        ph.project_id as projectId,
        ph.hotel_id as hotelId,
        nvl2((select min(rog.org_id) from htl_rfp.t_org rog inner join t_org_related_hotel orh on rog.org_id = orh.org_id where orh.hotel_id = ph.hotel_id and rog.state = 1),1,0) as registrationStatus,
        ph.platform_contact_name as platformContactName
        from htl_rfp.t_project p,
        htl_rfp.t_project_intent_hotel ph,
        htl_rfp.t_org o,
        htl_info.t_hotel h
        where p.project_id = ph.project_id
        and p.tender_org_id = o.org_id
        and ph.hotel_id = h.hotelid
        and ph.bid_state = 0
        and p.PROJECT_STATE = 1
        <if test="projectName != null and projectName !=''">
            and p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="queryOrgId != null">
            and p.tender_org_id = #{queryOrgId}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and h.city = #{cityCode}
        </if>
        <if test="hotelName != null and hotelName !='' and hotelId == null">
            and h.chn_name like CONCAT(CONCAT('%',#{hotelName}),'%')
        </if>
        <if test="hotelId != null">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="inviteStatus != null and inviteStatus==0">
            and ph.invite_status = 0
        </if>
        <if test="inviteStatus != null and inviteStatus==1">
            and ph.invite_status = 1
        </if>
        <if test="registrationStatus != null and registrationStatus==0">
            and not exists (select 1
            from htl_rfp.t_org og inner join htl_rfp.t_org_related_hotel orh on og.org_id = orh.org_id
            where orh.hotel_id = ph.hotel_id
            and og.state = 1)
        </if>
        <if test="registrationStatus != null and registrationStatus==1">
            and exists (select 1
            from htl_rfp.t_org og inner join htl_rfp.t_org_related_hotel orh on og.org_id = orh.org_id
            where orh.hotel_id = ph.hotel_id
            and og.state = 1)
        </if>
        <if test="queryplatformContactName != null and queryplatformContactName !=''">
            and ph.platform_contact_name like CONCAT(CONCAT('%',#{queryplatformContactName}),'%')
        </if>
        <if test="bidDateFrom != null and bidDateFrom != ''">
            AND ph.create_time >= TO_DATE(#{bidDateFrom}, 'yyyy-MM-dd')
        </if>
        <if test="bidDateTo != null and bidDateTo != ''">
            AND ph.create_time &lt; TO_DATE(#{bidDateTo}, 'yyyy-MM-dd')
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        order by ph.project_intent_hotel_id desc
    </select>

    <update id="processingBidState" parameterType="com.fangcang.rfp.common.dto.request.ProcessingBidStateRequest">
        update htl_rfp.T_PROJECT_INTENT_HOTEL set MODIFY_TIME = sysdate
        <if test="operator != null">
            ,modifier = #{operator,jdbcType=VARCHAR}
        </if>
        <if test="platformContactName != null and platformContactName !=''">
            ,PLATFORM_CONTACT_NAME = #{operator,jdbcType=VARCHAR}
        </if>
        <if test="operatorId != null">
            ,PLATFORM_CONTACT_UID = #{operatorId}
        </if>
        <if test="bidState != null and bidState== 5">
            ,bid_state = #{bidState}
        </if>
        <if test="unPriceRemark !=null and unPriceRemark !=''">
            ,un_price_remark = #{unPriceRemark}
        </if>
        where
        project_intent_hotel_id in
        <foreach collection="projectIntentHotelIds" open="(" close=")" item="intentHotelId" separator="," index="index">
            #{intentHotelId}
        </foreach>
    </update>

    <select id="queryRegisterHotelInfo" parameterType="com.fangcang.rfp.common.dto.request.BatchHotelRegisterRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryRegisterHotelInfoResponse">
        select
        ph.contact_name AS contactName,
        ph.contact_mobile AS contactMobile,
        ph.contact_email AS contactEmail,
        ph.hotel_id as hotelId,
        h.chn_name as hotelName
        from htl_rfp.T_PROJECT_INTENT_HOTEL ph, htl_info.t_hotel h
        where ph.hotel_id = h.hotelid and h.isactive = 1 and PROJECT_INTENT_HOTEL_ID in
        <foreach collection="projectIntentHotelIds" open="(" close=")" item="intentHotelId" separator="," index="index">
            #{intentHotelId}
        </foreach>
    </select>

    <update id="assignPlatformUser" parameterType="com.fangcang.rfp.common.dto.request.AssignPlatformUserRequest">
        update htl_rfp.T_PROJECT_INTENT_HOTEL set MODIFY_TIME = sysdate
        <if test="operator != null">
            ,modifier = #{operator,jdbcType=VARCHAR}
        </if>
        ,PLATFORM_CONTACT_UID = #{platformContactUid}
        ,PLATFORM_CONTACT_NAME = #{platformContactName}
        where project_intent_hotel_id in
        <foreach collection="projectIntentHotelIds" open="(" close=")" item="intentHotelId" separator="," index="index">
            #{intentHotelId}
        </foreach>
    </update>
    
    <update id="updateHotelServicePoints">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET HOTEL_SERVICE_POINTS = CASE
        WHEN (HOTEL_SERVICE_POINTS - #{penaltyScore}) &lt; 0 THEN 0
        ELSE (HOTEL_SERVICE_POINTS - #{penaltyScore})
        END,
        LAST_VIOLATION_TIME = SYSDATE
        where project_intent_hotel_id = #{projectIntentHotelId}
    </update>

    <update id="addHotelServicePoints">
         UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET HOTEL_SERVICE_POINTS = CASE
        WHEN (HOTEL_SERVICE_POINTS + #{penaltyScore}) > 100 THEN 100
        ELSE (HOTEL_SERVICE_POINTS + #{penaltyScore})
        END
        where project_intent_hotel_id = #{projectIntentHotelId}
    </update>

    <select id="selectAllProjectIntentHotel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL ORDER BY project_intent_hotel_id ASC
    </select>

    <update id="updateCertsUrl" parameterType="com.fangcang.rfp.common.dto.request.UpdateCertsUrlRequest">
        UPDATE
            htl_rfp.T_PROJECT_INTENT_HOTEL
        SET
            CERTS_URL = #{certsUrl,jdbcType=VARCHAR}
        WHERE
            PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <select id="queryInviteHotelList" resultType="java.lang.Long">
        SELECT HOTEL_ID
        FROM htl_rfp.T_PROJECT_INTENT_HOTEL
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND INVITE_STATUS = 1
    </select>

    <select id="selectProjectIntentHotelServicePoint" resultType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        SELECT
            HOTEL_ID as hotelId,
            HOTEL_SERVICE_POINTS as hotelServicePoints
        FROM htl_rfp.T_PROJECT_INTENT_HOTEL
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND BID_STATE = 3
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" open="(" close=")" item="hotelId" separator="," index="index">
            #{hotelId}
        </foreach>
    </select>

    <select id="queryLastNotCooperationProjectIntentHotel" resultType="java.lang.Long">
        SELECT
            PROJECT_INTENT_HOTEL_ID
        FROM
            (
                SELECT
                    VIOLATION_STAT_PROCESS_ID,
                    PROJECT_INTENT_HOTEL_ID,
                    STAT_PROCESS_STATUS,
                    CREATE_TIME
                FROM
                    htl_rfp.T_HOTEL_VIOLATION_STAT_PROCESS
                WHERE
                    VIOLATION_STAT_PROCESS_ID IN (
                        SELECT
                            max( VIOLATION_STAT_PROCESS_ID )
                        FROM
                            htl_rfp.T_HOTEL_VIOLATION_STAT_PROCESS
                        WHERE
                            PROJECT_INTENT_HOTEL_ID IN ( SELECT DISTINCT PROJECT_INTENT_HOTEL_ID FROM htl_rfp.T_HOTEL_VIOLATION_STAT_PROCESS WHERE STAT_PROCESS_STATUS = 5 )
                        GROUP BY
                            PROJECT_INTENT_HOTEL_ID
                    )
            ) t
        WHERE
            t.STAT_PROCESS_STATUS = 5
    </select>

    <update id="updateCertInfo" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET SPECIAL_TRADES_CERT_URL = #{specialTradesCertUrl,jdbcType=VARCHAR},
            HYGIENE_LICENSE_CERT_URL = #{hygieneLicenseCertUrl,jdbcType=VARCHAR},
            FIRE_SAFETY_CERT_URL = #{fireSafetyCertUrl,jdbcType=VARCHAR},
            SPECIAL_TRADES_CERT_DATE = #{specialTradesCertDate,jdbcType=TIMESTAMP},
            HYGIENE_LICENSE_CERT_DATE = #{hygieneLicenseCertDate,jdbcType=TIMESTAMP},
            FIRE_SAFETY_CERT_DATE = #{fireSafetyCertDate,jdbcType=TIMESTAMP},
            modifier = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

    <update id="updateBidWeight" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotel">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL
        SET BID_WEIGHT = #{bidWeight},
            modifier = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>

</mapper>