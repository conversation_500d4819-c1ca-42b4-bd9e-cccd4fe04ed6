<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelPriceDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelPrice">
    <id column="PRICE_CODE" jdbcType="DECIMAL" property="priceCode" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="DECIMAL" property="projectIntentHotelId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_PRICE_GROUP_ID" jdbcType="DECIMAL" property="hotelPriceGroupId" />
    <result column="PRICE_TYPE" jdbcType="INTEGER" property="priceType" />
    <result column="BASE_PRICE" jdbcType="DECIMAL" property="basePrice" />
    <result column="LAST_BASE_PRICE" jdbcType="DECIMAL" property="lastBasePrice" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="BREAKFAST_NUM" jdbcType="DECIMAL" property="breakfastNum" />
    <result column="ROOM_LEVEL_NO" jdbcType="INTEGER" property="roomLevelNo" />
    <result column="APPLICABLE_WEEKS" jdbcType="VARCHAR" property="applicableWeeks" />
    <result column="CANCEL_RESTRICT_TYPE" jdbcType="INTEGER" property="cancelRestrictType" />
    <result column="CANCEL_RESTRICT_DAY" jdbcType="INTEGER" property="cancelRestrictDay" />
    <result column="CANCEL_RESTRICT_TIME" jdbcType="VARCHAR" property="cancelRestrictTime" />
    <result column="LRA" jdbcType="INTEGER" property="lra" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    PRICE_CODE, PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, HOTEL_PRICE_GROUP_ID, PRICE_TYPE, BREAKFAST_NUM,ROOM_LEVEL_NO,BASE_PRICE,LAST_BASE_PRICE,
    APPLICABLE_WEEKS,CANCEL_RESTRICT_TYPE,CANCEL_RESTRICT_DAY,CANCEL_RESTRICT_TIME,LRA,REMARK,
    CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_PRICE
    where PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
  </select>

  <select id="selectInfoByProjectIdAndHotelIdAndGroupId" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_PRICE
    WHERE 1=1
         AND PROJECT_ID = #{projectId}
      <if test="hotelId != null">
        AND HOTEL_ID = #{hotelId}
      </if>
        <if test="hotelPriceGroupId != null">
          AND HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId}
        </if>
        <if test="priceType != null">
          AND PRICE_TYPE = #{priceType}
        </if>
      <if test="roomLevelNo != null">
        AND ROOM_LEVEL_NO = #{roomLevelNo}
      </if>
        ORDER BY BASE_PRICE asc ,ROOM_LEVEL_NO ASC
  </select>

  <select id="selectMinPriceInfoByProjectIdAndHotelIdAndGroupId" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_PRICE
    WHERE 1=1
    AND PROJECT_ID = #{projectId}
    <if test="hotelId != null">
      AND HOTEL_ID = #{hotelId}
    </if>
    <if test="hotelPriceGroupId != null">
      AND HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId}
    </if>
    <if test="priceType != null">
      AND PRICE_TYPE = #{priceType}
    </if>
    <if test="roomLevelNo != null">
      AND ROOM_LEVEL_NO = #{roomLevelNo}
    </if>
    ORDER BY ROOM_LEVEL_NO ASC, BASE_PRICE asc ,PRICE_CODE ASC
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_PROJECT_HOTEL_PRICE
    where PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByGroupId" parameterType="java.lang.Long">
    delete from T_PROJECT_HOTEL_PRICE
    where HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectAndHotelId">
    delete from T_PROJECT_HOTEL_PRICE
    where PROJECT_ID = #{projectId,jdbcType=DECIMAL}
      AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByGroupIdAndExcludePriceCodes">
    delete from T_PROJECT_HOTEL_PRICE
    where HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
      <if test="excludePriceCodes != null and excludePriceCodes.size() > 0">
        AND PRICE_CODE NOT IN
        <foreach collection="excludePriceCodes" open="(" close=")" item="priceCode" separator="," index="index">
          #{priceCode,jdbcType=DECIMAL}
        </foreach>
      </if>
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice">
    <selectKey keyProperty="priceCode" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_HOTEL_PRICE_CODE.nextval from dual
    </selectKey>
    insert into T_PROJECT_HOTEL_PRICE (PRICE_CODE, PROJECT_INTENT_HOTEL_ID, 
      PROJECT_ID, HOTEL_ID, HOTEL_PRICE_GROUP_ID, PRICE_TYPE, BASE_PRICE,
      BREAKFAST_NUM, ROOM_LEVEL_NO,CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME)
    values (#{priceCode,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=DECIMAL}, 
      #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL},
      #{hotelPriceGroupId,jdbcType=DECIMAL}, #{priceType,jdbcType=INTEGER}, #{basePrice,jdbcType=DECIMAL},
      #{breakfastNum,jdbcType=DECIMAL}, #{roomLevelNo,jdbcType=DECIMAL}, #{creator,jdbcType=VARCHAR}, SYSDATE,
      #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <insert id="insertBatch" parameterType="list">
    insert into htl_rfp.T_PROJECT_HOTEL_PRICE (
    PRICE_CODE, PROJECT_INTENT_HOTEL_ID,
    PROJECT_ID, HOTEL_ID, HOTEL_PRICE_GROUP_ID, PRICE_TYPE, BASE_PRICE,LAST_BASE_PRICE,
    BREAKFAST_NUM, ROOM_LEVEL_NO, CREATOR, CREATE_TIME,
    MODIFIER, MODIFY_TIME
    )
    SELECT htl_rfp.SEQ_RFP_HOTEL_PRICE_CODE.nextval AS priceCode, t.* FROM (
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT
      #{item.projectIntentHotelId,jdbcType=DECIMAL} AS projectIntentHotelId,
      #{item.projectId,jdbcType=DECIMAL} AS projectId,
      #{item.hotelId,jdbcType=DECIMAL} AS hotelId,
      #{item.hotelPriceGroupId,jdbcType=DECIMAL} AS hotelPriceGroupId,
      #{item.priceType,jdbcType=INTEGER} AS priceType,
      #{item.basePrice,jdbcType=DECIMAL} AS basePrice,
      #{item.lastBasePrice,jdbcType=DECIMAL} AS lastBasePrice,
      #{item.breakfastNum,jdbcType=DECIMAL} AS breakfastNum,
      #{item.roomLevelNo,jdbcType=DECIMAL} AS roomLevelNo,
      #{item.creator,jdbcType=VARCHAR} AS creator,
      SYSDATE AS creatorTime,
      #{item.modifier,jdbcType=VARCHAR} AS modifier,
      SYSDATE AS modifierTime
      FROM dual
    </foreach>)t
  </insert>

  <select id="selectNextSequenceKey" resultType="java.lang.Long">
    SELECT htl_rfp.SEQ_RFP_HOTEL_PRICE_CODE.nextval FROM DUAL
  </select>


  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice">
    UPDATE T_PROJECT_HOTEL_PRICE
    <set>
      <if test="breakfastNum != null">
        BREAKFAST_NUM = #{breakfastNum,jdbcType=DECIMAL},
      </if>
      <if test="priceType != null">
        PRICE_TYPE = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="basePrice != null">
        BASE_PRICE = #{basePrice,jdbcType=CHAR},
      </if>
      <if test="hotelPriceGroupId != null">
        HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL},
      </if>
      <if test="roomLevelNo != null">
        ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=DECIMAL},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      MODIFY_TIME = sysdate,
    </set>
    WHERE PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
    AND PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice">
    update T_PROJECT_HOTEL_PRICE
    set PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL},
      PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      HOTEL_ID = #{hotelId,jdbcType=DECIMAL},
      HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL},
      PRICE_TYPE = #{priceType, jdbcType=INTEGER},
      BREAKFAST_NUM = #{breakfastNum,jdbcType=DECIMAL},
      ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=DECIMAL},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
    where PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
  </update>

  <select id="selectMinPriceByHotelIdsAndProjectId"
          resultType="com.fangcang.rfp.common.dto.response.HotelMinPriceResponse">
    select t.hotel_id as hotelId,min(t.base_price) as minPrice
    from htl_rfp.T_PROJECT_HOTEL_PRICE t
    where t.project_id = #{projectId,jdbcType=BIGINT} and t.hotel_id in
    <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
      #{hotelId}
    </foreach>
    group by t.hotel_id
  </select>


  <select id="selectMinPriceAndBreakfast" resultType="com.fangcang.rfp.common.dto.response.HotelMinPriceResponse">
    select pr.hotel_id as hotelId, min(pr.breakfast_num) as breakfastNum, pr.base_price as minPrice,pr.project_intent_hotel_id as projectIntentHotelId
    from htl_rfp.T_PROJECT_HOTEL_PRICE pr,
    (select t.project_intent_hotel_id, min(t.base_price) as minPrice
    from htl_rfp.T_PROJECT_HOTEL_PRICE t
    where t.project_intent_hotel_id in
    <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator=","
             index="index">
      #{projectIntentHotelId}
    </foreach>
    group by t.project_intent_hotel_id) temp
    where pr.project_intent_hotel_id = temp.project_intent_hotel_id
    and pr.base_price = temp.minPrice
    group by pr.hotel_id, pr.base_price,pr.project_intent_hotel_id
  </select>

    <select id="selectMinPrice" resultType="java.math.BigDecimal">
        SELECT min(BASE_PRICE) FROM htl_rfp.T_PROJECT_HOTEL_PRICE WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </select>


  <select id="selectLevel1MinPriceAndBreakfast" resultType="com.fangcang.rfp.common.dto.response.HotelMinPriceResponse">
    select pr.hotel_id as hotelId, min(pr.breakfast_num) as breakfastNum, pr.base_price as minPrice,pr.project_intent_hotel_id as projectIntentHotelId
    from htl_rfp.T_PROJECT_HOTEL_PRICE pr,
    (select t.project_intent_hotel_id, min(t.base_price) as minPrice
    from htl_rfp.T_PROJECT_HOTEL_PRICE t
    where t.project_intent_hotel_id in
    <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator=","
             index="index">
      #{projectIntentHotelId}
    </foreach>
    AND t.ROOM_LEVEL_NO = 1
    group by t.project_intent_hotel_id) temp
    where pr.project_intent_hotel_id = temp.project_intent_hotel_id
    and pr.base_price = temp.minPrice
    AND pr.ROOM_LEVEL_NO = 1
    group by pr.hotel_id, pr.base_price,pr.project_intent_hotel_id
  </select>


  <select id="selectInfoByProjectIntentHotelId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM htl_rfp.T_PROJECT_HOTEL_PRICE
    WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    AND HOTEL_PRICE_GROUP_ID IS NOT NULL
  </select>
  <select id="selectInfoByProjectIntentHotelIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM htl_rfp.T_PROJECT_HOTEL_PRICE
    WHERE PROJECT_INTENT_HOTEL_ID IN
          <foreach collection="projectIntentHotelIds" item="projectIntentHotelId" separator="," open="(" close=")">
            #{projectIntentHotelId}
          </foreach>
    AND HOTEL_PRICE_GROUP_ID IS NOT NULL
    ORDER BY BASE_PRICE ASC, PRICE_CODE ASC
  </select>


  <select id="selectInfoByProjectHotelGroupId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from htl_rfp.T_PROJECT_HOTEL_PRICE
    where HOTEL_PRICE_GROUP_ID = #{projectHotelGroupId}
  </select>


  <select id="selectHotelMinPriceByIntentHotelId" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelLowPriceResponse">
    select t.PRICE_CODE priceCode,
            t.BASE_PRICE basePrice,
           t.PROJECT_INTENT_HOTEL_ID projectIntentHotelId,
           t.PROJECT_ID projectId,
           t.HOTEL_ID hotelId,
           t.BREAKFAST_NUM breakfastNum,
           h.CITY cityCode
    from htl_rfp.T_PROJECT_HOTEL_PRICE t
    left join HTL_INFO.T_HOTEL h on t.HOTEL_ID = h.HOTELID
    where t.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    order by t.BASE_PRICE asc, t.PRICE_CODE desc
  </select>

  <select id="selectHotelMinPriceByProjectHotelId" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelLowPriceResponse">
    SELECT t.PRICE_CODE priceCode,
           t.BASE_PRICE basePrice,
           t.PROJECT_INTENT_HOTEL_ID projectIntentHotelId,
           t.PROJECT_ID projectId,
           t.HOTEL_ID hotelId,
           t.HOTEL_PRICE_GROUP_ID hotelPriceGroupId,
           tg.ROOM_LEVEL_NO roomLevelNo,
           t.BREAKFAST_NUM breakfastNum,
           h.CITY cityCode
    FROM htl_rfp.T_PROJECT_HOTEL_PRICE t
           LEFT JOIN HTL_INFO.T_HOTEL h ON t.HOTEL_ID = h.HOTELID
           LEFT JOIN  T_PROJECT_HOTEL_PRICE_GROUP tg ON t.HOTEL_PRICE_GROUP_ID = tg.HOTEL_PRICE_GROUP_ID
    WHERE t.PROJECT_ID = #{projectId,jdbcType=BIGINT}
    AND t.HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    AND t.PRICE_TYPE = 1
    order by t.BASE_PRICE asc, t.PRICE_CODE desc
  </select>

  <select id="selectHotelMinPriceByProjectHotelPrice" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice" resultMap="BaseResultMap">
     SELECT
         <include refid="Base_Column_List"></include>
      FROM
        htl_rfp.T_PROJECT_HOTEL_PRICE
       WHERE
        PROJECT_ID = #{projectId}
       AND
         HOTEL_ID = #{hotelId}
       AND
        BREAKFAST_NUM = #{breakfastNum}
       AND
        PRICE_TYPE = 1
    order by BASE_PRICE asc
  </select>

  <select id="selectProjectHotelPriceCount" resultType="com.fangcang.rfp.common.dto.ProjectHotelPriceCountResult">
    SELECT
      tm.ROOM_TYPE_ID AS roomTypeId,
      tm.BREAKFAST_NUM AS breakfastNum,
      tm.count AS priceCount,
      tm.PRICE AS price
    FROM
      (
        SELECT
          t.PROJECT_INTENT_HOTEL_ID,
          t.HOTEL_ID,
          t.PROJECT_ID,
          t.ROOM_TYPE_ID,
          t.BREAKFAST_NUM,
          count( * ) AS count,
		MIN( t.PRICE ) AS PRICE
        FROM
          (
          SELECT
          t1.PROJECT_INTENT_HOTEL_ID,
          t1.HOTEL_ID,
          t1.PROJECT_ID,
          t1.ROOM_TYPE_ID,
          t2.BREAKFAST_NUM,
          t2.BASE_PRICE,
          MIN( t2.BASE_PRICE ) AS PRICE
          FROM
          T_PRICE_APPLICABLE_ROOM t1
          INNER JOIN T_PROJECT_HOTEL_PRICE t2 ON t1.PRICE_CODE = t2.PRICE_CODE
          AND t1.PROJECT_INTENT_HOTEL_ID = t2.PROJECT_INTENT_HOTEL_ID
          WHERE
          t1.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
          GROUP BY
          t1.PROJECT_INTENT_HOTEL_ID,
          t1.HOTEL_ID,
          t1.PROJECT_ID,
          t1.ROOM_TYPE_ID,
          t2.BREAKFAST_NUM,
          t2.BASE_PRICE
          ) t
        GROUP BY
          t.PROJECT_INTENT_HOTEL_ID,
          t.HOTEL_ID,
          t.PROJECT_ID,
          t.ROOM_TYPE_ID,
          t.BREAKFAST_NUM
      ) tm
    ORDER BY
      priceCount DESC,
      price ASC
  </select>
  <select id="selectAllNoGroupIdHotelPrice" resultType="com.fangcang.rfp.common.entity.ProjectHotelPrice" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice">
        SELECT
          PRICE_CODE AS priceCode,
          PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
          PROJECT_ID AS projectId,
          HOTEL_ID AS hotelId,
          APPLICABLE_WEEKS AS applicableWeeks,
          BREAKFAST_NUM AS breakfastNum,
          LRA AS lra,
          REMARK AS remark,
          BASE_PRICE AS basePrice,
          CANCEL_RESTRICT_TYPE AS cancelRestrictType,
          CANCEL_RESTRICT_DAY AS cancelRestrictDay,
          CANCEL_RESTRICT_TIME AS cancelRestrictTime,
          CREATOR AS creator,
          CREATE_TIME AS createTime,
          MODIFIER AS modifier,
          MODIFY_TIME AS modifyTime,
          HOTEL_PRICE_GROUP_ID AS hotelPriceGroupId,
          PRICE_TYPE AS priceType,
          ROOM_LEVEL_NO AS roomLevelNo
       FROM
         htl_rfp.T_PROJECT_HOTEL_PRICE
        WHERE
            1=1 AND
          PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
        ORDER BY BASE_PRICE ASC, PRICE_CODE ASC
  </select>

  <select id="queryHotelTenderInfo" resultType="com.fangcang.rfp.common.dto.response.ExportTenderHotelPriceResponse">
    select ph.bid_state as bidState,
       h.chn_name   as hotelName,
       h.hotelid    as hotelId,
       h.hotel_star  as hotelStar,
       h.lng_baidu  as lngBaidu,
       h.lat_baidu  as latBaidu,
       h.city AS cityCode,
       h.chn_address as hotelAddress,
       ig.brandname as hotelGroupName,
       ib.brandname as hotelBrandName,
       bc.dataname  as cityName,
       ph.BID_CONTACT_NAME as contactName,
       ph.BID_CONTACT_MOBILE as contactMobile,
       ph.BID_CONTACT_EMAIL as contactEmail,
       ph.HOTEL_GROUP_BID_CONTACT_NAME as hotelGroupContactName,
       ph.HOTEL_GROUP_BID_CONTACT_MOBILE as hotelGroupContactMobile,
       ph.HOTEL_GROUP_BID_CONTACT_EMAIL as hotelGroupContactEmail,
       ph.EMPLOYEE_RIGHT as employeeRight,
       ph.MODIFY_TIME as modifyTime
    from  htl_rfp.t_project_intent_hotel ph
    left join htl_info.t_hotel h
      on ph.hotel_id = h.hotelid
    left join htl_info.t_brand ig
      on h.hotel_group = ig.brandid
    left join htl_info.t_brand ib
      on h.hotelbrand = ib.brandid
    left join htl_base.t_areadata bc
      on h.city = bc.datacode
     and bc.datatype = 3
     and bc.countrycode = 'CN'
   where ph.project_id = #{projectId,jdbcType=BIGINT}
  </select>

  <select id="queryHotelGroupTenderInfo" resultType="com.fangcang.rfp.common.dto.response.ExportTenderHotelPriceResponse">
    select ph.bid_state as bidState,
           h.chn_name   as hotelName,
           h.hotelid    as hotelId,
           h.hotel_star  as hotelStar,
           h.lng_baidu  as lngBaidu,
           h.lat_baidu  as latBaidu,
           h.city AS cityCode,
           h.chn_address as hotelAddress,
           ig.brandname as hotelGroupName,
           ib.brandname as hotelBrandName,
           bc.dataname  as cityName,
           ph.BID_CONTACT_NAME as contactName,
           ph.BID_CONTACT_MOBILE as contactMobile,
           ph.BID_CONTACT_EMAIL as contactEmail,
           ph.HOTEL_GROUP_BID_CONTACT_NAME as hotelGroupContactName,
           ph.HOTEL_GROUP_BID_CONTACT_MOBILE as hotelGroupContactMobile,
           ph.HOTEL_GROUP_BID_CONTACT_EMAIL as hotelGroupContactEmail,
           ph.EMPLOYEE_RIGHT as employeeRight,
           ph.MODIFY_TIME as modifyTime
    from  htl_rfp.t_project_intent_hotel ph
            left join htl_info.t_hotel h
                      on ph.hotel_id = h.hotelid
            left join htl_info.t_brand ig
                      on h.hotel_group = ig.brandid
            left join htl_info.t_brand ib
                      on h.hotelbrand = ib.brandid
            left join htl_base.t_areadata bc
                      on h.city = bc.datacode
                        and bc.datatype = 3
                        and bc.countrycode = 'CN'
    where ph.project_id = #{projectId,jdbcType=BIGINT}
      AND h.hotelid IS NOT NULL
    AND ((ph.BID_ORG_ID = #{orgId} AND ph.BID_ORG_TYPE = 4)
          OR (h.HOTELBRAND IN
          <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
            #{hotelGroupBrandId}
          </foreach> AND ph.BID_ORG_TYPE = 2)
    )
  </select>

  <select id="selectAllMonitorRoomTypeBreakfastNumber" parameterType="java.lang.Long" resultType="com.fangcang.rfp.common.dto.MonitorHotelPrice">
    SELECT
      t1.ROOM_TYPE_ID AS roomTypeId,
      t2.BREAKFAST_NUM AS breakfastNum,
      t2.PRICE_CODE AS priceCode,
      t2.HOTEL_PRICE_GROUP_ID AS hotelPriceGroupId,
      t2.PRICE_TYPE AS priceType,
      t2.BASE_PRICE AS basePrice
    FROM
      HTL_RFP.T_PRICE_APPLICABLE_ROOM t1
    INNER JOIN T_PROJECT_HOTEL_PRICE t2 ON t1.ROOM_LEVEL_NO = t2.ROOM_LEVEL_NO
        AND t1.PROJECT_INTENT_HOTEL_ID = t2.PROJECT_INTENT_HOTEL_ID
        AND t1.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
        AND t1.IS_DELETED = 0
    ORDER BY basePrice ASC
  </select>

  <select id="selectOldInfoByProjectIdAndHotelId" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPrice" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_PRICE
    where 1=1
    and PROJECT_ID = #{projectId}
    and HOTEL_ID = #{hotelId}
    and (CREATOR IS NULL OR CREATOR != 'Convert')
    order by BASE_PRICE asc ,PRICE_CODE desc
  </select>

  <update id="recordLastPrice" parameterType="com.fangcang.rfp.common.dto.request.UpdateProjectIntentHotelDto">
    UPDATE HTL_RFP.T_PROJECT_HOTEL_PRICE
    SET LAST_BASE_PRICE = BASE_PRICE,
        MODIFY_TIME = SYSDATE
    where project_id = #{projectId,jdbcType=BIGINT}
    <if test="hotelIds != null">
      and hotel_id in
      <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
        #{hotelId}
      </foreach>
    </if>
  </update>

  <select id="queryHotelOldTenderPriceInfo" resultType="com.fangcang.rfp.common.dto.response.ExportOldTenderPriceResponse">
    select ph.bid_state as bidState,
           h.chn_name   as hotelName,
           h.hotelid    as hotelId,
           ig.brandname as hotelGroupName,
           ib.brandname as hotelBrandName,
           bc.dataname  as cityName,
           h.chn_address as hotelAddress,
           ph.BID_CONTACT_NAME as contactName,
           ph.BID_CONTACT_MOBILE as contactMobile,
           ph.BID_CONTACT_EMAIL as contactEmail,
           t.base_price as basePrice,
           t.breakfast_num as breakfastNum,
           t.lra as lra,
           t.cancel_restrict_type as cancelRestrictType,
           t.cancel_restrict_day as cancelRestrictDay,
           t.cancel_restrict_time as cancelRestrictTime,
           t.remark as remark,
           t.applicable_weeks  as applicableWeeks,
           t.price_code as priceCode
    from htl_rfp.T_PROJECT_HOTEL_PRICE t
           left join htl_rfp.t_project_intent_hotel ph
                     on t.project_intent_hotel_id = ph.project_intent_hotel_id
           left join htl_info.t_hotel h
                     on t.hotel_id = h.hotelid
           left join htl_info.t_brand ig
                     on h.hotel_group = ig.brandid
           left join htl_info.t_brand ib
                     on h.hotelbrand = ib.brandid
           left join htl_base.t_areadata bc
                     on h.city = bc.datacode
                       and bc.datatype = 3
                       and bc.countrycode = 'CN'
    where t.project_id = #{projectId}
  </select>
</mapper>