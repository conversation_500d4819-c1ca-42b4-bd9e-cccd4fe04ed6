<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectIntentHotelGroupDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
        <id column="PROJECT_INTENT_HOTEL_GROUP_ID" jdbcType="DECIMAL" property="projectIntentHotelGroupId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="HOTEL_GROUP_ORG_ID" jdbcType="DECIMAL" property="hotelGroupOrgId"/>
        <result column="HOTEL_GROUP_CONTACT_UID" jdbcType="DECIMAL" property="hotelGroupContactUid"/>
        <result column="HOTEL_GROUP_CONTACT_NAME" jdbcType="VARCHAR" property="hotelGroupContactName"/>
        <result column="BID_CONTACT_NAME" jdbcType="VARCHAR" property="bidContactName"/>
        <result column="BID_CONTACT_MOBILE" jdbcType="VARCHAR" property="bidContactMobile"/>
        <result column="BID_CONTACT_EMAIL" jdbcType="VARCHAR" property="bidContactEmail"/>
        <result column="HOTEL_GROUP_BID_CONTACT_NAME" jdbcType="VARCHAR" property="hotelGroupBidContactName"/>
        <result column="HOTEL_GROUP_BID_CONTACT_MOBILE" jdbcType="VARCHAR" property="hotelGroupBidContactMobile"/>
        <result column="HOTEL_GROUP_BID_CONTACT_EMAIL" jdbcType="VARCHAR" property="hotelGroupBidContactEmail"/>
        <result column="SEND_MAIL_STATUS" jdbcType="DECIMAL" property="sendMailStatus"/>
        <result column="SUPPORT_PAY_AT_HOTEL" jdbcType="DECIMAL" property="supportPayAtHotel"/>
        <result column="SUPPORT_CO_PAY" jdbcType="DECIMAL" property="supportCoPay"/>
        <result column="SUPPORT_NO_GUARANTEE" jdbcType="DECIMAL" property="supportNoGuarantee"/>
        <result column="SUPPORT_CHECKIN_INFO" jdbcType="DECIMAL" property="supportCheckinInfo"/>
        <result column="SUPPORT_PAY_EARLY_CHECKOUT" jdbcType="DECIMAL" property="supportPayEarlyCheckout"/>
        <result column="EARLY_CHECKIN_TIME" jdbcType="VARCHAR" property="earlyCheckinTime"/>
        <result column="LATE_CHECKOUT_TIME" jdbcType="VARCHAR" property="lateCheckoutTime"/>
        <result column="LATE_RESERVE_TIME" jdbcType="VARCHAR" property="lateReserveTime"/>
        <result column="DO_AFTER_LATE_RESERVE_TIME" jdbcType="DECIMAL" property="doAfterLateReserveTime"/>
        <result column="SUPPORT_MONTHLY_BALANCE" jdbcType="DECIMAL" property="supportMonthlyBalance"/>
        <result column="SUPPORT_INCLUDE_COMMISSION" jdbcType="DECIMAL" property="supportIncludeCommission"/>
        <result column="SUPPORT_WIFI" jdbcType="DECIMAL" property="supportWifi"/>
        <result column="SUPPORT_INCLUDE_TAX_SERVICE" jdbcType="DECIMAL" property="supportIncludeTaxService"/>
        <result column="TEND_COMMISSION" jdbcType="DECIMAL" property="tendCommission"/>
        <result column="PROVIDE_INVOICE_TYPE" jdbcType="DECIMAL" property="provideInvoiceType"/>
        <result column="PROVIDE_INVOICE_TAX_RATE" jdbcType="DECIMAL" property="provideInvoiceTaxRate"/>
        <result column="RECEIVE_ORDER_METHOD" jdbcType="INTEGER" property="receiveOrderMethod"/>
        <result column="is_active" jdbcType="DECIMAL" property="isActive"/>
        <result column="IS_BRAND_LIMIT" jdbcType="INTEGER" property="isBrandLimit"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    PROJECT_INTENT_HOTEL_GROUP_ID, PROJECT_ID, HOTEL_GROUP_ORG_ID, HOTEL_GROUP_CONTACT_UID, 
    HOTEL_GROUP_CONTACT_NAME, BID_CONTACT_NAME, BID_CONTACT_MOBILE, BID_CONTACT_EMAIL,
    HOTEL_GROUP_BID_CONTACT_NAME, HOTEL_GROUP_BID_CONTACT_MOBILE, HOTEL_GROUP_BID_CONTACT_EMAIL,
    SEND_MAIL_STATUS, SUPPORT_PAY_AT_HOTEL, SUPPORT_CO_PAY, SUPPORT_NO_GUARANTEE, SUPPORT_CHECKIN_INFO, 
    SUPPORT_PAY_EARLY_CHECKOUT, EARLY_CHECKIN_TIME, LATE_CHECKOUT_TIME, LATE_RESERVE_TIME, 
    DO_AFTER_LATE_RESERVE_TIME, SUPPORT_MONTHLY_BALANCE, SUPPORT_WIFI, SUPPORT_INCLUDE_TAX_SERVICE, SUPPORT_INCLUDE_COMMISSION,
    TEND_COMMISSION, PROVIDE_INVOICE_TYPE, PROVIDE_INVOICE_TAX_RATE, CREATOR, CREATE_TIME, 
    MODIFIER, MODIFY_TIME,IS_ACTIVE,RECEIVE_ORDER_METHOD,IS_BRAND_LIMIT
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_INTENT_HOTEL_GROUP
        where PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteProjectIntentHotelGroup"
            parameterType="com.fangcang.rfp.common.dto.request.DeleteProjectIntentHotelGroupDto">
        update htl_rfp.T_PROJECT_INTENT_HOTEL_GROUP set is_active = 0,MODIFY_TIME=sysdate
        <if test="operator != null and operator !=''">
            ,MODIFIER = #{operator}
        </if>
        where PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId,jdbcType=DECIMAL} and PROJECT_ID =#{projectId}
    </delete>

    <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
        <selectKey keyProperty="projectIntentHotelGroupId" resultType="_long" order="BEFORE">
            select htl_rfp.seq_rfp_proj_intent_h_grp_id.nextval from dual
        </selectKey>
        insert into T_PROJECT_INTENT_HOTEL_GROUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            PROJECT_INTENT_HOTEL_GROUP_ID,
            PROJECT_ID,
            HOTEL_GROUP_ORG_ID,
            <if test="hotelGroupContactUid != null">
                HOTEL_GROUP_CONTACT_UID,
            </if>
            <if test="hotelGroupContactName != null">
                HOTEL_GROUP_CONTACT_NAME,
            </if>
            <if test="bidContactName != null">
                BID_CONTACT_NAME,
            </if>
            <if test="bidContactMobile != null">
                BID_CONTACT_MOBILE,
            </if>
            <if test="bidContactEmail != null">
                BID_CONTACT_EMAIL,
            </if>
            <if test="hotelGroupBidContactName != null">
                HOTEL_GROUP_BID_CONTACT_NAME,
            </if>
            <if test="hotelGroupBidContactMobile != null">
                HOTEL_GROUP_BID_CONTACT_MOBILE,
            </if>
            <if test="hotelGroupBidContactEmail != null">
                HOTEL_GROUP_BID_CONTACT_EMAIL,
            </if>
            <if test="sendMailStatus != null">
                SEND_MAIL_STATUS,
            </if>
            <if test="supportPayAtHotel != null">
                SUPPORT_PAY_AT_HOTEL,
            </if>
            <if test="supportCoPay != null">
                SUPPORT_CO_PAY,
            </if>
            <if test="supportNoGuarantee != null">
                SUPPORT_NO_GUARANTEE,
            </if>
            <if test="supportCheckinInfo != null">
                SUPPORT_CHECKIN_INFO,
            </if>
            <if test="supportPayEarlyCheckout != null">
                SUPPORT_PAY_EARLY_CHECKOUT,
            </if>
            <if test="earlyCheckinTime != null">
                EARLY_CHECKIN_TIME,
            </if>
            <if test="lateCheckoutTime != null">
                LATE_CHECKOUT_TIME,
            </if>
            <if test="lateReserveTime != null">
                LATE_RESERVE_TIME,
            </if>
            <if test="doAfterLateReserveTime != null">
                DO_AFTER_LATE_RESERVE_TIME,
            </if>
            <if test="supportMonthlyBalance != null">
                SUPPORT_MONTHLY_BALANCE,
            </if>
            <if test="supportIncludeCommission != null">
                SUPPORT_INCLUDE_COMMISSION,
            </if>
            <if test="supportWifi != null">
                SUPPORT_WIFI,
            </if>
            <if test="supportIncludeTaxService != null">
                SUPPORT_INCLUDE_TAX_SERVICE,
            </if>
            <if test="tendCommission != null">
                TEND_COMMISSION,
            </if>
            <if test="provideInvoiceType != null">
                PROVIDE_INVOICE_TYPE,
            </if>
            <if test="provideInvoiceTaxRate != null">
                PROVIDE_INVOICE_TAX_RATE,
            </if>
            <if test="receiveOrderMethod != null">
                RECEIVE_ORDER_METHOD,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            CREATE_TIME,
            <if test="modifier != null">
                MODIFIER,
            </if>
            MODIFY_TIME,
            IS_ACTIVE,
            IS_BRAND_LIMIT,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{projectIntentHotelGroupId},
            #{projectId,jdbcType=DECIMAL},
            #{hotelGroupOrgId,jdbcType=DECIMAL},
            <if test="hotelGroupContactUid != null">
                #{hotelGroupContactUid,jdbcType=DECIMAL},
            </if>
            <if test="hotelGroupContactName != null">
                #{hotelGroupContactName,jdbcType=VARCHAR},
            </if>
            <if test="bidContactName != null">
                #{bidContactName,jdbcType=VARCHAR},
            </if>
            <if test="bidContactMobile != null">
                #{bidContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="bidContactEmail != null">
                #{bidContactEmail,jdbcType=VARCHAR},
            </if>
            <if test="hotelGroupBidContactName != null">
                #{hotelGroupBidContactName,jdbcType=VARCHAR},
            </if>
            <if test="hotelGroupBidContactMobile != null">
                #{hotelGroupBidContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="hotelGroupBidContactEmail != null">
                #{hotelGroupBidContactEmail,jdbcType=VARCHAR},
            </if>
            <if test="sendMailStatus != null">
                #{sendMailStatus,jdbcType=DECIMAL},
            </if>
            <if test="supportPayAtHotel != null">
                #{supportPayAtHotel,jdbcType=DECIMAL},
            </if>
            <if test="supportCoPay != null">
                #{supportCoPay,jdbcType=DECIMAL},
            </if>
            <if test="supportNoGuarantee != null">
                #{supportNoGuarantee,jdbcType=DECIMAL},
            </if>
            <if test="supportCheckinInfo != null">
                #{supportCheckinInfo,jdbcType=DECIMAL},
            </if>
            <if test="supportPayEarlyCheckout != null">
                #{supportPayEarlyCheckout,jdbcType=DECIMAL},
            </if>
            <if test="earlyCheckinTime != null">
                #{earlyCheckinTime,jdbcType=VARCHAR},
            </if>
            <if test="lateCheckoutTime != null">
                #{lateCheckoutTime,jdbcType=VARCHAR},
            </if>
            <if test="lateReserveTime != null">
                #{lateReserveTime,jdbcType=VARCHAR},
            </if>
            <if test="doAfterLateReserveTime != null">
                #{doAfterLateReserveTime,jdbcType=DECIMAL},
            </if>
            <if test="supportMonthlyBalance != null">
                #{supportMonthlyBalance,jdbcType=DECIMAL},
            </if>
            <if test="supportIncludeCommission != null">
                #{supportIncludeCommission,jdbcType=DECIMAL},
            </if>
            <if test="supportWifi != null">
                #{supportWifi,jdbcType=DECIMAL},
            </if>
            <if test="supportIncludeTaxService != null">
                #{supportIncludeTaxService,jdbcType=DECIMAL},
            </if>
            <if test="tendCommission != null">
                #{tendCommission,jdbcType=DECIMAL},
            </if>
            <if test="provideInvoiceType != null">
                #{provideInvoiceType,jdbcType=DECIMAL},
            </if>
            <if test="provideInvoiceTaxRate != null">
                #{provideInvoiceTaxRate,jdbcType=DECIMAL},
            </if>
            <if test="receiveOrderMethod != null">
                #{receiveOrderMethod,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            sysdate,
            <if test="modifier != null">
                #{modifier,jdbcType=VARCHAR},
            </if>
            sysdate,
            1,
            #{isBrandLimit,jdbcType=INTEGER},
        </trim>
    </insert>
    <update id="updateDefaultPrice" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
        update htl_rfp.T_PROJECT_INTENT_HOTEL_GROUP set
                BID_CONTACT_NAME=#{bidContactName,jdbcType=VARCHAR},
                BID_CONTACT_MOBILE=#{bidContactMobile,jdbcType=VARCHAR},
                BID_CONTACT_EMAIL=#{bidContactEmail,jdbcType=VARCHAR},
                HOTEL_GROUP_BID_CONTACT_NAME=#{hotelGroupBidContactName,jdbcType=VARCHAR},
                HOTEL_GROUP_BID_CONTACT_MOBILE=#{hotelGroupBidContactMobile,jdbcType=VARCHAR},
                HOTEL_GROUP_BID_CONTACT_EMAIL=#{hotelGroupBidContactEmail,jdbcType=VARCHAR},
                SUPPORT_PAY_AT_HOTEL = #{supportPayAtHotel,jdbcType=DECIMAL},
                SUPPORT_CO_PAY = #{supportCoPay,jdbcType=DECIMAL},
                SUPPORT_NO_GUARANTEE = #{supportNoGuarantee,jdbcType=DECIMAL},
                SUPPORT_CHECKIN_INFO = #{supportCheckinInfo,jdbcType=DECIMAL},
                SUPPORT_PAY_EARLY_CHECKOUT = #{supportPayEarlyCheckout,jdbcType=DECIMAL},
                EARLY_CHECKIN_TIME = #{earlyCheckinTime,jdbcType=VARCHAR},
                LATE_CHECKOUT_TIME = #{lateCheckoutTime,jdbcType=VARCHAR},
                LATE_RESERVE_TIME = #{lateReserveTime,jdbcType=VARCHAR},
                DO_AFTER_LATE_RESERVE_TIME = #{doAfterLateReserveTime,jdbcType=DECIMAL},
                SUPPORT_MONTHLY_BALANCE = #{supportMonthlyBalance,jdbcType=DECIMAL},
                SUPPORT_INCLUDE_COMMISSION = #{supportIncludeCommission,jdbcType=DECIMAL},
                SUPPORT_WIFI = #{supportWifi,jdbcType=DECIMAL},
                SUPPORT_INCLUDE_TAX_SERVICE = #{supportIncludeTaxService,jdbcType=DECIMAL},
                TEND_COMMISSION = #{tendCommission,jdbcType=DECIMAL},
                PROVIDE_INVOICE_TYPE = #{provideInvoiceType,jdbcType=DECIMAL},
                PROVIDE_INVOICE_TAX_RATE = #{provideInvoiceTaxRate,jdbcType=DECIMAL},
                RECEIVE_ORDER_METHOD = #{receiveOrderMethod,jdbcType=INTEGER},
                MODIFIER = #{modifier,jdbcType=VARCHAR},
                MODIFY_TIME = sysdate
        where PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId} and PROJECT_ID = #{projectId}
    </update>

    <update id="updateIsBrandLimit" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
        UPDATE htl_rfp.T_PROJECT_INTENT_HOTEL_GROUP
        SET
            IS_BRAND_LIMIT=#{isBrandLimit,jdbcType=INTEGER},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = sysdate
        WHERE PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId} AND PROJECT_ID = #{projectId}
    </update>
    <update id="modifyHotelGroupContact" parameterType="com.fangcang.rfp.common.dto.request.ModifyHotelGroupContactDto">
        update htl_rfp.T_PROJECT_INTENT_HOTEL_GROUP set hotel_group_contact_uid = #{hotelGroupContactUid},
        hotel_group_contact_name = #{hotelGroupContactName},modifier = #{operator,jdbcType=VARCHAR},modify_time =
        sysdate
        where PROJECT_INTENT_HOTEL_GROUP_ID IN
        <foreach collection="projectIntentHotelGroupIds" open="(" close=")" item="projectIntentHotelGroupId"
                 separator="," index="index">
            #{projectIntentHotelGroupId}
        </foreach>
        and HOTEL_GROUP_ORG_ID = #{orgId}
    </update>

    <select id="queryInviteHotelGroup" parameterType="com.fangcang.rfp.common.dto.request.QueryInviteHotelGroupRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryInviteHotelGroupResponse">
        select t.logo_url as logoUrl,
        t.org_name as orgName,
        t.org_id as orgId,
        t.contact_name as contactName,
        t.contact_mobile as contactMobile,
        t.contact_email as contactEmail,
        nvl2(g.project_intent_hotel_group_id,1,0) as inviteState,
        nvl2(g.send_mail_status,send_mail_status,0) as sendMailStatus,
        g.project_intent_hotel_group_id as projectIntentHotelGroupId,
        g.IS_BRAND_LIMIT as isBrandLimit
        from htl_rfp.t_org t
        left join htl_rfp.t_project_intent_hotel_group g on t.org_id=g.hotel_group_org_id and g.project_id= #{projectId}
        and g.is_active=1
        where t.org_type = 4
        and t.state = 1
        <if test="groupName != null and groupName !=''">
            and t.org_name like CONCAT(CONCAT('%',#{groupName}),'%')
        </if>
        <if test="inviteState != null and inviteState==1">
            and exists(select 1 from htl_rfp.t_project_intent_hotel_group tg where t.org_id=tg.hotel_group_org_id and
            tg.project_id= #{projectId} and tg.is_active=1)
        </if>
        <if test="inviteState != null and inviteState==0">
            and not exists(select 1 from htl_rfp.t_project_intent_hotel_group tg where t.org_id=tg.hotel_group_org_id
            and tg.project_id= #{projectId} and tg.is_active=1)
        </if>
    </select>
    <select id="selectByProjectAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.t_project_intent_hotel_group where project_id= #{projectId} and hotel_group_org_id=#{orgId} and
        is_active=1
    </select>

    <insert id="megreProjectIntentHotelGroup" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
    MERGE INTO htl_rfp.t_project_intent_hotel_group trg
USING (
  SELECT
    #{projectId,jdbcType=DECIMAL} as project_id,
    #{hotelGroupOrgId,jdbcType=DECIMAL} as hotel_group_org_id,
    #{hotelGroupContactUid,jdbcType=DECIMAL} as hotel_group_contact_uid,
      #{hotelGroupContactName,jdbcType=VARCHAR} as hotel_group_contact_name,
      #{bidContactName,jdbcType=VARCHAR} as bid_contact_name,
      #{bidContactMobile,jdbcType=VARCHAR} as bid_contact_mobile,
      #{bidContactEmail,jdbcType=VARCHAR} as bid_contact_email,
    #{hotelGroupBidContactName,jdbcType=VARCHAR} as hotel_group_bid_contact_name,
    #{hotelGroupBidContactMobile,jdbcType=VARCHAR} as hotel_group_bid_contact_mobile,
    #{hotelGroupBidContactEmail,jdbcType=VARCHAR} as hotel_group_bid_contact_email,
       #{sendMailStatus,jdbcType=DECIMAL} as send_mail_status,
     #{supportPayAtHotel,jdbcType=DECIMAL} as support_pay_at_hotel,
    #{supportCoPay,jdbcType=DECIMAL} as support_co_pay,
    #{supportNoGuarantee,jdbcType=DECIMAL} as support_no_guarantee,
    #{supportCheckinInfo,jdbcType=DECIMAL} as support_checkin_info,
    #{supportPayEarlyCheckout,jdbcType=DECIMAL} as support_pay_early_checkout,
    #{earlyCheckinTime,jdbcType=VARCHAR} as early_checkin_time,
    #{lateCheckoutTime,jdbcType=VARCHAR} as late_checkout_time,
    #{lateReserveTime,jdbcType=VARCHAR} as late_reserve_time,
    #{doAfterLateReserveTime,jdbcType=DECIMAL} as do_after_late_reserve_time,
   #{supportMonthlyBalance,jdbcType=DECIMAL} as support_monthly_balance ,
    #{supportIncludeCommission,jdbcType=DECIMAL} as support_include_commission,
    #{supportWifi,jdbcType=DECIMAL} as SUPPORT_WIFI,
    #{supportIncludeTaxService,jdbcType=DECIMAL} as SUPPORT_INCLUDE_TAX_SERVICE,
    #{tendCommission,jdbcType=DECIMAL} as tend_commission,
     #{provideInvoiceType,jdbcType=DECIMAL} as provide_invoice_type,
   #{provideInvoiceTaxRate,jdbcType=DECIMAL} as provide_invoice_tax_rate,
    #{receiveOrderMethod,jdbcType=INTEGER} as RECEIVE_ORDER_METHOD,
    #{isBrandLimit,jdbcType=INTEGER} as IS_BRAND_LIMIT,
     #{creator,jdbcType=VARCHAR} as creator,
    sysdate as create_time,
    #{modifier,jdbcType=VARCHAR} as modifier,
    sysdate as modify_time,
    #{isActive} as is_active
  FROM dual
) src
ON (trg.project_id = src.project_id AND trg.hotel_group_org_id = src.hotel_group_org_id)
WHEN MATCHED THEN
   UPDATE SET trg.hotel_group_contact_uid = src.hotel_group_contact_uid,
              trg.hotel_group_contact_name = src.hotel_group_contact_name,
              trg.bid_contact_name = src.bid_contact_name,
              trg.bid_contact_mobile = src.bid_contact_mobile,
              trg.bid_contact_email = src.bid_contact_email,
               trg.hotel_group_bid_contact_name = src.hotel_group_bid_contact_name,
               trg.hotel_group_bid_contact_mobile = src.hotel_group_bid_contact_mobile,
               trg.hotel_group_bid_contact_email = src.hotel_group_bid_contact_email,
              trg.send_mail_status = src.send_mail_status,
              trg.support_pay_at_hotel = src.support_pay_at_hotel,
              trg.support_co_pay = src.support_co_pay,
              trg.support_no_guarantee = src.support_no_guarantee,
              trg.support_checkin_info = src.support_checkin_info,
              trg.support_pay_early_checkout = src.support_pay_early_checkout,
              trg.early_checkin_time = src.early_checkin_time,
              trg.late_checkout_time = src.late_checkout_time,
              trg.late_reserve_time = src.late_reserve_time,
              trg.do_after_late_reserve_time = src.do_after_late_reserve_time,
              trg.support_monthly_balance = src.support_monthly_balance,
              trg.support_include_commission = src.support_include_commission,
              trg.SUPPORT_WIFI = src.SUPPORT_WIFI,
              trg.SUPPORT_INCLUDE_TAX_SERVICE = src.SUPPORT_INCLUDE_TAX_SERVICE,
              trg.tend_commission = src.tend_commission,
              trg.provide_invoice_type = src.provide_invoice_type,
              trg.provide_invoice_tax_rate = src.provide_invoice_tax_rate,
              trg.RECEIVE_ORDER_METHOD = src.RECEIVE_ORDER_METHOD,
              trg.IS_BRAND_LIMIT = src.IS_BRAND_LIMIT,
              trg.is_active = src.is_active,
              trg.modifier = src.modifier,
              trg.modify_time = src.modify_time
WHEN NOT MATCHED THEN
   INSERT (project_intent_hotel_group_id, project_id, hotel_group_org_id,
           hotel_group_contact_uid, hotel_group_contact_name, bid_contact_name,
           bid_contact_mobile, bid_contact_email,
           hotel_group_bid_contact_name, hotel_group_bid_contact_mobile, hotel_group_bid_contact_email,
           send_mail_status,
           support_pay_at_hotel, support_co_pay, support_no_guarantee,
           support_checkin_info, support_pay_early_checkout, early_checkin_time,
           late_checkout_time, late_reserve_time, do_after_late_reserve_time,
           support_monthly_balance, support_include_commission, SUPPORT_WIFI, SUPPORT_INCLUDE_TAX_SERVICE, tend_commission,
           provide_invoice_type, provide_invoice_tax_rate, is_active, RECEIVE_ORDER_METHOD, IS_BRAND_LIMIT,
           creator, create_time, modifier, modify_time)
   VALUES (htl_rfp.seq_rfp_proj_intent_h_grp_id.nextval, src.project_id, src.hotel_group_org_id,
           src.hotel_group_contact_uid, src.hotel_group_contact_name, src.bid_contact_name,
           src.bid_contact_mobile, src.bid_contact_email,
           src.hotel_group_bid_contact_name,
           src.hotel_group_bid_contact_mobile, src.hotel_group_bid_contact_email,
           src.send_mail_status,
           src.support_pay_at_hotel, src.support_co_pay, src.support_no_guarantee,
           src.support_checkin_info, src.support_pay_early_checkout, src.early_checkin_time,
           src.late_checkout_time, src.late_reserve_time, src.do_after_late_reserve_time,
           src.support_monthly_balance, src.support_include_commission, src.SUPPORT_WIFI, src.SUPPORT_INCLUDE_TAX_SERVICE, src.tend_commission,
           src.provide_invoice_type, src.provide_invoice_tax_rate, src.is_active, src.RECEIVE_ORDER_METHOD, src.IS_BRAND_LIMIT,
           src.creator, src.create_time, src.modifier, src.modify_time)
  </insert>

    <select id="queryProjectOverviewInfo"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectOverviewRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectOverviewResponse">
        select
        p.project_id as projectId,
        p.project_name as projectName,
        o.org_name as orgName,
        p.tender_type as tenderType,
        p.project_type as projectType,
        p.tender_count as tenderCount,
        p.diff_min_amount as diffMinAmount,
        p.diff_max_amount as diffMaxAmount,
        p.enroll_start_time as enrollStartTime,
        p.enroll_end_time as enrollEndTime,
        p.first_bid_start_time as firstBidStartTime,
        p.first_bid_end_time as firstBidEndTime,
        p.second_bid_start_time as secondBidStartTime,
        p.second_bid_end_time as secondBidEndTime,
        p.third_bid_start_time as thirdBidStartTime,
        p.third_bid_end_time as thirdBidEndTime,
        g.hotel_group_contact_name as hotelGroupContactName,
        p.project_state as projectState,
        p.need_online_contracts as needOnlineContracts,
        g.project_intent_hotel_group_id as projectIntentHotelGroupId,
        P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
        p.PRICE_MONITOR_END_DATE as priceMonitorEndDate,
        p.DISPLAY_ORDER AS displayOrder,
        g.IS_OPEN_GROUP_APPROVE AS isOpenGroupApprove,
        g.IS_BRAND_LIMIT AS isBrandLimit
        from htl_rfp.t_project_intent_hotel_group g,
        htl_rfp.t_project p,
        htl_rfp.t_org o
        where g.project_id = p.project_id
        and p.tender_org_id = o.org_id
        and g.is_active=1
        and g.hotel_group_org_id = #{orgId}
        <if test="searchProjectName != null and searchProjectName !=''">
            and p.project_name like concat(concat('%',#{searchProjectName}),'%')
        </if>
        <if test="searchOrgName != null and searchOrgName !=''">
            and o.org_name like concat(concat('%',#{searchOrgName}),'%')
        </if>
        <if test="projectState == null or projectState == 0">
            and p.project_state != 0
        </if>
        <if test="projectState != null and projectState != 0">
            and p.project_state = #{projectState}
        </if>
        <if test="userId != null">
            and g.hotel_group_contact_uid = #{userId}
        </if>
        <if test="quoteStatus != null and quoteStatus == -1">
            and p.project_state = 1
        </if>
        order by p.display_order desc nulls last, p.create_time desc
    </select>

    <select id="queryTendProjectInviteHotelInfo"  parameterType="com.fangcang.rfp.common.dto.request.QueryTendProjectInviteHotelInfoRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectOverviewResponse">
        select
        p.project_id as projectId,
        p.project_name as projectName,
        o.org_name as orgName,
        p.tender_type as tenderType,
        p.project_type as projectType,
        g.PROJECT_INTENT_HOTEL_GROUP_ID AS projectIntentHotelGroupId,
        g.hotel_group_contact_name as hotelGroupContactName,
        p.project_state as projectState,
        p.need_online_contracts as needOnlineContracts,
        h.hotelid as hotelId,
        h.city_name as cityName,
        h.chn_name as hotelName
        from htl_rfp.t_project_intent_hotel_group g,
        (SELECT t1.PROJECT_ID, t1.HOTEL_ID FROM htl_rfp.t_project_invite_hotel t1
            WHERE t1.PROJECT_ID IN (SELECT PROJECT_ID FROM htl_rfp.T_PROJECT WHERE PROJECT_STATE = 1 ) AND t1.HOTEL_ID NOT IN (
            SELECT t2.HOTEL_ID
            FROM htl_rfp.T_PROJECT_INTENT_HOTEL t2
            WHERE  t1.project_id = t2.project_id
            AND t2.BID_STATE >= 1)
        ) pih,
        htl_rfp.t_project p,
        htl_rfp.t_org o,
        htl_info.t_hotel h
        where g.project_id = p.project_id
        and pih.project_id =  p.project_id
        and pih.hotel_id = h.hotelid
        and p.tender_org_id = o.org_id
        and h.HOTEL_GROUP = #{hotelGroupId}
        and g.hotel_group_org_id = #{orgId}
        and h.isactive=1
        and p.project_state = 1
        <if test="searchProjectName != null and searchProjectName !=''">
            and p.project_name like concat(concat('%',#{searchProjectName}),'%')
        </if>
        <if test="userId != null">
            and g.hotel_group_contact_uid = #{userId}
        </if>
        <if test="hotelId != null">
            and pih.hotel_id = #{hotelId}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and h.CITY = #{cityCode}
        </if>
        order by p.display_order nulls last, p.create_time desc
    </select>

    <select id="queryTendProjectInviteHotelInfoCount"  parameterType="com.fangcang.rfp.common.dto.request.QueryTendProjectInviteHotelInfoRequest"
            resultType="java.lang.Integer">
        select
        COUNT(*)
        from htl_rfp.t_project_intent_hotel_group g,
        (SELECT t1.PROJECT_ID, t1.HOTEL_ID FROM htl_rfp.t_project_invite_hotel t1
        WHERE t1.PROJECT_ID IN (SELECT PROJECT_ID FROM htl_rfp.T_PROJECT WHERE PROJECT_STATE = 1 ) AND t1.HOTEL_ID NOT IN (
        SELECT t2.HOTEL_ID
        FROM htl_rfp.T_PROJECT_INTENT_HOTEL t2
        WHERE  t1.project_id = t2.project_id
        AND t2.BID_STATE >= 1)
        ) pih,
        htl_rfp.t_project p,
        htl_rfp.t_org o,
        htl_info.t_hotel h
        where g.project_id = p.project_id
        and pih.project_id =  p.project_id
        and pih.hotel_id = h.hotelid
        and p.tender_org_id = o.org_id
        and h.HOTEL_GROUP = #{hotelGroupId}
        and g.hotel_group_org_id = #{orgId}
        and h.isactive=1
        and p.project_state = 1
        <if test="userId != null">
            and g.hotel_group_contact_uid = #{userId}
        </if>
    </select>
    <select id="queryTendProjectInfo"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectOverviewRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectOverviewResponse">
    select
        p.project_id as projectId,
        p.project_name as projectName,
        o.org_name as orgName,
        p.tender_type as tenderType,
        p.project_type as projectType,
        p.tender_count as tenderCount,
        p.diff_min_amount as diffMinAmount,
        p.diff_max_amount as diffMaxAmount,
        p.enroll_start_time as enrollStartTime,
        p.enroll_end_time as enrollEndTime,
        p.first_bid_start_time as firstBidStartTime,
        p.first_bid_end_time as firstBidEndTime,
        p.second_bid_start_time as secondBidStartTime,
        p.second_bid_end_time as secondBidEndTime,
        p.third_bid_start_time as thirdBidStartTime,
        p.third_bid_end_time as thirdBidEndTime,
        p.project_state as projectState,
        p.need_online_contracts as needOnlineContracts,
        h.hotelid as hotelId,
        h.city_name as cityName,
        h.chn_name as hotelName,
        h.HOTELBRAND as brandId,
        ph.project_intent_hotel_id as projectIntentHotelId,
        ph.BID_CONTACT_NAME as bidContactName,
        ph.BID_CONTACT_MOBILE as bidContactMobile,
        ph.BID_CONTACT_EMAIL as bidContactEmail,
        ph.HOTEL_GROUP_BID_CONTACT_NAME as hotelGroupBidContactName,
        ph.HOTEL_GROUP_BID_CONTACT_MOBILE as hotelGroupBidContactMobile,
        ph.HOTEL_GROUP_BID_CONTACT_EMAIL as hotelGroupBidContactEmail,
        ph.CONTACT_NAME as contactName,
        ph.CONTACT_MOBILE as contactMobile,
        ph.CONTACT_EMAIL as contactEmail,
        ph.BID_ORG_TYPE as bidOrgType,
        ph.BID_STATE as bidState,
        ph.HOTEL_GROUP_APPROVE_STATUS as hotelGroupApproveStatus,
        P.PRICE_MONITOR_START_DATE as priceMonitorStartDate,
        p.PRICE_MONITOR_END_DATE as priceMonitorEndDate
        from htl_rfp.t_project p,
        htl_rfp.t_org o,
        htl_rfp.t_project_intent_hotel ph,
        htl_info.t_hotel h
    where
        p.project_id IN (SELECT project_id FROM htl_rfp.t_project_intent_hotel_group
                            WHERE hotel_group_org_id = #{orgId}
                            <if test="userId != null">
                                and hotel_group_contact_uid = #{userId}
                            </if>)
        AND p.tender_org_id = o.org_id
        and p.project_id = ph.project_id
        and h.hotelid = ph.hotel_id
        and h.isactive=1
        <if test="searchProjectName != null and searchProjectName !=''">
            and p.project_name like concat(concat('%',#{searchProjectName}),'%')
        </if>
        <if test="searchOrgName != null and searchOrgName !=''">
            and o.org_name like concat(concat('%',#{searchOrgName}),'%')
        </if>
        <if test="projectState == null or projectState == 0">
            and p.project_state != 0
        </if>
        <if test="projectState != null and projectState != 0">
            and p.project_state = #{projectState}
        </if>
        <if test="quoteStatus != null and quoteStatus > -1">
            and ph.bid_state = #{quoteStatus}
        </if>
        <if test="quoteStatus == 0 and hotelGroupApproveStatus == null">
            AND ph.INVITE_STATUS = 1
            <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
                 AND h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach>
            </if>
        </if>
        <if test="hotelGroupApproveStatus != null">
            and ph.HOTEL_GROUP_APPROVE_STATUS = #{hotelGroupApproveStatus}
        </if>
        <if test="quoteStatus !=0 and hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{orgId} AND ph.BID_ORG_TYPE = 4)
                    OR (h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        <if test="hotelId != null">
            and ph.hotel_id = #{hotelId}
        </if>
        <if test="hotelId == null and hotelName != null and hotelName !=''">
            and h.chn_name like concat(concat('%',#{hotelName}),'%')
        </if>
        <if test="brandId != null and brandId > 0">
            and h.HOTELBRAND = #{brandId}
        </if>
        <if test="bidOrgType != null">
            and ph.bid_org_type = #{bidOrgType}
        </if>
        order by p.display_order nulls last, p.create_time desc
    </select>


    <select id="queryTendProjectInfoGroupByBidStat"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectOverviewRequest"
            resultType="com.fangcang.rfp.common.dto.response.BidStateCountDto">
        select
        COUNT(ph.BID_STATE) AS bidStateCount,
        ph.BID_STATE AS bidState
        from
        htl_rfp.t_project p,
        htl_rfp.t_org o,
        htl_rfp.t_project_intent_hotel ph,
        htl_info.t_hotel h
        where  p.project_id IN (SELECT project_id
                                FROM htl_rfp.t_project_intent_hotel_group
                                WHERE hotel_group_org_id = #{orgId}
                                <if test="userId != null">
                                    and hotel_group_contact_uid = #{userId}
                                </if>
         )
        and p.tender_org_id = o.org_id
        and p.project_id = ph.project_id
        and h.hotelid = ph.hotel_id
        and h.isactive=1

        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND ((ph.BID_ORG_ID = #{orgId} AND ph.BID_ORG_TYPE = 4)
            OR (h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND ph.BID_ORG_TYPE = 2)
            )
        </if>
        GROUP BY ph.BID_STATE
    </select>

    <select id="queryTendProjectNoBidCount"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectOverviewRequest"
            resultType="java.lang.Integer">
        select
        COUNT(*) AS bidStateCount
        from htl_rfp.t_project_intent_hotel_group g,
        htl_rfp.t_project p,
        htl_rfp.t_org o,
        htl_rfp.t_project_intent_hotel ph,
        htl_info.t_hotel h
        where g.project_id = p.project_id
        and p.tender_org_id = o.org_id
        and g.hotel_group_org_id = #{orgId}
        and p.project_id = ph.project_id
        and h.hotelid = ph.hotel_id
        and h.isactive=1
        and ph.bid_state = 0
        AND ph.HOTEL_GROUP_APPROVE_STATUS IS NULL
        AND ph.INVITE_STATUS = 1
        <if test="userId != null">
            and g.hotel_group_contact_uid = #{userId}
        </if>
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach>
        </if>
    </select>

    <select id="queryHotelGroupBidPriceByHotelOrgId" parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupBidPriceRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryAllHotelGroupPriceResponse">
        SELECT
            p.project_id as projectId,
            p.project_name as projectName,
            o.org_name as orgName,
            p.project_type as projectType,
            p.tender_type as tenderType,
            h.hotelid as hotelId,
            h.chn_name as hotelName,
            h.city_name as cityName,
            t.bid_contact_name as bidContactName,
            t.bid_contact_mobile as bidContactMobile,
            t.bid_contact_email as bidContactEmail,
            t.project_intent_hotel_id as projectIntentHotelId,
            t.bid_state as bidState,
            t.bid_org_id as hotelOrgId
        FROM htl_rfp.t_project_intent_hotel t
        JOIN htl_info.t_hotel h
        ON t.hotel_id = h.hotelid
        AND h.isactive = 1
        <if test="cityCode != null and cityCode !=''">
            and h.city = #{cityCode}
        </if>
        <if test="hotelId != null ">
            and h.hotelid = #{hotelId}
        </if>
        AND h.hotelid IN (SELECT HOTEL_ID FROM T_ORG_RELATED_HOTEL WHERE ORG_ID = #{hotelOrgId})
        <if test="hotelName != null and hotelName !=''">
            and h.chn_name like concat(concat('%',#{hotelName}),'%')
        </if>
        JOIN htl_rfp.t_project p ON t.project_id = p.project_id
        JOIN htl_rfp.t_org o ON p.tender_org_id = o.org_id
        WHERE
          t.bid_org_id IN (SELECT ORG_ID FROM T_ORG WHERE ORG_TYPE = 4 AND STATE = 1)
        <if test="projectName != null and projectName !=''">
            AND p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="bidState != null and bidState !=5">
            AND t.bid_state = #{bidState}
        </if>
        order by t.create_time desc
    </select>
    <select id="queryAllHotelGroupPrice"
            parameterType="com.fangcang.rfp.common.dto.request.QueryAllHotelGroupPriceRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryAllHotelGroupPriceResponse">
        select
                p.project_id as projectId,
                p.project_name as projectName,
                o.org_name as orgName,
                p.project_type as projectType,
                p.tender_type as tenderType,
                h.hotelid as hotelId,
                h.chn_name as hotelName,
                h.city_name as cityName,
                h.HOTELBRAND as brandId,
                t.bid_contact_name as bidContactName,
                t.bid_contact_mobile as bidContactMobile,
                t.bid_contact_email as bidContactEmail,
                t.project_intent_hotel_id as projectIntentHotelId,
                t.bid_state as bidState,
                t.bid_org_id as hotelOrgId
        from htl_rfp.t_project_intent_hotel t
        join htl_info.t_hotel h
        on t.hotel_id = h.hotelid
        and h.isactive = 1
        <if test="cityCode != null and cityCode !=''">
            and h.city = #{cityCode}
        </if>
        <if test="hotelId != null ">
            and h.hotelid = #{hotelId}
        </if>
        <if test="hotelId == null and hotelName != null and hotelName !=''">
            and h.chn_name like concat(concat('%',#{hotelName}),'%')
        </if>
        <if test="brandId != null and brandId > 0">
            and h.HOTELBRAND = #{brandId}
        </if>
        join htl_rfp.t_project p
        on t.project_id = p.project_id
        join htl_rfp.t_org o
        on p.tender_org_id = o.org_id
        where
            1=1
        <if test="priceSource == 0">
            <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
                AND ((t.BID_ORG_ID = #{hotelGroupOrgId} AND t.BID_ORG_TYPE = 4)
                OR (h.HOTELBRAND IN
                <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                    #{hotelGroupBrandId}
                </foreach> AND (t.BID_ORG_TYPE = 2 OR t.BID_STATE = 0 OR t.PROJECT_INTENT_HOTEL_ID IS NULL))
             )
            </if>
            <if test="hotelGroupBrandIdList == null and hotelGroupBrandIdList.size() == 0">
                AND t.BID_ORG_ID = #{hotelGroupOrgId}
            </if>
        </if>
        <if test="priceSource == 2">
            AND h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" close=")" separator=",">
                #{hotelGroupBrandId}
            </foreach> AND t.BID_ORG_TYPE = 2
        </if>
        <if test="priceSource == 1">
            AND t.bid_org_id =#{hotelGroupOrgId} AND AND t.BID_ORG_TYPE = 4
        </if>
        <if test="projectName != null and projectName !=''">
            AND p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="bidState != null and bidState !=''">
            AND t.bid_state = #{bidState}
        </if>

        <if test="projectIds != null">
            AND p.project_id IN
            <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
                #{projectId}
            </foreach>
        </if>
        order by t.create_time desc
    </select>

    <select id="queryIntentHotelGroupInfo" resultMap="BaseResultMap"
            parameterType="com.fangcang.rfp.common.dto.request.QueryProjectIntentHotelGroupRequest">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.t_project_intent_hotel_group
        where hotel_group_org_id = #{hotelGroupOrgId}
        <if test="projectIds != null">
            and project_id in
            <foreach collection="projectIds" open="(" close=")" item="projectId" separator="," index="index">
                #{projectId}
            </foreach>
        </if>
        <if test="hotelGroupContactUid != null">
            and hotel_group_contact_uid = #{hotelGroupContactUid}
        </if>
    </select>

    <update id="updateContactInfo" parameterType="com.fangcang.rfp.common.dto.request.UpdateContactInfoDto">
         update htl_rfp.t_project_intent_hotel_group set MODIFIER = #{operatorName} , MODIFY_TIME=sysdate,
         HOTEL_GROUP_CONTACT_UID = null, HOTEL_GROUP_CONTACT_NAME = null
         where HOTEL_GROUP_CONTACT_UID = #{userId}
    </update>

    <update id="updateGroupApprove" parameterType="com.fangcang.rfp.common.entity.ProjectIntentHotelGroup">
        UPDATE
            htl_rfp.t_project_intent_hotel_group
        SET MODIFIER = #{modifier} ,
            MODIFY_TIME = sysdate,
            IS_OPEN_GROUP_APPROVE = #{isOpenGroupApprove}
        WHERE
            PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId}
    </update>

    <select id="queryNeedApproveHotelGroupOrgId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT
            HOTEL_GROUP_ORG_ID
        FROM
            htl_rfp.t_project_intent_hotel_group
        WHERE
            PROJECT_ID = #{projectId}
        AND IS_ACTIVE = 1
        AND IS_OPEN_GROUP_APPROVE = 1
    </select>
</mapper>