<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrderExcludeSupConfigDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.OrderExcludeSupConfig">
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="MERCHANT_CODE" jdbcType="VARCHAR" property="merchantCode" />
    <result column="SUPPLIER_CODE" jdbcType="VARCHAR" property="supplierCode" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="STATE" jdbcType="INTEGER" property="state" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Coumn_List">
    ORG_ID,MERCHANT_CODE,SUPPLIER_CODE,SUPPLIER_NAME,STATE,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.OrderExcludeSupConfig">
    insert into htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG (ORG_ID, MERCHANT_CODE, SUPPLIER_CODE, SUPPLIER_NAME, STATE,
      CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME)
    values (#{orgId,jdbcType=DECIMAL}, #{merchantCode,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{state,jdbcType=DECIMAL},
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <update id="update" parameterType="com.fangcang.rfp.common.entity.OrderExcludeSupConfig">
    update htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG
    set STATE = #{state},
        MODIFIER = #{modifier},
        MODIFY_TIME = SYSDATE
    where ORG_ID = #{orgId} and SUPPLIER_CODE = #{supplierCode}
  </update>

  <select id="selectInfoByOrgIdAndSupplierCode"  parameterType="com.fangcang.rfp.common.entity.OrderExcludeSupConfig" resultMap="BaseResultMap">
    select <include refid="Base_Coumn_List"/>
    from htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG
    where ORG_ID = #{orgId} and SUPPLIER_CODE = #{supplierCode}
  </select>

  <select id="selectOrgSupplierCount" resultType="com.fangcang.rfp.common.dto.response.OrderMonitorConfigResponse">
    SELECT ORG_ID as orgId,
           COUNT(ORG_ID) AS excludeSupplierCount
    FROM  htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG
    WHERE ORG_ID IN
    <foreach collection="list" item="orgId" separator="," open="（" close=")">
      #{orgId}
    </foreach>
    AND STATE = 1
    GROUP BY ORG_ID
  </select>


  <select id="selectExcludeSupplierCodeList" parameterType="com.fangcang.rfp.common.dto.request.OrderExcludeSupplierConfigRequest"
          resultType="com.fangcang.rfp.common.dto.response.OrderExcludeSupplierConfigResponse">
    select t.ORG_ID orgId,
           t.SUPPLIER_CODE supplierCode,
           t.SUPPLIER_NAME supplierName,
           t.MODIFIER modifier,
           t.MODIFY_TIME modifyTime
    from htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG t
    where t.STATE = 1 and t.ORG_ID = #{orgId}
    <if test="supplierCode != null and supplierCode != '' ">
        and t.SUPPLIER_CODE like concat('%',concat(#{supplierCode},'%'))
    </if>
    <if test="supplierName != null and supplierName != '' ">
      and t.SUPPLIER_NAME like concat('%',concat(#{supplierName},'%'))
    </if>
  </select>

  <select id="queryOrderExcludeSupplierConfig" resultType="com.fangcang.rfp.common.dto.response.QueryOrderExcludeSupplierConfigResponse">
    select c.ORG_ID as orgId,
    c.SUPPLIER_CODE as supplierCode,
    c.STATE as state,
    c.SUPPLIER_NAME as supplierName,
    p.project_id as projectId
    from htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG c,
    htl_rfp.t_project p where c.ORG_ID = p.tender_org_id
    and c.STATE=1
    and p.project_id in
    <foreach collection="projectIds" open="(" close=")" item="projectId" separator=","
             index="index">
      #{projectId}
    </foreach>
  </select>

  <select id="selectExcludeSupplierCodeListByDisCodeList" resultType="java.lang.String">
        SELECT DISTINCT SUPPLIER_CODE
        FROM htl_rfp.T_ORDER_EXCLUDE_SUP_CONFIG
        WHERE ORG_ID IN (SELECT ORG_ID FROM htl_rfp.T_ORDER_MONITOR_CONFIG WHERE DISTRIBUTOR_CODE IN
        <foreach collection="distributorCodeList" item="distributorCode" separator="," open="(" close=")">
          #{distributorCode}
        </foreach> AND STATE = 1)
        AND STATE = 1
  </select>

</mapper>