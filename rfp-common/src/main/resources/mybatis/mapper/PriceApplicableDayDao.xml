<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PriceApplicableDayDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PriceApplicableDay">
    <id column="APPLICABLE_DAY_ID" jdbcType="DECIMAL" property="applicableDayId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="PRICE_TYPE" jdbcType="INTEGER" property="priceType" />
    <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate" />
    <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate" />
    <result column="PRICE_CODE" jdbcType="DECIMAL" property="priceCode" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
    APPLICABLE_DAY_ID, PROJECT_ID, HOTEL_ID, PRICE_TYPE, START_DATE, END_DATE, PRICE_CODE, CREATOR,
    CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectPriceApplicableDayList" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PRICE_APPLICABLE_DAY
    where
        PROJECT_ID = #{projectId,jdbcType=BIGINT}
    <if test="hotelId != null">
      AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </if>
    <if test="priceType != null">
      AND PRICE_TYPE = #{priceType,jdbcType=INTEGER}
    </if>
    AND PRICE_TYPE IS NOT NULL
    ORDER BY PRICE_TYPE ASC
  </select>

  <select id="selectOldPriceApplicableDayList" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PRICE_APPLICABLE_DAY
    where
    PROJECT_ID = #{projectId,jdbcType=BIGINT}
    <if test="hotelId != null">
      AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </if>
    <if test="priceCode != null">
      AND PRICE_CODE = #{priceCode,jdbcType=BIGINT}
    </if>
    ORDER BY PRICE_CODE ASC
  </select>

  <select id="selectAllPriceApplicableDayList" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PRICE_APPLICABLE_DAY
    where
    PROJECT_ID = #{projectId,jdbcType=BIGINT}
    <if test="hotelId != null">
      AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </if>
    <if test="priceType != null">
      AND PRICE_TYPE = #{priceType,jdbcType=INTEGER}
    </if>
    ORDER BY PRICE_TYPE ASC
  </select>

  <select id="selectPriceApplicableDayListByProject" parameterType="com.fangcang.rfp.common.dto.request.ProjectHotelBidStrategyRequest" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PRICE_APPLICABLE_DAY
    where 1 =1
    <if test="projectId != null">
      and PROJECT_ID = #{projectId}
    </if>
    <if test="hotelId != null">
      and HOTEL_ID = #{hotelId}
    </if>
    AND PRICE_TYPE IS NOT NULL
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_PRICE_APPLICABLE_DAY
    where APPLICABLE_DAY_ID = #{applicableDayId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByPriceCode" parameterType="java.lang.Long">
    delete from T_PRICE_APPLICABLE_DAY
    where PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectAndHotelId">
    DELETE FROM T_PRICE_APPLICABLE_DAY
    WHERE PROJECT_ID = #{projectId,jdbcType=DECIMAL} AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
  </delete>
  <delete id="deleteByProjectAndHotelIdAndPriceType">
    DELETE FROM T_PRICE_APPLICABLE_DAY
    WHERE PROJECT_ID = #{projectId,jdbcType=DECIMAL} AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL} AND PRICE_TYPE = #{priceType,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay">
    <selectKey keyProperty="applicableDayId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_APPLICABLE_DAY_ID.nextval from dual
    </selectKey>
    insert into T_PRICE_APPLICABLE_DAY (APPLICABLE_DAY_ID, PROJECT_ID, HOTEL_ID, 
       PRICE_TYPE, START_DATE, END_DATE,
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME)
    values (#{applicableDayId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, 
      #{priceType,jdbcType=INTEGER}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP},
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
            SYSDATE)
  </insert>

  <select id="selectNextApplicableDayId" resultType="java.lang.Long">
    SELECT htl_rfp.SEQ_RFP_APPLICABLE_DAY_ID.nextval
    FROM DUAL
  </select>

  <insert id="insertBatchWithId" parameterType="list">
    insert into htl_rfp.T_PRICE_APPLICABLE_DAY (
    APPLICABLE_DAY_ID, PROJECT_ID, HOTEL_ID,
    PRICE_TYPE, START_DATE, END_DATE,
    CREATOR, CREATE_TIME, MODIFIER,
    MODIFY_TIME
    )
    select t.* from(
    <foreach collection="list" item="item" index="index" separator="union all">
      select
      #{item.applicableDayId,jdbcType=BIGINT} AS applicableDayId,
      #{item.projectId,jdbcType=BIGINT} AS projectId,
      #{item.hotelId,jdbcType=BIGINT} AS,
      #{item.priceType,jdbcType=INTEGER} AS priceType,
      #{item.startDate,jdbcType=TIMESTAMP} AS startDate,
      #{item.endDate,jdbcType=TIMESTAMP} AS endDate,
      #{item.creator,jdbcType=VARCHAR} AS creator,
      SYSDATE AS createTime,
      #{item.modifier,jdbcType=VARCHAR} AS modifier,
      SYSDATE AS modifyTime
      from dual
    </foreach>)t
  </insert>
  <insert id="insertBatch" parameterType="list">
    insert into htl_rfp.T_PRICE_APPLICABLE_DAY (
    APPLICABLE_DAY_ID, PROJECT_ID, HOTEL_ID,
    PRICE_TYPE, START_DATE, END_DATE,
    CREATOR, CREATE_TIME, MODIFIER,
    MODIFY_TIME
    )
    select htl_rfp.SEQ_RFP_APPLICABLE_DAY_ID.nextval AS applicableDayId, t.* from(
    <foreach collection="list" item="item" index="index" separator="union all">
      select
      #{item.projectId,jdbcType=BIGINT} AS projectId,
      #{item.hotelId,jdbcType=BIGINT} AS,
      #{item.priceType,jdbcType=INTEGER} AS priceType,
      #{item.startDate,jdbcType=TIMESTAMP} AS startDate,
      #{item.endDate,jdbcType=TIMESTAMP} AS endDate,
      #{item.creator,jdbcType=VARCHAR} AS creator,
      SYSDATE AS createTime,
      #{item.modifier,jdbcType=VARCHAR} AS modifier,
      SYSDATE AS modifyTime
      from dual
    </foreach>)t
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay">
    update T_PRICE_APPLICABLE_DAY
    <set>
      <if test="projectId != null">
        PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      </if>
      <if test="hotelId != null">
        HOTEL_ID = #{hotelId,jdbcType=DECIMAL},
      </if>
      <if test="priceType != null">
        PRICE_TYPE = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where APPLICABLE_DAY_ID = #{applicableDayId,jdbcType=DECIMAL}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.PriceApplicableDay">
    update T_PRICE_APPLICABLE_DAY
    set
      START_DATE = #{startDate,jdbcType=TIMESTAMP},
      END_DATE = #{endDate,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = SYSDATE
    where APPLICABLE_DAY_ID = #{applicableDayId,jdbcType=DECIMAL}
  </update>

  <select id="queryPriceApplicableDayByPriceCodes" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from htl_rfp.T_PRICE_APPLICABLE_DAY
    where  PRICE_CODE in
    <foreach collection="priceCodeSet" open="(" close=")" item="priceCode" separator="," index="index">
      #{priceCode}
    </foreach>
    AND PRICE_TYPE IS NOT NULL
  </select>

</mapper>