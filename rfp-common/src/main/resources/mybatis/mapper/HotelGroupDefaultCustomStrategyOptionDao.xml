<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelGroupDefaultCustomStrategyOptionDao">

    <delete id="deleteByHotelGroupDefaultCustomStrategy" parameterType="com.fangcang.rfp.common.dto.request.DeleteHotelGroupDefaultCustomStrategyDto">
        delete from htl_rfp.T_HG_DEAFULT_STRATEGY_OPTION where
        project_intent_hotel_group_id = #{projectIntentHotelGroupId} and project_id = #{projectId}
        <if test="customTendStrategyIds != null">
            and custom_tend_strategy_id in
            <foreach collection="customTendStrategyIds" open="(" close=")" item="customTendStrategyId" separator="," index="index">
                #{customTendStrategyId}
            </foreach>
        </if>
    </delete>

    <select id="queryByProjectIntentHotelGroupId" resultType="com.fangcang.rfp.common.entity.HotelGroupDefaultCustomStrategyOption">
        SELECT
            t.OPTION_ID AS optionId,
            t.CUSTOM_TEND_STRATEGY_ID AS customTendStrategyId,
            t.PROJECT_INTENT_HOTEL_GROUP_ID AS projectIntentHotelGroupId,
            t.PROJECT_ID AS projectId,
            t.OPTION_NAME AS optionName,
            t.IS_SUPPORT AS isSupport,
            t.CREATOR AS  creator,
            t.CREATE_TIME AS createTime,
            t.MODIFIER AS modifier,
            t.MODIFY_TIME AS modifyTime
        FROM
            htl_rfp.T_HG_DEAFULT_STRATEGY_OPTION t
        LEFT JOIN htl_rfp.T_PROJECT_C_STRATEGY_OPTION t1 ON t.OPTION_ID = t1.OPTION_ID
        WHERE
            t.PROJECT_INTENT_HOTEL_GROUP_ID = #{projectIntentHotelGroupId}
    </select>

    <insert id="batchMergeHotelGroupDefaultCustomStrategyOption" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            MERGE INTO htl_rfp.T_HG_DEAFULT_STRATEGY_OPTION t
            USING dual
            ON (t.OPTION_ID = #{item.optionId} AND
                t.custom_tend_strategy_id = #{item.customTendStrategyId} AND
                t.project_intent_hotel_group_id = #{item.projectIntentHotelGroupId} AND
                t.project_id = #{item.projectId})
            WHEN MATCHED THEN
            UPDATE SET
            t.OPTION_NAME = #{item.optionName,jdbcType=VARCHAR},
            t.IS_SUPPORT = #{item.isSupport,jdbcType=INTEGER},
            t.modifier = #{item.modifier,jdbcType=VARCHAR},
            t.modify_time = sysdate
            WHEN NOT MATCHED THEN
            INSERT (OPTION_ID, CUSTOM_TEND_STRATEGY_ID, PROJECT_INTENT_HOTEL_GROUP_ID,
            PROJECT_ID, OPTION_NAME, IS_SUPPORT, CREATOR,CREATE_TIME, modifier, modify_time)
            VALUES (#{item.optionId}, #{item.customTendStrategyId}, #{item.projectIntentHotelGroupId}, #{item.projectId},
            #{item.optionName}, #{item.isSupport},
            #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
        </foreach>
    </insert>
</mapper>