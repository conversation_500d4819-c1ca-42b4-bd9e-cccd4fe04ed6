<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.BidOperateLogDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.BidOperateLog">
        <result column="BID_OPERATE_LOG_ID" jdbcType="BIGINT" property="bidOperateLogId"/>
        <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId" />
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="ORG_TYPE_ID" jdbcType="INTEGER" property="orgTypeId"/>
        <result column="OPERATOR" jdbcType="VARCHAR" property="operator"/>
        <result column="OPERATE_CONTENT" jdbcType="VARCHAR" property="operateContent"/>
        <result column="OPERATE_TIME" jdbcType="TIMESTAMP" property="operateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        BID_OPERATE_LOG_ID,PROJECT_INTENT_HOTEL_ID , PROJECT_ID, HOTEL_ID, ORG_TYPE_ID, OPERATOR, OPERATE_CONTENT,OPERATE_TIME
    </sql>

    <select id="queryOperateLogList" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.BidOperateLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_BID_OPERATE_LOG
        WHERE
            1 = 1
            <if test="projectIntentHotelId != null and projectIntentHotelId > 0">
                AND PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
            </if>
            <if test="projectId != null and projectId > 0">
                AND PROJECT_ID = #{projectId,jdbcType=BIGINT}
            </if>
            <if test="hotelId != null and hotelId > 0">
                AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
            </if>
        ORDER BY BID_OPERATE_LOG_ID DESC
    </select>

    <insert id="insertBidOperateLog" parameterType="com.fangcang.rfp.common.entity.BidOperateLog">
        <selectKey keyProperty="bidOperateLogId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_BID_OPERATE_LOG.nextval from dual
        </selectKey>
        insert into htl_rfp.T_BID_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides="," >
            BID_OPERATE_LOG_ID,
            PROJECT_INTENT_HOTEL_ID,
            PROJECT_ID,
            HOTEL_ID,
            ORG_TYPE_ID,
            OPERATOR,
            OPERATE_CONTENT,
            OPERATE_TIME
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            #{bidOperateLogId},
            #{projectIntentHotelId,jdbcType=BIGINT},
            #{projectId,jdbcType=BIGINT},
            #{hotelId,jdbcType=BIGINT},
            #{orgTypeId,jdbcType=INTEGER},
            #{operator,jdbcType=VARCHAR},
            #{operateContent,jdbcType=VARCHAR},
            SYSDATE
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="list">
        insert into htl_rfp.T_BID_OPERATE_LOG (
        BID_OPERATE_LOG_ID,
        PROJECT_INTENT_HOTEL_ID,
        PROJECT_ID,
        HOTEL_ID,
        ORG_TYPE_ID,
        OPERATOR,
        OPERATE_CONTENT,
        OPERATE_TIME
        )
        select htl_rfp.SEQ_BID_OPERATE_LOG.nextval AS bidOperateLogId, t.* from(
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.projectIntentHotelId,jdbcType=BIGINT} AS projectIntentHotelId,
            #{item.projectId,jdbcType=BIGINT} AS projectId,
            #{item.hotelId,jdbcType=BIGINT} AS hotelId,
            #{item.orgTypeId,jdbcType=INTEGER} AS orgTypeId,
            #{item.operator,jdbcType=VARCHAR} AS operator,
            #{item.operateContent,jdbcType=VARCHAR} AS operateContent,
            SYSDATE AS operateTime
            from dual
        </foreach>)t
    </insert>

</mapper>