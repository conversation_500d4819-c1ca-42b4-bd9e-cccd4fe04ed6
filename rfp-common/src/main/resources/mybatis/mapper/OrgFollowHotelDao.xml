<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrgFollowHotelDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.OrgFollowHotel">
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORG_ID, HOTEL_ID,CREATOR, CREATE_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.dto.request.AddOrgFollowHotelDto">
        INSERT INTO htl_rfp.T_ORG_FOLLOW_HOTEL (ORG_ID, HOTEL_ID, CREATOR, CREATE_TIME)
        VALUES ( #{orgId,jdbcType=BIGINT},  #{hotelId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR},  sysdate)
    </insert>

    <delete id="delete" parameterType="com.fangcang.rfp.common.dto.request.DeleteOrgFollowHotelDto">
        DELETE FROM
            htl_rfp.T_ORG_FOLLOW_HOTEL
        WHERE ORG_ID = #{orgId,jdbcType=BIGINT}
          AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </delete>

    <select id="getOrgFollowHotel" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM htl_rfp.T_ORG_FOLLOW_HOTEL WHERE ORG_ID = #{orgId} AND HOTEL_ID = #{hotelId}
    </select>

    <select id="queryQueryOrgFollowHotelResponseList" parameterType="com.fangcang.rfp.common.dto.request.QueryOrgFollowHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryOrgFollowHotelResponse">
        SELECT
            ofh.ORG_ID as orgId,
            ofh.HOTEL_ID as hotelId,
            o.ORG_NAME AS orgName,
            h.CHN_NAME AS hotelName,
            h.PROVINCE AS province,
            h.CITY AS city,
            h.HOTEL_GROUP AS hotelGroupId,
            h.HOTELBRAND AS hotelBrandId,
            c.dataname AS cityName,
            p.dataname AS provinceName,
            hg.BRANDNAME AS hotelGroupName,
            hb.BRANDNAME AS hotelBrandName,
            ofh.CREATOR as creator,
            ofh.CREATE_TIME as createTime
        FROM
            htl_rfp.T_ORG_FOLLOW_HOTEL ofh
        LEFT JOIN htl_rfp.T_ORG o ON ofh.ORG_ID = o.ORG_ID
        LEFT JOIN htl_info.T_HOTEL h ON  ofh.HOTEL_ID =  h.HOTELID
        LEFT JOIN htl_base.t_areadata c ON h.CITY = c.datacode AND c.datatype = 3 AND c.countrycode = 'CN'
        LEFT JOIN htl_base.t_areadata p ON h.PROVINCE = p.datacode AND p.datatype = 2
        LEFT JOIN htl_info.T_BRAND hg ON h.HOTEL_GROUP = hg.BRANDID AND hg.GROUPID = 0
        LEFT JOIN htl_info.T_BRAND hb ON h.HOTELBRAND = hb.BRANDID
        WHERE 1=1
            <if test="orgId != null and orgId > 0">
                AND ofh.ORG_ID = #{orgId}
            </if>
            <if test="hotelId != null and hotelId > 0">
                AND ofh.HOTEL_ID = #{hotelId}
            </if>
            <if test="province != null and province != ''">
                AND h.PROVINCE = #{province}
            </if>
            <if test="city != null and city != ''">
                AND h.CITY = #{city}
            </if>
            <if test="hotelGroupId != null and hotelGroupId > 0">
                AND h.HOTEL_GROUP = #{hotelGroupId,jdbcType=BIGINT}
            </if>
            <if test="hotelBrandId != null and hotelBrandId > 0">
                AND h.HOTELBRAND = #{hotelBrandId,jdbcType=BIGINT}
            </if>
            <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                AND ofh.org_id IN
                <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                    #{userRelatedOrgId}
                </foreach>
            </if>
        ORDER BY ofh.CREATE_TIME DESC
    </select>


</mapper>