<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ConvertPriceFailedRecordDao">

    <insert id="insertData" parameterType="com.fangcang.rfp.common.entity.ConvertPriceFailedRecord">
        insert into HTL_RFP.T_CONVERT_PRICE_FAILED_RECORD(
            PROJECT_INTENT_HOTEL_ID,
            FAILED_REASON,
            CREATOR,
            CREATE_TIME
        ) VALUES (
            #{projectIntentHotelId},
            #{failedReason},
            #{creator},
            SYSDATE
        )
    </insert>

</mapper>