<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.ContractTemplateDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.request.ContractTemplateDto" >
    <id column="template_id" property="templateId" jdbcType="BIGINT" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="template_type" property="templateType" jdbcType="INTEGER" />
    <result column="template_biz_type" property="templateBizType" jdbcType="INTEGER" />
    <result column="template_name" property="templateName" jdbcType="VARCHAR" />
    <result column="template_state" property="templateState" jdbcType="INTEGER" />
    <result column="main_template_url" property="mainTemplateUrl" jdbcType="VARCHAR" />
    <result column="final_template_url" property="finalTemplateUrl" jdbcType="VARCHAR" />
    <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
        template_id,org_id,
        template_type,template_biz_type,
        template_name,template_state,
        main_template_url,final_template_url,
        org_name, creator, create_time,
        modifier, modify_time
  </sql>

  <insert id="insertContractTemplate" parameterType="com.fangcang.rfp.common.entity.ContractTemplate" >
    <selectKey keyProperty="templateId" resultType="_long" order="BEFORE">
        select htl_rfp.SEQ_RFP_CONTRACT_TEMPLATE_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.T_RFP_CONTRACT_TEMPLATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
        TEMPLATE_ID,
        ORG_ID,
        template_type,
        template_biz_type,
        template_name,
        template_state,
        MAIN_TEMPLATE_URL,
        FINAL_TEMPLATE_URL,
        CREATOR,
        CREATE_TIME
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        #{templateId},
        #{orgId},
        #{templateType,jdbcType=INTEGER},
        #{templateBizType,jdbcType=INTEGER},
        #{templateName,jdbcType=VARCHAR},
        #{templateState,jdbcType=INTEGER},
        #{mainTemplateUrl,jdbcType=VARCHAR},
        #{finalTemplateUrl,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        SYSDATE

    </trim>
  </insert>

  <select id="queryContractTemplateList"  resultMap="BaseResultMap"
          parameterType="com.fangcang.rfp.common.dto.request.ContractTemplateRequest">
    select t1.template_id,
           t1.org_id,
           t2.org_name,
           t1.template_type,
           t1.template_biz_type,
           t1.template_name,
           t1.template_state,
           t1.main_template_url,
           t1.final_template_url
    from htl_rfp.T_RFP_CONTRACT_TEMPLATE t1
    left join htl_rfp.t_org t2 on t1.ORG_ID = t2.ORG_ID
    where 1 = 1
    <if test="templateType != null  ">
        and t1.template_type = #{templateType}
    </if>
    <if test="templateBizType != null  ">
        and t1.template_biz_type = #{templateBizType}
    </if>
    <if test="orgId != null  ">
      and t1.org_id = #{orgId}
    </if>
  </select>

    <update id="updateContractTemplate" parameterType="com.fangcang.rfp.common.entity.ContractTemplate">
        update htl_rfp.T_RFP_CONTRACT_TEMPLATE
        set MAIN_TEMPLATE_URL = #{mainTemplateUrl}
        where TEMPLATE_ID = #{templateId}
    </update>

    <select id="queryAttachmentByMainTemplateUrl" parameterType="com.fangcang.rfp.common.dto.request.ContractTemplateRequest"
            resultType="com.fangcang.rfp.common.entity.Attachment">
        select ta.FILE_NAME fileName,
               ta.FILE_FTP_PATH fileFtpPath,
               ta.FILE_URL fileUrl
        from htl_rfp.T_RFP_CONTRACT_TEMPLATE trct
        left join htl_rfp.T_ATTACHMENT ta on trct.MAIN_TEMPLATE_URL = ta.FILE_URL
        where trct.TEMPLATE_ID = #{templateId,jdbcType=BIGINT}
    </select>

    <select id="selectCommonContractTemplate" resultMap="BaseResultMap">
        select  template_id,org_id,template_type,template_biz_type,
        template_name,template_state,main_template_url,final_template_url, creator, create_time,
        modifier, modify_time
        from htl_rfp.T_RFP_CONTRACT_TEMPLATE
        where org_id = 0 and template_biz_type = #{templateBizType}
    </select>

    <select id="selectByPrimaryKey" parameterType="long" resultType="com.fangcang.rfp.common.entity.ContractTemplate">
        select template_id templateId,
               org_id orgId,
               template_type templateType,
               template_biz_type templateBizType,
               template_name templateName,
               template_state templateState,
               main_template_url mainTemplateUrl,
               final_template_url finalTemplateUrl
        from htl_rfp.T_RFP_CONTRACT_TEMPLATE
        where TEMPLATE_ID = #{templateId}
    </select>

</mapper>
