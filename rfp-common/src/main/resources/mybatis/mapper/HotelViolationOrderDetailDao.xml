<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelViolationOrderDetailDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelViolationOrderDetail">
        <result column="VIOLATIONS_MONITOR_ID" jdbcType="DECIMAL" property="violationsMonitorId"/>
        <result column="ORDER_CODE" jdbcType="VARCHAR" property="orderCode"/>
        <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId"/>
        <result column="ROOMNUM" jdbcType="DECIMAL" property="roomnum"/>
        <result column="NIGHTS" jdbcType="DECIMAL" property="nights"/>
        <result column="BREAKFASTNUM" jdbcType="DECIMAL" property="breakfastnum"/>
        <result column="ORDER_AMOUNT" jdbcType="DECIMAL" property="orderAmount"/>
        <result column="OTA_AMOUNT" jdbcType="DECIMAL" property="otaAmount"/>
        <result column="SAVE_AMOUNT" jdbcType="DECIMAL" property="saveAmount"/>
        <result column="CREATE_ORDER_TIME" jdbcType="TIMESTAMP" property="createOrderTime"/>
        <result column="OTA_PRICE_IDS" jdbcType="VARCHAR" property="otaPriceIds"/>
        <result column="ORDER_DAILY_PRICE" jdbcType="VARCHAR" property="orderDailyPrice"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
     VIOLATIONS_MONITOR_ID, ORDER_CODE, ORDER_ID, HOTEL_ID, ROOM_TYPE_ID, ROOMNUM,
      NIGHTS, BREAKFASTNUM, ORDER_AMOUNT,  OTA_AMOUNT, SAVE_AMOUNT, CREATE_ORDER_TIME,
      OTA_PRICE_IDS, ORDER_DAILY_PRICE, CREATE_TIME
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into htl_rfp.T_HOTEL_VIOLATION_ORDER_DETAIL (VIOLATIONS_MONITOR_ID, ORDER_CODE, ORDER_ID,
        HOTEL_ID, ROOM_TYPE_ID, ROOMNUM,
        NIGHTS, BREAKFASTNUM, ORDER_AMOUNT,
        OTA_AMOUNT, SAVE_AMOUNT, CREATE_ORDER_TIME,
        OTA_PRICE_IDS, ORDER_DAILY_PRICE, CREATE_TIME
        )
        <foreach collection="hotelViolationOrderDetails" item="item" index="index" separator="union all">
            ( select #{item.violationsMonitorId,jdbcType=DECIMAL},
            #{item.orderCode,jdbcType=VARCHAR},
            #{item.orderId,jdbcType=DECIMAL},
            #{item.hotelId,jdbcType=DECIMAL},
            #{item.roomTypeId,jdbcType=DECIMAL},
            #{item.roomnum,jdbcType=DECIMAL},
            #{item.nights,jdbcType=DECIMAL},
            #{item.breakfastnum,jdbcType=DECIMAL},
            #{item.orderAmount,jdbcType=DECIMAL},
            #{item.otaAmount,jdbcType=DECIMAL},
            #{item.saveAmount,jdbcType=DECIMAL},
            #{item.createOrderTime,jdbcType=TIMESTAMP},
            #{item.otaPriceIds,jdbcType=VARCHAR},
            #{item.orderDailyPrice,jdbcType=VARCHAR},
            sysdate from dual)
        </foreach>
    </insert>

    <select id="queryNeedHotelOrderMonitor"
            resultType="com.fangcang.rfp.common.dto.response.NeedHotelOrderMonitorResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryNeedHotelOrderMonitorRequest">
        SELECT o.ID as orderId, o.ordercode as orderCode, o.ordersum AS orderSum,temp.total_minprice as otaAmount,d.distributorcode AS
            distributorCode,temp.id_list as otaPriceIds,o.hotelid as hotelId,o.roomnum
        FROM htl_order.t_hotel_order o
        INNER JOIN htl_order.t_distributor d ON o.id = d.orderid
        INNER JOIN (
            SELECT otaTemp.orderid, SUM(otaTemp.minprice) AS total_minprice,LISTAGG(otaTemp.id, ',') WITHIN GROUP (ORDER BY
                otaTemp.minprice) AS id_list
            FROM (
                SELECT ota.orderid, MIN(ota.minprice) AS minprice,MIN(ota.id) AS id
                FROM HTL_ORDER.T_OTA_ROOMRATEDETAILS ota
                WHERE ota.createDate &gt;= TO_DATE(#{queryStartDate}, 'YYYY-MM-DD HH24:MI:SS')
                AND ota.createDate &lt;= TO_DATE(#{queryEndDate}, 'YYYY-MM-DD HH24:MI:SS')
                GROUP BY ota.orderid, ota.roomdate
                ) otaTemp
            GROUP BY otaTemp.orderid
        ) temp ON o.id = temp.orderid
        WHERE o.orderstate = 3 and d.distributorcode in
        <foreach collection="distributorCodes" open="(" close=")" item="distributorCode" separator="," index="index">
            #{distributorCode}
        </foreach>
    </select>

    <select id="queryExcludeSupplierOrderId" resultType="java.lang.Long">
        SELECT ORDERID
        FROM htl_order.T_HOTELSUPPLYORDER
        WHERE
            ORDERID IN
            <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
                #{orderId}
            </foreach>
        AND SUPPLIERCODE IN
        <foreach collection="excludeSupplierCodeList" item="supplierCode" separator="," open="(" close=")">
            #{supplierCode}
        </foreach>
    </select>

    <select id="queryByOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_VIOLATION_ORDER_DETAIL
        where ORDER_CODE in
        <foreach collection="orderCodes" open="(" close=")" item="orderCode" separator="," index="index">
            #{orderCode}
        </foreach>
    </select>

    <select id="queryHotelOrderInfo" resultType="com.fangcang.rfp.common.dto.response.QueryHotelOrderInfoResponse">
        select t.id as orderId,
        t.ordercode as orderCode,
        t.hotelid as hotelId,
        t.roomtypeid as roomTypeId,
        t.checkindate as checkInDate,
        t.checkoutdate as checkOutDate,
        t.roomnum as roomNum,
        t.createtime as createTime
        from htl_order.t_hotel_order t
        where t.ordercode in
        <foreach collection="orderCodes" open="(" close=")" item="orderCode" separator="," index="index">
            #{orderCode}
        </foreach>
    </select>

    <select id="queryHotelOrderRoomRateDetails"
            resultType="com.fangcang.rfp.common.dto.response.HotelOrderRoomRateDetailsResponse">
        select t.roomdate as roomDate,t.roomprice as roomPrice,t.breakfastnum AS breakfastNum,t.orderid as orderId
        from htl_order.T_ROOMRATEDETAILS t where t.orderid in
        <foreach collection="orderIds" open="(" close=")" item="orderId" separator="," index="index">
            #{orderId}
        </foreach>
    </select>

    <select id="queryByViolationsMonitorId" resultType="com.fangcang.rfp.common.dto.response.OrderDetailDto">
        select t.order_code   as orderCode,
       r.roomtypename as roomName,
       t.roomnum      as roomNum,
       t.nights as nights,
       t.breakfastnum as breakfastnum,
       t.order_amount as orderAmount,
       t.ota_amount   as otherAmount,
       t.save_amount  as saveAmount,
       t.create_order_time as createOrderTime,
       T.create_time as sendTime,
       t.order_daily_price as orderDailyPrice,
       t.ota_price_ids as otaPriceIds
    from htl_rfp.t_hotel_violation_order_detail t, htl_info.t_roomtype r
    where t.hotel_id = r.hotelid
    and t.room_type_id = r.roomtypeid
    and t.violations_monitor_id = #{violationsMonitorId}
    </select>
    <select id="queryOtaOrderPrices" resultType="com.fangcang.rfp.common.dto.response.OtaOrderPriceDto" parameterType="java.util.List">
        select t.roomdate as roomDate,t.minprice as roomPrice
        from htl_order.T_OTA_ROOMRATEDETAILS t where t.id in
        <foreach collection="otaPriceIds" open="(" close=")" item="otaPriceId" separator="," index="index">
            #{otaPriceId}
        </foreach>
    </select>
</mapper>