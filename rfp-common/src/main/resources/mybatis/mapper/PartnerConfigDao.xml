<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PartnerConfigDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PartnerConfig">
        <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode"/>
        <result column="SECRET_KEY" jdbcType="VARCHAR" property="secretKey"/>
        <result column="PARTNER_NAME" jdbcType="VARCHAR" property="partnerName"/>
        <result column="DOMAIN_NAME" jdbcType="VARCHAR" property="domainName"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHANNEL_CODE, SECRET_KEY, PARTNER_NAME, DOMAIN_NAME, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

  <select id="selectByChannelCode" resultMap="BaseResultMap">
      SELECT
          <include refid="Base_Column_List"/>
      FROM HTL_RFP.T_PARTNER_CONFIG
      WHERE CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
  </select>
</mapper>