<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelApplyOnlineLogDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelApplyOnlineLog">
        <result column="HOTEL_ONLINE_ID" jdbcType="DECIMAL" property="hotelOnlineId"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="CONTENT_TYPE" jdbcType="DECIMAL" property="contentType"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    HOTEL_ONLINE_ID, CONTENT, CONTENT_TYPE, CREATOR, CREATE_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelApplyOnlineLog">

    insert into htl_rfp.T_HOTEL_APPLY_ONLINE_LOG (HOTEL_ONLINE_ID, CONTENT, CONTENT_TYPE,
      CREATOR, CREATE_TIME)
    values (#{hotelOnlineId,jdbcType=DECIMAL}, #{content,jdbcType=VARCHAR}, #{contentType,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, sysdate)
  </insert>

    <select id="queryHotelApplyOnlineLogDao" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_APPLY_ONLINE_LOG  where HOTEL_ONLINE_ID = #{hotelOnlineId}
        <if test="contentType != null and contentType !=''">
           and CONTENT_TYPE = #{contentType}
        </if>
        order by CREATE_TIME desc
    </select>

</mapper>