<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelGroupDefaultApplicableDayDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelGroupDefaultApplicableDay">
        <id column="DEFAULT_APPLICABLE_DAY_ID" jdbcType="DECIMAL" property="defaultApplicableDayId"/>
        <result column="PROJECT_INTENT_HOTEL_GROUP_ID" jdbcType="DECIMAL" property="projectIntentHotelGroupId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="PRICE_TYPE" jdbcType="INTEGER" property="priceType"/>
        <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        DEFAULT_APPLICABLE_DAY_ID, PROJECT_INTENT_HOTEL_GROUP_ID, PROJECT_ID, PRICE_TYPE, START_DATE,
    END_DATE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>
    <select id="queryHotelGroupDefaultApplicableDay" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_HGRP_DEFAULT_APPLICABLE_DAY
        WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId}
    </select>
    <delete id="deleteHotelGroupDefaultApplicableDay">
    DELETE FROM T_HGRP_DEFAULT_APPLICABLE_DAY
    WHERE project_intent_hotel_group_id = #{projectIntentHotelGroupId} AND project_id = #{projectId}
    </delete>
    <!-- 定义insertBatch方法 -->
    <insert id="insertBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO htl_rfp.T_HGRP_DEFAULT_APPLICABLE_DAY (default_applicable_day_id,
            project_intent_hotel_group_id,
            project_id, price_type, start_date, end_date, creator, create_time, modifier, modify_time)
            VALUES (htl_rfp.SEQ_HGRP_DEFAULT_APPLICABLEDAY.nextval, #{item.projectIntentHotelGroupId}, #{item.projectId},
            #{item.priceType}, #{item.startDate},
            #{item.endDate}, #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate)
        </foreach>
    </insert>
</mapper>