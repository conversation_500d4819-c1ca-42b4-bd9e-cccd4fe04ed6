<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.LanyonImportDataDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.LanyonImportData">
        <result column="LANYON_IMPORT_DATA_ID" jdbcType="BIGINT" property="lanyonImportDataId"/>
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="DATA_TYPE" jdbcType="INTEGER" property="dataType"/>
        <result column="JSON_DATA" jdbcType="CLOB" property="jsonData"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        LANYON_IMPORT_DATA_ID, PROJECT_ID, HOTEL_ID, DATA_TYPE, JSON_DATA,
        CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.LanyonImportData" keyProperty="lanyonImportDataId"
            useGeneratedKeys="true">
        <selectKey keyProperty="lanyonImportDataId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_LANYON_IMPORT_DATA.nextval from dual
        </selectKey>
        insert into htl_rfp.T_LANYON_IMPORT_DATA (LANYON_IMPORT_DATA_ID, PROJECT_ID, HOTEL_ID, DATA_TYPE,
        JSON_DATA, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
        )
        values (#{lanyonImportDataId}, #{projectId,jdbcType=BIGINT}, #{hotelId,jdbcType=BIGINT},#{dataType,jdbcType=INTEGER},
        #{jsonData,jdbcType=CLOB}, #{creator,jdbcType=VARCHAR}, sysdate, #{modifier,jdbcType=VARCHAR}, sysdate
        )
    </insert>

    <select id="getByProjectHotelId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_LANYON_IMPORT_DATA
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        AND DATA_TYPE = #{dataType,jdbcType=INTEGER}
    </select>

    <select id="getDataCountByProjectHotelId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM htl_rfp.T_LANYON_IMPORT_DATA
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </select>


    <update id="update" parameterType="com.fangcang.rfp.common.entity.LanyonImportData">
        UPDATE
            htl_rfp.T_LANYON_IMPORT_DATA
        SET
            JSON_DATA = #{jsonData,jdbcType=CLOB},
            MODIFY_TIME = SYSDATE
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
        AND DATA_TYPE = #{dataType,jdbcType=INTEGER}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_LANYON_IMPORT_DATA
        WHERE LANYON_IMPORT_DATA_ID = #{lanyonImportDataId,jdbcType=BIGINT}
    </select>

</mapper>