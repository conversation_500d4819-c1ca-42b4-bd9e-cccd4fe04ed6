<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.OrgPoiDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.response.OrgPoiResponse" >
    <id column="poi_id" property="poiId" jdbcType="BIGINT" />
    <result column="baidu_poi_id" property="baiDuPoiId" jdbcType="VARCHAR" />
    <result column="poi_name" property="poiName" jdbcType="VARCHAR" />
    <result column="poi_address" property="poiAddress" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="city_code" property="cityCode" jdbcType="VARCHAR" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="lng_baidu" property="lngBaiDu" jdbcType="INTEGER" />
    <result column="lat_baidu" property="latBaiDu" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    
  </resultMap>

  <sql id="Base_Column_List" >
    poi_id, baidu_poi_id, poi_name, poi_address, org_id, city_code,
    city_name, state, lng_baidu, lat_baidu, creator,
    create_time, modifier,
    modify_time
  </sql>

  <select id="queryOrgPoiPage" resultType="com.fangcang.rfp.common.dto.response.OrgPoiResponse"
          parameterType="com.fangcang.rfp.common.dto.request.OrgPoiRequest">
    select  t1.POI_ID poiId,
            t1.BAIDU_POI_ID baiDuPoiId,
            t1.POI_NAME poiName,
            t1.POI_ADDRESS poiAddress,
            t1.CITY_CODE cityCode,
            t1.CITY_NAME cityName,
            t1.STATE state,
            t1.LNG_BAIDU lngBaiDu,
            t1.LAT_BAIDU latBaiDu,
           t2.org_id orgId,
           t2.ORG_NAME orgName
    from htl_rfp.t_org_poi t1
    join htl_rfp.t_org t2 on t1.ORG_ID = t2.ORG_ID
    where 1 = 1 and t1.STATE = 1
    <if test="poiName != null and poiName != '' ">
      and t1.POI_NAME like concat(concat('%',#{poiName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="cityName != null and cityName != '' ">
      and t1.CITY_NAME like concat(concat('%',#{cityName,jdbcType=VARCHAR}),'%')
    </if>
    <if test="orgId != null and orgId != '' ">
      and t1.ORG_ID = #{orgId}
    </if>
    <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
      AND t2.org_id IN
      <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
        #{userRelatedOrgId}
      </foreach>
    </if>
  </select>

  <insert id="addOrgPoiInfo" parameterType="com.fangcang.rfp.common.entity.OrgPoi">
    <selectKey keyProperty="poiId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_ORG_POI_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.t_org_poi
    <trim prefix="(" suffix=")" suffixOverrides="," >
      POI_ID,
        BAIDU_POI_ID,
        POI_NAME,
        POI_ADDRESS,
        ORG_ID,
        CITY_CODE,
        CITY_NAME,
        STATE,
        LNG_BAIDU,
        LAT_BAIDU,
        CREATOR,
        CREATE_TIME
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
        #{poiId},
        #{baiDuPoiId},
        #{poiName,jdbcType=VARCHAR},
        #{poiAddress,jdbcType=VARCHAR},
        #{orgId},
        #{cityCode},
        #{cityName},
        #{state},
        #{lngBaiDu},
        #{latBaiDu},
        #{creator,jdbcType=VARCHAR},
        SYSDATE
    </trim>
  </insert>


  <insert id="addForUploadOrgPoiInfo" parameterType="com.fangcang.rfp.common.entity.OrgPoi">
    <selectKey keyProperty="poiId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_ORG_POI_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.t_org_poi
    <trim prefix="(" suffix=")" suffixOverrides="," >
      POI_ID,
      POI_NAME,
      POI_ADDRESS,
      ORG_ID,
      CITY_CODE,
      CITY_NAME,
      STATE,
      LNG_BAIDU,
      LAT_BAIDU,
      CREATOR,
      CREATE_TIME
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{poiId},
      #{poiName,jdbcType=VARCHAR},
      #{poiAddress,jdbcType=VARCHAR},
      #{orgId},
      #{cityCode},
      #{cityName},
      #{state},
      #{lngBaiDu},
      #{latBaiDu},
      #{creator,jdbcType=VARCHAR},
      SYSDATE
    </trim>
  </insert>

  <update id="updateOrgPoiInfo" parameterType="com.fangcang.rfp.common.entity.OrgPoi">
    update htl_rfp.t_org_poi
    <set >
      <if test="cityCode != null" >
        CITY_CODE = #{cityCode},
      </if>
      <if test="cityName != null" >
        CITY_NAME = #{cityName},
      </if>
      <if test="poiName != null" >
        POI_NAME = #{poiName},
      </if>
      <if test="poiAddress != null" >
        POI_ADDRESS = #{poiAddress},
      </if>
      <if test="lngBaiDu != null" >
        LNG_BAIDU = #{lngBaiDu},
      </if>
      <if test="latBaiDu != null" >
        LAT_BAIDU = #{latBaiDu},
      </if>
      <if test="state != null" >
        STATE = #{state},
      </if>
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = SYSDATE
    </set>
    where POI_ID = #{poiId}
  </update>


  <select id="selectPoiCountByOrgAndBaiduPoi" parameterType="com.fangcang.rfp.common.entity.OrgPoi" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from htl_rfp.t_org_poi
    where ORG_ID = #{orgId} and BAIDU_POI_ID = #{baiDuPoiId}
  </select>

  <select id="selectOrgPoiByPoiNameAndCityCode" parameterType="com.fangcang.rfp.common.entity.OrgPoi" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from htl_rfp.t_org_poi
    where ORG_ID = #{orgId} AND POI_NAME = #{poiName} AND CITY_CODE = #{cityCode}
  </select>

  <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from htl_rfp.t_org_poi
    where POI_ID = #{poiId}
  </select>

  <select id="queryPoiLikeName" parameterType="com.fangcang.rfp.common.dto.request.OrgPoiRequest" resultType="com.fangcang.rfp.common.dto.response.OrgPoiResponse">
    select
    POI_ID poiId,
    BAIDU_POI_ID baiDuPoiId,
    POI_NAME poiName,
    POI_ADDRESS poiAddress,
    CITY_CODE cityCode,
    CITY_NAME cityName,
    STATE state,
    LNG_BAIDU lngBaiDu,
    LAT_BAIDU latBaiDu
    from htl_rfp.t_org_poi
    where ORG_ID = #{orgId} and STATE =1
    <if test="poiName != null  and poiName != ''">
      and poi_name like CONCAT(CONCAT('%',#{poiName}),'%')
      order by UTL_MATCH.edit_distance_similarity(LTRIM(rTRIM(#{poiName}))  ,poi_name) desc
    </if>
  </select>

  <select id="queryCityName" resultType="java.lang.String">
    SELECT
      DATANAME
    FROM
      htl_base.t_areadata
    WHERE
      DATACODE = #{cityCode}
    AND DATATYPE = 3 AND countrycode = 'CN'
  </select>

  <select id="queryCityCode" resultType="java.lang.String">
    SELECT
      DATACODE
    FROM
      htl_base.t_areadata
    WHERE
      DATANAME = #{cityName}
      AND DATATYPE = 3 AND countrycode = 'CN'
  </select>


</mapper>
