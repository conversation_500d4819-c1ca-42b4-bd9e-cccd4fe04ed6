<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ContractExternalDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ContractExternal">
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId"/>
        <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode" />
        <result column="CONTRACT_STATE" jdbcType="INTEGER" property="contractState"/>
        <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl"/>
        <result column="SIGN_METHOD" jdbcType="INTEGER" property="signMethod"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="SIGN_DATE" jdbcType="TIMESTAMP" property="signDate"/>
        <result column="SIGNED_CONTRACT_URL" jdbcType="VARCHAR" property="signedContractUrl"/>
        <result column="UPLOAD_STATE" jdbcType="INTEGER" property="uploadState"/>
        <result column="SIGN_CONFIG" jdbcType="VARCHAR" property="signConfig"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
       CONTRACT_ID,CONTRACT_CODE,CONTRACT_STATE, CONTRACT_URL,SIGN_METHOD, CREATOR,
       CREATE_TIME, MODIFIER, MODIFY_TIME, SIGN_DATE, SIGNED_CONTRACT_URL,UPLOAD_STATE,SIGN_CONFIG,REMARK
    </sql>

<!--    <select id="selectByProjectBussinessId" resultMap="BaseResultMap">-->
<!--        select-->
<!--        <include refid="Base_Column_List"/>-->
<!--        from htl_rfp.T_RFP_CONTRACT-->
<!--        where project_bussiness_id in-->
<!--        <foreach collection="projectBussinessIds" open="(" close=")" item="projectBussinessId" separator=","-->
<!--                 index="index">-->
<!--            #{projectBussinessId}-->
<!--        </foreach>-->
<!--    </select>-->

<!--    <select id="checkContractIsRepeat" parameterType="com.fangcang.rfp.common.entity.Contract" resultType="int">-->
<!--        select count(1)-->
<!--        from htl_rfp.T_RFP_CONTRACT-->
<!--        where PROJECT_ID = #{projectId} and PROJECT_BUSSINESS_ID = #{projectBussinessId}  and CONTRACT_BIZ_TYPE = #{contractBizType}-->
<!--    </select>-->

<!--    <select id="selectContractList" parameterType="com.fangcang.rfp.common.dto.request.ContractQueryRequest"-->
<!--            resultType="com.fangcang.rfp.common.dto.response.ContractResponse">-->
<!--        select trc.CONTRACT_ID contractId,-->
<!--               trc.CONTRACT_CODE contractCode,-->
<!--               trc.PROJECT_ID projectId,-->
<!--               trc.TEMPLATE_ID templateId,-->
<!--               trc.PROJECT_BUSSINESS_ID projectBussinessId,-->
<!--               tp.PROJECT_NAME projectName,-->
<!--               tp.TENDER_TYPE tenderType,-->
<!--               trc.PARTY_A_ORG_ID partyAorgId,-->
<!--               toA.ORG_NAME partyAorgName,-->
<!--               trc.PARTY_B_ORG_ID partyBorgId,-->
<!--               toB.ORG_NAME partyBorgName,-->
<!--               trc.PARTY_A_SUBJECT_ID partyAsubjectId,-->
<!--               tosA.SUBJECT_NAME partyAsubjectName,-->
<!--               tosA.cert_code partyAcertCode,-->
<!--               trc.PARTY_B_SUBJECT_ID partyBsubjectId,-->
<!--               tosB.SUBJECT_NAME partyBsubjectName,-->
<!--               tosB.cert_code partyBcertCode,-->
<!--               trc.CONTRACT_BIZ_TYPE contractBizType,-->
<!--               trc.CONTRACT_STATE contractState,-->
<!--               trc.SIGN_METHOD signMethod,-->
<!--               trc.SIGN_DATE signDate,-->
<!--               trc.CREATOR creator-->
<!--        from htl_rfp.T_RFP_CONTRACT trc-->
<!--        left join htl_rfp.T_PROJECT tp on trc.PROJECT_ID = tp.PROJECT_ID-->
<!--        left join htl_rfp.T_ORG toA on toA.ORG_ID = trc.PARTY_A_ORG_ID-->
<!--        left join htl_rfp.T_ORG toB on toB.ORG_ID = trc.PARTY_B_ORG_ID-->
<!--        left join htl_rfp.T_ORG_SUBJECT tosA on tosA.SUBJECT_ID = trc.PARTY_A_SUBJECT_ID-->
<!--        left join htl_rfp.T_ORG_SUBJECT tosB on tosB.SUBJECT_ID = trc.PARTY_B_SUBJECT_ID-->
<!--        where 1=1-->
<!--        <if test="orgId != null">-->
<!--            and ( trc.PARTY_A_ORG_ID = #{orgId} or trc.PARTY_B_ORG_ID = #{orgId}  )-->
<!--        </if>-->
<!--        <if test="projectName != null and projectName != '' ">-->
<!--            and tp.PROJECT_NAME like CONCAT(CONCAT('%',#{projectName,jdbcType=VARCHAR}),'%')-->
<!--        </if>-->
<!--        <if test="partyAorgId != null ">-->
<!--            and trc.PARTY_A_ORG_ID = #{partyAorgId}-->
<!--        </if>-->
<!--        <if test="subjectAname != null and subjectAname != '' ">-->
<!--            and tosA.SUBJECT_NAME like CONCAT(CONCAT('%',#{subjectAname,jdbcType=VARCHAR}),'%')-->
<!--        </if>-->
<!--        <if test="partyBorgId != null ">-->
<!--            and trc.PARTY_B_ORG_ID = #{partyBorgId}-->
<!--        </if>-->
<!--        <if test="subjectBname != null and subjectBname != '' ">-->
<!--            and tosB.SUBJECT_NAME like CONCAT(CONCAT('%',#{subjectBname,jdbcType=VARCHAR}),'%')-->
<!--        </if>-->
<!--        <if test="signMethod != null">-->
<!--             and trc.SIGN_METHOD = #{signMethod}-->
<!--        </if>-->
<!--        <if test="contractState != null">-->
<!--            and trc.CONTRACT_STATE = #{contractState}-->
<!--        </if>-->
<!--        <if test="contractBizType != null">-->
<!--            and trc.CONTRACT_BIZ_TYPE = #{contractBizType}-->
<!--        </if>-->
<!--        <if test="projectBussinessIdList != null">-->
<!--            and PROJECT_BUSSINESS_ID in-->
<!--            <foreach collection="projectBussinessIdList" open="(" close=")" item="projectBussinessId" separator=","-->
<!--                     index="index">-->
<!--                #{projectBussinessId}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        order by trc.CREATE_TIME desc-->
<!--    </select>-->

    <insert id="insertContract" parameterType="com.fangcang.rfp.common.entity.ContractExternal">
        <selectKey keyProperty="contractId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_RFP_CONTRACT_EXTERNAL_ID.nextval from dual
        </selectKey>
        insert into htl_rfp.T_RFP_CONTRACT_EXTERNAL
        <trim prefix="(" suffix=")" suffixOverrides="," >
            CONTRACT_ID,
            CONTRACT_CODE,
            CONTRACT_STATE,
            CONTRACT_URL,
            SIGN_METHOD,
            CREATOR,
            CREATE_TIME,
            MODIFIER,
            MODIFY_TIME,
            SIGN_DATE,
            REMARK,
            SIGN_CONFIG
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            #{contractId},
            #{contractCode},
            #{contractState,jdbcType=INTEGER},
            #{contractUrl,jdbcType=VARCHAR},
            #{signMethod,jdbcType=INTEGER},
            #{creator,jdbcType=VARCHAR},
            SYSDATE,
            #{modifier,jdbcType=VARCHAR},
            SYSDATE,
            #{signDate,jdbcType=DATE},
            #{remark,jdbcType=VARCHAR},
            #{signConfig,jdbcType=VARCHAR}
        </trim>
    </insert>

<!--    <insert id="batchAddContract" parameterType="java.util.List">-->
<!--        insert into htl_rfp.T_RFP_CONTRACT (CONTRACT_ID, CONTRACT_CODE,PROJECT_ID, PROJECT_BUSSINESS_ID,-->
<!--        PARTY_A_ORG_ID, PARTY_B_ORG_ID, PARTY_A_SUBJECT_ID,PARTY_B_SUBJECT_ID,-->
<!--        CONTRACT_BIZ_TYPE,CONTRACT_STATE,CONTRACT_URL,TEMPLATE_ID,-->
<!--        SIGN_METHOD,CREATOR,CREATE_TIME,-->
<!--        MODIFIER,MODIFY_TIME,SIGN_DATE,REMARK-->
<!--        )-->
<!--        select  htl_rfp.SEQ_RFP_CONTRACT_ID.nextval as contractId, t.* from(-->
<!--        <foreach collection="list" item="item" index="index" separator="union all">-->
<!--            select fun_gen_rfp_contract_code(#{item.contractBizType,jdbcType=INTEGER} ,#{item.signMethod,jdbcType=INTEGER}) as contractCode,#{item.projectId,jdbcType=BIGINT} as projectId, #{item.projectBussinessId,jdbcType=BIGINT} as projectBussinessId,-->
<!--            #{item.partyAorgId,jdbcType=BIGINT} as partyAorgId,#{item.partyBorgId,jdbcType=BIGINT} as partyBorgId,#{item.partyAsubjectId,jdbcType=BIGINT} as partyAsubjectId,#{item.partyBsubjectId,jdbcType=BIGINT} as partyBsubjectId,-->
<!--            #{item.contractBizType,jdbcType=INTEGER} as contractBizType,#{item.contractState,jdbcType=INTEGER} as contractState,#{item.contractUrl,jdbcType=VARCHAR} as contractUrl,#{item.templateId,jdbcType=BIGINT} as templateId,-->
<!--            #{item.signMethod,jdbcType=INTEGER} as signMethod,#{item.creator,jdbcType=VARCHAR} as creator,SYSDATE as createTime,-->
<!--            #{item.modifier,jdbcType=VARCHAR} as modifier,SYSDATE as modifyTime,#{item.signDate,jdbcType=TIMESTAMP} as signDate,#{item.remark,jdbcType=VARCHAR} as remark from dual-->
<!--        </foreach>)t-->
<!--    </insert>-->

    <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from htl_rfp.T_RFP_CONTRACT_EXTERNAL
        where CONTRACT_ID = #{contractId,jdbcType=BIGINT}
    </select>

<!--    <select id="selectAttachmentByContractId" parameterType="long" resultType="com.fangcang.rfp.common.entity.Attachment">-->
<!--        select ta.FILE_NAME fileName,-->
<!--               ta.FILE_FTP_PATH fileFtpPath,-->
<!--               ta.FILE_URL fileUrl-->
<!--        from htl_rfp.T_RFP_CONTRACT trc-->
<!--        left join htl_rfp.T_ATTACHMENT ta on trc.CONTRACT_URL = ta.FILE_URL-->
<!--        where trc.CONTRACT_ID = #{contractId,jdbcType=BIGINT}-->
<!--    </select>-->

    <!--查询已签约酒店数-->
<!--    <select id="selectConfirmedHotelCount" resultType="java.lang.Integer">-->
<!--        select count(1) from htl_rfp.T_RFP_CONTRACT where project_id=#{projectId} and contract_state = 1 and contract_biz_type = 1-->
<!--    </select>-->

<!--    <select id="querySimpleContractList" parameterType="com.fangcang.rfp.common.dto.request.ContractQueryRequest"-->
<!--            resultType="com.fangcang.rfp.common.dto.response.ContractResponse">-->
<!--        select trc.CONTRACT_ID contractId,-->
<!--        trc.CONTRACT_CODE contractCode,-->
<!--        trc.PROJECT_ID projectId,-->
<!--        trc.PROJECT_BUSSINESS_ID projectBussinessId,-->
<!--        trc.PARTY_A_ORG_ID partyAorgId,-->
<!--        trc.PARTY_B_ORG_ID partyBorgId,-->
<!--        trc.PARTY_A_SUBJECT_ID partyAsubjectId,-->
<!--        trc.PARTY_B_SUBJECT_ID partyBsubjectId,-->
<!--        trc.CONTRACT_BIZ_TYPE contractBizType,-->
<!--        trc.CONTRACT_STATE contractState,-->
<!--        trc.SIGN_DATE signDate,-->
<!--        trc.SIGN_METHOD signMethod,-->
<!--        trc.TEMPLATE_ID templateId-->
<!--        from htl_rfp.T_RFP_CONTRACT trc-->
<!--        where 1 =1-->
<!--            <if test="projectId != null">-->
<!--                and trc.PROJECT_ID = #{projectId}-->
<!--            </if>-->
<!--            <if test="contractBizType != null">-->
<!--                and trc.CONTRACT_BIZ_TYPE = #{contractBizType}-->
<!--            </if>-->
<!--            <if test="contractState != null">-->
<!--                and trc.CONTRACT_STATE = #{contractState}-->
<!--            </if>-->
<!--            <if test="projectBussinessIdList != null">-->
<!--                and PROJECT_BUSSINESS_ID in-->
<!--                <foreach collection="projectBussinessIdList" open="(" close=")" item="projectBussinessId" separator=","-->
<!--                         index="index">-->
<!--                    #{projectBussinessId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--    </select>-->

    <update id="updateContractById" parameterType="com.fangcang.rfp.common.dto.request.ContractExternalDto">
        update htl_rfp.T_RFP_CONTRACT_EXTERNAL
        set MODIFY_TIME = SYSDATE
        <if test="contractUrl != null and contractUrl != '' ">
            ,CONTRACT_URL = #{contractUrl}
        </if>
        <if test="contractState != null ">
            ,CONTRACT_STATE = #{contractState}
        </if>
        <if test="signDate != null">
            ,SIGN_DATE = #{signDate}
        </if>
        <if test="modifier != null and modifier != '' ">
            ,MODIFIER = #{modifier}
        </if>
        <if test="signedContractUrl != null and signedContractUrl != '' ">
            ,SIGNED_CONTRACT_URL = #{signedContractUrl}
        </if>
        <if test="uploadState != null">
            ,UPLOAD_STATE = #{uploadState}
        </if>
        <if test="signMethod != null">
            ,SIGN_METHOD = #{signMethod}
        </if>
        <if test="remark != null and remark != '' ">
            ,REMARK = #{remark}
        </if>
        where  CONTRACT_ID = #{contractId}

    </update>

    <update id="updateContractByCode" parameterType="com.fangcang.rfp.common.dto.request.ContractExternalDto">
        update htl_rfp.T_RFP_CONTRACT_EXTERNAL
        set MODIFY_TIME = SYSDATE
        <if test="contractUrl != null and contractUrl != '' ">
            ,CONTRACT_URL = #{contractUrl}
        </if>
        <if test="contractState != null ">
            ,CONTRACT_STATE = #{contractState}
        </if>
        <if test="signDate != null">
            ,SIGN_DATE = #{signDate}
        </if>
        <if test="modifier != null and modifier != '' ">
            ,MODIFIER = #{modifier}
        </if>
        <if test="signedContractUrl != null and signedContractUrl != '' ">
            ,SIGNED_CONTRACT_URL = #{signedContractUrl}
        </if>
        <if test="uploadState != null">
            ,UPLOAD_STATE = #{uploadState}
        </if>
        <if test="remark != null and remark != '' ">
            ,REMARK = #{remark}
        </if>
        where CONTRACT_CODE = #{contractCode}
    </update>

    <select id="selectByContractCode" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from htl_rfp.T_RFP_CONTRACT_EXTERNAL
        where CONTRACT_CODE = #{contractCode}
    </select>

<!--    <select id="queryPendingContractCount" parameterType="com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest" resultType="integer">-->
<!--        select count(1)-->
<!--        from htl_rfp.T_PROJECT_INTENT_HOTEL pih-->
<!--        where pih.BID_STATE = 3-->
<!--        <if test="hotelId != null ">-->
<!--            and pih.HOTEL_ID = #{hotelId}-->
<!--        </if>-->
<!--        and not exists ( select * from htl_rfp.T_RFP_CONTRACT t where t.PROJECT_ID = pih.PROJECT_ID )-->
<!--    </select>-->

<!--    <select id="querySignNoCreatContractCount" parameterType="java.util.List" resultType="integer">-->
<!--        select count(1)-->
<!--        from htl_rfp.t_project_intent_hotel pih-->
<!--        where pih.BID_STATE = 3 and  pih.project_id in-->
<!--        <foreach collection="projectList" open="(" close=")" item="projectId" separator="," index="index">-->
<!--            #{projectId}-->
<!--        </foreach>-->
<!--        and not exists ( select * from htl_rfp.T_RFP_CONTRACT t where t.PROJECT_ID = pih.PROJECT_ID )-->
<!--    </select>-->

<!--    <select id="querySignElectronicContractCount" parameterType="java.util.List" resultType="integer">-->
<!--        select count(1)-->
<!--        from htl_rfp.t_project_intent_hotel pih-->
<!--        left join htl_rfp.T_RFP_CONTRACT t on t.PROJECT_ID = pih.PROJECT_ID-->
<!--        where pih.BID_STATE = 3 and t.SIGN_METHOD = 1 and t.CONTRACT_STATE != 1 and  pih.project_id in-->
<!--        <foreach collection="projectList" open="(" close=")" item="projectId" separator="," index="index">-->
<!--            #{projectId}-->
<!--        </foreach>-->

<!--    </select>-->

<!--    <select id="selectFinishContractByProjectIdAndIntentHotelId" resultType="integer" parameterType="com.fangcang.rfp.common.dto.request.ContractQueryRequest">-->
<!--        select count(1)-->
<!--        from htl_rfp.T_RFP_CONTRACT pih-->
<!--        where pih.CONTRACT_STATE = 1 and pih.PROJECT_ID = #{projectId} and pih.PROJECT_BUSSINESS_ID = #{projectBussinessId} and pih.CONTRACT_BIZ_TYPE = 1-->
<!--    </select>-->

    <select id="selectByContractState" parameterType="integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from htl_rfp.T_RFP_CONTRACT_EXTERNAL
        where CONTRACT_STATE = #{contractState}
    </select>

</mapper>