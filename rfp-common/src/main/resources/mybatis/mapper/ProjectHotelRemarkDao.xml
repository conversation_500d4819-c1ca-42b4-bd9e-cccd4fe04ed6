<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelRemarkDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelRemark">
        <result column="PROJECT_HOTEL_REMARK_ID" jdbcType="BIGINT" property="projectHotelRemarkId"/>
        <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId" />
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId" />
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="REMARK_TYPE" jdbcType="VARCHAR" property="remarkType"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROJECT_HOTEL_REMARK_ID, PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, REMARK_TYPE, REMARK,CREATOR,
       CREATE_TIME
    </sql>

    <select id="selectList" resultType="com.fangcang.rfp.common.dto.response.ProjectHotelRemarkResponse">
        SELECT
            PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            PROJECT_ID AS projectId,
            HOTEL_ID AS hotelId,
            REMARK_TYPE AS remarkType,
            REMARK AS remark,
            CREATOR AS creator,
            CREATE_TIME AS createTime
        FROM htl_rfp.T_PROJECT_HOTEL_REMARK
        WHERE PROJECT_ID = #{projectId}
        <if test="hotelId != null">
            AND HOTEL_ID = #{hotelId}
        </if>
        <if test="remarkType != null and remarkType != ''">
            AND REMARK_TYPE = #{remarkType}
        </if>
        ORDER BY CREATE_TIME DESC NULLS FIRST
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectHotelRemark">
        <selectKey keyProperty="projectHotelRemarkId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_PROJECT_HOTEL_REMARK_ID.nextval from dual
        </selectKey>
        INSERT INTO htl_rfp.T_PROJECT_HOTEL_REMARK (
            PROJECT_HOTEL_REMARK_ID,
            PROJECT_INTENT_HOTEL_ID,
            PROJECT_ID,
            HOTEL_ID,
            REMARK_TYPE,
            REMARK,
            CREATOR,
            CREATE_TIME
        ) VALUES (
              #{projectHotelRemarkId,jdbcType=BIGINT},
              #{projectIntentHotelId,jdbcType=BIGINT},
              #{projectId,jdbcType=BIGINT},
              #{hotelId,jdbcType=BIGINT},
              #{remarkType,jdbcType=VARCHAR},
              #{remark,jdbcType=VARCHAR},
              #{creator,jdbcType=VARCHAR},
              SYSDATE
        )
    </insert>
</mapper>