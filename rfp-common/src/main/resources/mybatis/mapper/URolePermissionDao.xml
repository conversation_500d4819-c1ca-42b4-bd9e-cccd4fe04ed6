<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.URolePermissionDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.RolePermission" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
    <result column="permission_id" property="permissionId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, role_id, permission_id
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_role_permission
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from htl_rfp.t_role_permission
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.dto.RolePermission" >

    insert into htl_rfp.t_role_permission (id, role_id, permission_id
      )
    values (htl_rfp.SEQ_RFP_ROLE_PERMISSION.nextval,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}, #{permissionId,jdbcType=INTEGER}
      )
  </insert>

  <insert id="insertSelective" parameterType="com.fangcang.rfp.common.dto.RolePermission" >
      <selectKey keyProperty="id" resultType="_long" order="BEFORE">
          select htl_rfp.SEQ_RFP_ROLE_PERMISSION.nextval from dual
      </selectKey>
    insert into htl_rfp.t_role_permission
    <trim prefix="(" suffix=")" suffixOverrides="," >
        id,
      <if test="roleId != null" >
        role_id,
      </if>
      <if test="permissionId != null" >
        permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >

        #{id,jdbcType=INTEGER},

      <if test="roleId != null" >
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null" >
        #{permissionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.dto.RolePermission" >
    update htl_rfp.t_role_permission
    <set >
      <if test="roleId != null" >
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null" >
        permission_id = #{permissionId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.dto.RolePermission" >
    update htl_rfp.t_role_permission
    set role_id = #{roleId,jdbcType=INTEGER},
      permission_id = #{permissionId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>


	<!-- 根据权限ID查找 -->
	<select id="findRolePermissionByPid" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from htl_rfp.t_role_permission
		where permission_id = #{id,jdbcType=BIGINT}
	</select>
	<!-- 根据角色ID查找 -->
	<select id="findRolePermissionByRid" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from htl_rfp.t_role_permission
		where role_id = #{id,jdbcType=BIGINT}
	</select>
	<!-- 根据权限 && 角色ID查找 -->
	<select id="find" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from htl_rfp.t_role_permission
		where role_id = #{rid,jdbcType=BIGINT}
		and permission_id = #{pid,jdbcType=BIGINT}
	</select>
	<!-- 根据权限ID删除 -->
	<delete id="deleteByPid">
		delete from htl_rfp.t_role_permission where permission_id = #{id,jdbcType=BIGINT}
	</delete>
	<!-- 根据角色ID删除 -->
	<delete id="deleteByRid">
		delete from htl_rfp.t_role_permission where role_id = #{id,jdbcType=BIGINT}
	</delete>
	<!-- 根据角色ID && 权限ID删除 -->
	<delete id="delete">
		delete from htl_rfp.t_role_permission where role_id = #{rid,jdbcType=BIGINT}
		and permission_id = #{pid,jdbcType=BIGINT}
	</delete>
	<!-- 根据角色IDs删除 -->
	<delete id="deleteByRids">
		delete from htl_rfp.t_role_permission where role_id in(${roleIds})
	</delete>
</mapper>