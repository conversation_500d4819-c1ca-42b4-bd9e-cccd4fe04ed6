<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelViolationsMonitorDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelViolationsMonitor">
        <result column="VIOLATIONS_MONITOR_ID" jdbcType="DECIMAL" property="violationsMonitorId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="VIOLATION_ITEM" jdbcType="VARCHAR" property="violationItem"/>
        <result column="VIOLATION_TYPE" jdbcType="DECIMAL" property="violationType"/>
        <result column="PROCUREMENT_VOLUME" jdbcType="DECIMAL" property="procurementVolume"/>
        <result column="HOTEL_SERVICE_POINTS" jdbcType="DECIMAL" property="hotelServicePoints"/>
        <result column="REMINDER_LEVEL" jdbcType="VARCHAR" property="reminderLevel"/>
        <result column="PENALTY_SCORE" jdbcType="DECIMAL" property="penaltyScore"/>
        <result column="FULL_PENALTY_SCORE" jdbcType="DECIMAL" property="fullPenaltyScore"/>
        <result column="UP_PENALTY_SCORE" jdbcType="DECIMAL" property="upPenaltyScore"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="HOTEL_REPLY_MESSAGE" jdbcType="VARCHAR" property="hotelReplyMessage"/>
        <result column="HOTEL_REPLY_DATE" jdbcType="TIMESTAMP" property="hotelReplyDate"/>
        <result column="STATUS" jdbcType="DECIMAL" property="status"/>
        <result column="PROCESSING_INFO" jdbcType="VARCHAR" property="processingInfo"/>
        <result column="PROCESSING_DATE" jdbcType="TIMESTAMP" property="processingDate"/>
        <result column="HANDLER" jdbcType="VARCHAR" property="handler"/>
        <result column="HOTEL_REPLY" jdbcType="VARCHAR" property="hotelReply"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="SEND_STATUS" jdbcType="DECIMAL" property="sendStatus"/>
        <result column="ORDER_MONITOR_TIME" jdbcType="VARCHAR" property="orderMonitorTime"/>
        <result column="IS_PAYBACK_SCORE" jdbcType="INTEGER" property="isPaybackScore"/>
        <result column="SEND_PAYBACK_STATUS" jdbcType="INTEGER" property="sendPaybackStatus"/>
        <result column="VIOLATIONS_ID" jdbcType="DECIMAL" property="violationsId"/>
        <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId"/>
        <result column="BREAKFAST_NUM" jdbcType="DECIMAL" property="breakfastNum"/>
        <result column="TOTAL_VIOLATION_DAY_COUNT" jdbcType="INTEGER" property="totalViolationDayCount"/>
        <result column="RESTORE_PRICE_DAY_COUNT" jdbcType="INTEGER" property="restorePriceDayCount"/>
        <result column="THE_SAME_LEVEL_DAY_COUNT" jdbcType="INTEGER" property="theSameLevelDayCount"/>
        <result column="CLIENT_ROOM_CLOSED_DAY_COUNT" jdbcType="INTEGER" property="clientRoomClosedDayCount"/>


    </resultMap>

    <sql id="Base_Column_List">
      VIOLATIONS_MONITOR_ID, HOTEL_ID, PROJECT_ID,VIOLATION_ITEM, VIOLATION_TYPE, PENALTY_SCORE,FULL_PENALTY_SCORE,UP_PENALTY_SCORE,
      SEND_TIME, HOTEL_REPLY_MESSAGE, HOTEL_REPLY_DATE,STATUS, PROCESSING_INFO, PROCESSING_DATE,
      CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME,PROCUREMENT_VOLUME,HOTEL_SERVICE_POINTS,REMINDER_LEVEL,
      HANDLER,HOTEL_REPLY,SEND_STATUS,ORDER_MONITOR_TIME,IS_PAYBACK_SCORE,SEND_PAYBACK_STATUS,VIOLATIONS_ID,
      ROOM_TYPE_ID,BREAKFAST_NUM,TOTAL_VIOLATION_DAY_COUNT,RESTORE_PRICE_DAY_COUNT,THE_SAME_LEVEL_DAY_COUNT,CLIENT_ROOM_CLOSED_DAY_COUNT
    </sql>

    <select id="getByViolationsMonitorId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM htl_rfp.t_hotel_violations_monitor WHERE VIOLATIONS_MONITOR_ID = #{hotelViolationsMonitorId}
    </select>
    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelViolationsMonitor">
        <selectKey keyProperty="violationsMonitorId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_HOTEL_VIOLATIONS_MONITOR.nextval from dual
        </selectKey>
        insert into T_HOTEL_VIOLATIONS_MONITOR (VIOLATIONS_MONITOR_ID, HOTEL_ID, PROJECT_ID,
        VIOLATION_ITEM, VIOLATION_TYPE,PROCUREMENT_VOLUME,HOTEL_SERVICE_POINTS,REMINDER_LEVEL, PENALTY_SCORE,
        FULL_PENALTY_SCORE,UP_PENALTY_SCORE,
        SEND_TIME, HOTEL_REPLY_MESSAGE, HOTEL_REPLY_DATE,
        STATUS, PROCESSING_INFO, PROCESSING_DATE, HANDLER,HOTEL_REPLY,
        CREATOR, CREATE_TIME, MODIFIER,MODIFY_TIME,SEND_STATUS,ORDER_MONITOR_TIME,SEND_PAYBACK_STATUS,IS_PAYBACK_SCORE,VIOLATIONS_ID,
        ROOM_TYPE_ID,BREAKFAST_NUM,TOTAL_VIOLATION_DAY_COUNT,RESTORE_PRICE_DAY_COUNT,THE_SAME_LEVEL_DAY_COUNT,CLIENT_ROOM_CLOSED_DAY_COUNT)
        values (#{violationsMonitorId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL},
        #{violationItem,jdbcType=VARCHAR}, #{violationType,jdbcType=DECIMAL}, #{procurementVolume,jdbcType=DECIMAL},
        #{hotelServicePoints,jdbcType=DECIMAL}, #{reminderLevel,jdbcType=DECIMAL}, #{penaltyScore,jdbcType=DECIMAL},
        #{fullPenaltyScore,jdbcType=DECIMAL},#{upPenaltyScore,jdbcType=DECIMAL},
        #{sendTime,jdbcType=TIMESTAMP}, #{hotelReplyMessage,jdbcType=VARCHAR}, #{hotelReplyDate,jdbcType=TIMESTAMP},
        #{status,jdbcType=DECIMAL}, #{processingInfo,jdbcType=VARCHAR}, #{processingDate,jdbcType=TIMESTAMP},
        #{handler,jdbcType=VARCHAR},#{hotelReply,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, sysdate, #{modifier,jdbcType=VARCHAR},
        sysdate,#{sendStatus,jdbcType=DECIMAL},#{orderMonitorTime,jdbcType=VARCHAR},
        #{sendPaybackStatus,jdbcType=INTEGER},
        #{isPaybackScore,jdbcType=INTEGER},
        #{violationsId,jdbcType=DECIMAL},
        #{roomTypeId,jdbcType=DECIMAL},
        #{breakfastNum,jdbcType=DECIMAL},
        #{totalViolationDayCount,jdbcType=INTEGER},
        #{restorePriceDayCount,jdbcType=INTEGER},
        #{theSameLevelDayCount,jdbcType=INTEGER},
        #{clientRoomClosedDayCount,jdbcType=INTEGER}
        )
    </insert>
    <update id="labelProcess" parameterType="com.fangcang.rfp.common.dto.request.LabelProcessDto">
        update htl_rfp.t_hotel_violations_monitor
        set MODIFY_TIME = sysdate,STATUS=#{processingStatus}
        <if test="processingInfo != null and processingInfo !=''">
            ,PROCESSING_INFO = #{processingInfo}
            <if test="operator != null and operator !=''">
                ,HANDLER = #{operator}
            </if>
            <if test="processingStatus == 1">
                ,PROCESSING_DATE = sysdate
            </if>
        </if>
        <if test="operator != null and operator !=''">
            ,MODIFIER = #{operator}
        </if>
        <if test="hotelReplyMessage != null and hotelReplyMessage !=''">
            ,HOTEL_REPLY_MESSAGE = #{hotelReplyMessage}
            <if test="operator != null and operator !=''">
                ,HOTEL_REPLY = #{operator}
            </if>
            <if test="processingStatus == 1">
                ,HOTEL_REPLY_DATE = sysdate
            </if>
        </if>
        where VIOLATIONS_MONITOR_ID =#{violationsMonitorId}
    </update>

    <update id="monitorStatLabelProcess" parameterType="com.fangcang.rfp.common.dto.request.MonitorStatLabelProcessDto">
        update htl_rfp.t_project_intent_hotel
        set MODIFY_TIME = sysdate,
            VIOLATION_STAT_PROCESS_STATUS=#{statProcessStatus},
            VIOLATION_STAT_PROCESS_MSG = #{processMSG},
            STAT_PROCESS_ORG_TYPE = #{orgType}
        <if test="operator != null and operator !=''">
            ,MODIFIER = #{operator}
        </if>
        where PROJECT_INTENT_HOTEL_ID =#{projectIntentHotelId}
    </update>

    <insert id="labelProcessRecord" parameterType="com.fangcang.rfp.common.dto.request.LabelProcessRecordDto">
        <selectKey keyProperty="violationsProcessId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_VIOLATION_PROCESS.nextval from dual
        </selectKey>
        insert into htl_rfp.T_VIOLATION_PROCESS
        (VIOLATION_PROCESS_ID, VIOLATIONS_MONITOR_ID, MONITOR_PROCESS_STATUS,PROCESS_MSG, ORG_TYPE,CREATOR,CREATE_TIME)
        values
        (#{violationsProcessId,jdbcType=DECIMAL}, #{violationsMonitorId,jdbcType=DECIMAL}, #{monitorProcessStatus,jdbcType=DECIMAL},
        #{processMSG,jdbcType=VARCHAR}, #{orgType,jdbcType=DECIMAL}, #{creator,jdbcType=DECIMAL}, sysdate)
    </insert>

    <insert id="labelViolationStatProcessRecord" parameterType="com.fangcang.rfp.common.dto.request.MonitorStatLabelProcessDto">
        <selectKey keyProperty="violationStatProcessId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_H_V_STAT_PROCESS.nextval from dual
        </selectKey>
        insert into htl_rfp.T_HOTEL_VIOLATION_STAT_PROCESS
        (VIOLATION_STAT_PROCESS_ID, PROJECT_INTENT_HOTEL_ID, STAT_PROCESS_STATUS,PROCESS_MSG, ORG_TYPE,CREATOR,CREATE_TIME)
        values
        (#{violationStatProcessId,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=DECIMAL}, #{statProcessStatus,jdbcType=DECIMAL},
        #{processMSG,jdbcType=VARCHAR}, #{orgType,jdbcType=DECIMAL}, #{operator,jdbcType=DECIMAL}, sysdate)
    </insert>

    <select id="getLabelProcessRecordsById" resultType="com.fangcang.rfp.common.dto.response.LabelProcessRecordResponse">
        SELECT
            p.VIOLATION_PROCESS_ID as violationsProcessId,
            p.VIOLATIONS_MONITOR_ID as violationsMonitorId,
            p.MONITOR_PROCESS_STATUS as monitorProcessStatus,
            p.PROCESS_MSG as processMSG,
            p.ORG_TYPE as orgType,
            p.CREATOR as creator,
            p.CREATE_TIME as repliedDateTime
        FROM htl_rfp.T_VIOLATION_PROCESS p
        WHERE
            p.VIOLATIONS_MONITOR_ID = #{hotelViolationsMonitorId}
        order by VIOLATION_PROCESS_ID desc
    </select>

    <select id="getLabelProcessRecordsByIds" resultType="com.fangcang.rfp.common.dto.response.LabelProcessRecordResponse">
        SELECT
            p.VIOLATION_PROCESS_ID as violationsProcessId,
            p.VIOLATIONS_MONITOR_ID as violationsMonitorId,
            p.MONITOR_PROCESS_STATUS as monitorProcessStatus,
            p.PROCESS_MSG as processMSG,
            p.ORG_TYPE as orgType,
            p.CREATOR as creator,
            p.CREATE_TIME as repliedDateTime
        FROM htl_rfp.T_VIOLATION_PROCESS p
        <if test="null != monitorIdList and monitorIdList.size > 0">
            WHERE p.VIOLATIONS_MONITOR_ID in
            <foreach collection="monitorIdList" index="index" item="list" open="(" separator="," close=")">
                #{list}
            </foreach>
        </if>
        order by VIOLATION_PROCESS_ID desc
    </select>

    <select id="getStatLabelProcessRecord" resultType="com.fangcang.rfp.common.dto.response.StatLabelProcessRecordResponse">
        SELECT
            s.VIOLATION_STAT_PROCESS_ID AS violationStatProcessId,
            s.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            s.STAT_PROCESS_STATUS AS statProcessStatus,
            s.PROCESS_MSG AS processMSG,
            s.ORG_TYPE AS orgType,
            s.CREATOR AS creator,
            s.CREATE_TIME AS remarkDateTime
        FROM
            htl_rfp.T_HOTEL_VIOLATION_STAT_PROCESS s
        WHERE
            s.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
        ORDER BY
            s.CREATE_TIME DESC
    </select>

    <select id="queryHotelViolationMonitorInfo"
            resultType="com.fangcang.rfp.common.dto.response.QueryHotelViolationMonitorResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelViolationMonitorRequest">
        SELECT
            vm.hotel_id as hotelId,
            vm.project_id as projectId,
            vm.violations_monitor_id as violationsMonitorId,
            h.chn_name as hotelName,
            p.project_name as projectName,
            vm.create_time as violationDate,
            b.brandname as hotelGroupName,
            vm.procurement_volume as procurementVolume,
            vm.hotel_service_points as hotelServicePoints,
            vm.violation_item as violationItem,
            vm.violation_type as violationType,
            vm.penalty_score as penaltyScore,
            vm.hotel_reply_message as hotelReplyMessage,
            vm.hotel_reply_date as hotelReplyDate,
            vm.reminder_level as reminderLevel,
            vm.status as processingStatus,
            vm.processing_info as processingInfo,
            vm.processing_date as processingDate,
            vm.create_time as createTime,
            vm.is_payback_score as isPaybackScore,
            o.org_name as orgName,
            h.hotelbrand as brandId,
            CASE WHEN orh.hotel_id IS NULL THEN 0 ELSE 1 END  AS isFollowHotel
        FROM htl_rfp.t_hotel_violations_monitor vm
        INNER JOIN 	htl_rfp.t_project p ON vm.project_id = p.project_id
        LEFT JOIN htl_rfp.t_org o ON p.tender_org_id = o.org_id
        LEFT JOIN htl_info.t_hotel h ON  vm.hotel_id = h.hotelid
        <if test="hotelGroupOrgId != null">
            LEFT JOIN htl_rfp.t_project_intent_hotel_group ph ON
             vm.project_id = ph.project_id
             AND ph.hotel_group_org_id = #{hotelGroupOrgId}
             AND ph.is_active = 1
        </if>
        <if test="hotelGroupId != null">
            LEFT JOIN htl_info.t_brand b ON vm.hotel_id = h.hotelid
            AND h.hotel_group = b.brandid
            AND b.groupid = 0
            AND h.hotel_group = #{hotelGroupId}
        </if>
        <if test="hotelGroupId == null">
            LEFT JOIN htl_info.t_brand b ON h.hotel_group = b.brandid AND b.groupid = 0
        </if>
        LEFT JOIN htl_rfp.T_ORG_FOLLOW_HOTEL orh ON p.TENDER_ORG_ID = orh.ORG_ID AND vm.hotel_id = orh.HOTEL_ID
        WHERE 1=1
        <if test="status != null ">
            AND vm.status = #{status}
        </if>
        <if test="projectId != null and projectId > 0">
            AND vm.project_id = #{projectId}
        </if>
        <if test="orgId != null">
            AND p.tender_org_id = #{orgId}
        </if>
        <if test="hotelId != null">
            AND vm.hotel_id = #{hotelId}
        </if>
        <if test="hotelId == null and hotelName != null and hotelName !=''">
            AND h.chn_name like CONCAT(CONCAT('%',#{hotelName}),'%')
        </if>
        <if test="brandId != null">
            AND h.hotelbrand = #{brandId}
        </if>
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND h.hotelbrand IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" open="(" separator="," close=")">
                #{hotelGroupBrandId}
            </foreach>
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" separator="," close=")">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="cityCode != null and cityCode !=''">
            AND h.city = #{cityCode}
        </if>
        <if test="province != null and province !=''">
            AND h.PROVINCE = #{province}
        </if>
        <if test="startViolationDate != null and endViolationDate !='' and startViolationDate !='' and endViolationDate != null ">
            AND vm.create_time BETWEEN TO_DATE(#{startViolationDate}, 'YYYY-MM-DD') and TO_DATE(#{endViolationDate},
            'YYYY-MM-DD')
        </if>
        <if test="reminderLevels != null and reminderLevels.size >0">
            AND (
            <foreach collection="reminderLevels" open="(" close=")" item="reminderLevel" separator="or" index="index">
                 vm.reminder_level like  CONCAT(CONCAT('%',#{reminderLevel}),'%')
            </foreach>
            )
        </if>
        <if test="hotelGroupOrgId != null and userIdList != null and userIdList.size() > 0 ">
            AND ph.hotel_group_contact_uid IN
            <foreach collection="userIdList" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="projectName != null and projectName != ''">
            AND p.project_name LIKE CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="queryViolationType != null and queryViolationType != 0">
            AND vm.violation_type = #{queryViolationType}
        </if>
        <if test="isFollowHotel != null and isFollowHotel > 0">
            AND orh.hotel_id IS NOT NULL
        </if>
        order by vm.CREATE_TIME desc
    </select>

    <select id="queryHotelViolationMonitorStatusCount"
            resultType="com.fangcang.rfp.common.dto.response.count.HotelViolationMonitorStatusCountResponse"
            parameterType="com.fangcang.rfp.common.dto.request.HotelViolationMonitorStatusCountRequest">
        select
        sum(case when vm.STATUS = 0 then 1 else 0 end) as pendingStatusCount,
        sum(case when vm.STATUS = 2 then 1 else 0 end) as unsettledCount,
        sum(case when vm.STATUS = 3 then 1 else 0 end) as repliedCount
        from htl_rfp.t_hotel_violations_monitor vm,
        htl_rfp.t_org o,
        htl_rfp.t_project p,
        <if test="hotelGroupOrgId != null">
            htl_rfp.t_project_intent_hotel_group ph,
        </if>
        htl_info.t_hotel h
        <if test="hotelGroupId != null">
            ,htl_info.t_brand b
        </if>
        <if test="hotelGroupId == null">
            left join htl_info.t_brand b on  h.hotel_group = b.brandid
            and b.groupid = 0
        </if>
        where vm.project_id = p.project_id
        and vm.hotel_id = h.hotelid
        <if test="hotelGroupId != null">
            and h.hotel_group = b.brandid
            and b.groupid = 0
            and h.hotel_group = #{hotelGroupId}
        </if>
        and p.tender_org_id = o.org_id
        <if test="projectId != null and projectId > 0">
            and vm.project_id = #{projectId}
        </if>
        <if test="orgId != null">
            and p.tender_org_id = #{orgId}
        </if>
        <if test="hotelId != null">
            and vm.hotel_id = #{hotelId}
        </if>
        <if test="hotelId == null and hotelName != null and hotelName !=''">
            and h.chn_name like CONCAT(CONCAT('%',#{hotelName}),'%')
        </if>

        <if test="cityCode != null and cityCode !=''">
            and h.city = #{cityCode}
        </if>
        <if test="province != null and province !=''">
            and h.PROVINCE = #{province}
        </if>
        <if test="startViolationDate != null and endViolationDate !='' and startViolationDate !='' and endViolationDate != null ">
            and vm.create_time BETWEEN TO_DATE(#{startViolationDate}, 'YYYY-MM-DD') and TO_DATE(#{endViolationDate},
            'YYYY-MM-DD')
        </if>
        <if test="reminderLevels != null and reminderLevels.size >0">
            and (
            <foreach collection="reminderLevels" open="(" close=")" item="reminderLevel" separator="or" index="index">
                vm.reminder_level like  CONCAT(CONCAT('%',#{reminderLevel}),'%')
            </foreach>
            )
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND h.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" separator="," open="(" close=")" >
                #{hotelGroupBrandId}
            </foreach>
        </if>
        <if test="hotelGroupOrgId != null">
            AND vm.project_id = ph.project_id
            AND ph.hotel_group_org_id = #{hotelGroupOrgId}
            AND ph.is_active = 1
        </if>
        <if test="hotelGroupOrgId != null and userIdList != null and userIdList.size() > 0 ">
            AND ph.hotel_group_contact_uid IN
            <foreach collection="userIdList" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="projectName != null and projectName != ''">
            AND p.project_name LIKE CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="queryViolationType != null and queryViolationType != 0">
            and vm.violation_type = #{queryViolationType}
        </if>
    </select>

    <select id="queryHotelViolationCheckPassStat" parameterType="com.fangcang.rfp.common.dto.request.QueryHotelViolationCheckPassStatRequest" resultType="com.fangcang.rfp.common.dto.response.QueryHotelViolationCheckPassStatResponse">
        SELECT
            vm.project_id AS projectId,
            p.project_name AS projectName,--项目名称
            p.TENDER_ORG_ID AS tenderOrgId, -- 项目机构ID
            vm.hotel_id AS hotelId,
            MAX(CASE WHEN orh.hotel_id IS NULL THEN 0 ELSE 1 END) AS isFollowHotel, --是否关注酒店
            pih.TOTAL_ROOM_NIGHT AS totalRoomNight,--总间夜数
            pih.HOTEL_SERVICE_POINTS AS hotelServicePoints,-- 酒店服务分
            COUNT(vm.VIOLATIONS_MONITOR_ID) AS totalViolationCount, --  总违规数量
            SUM(CASE WHEN vm.STATUS = 1 AND IS_PAYBACK_SCORE = 1 THEN 1 ELSE 0 END ) AS solvedCount, --已经解决次数
            SUM(vm.TOTAL_VIOLATION_DAY_COUNT) AS totalViolationDayCount, -- 总违规天数
            SUM(vm.RESTORE_PRICE_DAY_COUNT) AS restorePriceDayCount, -- 恢复原价天数
            SUM(vm.THE_SAME_LEVEL_DAY_COUNT) AS theSameLevelDayCount, -- 同房档通过天数
            SUM(vm.CLIENT_ROOM_CLOSED_DAY_COUNT) AS clientRoomClosedDayCount -- C端无房可售天数
        FROM
            htl_rfp.T_HOTEL_VIOLATIONS_MONITOR vm
        left join htl_info.t_hotel th on vm.hotel_id = th.hotelid and th.isactive=1 and th.country='CN'
        left join htl_rfp.t_project p on vm.project_id = p.project_id
        left join htl_rfp.t_project_intent_hotel pih on vm.PROJECT_ID = pih.PROJECT_ID AND vm.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_ORG_FOLLOW_HOTEL orh ON p.TENDER_ORG_ID = orh.ORG_ID AND vm.hotel_id = orh.HOTEL_ID
        WHERE
            vm.VIOLATION_TYPE = 1
         AND vm.TOTAL_VIOLATION_DAY_COUNT  > 0
            <if test="cityCode != null and cityCode !=''"> --城市
                AND th.city = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="province != null and province !=''">
                AND th.PROVINCE = #{province,jdbcType=VARCHAR}
            </if>
            <if test="hotelId != null"> -- 酒店
                AND pih.hotel_id = #{hotelId,jdbcType=BIGINT}
            </if>
            <if test="hotelGroupId != null and hotelGroupId > 0">
                AND th.hotel_group = #{hotelGroupId}
            </if>
            <if test="projectId != null"> -- 项目ID
                AND pih.project_id = #{projectId,jdbcType=BIGINT}
            </if>
            <if test="orgId != null"> -- 机构
                and p.tender_org_id = #{orgId}
            </if>
            <if test="isFollowHotel != null and isFollowHotel > 0">
                AND orh.hotel_id IS NOT NULL
            </if>
            <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                AND p.TENDER_ORG_ID IN
                <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                    #{userRelatedOrgId}
                </foreach>
            </if>
            <if test="startViolationDate != null and startViolationDate !=''">
                and vm.create_time >= TO_DATE(#{startViolationDate}, 'YYYY-MM-DD')
            </if>
            <if test="endViolationDate != null and endViolationDate !=''">
                and vm.create_time &lt; TO_DATE(#{endViolationDate}, 'YYYY-MM-DD') + 1
            </if>
            <if test="brandId != null">
                AND th.hotelBrand  = #{brandId}
            </if>
            <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
                AND th.HOTELBRAND IN
                <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" separator="," open="(" close=")">
                    #{hotelGroupBrandId}
                </foreach>
            </if>
            <if test="userIdList != null and userIdList.size() > 0 ">
                AND pih.HOTEL_CONTACT_UID IN
                <foreach collection="userIdList" open="(" close=")" item="userId" separator="," index="index">
                    #{userId}
                </foreach>
            </if>
        GROUP BY vm.project_id, p.project_name, vm.hotel_id, pih.TOTAL_ROOM_NIGHT,pih.HOTEL_SERVICE_POINTS,p.TENDER_ORG_ID
        <choose>
            <when test="roomNightSort == null and servicePointsSort == null">
                ORDER BY totalViolationCount DESC
            </when>
            <otherwise>
                <if test="roomNightSort != null and roomNightSort == 0"> --间夜降序
                    ORDER BY totalRoomNight DESC, totalViolationCount DESC
                </if>
                <if test="roomNightSort != null and roomNightSort == 1"> --间夜升序
                    ORDER BY totalRoomNight ASC, totalViolationCount DESC
                </if>
                <if test="servicePointsSort != null and servicePointsSort == 0"> --服务分降序
                    ORDER BY hotelServicePoints DESC , totalViolationCount DESC
                </if>
                <if test="servicePointsSort != null and servicePointsSort == 1"> --服务分间夜升序
                    ORDER BY hotelServicePoints ASC , totalViolationCount DESC
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="queryHotelViolationMonitorStat"
            resultType="com.fangcang.rfp.common.dto.response.QueryHotelViolationMonitorStatResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelViolationMonitorStatRequest">
        SELECT
            pih.PROJECT_INTENT_HOTEL_ID as projectIntentHotelId,
            pih.project_id AS projectId,
            p.project_name AS projectName,--项目名称
            p.TENDER_ORG_ID AS tenderOrgId, -- 项目机构ID
            pih.hotel_id AS hotelId,
            th.chn_name AS hotelName,--酒店名称
            th.city AS cityCode,
            bc.dataName AS cityName,--城市
            pro.dataName AS provinceName,
            th.province as province,
            pih.TOTAL_ROOM_NIGHT AS totalRoomNight,--总间夜数
            pih.HOTEL_SERVICE_POINTS AS hotelServicePoints,-- 酒店服务分
            pih.TOTAL_VIOLATION_COUNT AS totalViolationCount, -- 违规次数
            pih.LAST_REMINDER_LEVEL AS lastReminderLevel,--提醒等级
            pih.VIOLATION_STAT_PROCESS_STATUS AS processingStatus,--处理标签
            pih.VIOLATION_STAT_PROCESS_MSG AS processMSG,--处理备注
            pih.MODIFY_TIME as modifyTime,
            pih.MODIFIER as modifier, -- 更新人
            pih.STAT_PROCESS_ORG_TYPE as modifyOrgType,
            pih.MONITOR_FOLLOW_NAME as monitorFollowName,
            pih.LAST_VIOLATION_TIME as lastViolationTime,
            ig.groupId AS groupId,
            ig.brandname as hotelGroupName,-- 酒店集团
            p.PRICE_MONITOR_START_DATE as startViolationDate,
            p.PRICE_MONITOR_END_DATE  as endViolationDate,
            ib.brandId AS brandId,
            ib.brandname as brandName, -- 品牌
            CASE WHEN orh.hotel_id IS NOT NULL THEN 1 ELSE 0 END AS isFollowHotel
        FROM
            htl_rfp.t_project_intent_hotel pih
        left join htl_info.t_hotel th on pih.hotel_id = th.hotelid  and th.isactive=1   and th.country='CN'
        left join htl_rfp.t_project p on pih.project_id = p.project_id
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata pro ON th.province = pro.datacode AND pro.datatype = 2
        left join htl_rfp.T_ORG_FOLLOW_HOTEL orh ON p.TENDER_ORG_ID = orh.ORG_ID AND pih.hotel_id = orh.HOTEL_ID
        WHERE
            pih.BID_STATE = 3 -- 已经中签
        <if test="cityCode != null and cityCode !=''"> --城市
            AND th.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="province != null and province != ''">
            AND th.province = #{province,jdbcType=VARCHAR}
        </if>
        <if test="isFollowHotel != null and isFollowHotel > 0">
           AND orh.hotel_id IS NOT NULL
        </if>
        <if test="monitorFollowName != null and monitorFollowName !=''"> --平台履约跟进人
            AND pih.MONITOR_FOLLOW_NAME like CONCAT(CONCAT('%',#{monitorFollowName}),'%')
        </if>
        <if test="hotelId != null"> -- 酒店
            AND pih.hotel_id = #{hotelId,jdbcType=BIGINT}
        </if>
        <if test="brandId != null and brandId > 0">  -- hotel表 HOTELBRAND 存的 brandId
            AND th.HOTELBRAND = #{brandId}
        </if>
        <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
            AND th.HOTELBRAND IN
            <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" separator="," open="(" close=")">
                #{hotelGroupBrandId}
            </foreach>
        </if>
        <if test="hotelGroupId != null and hotelGroupId > 0"> -- 酒店集团
            AND th.hotel_group = #{hotelGroupId}
        </if>
        <if test="orgId != null"> -- 机构
            and p.tender_org_id = #{orgId}
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND p.TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        <if test="projectName != null and projectName !=''"> --项目名称
            and p.project_name like CONCAT(CONCAT('%',#{projectName}),'%')
        </if>
        <if test="processStatus != null ">
            and pih.VIOLATION_STAT_PROCESS_STATUS = #{processStatus}
        </if>
        <if test="reminderLevelss != null and reminderLevelss.size >0"> --提醒等级
            AND (
            <foreach collection="reminderLevelss" open="(" close=")" item="reminderLevel" separator="or" index="index">
                pih.LAST_REMINDER_LEVEL like  CONCAT(CONCAT('%',#{reminderLevel}),'%')
            </foreach>
            )
        </if>
        <if test="projectStatus != null and projectStatus == 1"> --生效中
            and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.PRICE_MONITOR_START_DATE and p.PRICE_MONITOR_END_DATE
        </if>
        <if test="projectStatus != null and projectStatus == 2"> --已过期
            and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') > p.PRICE_MONITOR_END_DATE
        </if>
        <if test="userIdList != null and userIdList.size() > 0 ">
            AND pih.HOTEL_CONTACT_UID IN
            <foreach collection="userIdList" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <choose>
            <when test="roomNightSort == null and servicePointsSort == null">
                ORDER BY processingStatus ASC, totalRoomNight DESC, projectIntentHotelId ASC
            </when>
            <otherwise>
                <if test="roomNightSort != null and roomNightSort == 0"> --间夜降序
                    ORDER BY totalRoomNight DESC, processingStatus ASC, projectIntentHotelId ASC
                </if>
                <if test="roomNightSort != null and roomNightSort == 1"> --间夜升序
                    ORDER BY totalRoomNight ASC, processingStatus ASC, projectIntentHotelId ASC
                </if>
                <if test="servicePointsSort != null and servicePointsSort == 0"> --服务分降序
                    ORDER BY hotelServicePoints DESC , processingStatus ASC, projectIntentHotelId ASC
                </if>
                <if test="servicePointsSort != null and servicePointsSort == 1"> --服务分间夜升序
                    ORDER BY hotelServicePoints ASC , processingStatus ASC, projectIntentHotelId ASC
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="queryServiceDeductionDetails"
            resultType="com.fangcang.rfp.common.dto.response.QueryServiceDeductionDetailsResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryServiceDeductionDetailsRequest">
      select create_time as violationDate,violation_item as violationItem,
      penalty_score as penaltyScore,hotel_reply_message as hotelReplyMessage,
      hotel_reply_date as hotelReplyDate,status as processingStatus,
      violation_type as violationType
      from htl_rfp.t_hotel_violations_monitor
      where  project_id=#{projectId}
      <if test="hotelId != null  and hotelId > 0">
          AND hotel_id = #{hotelId}
      </if>
        <if test="hotelIdList != null  and hotelIdList.size() > 0">
            AND hotel_id IN
            <foreach collection="hotelIdList" separator="," item="hotelIdItem" open="(" close=")">
                #{hotelIdItem}
            </foreach>
        </if>
      order by create_time desc
    </select>

    <select id="queryServiceDeductionDetailsByHotel"
            resultType="com.fangcang.rfp.common.dto.response.QueryServiceDeductionDetailsResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryServiceDeductionDetailsRequest">
            select
               vm.create_time       as violationDate,
               h.chn_name               as hotelName,
               p.project_name           as projectName,
               vm.penalty_score         as penaltyScore,
               vm.hotel_service_points  as hotelServicePoints,
               vm.violation_item        as violationItem,
               vm.violation_type        as violationType,
               vm.status                as processingStatus,
               vm.violations_monitor_id as violationsMonitorId
          from htl_rfp.t_hotel_violations_monitor vm
          left join htl_rfp.t_project p on vm.project_id = p.project_id
          left join htl_info.t_hotel h on vm.hotel_id = h.hotelid
        <if test="hotelOrgId != null">
          left join htl_rfp.T_ORG_RELATED_HOTEL orh on vm.hotel_id = orh.hotel_id
        </if>
         where 1=1
           <if test="hotelId != null and hotelId > 0">
               and vm.hotel_id = #{hotelId}
           </if>
           <if test="hotelIdList != null and hotelIdList.size() > 0">
               and vm.hotel_id IN
               <foreach collection="hotelIdList" open="(" close=")" item="hotelIdItem" separator=",">
                   #{hotelIdItem}
               </foreach>
           </if>
           <if test="hotelOrgId != null">
           and orh.ORG_ID = #{hotelOrgId}
           </if>
            <if test="status != null">
           and vm.status = #{status}
            </if>
           order by vm.create_time desc
    </select>
    <select id="queryViolationMonitorDetail"
            resultType="com.fangcang.rfp.common.dto.response.QueryViolationsDailyPriceResponse">
      select h.chn_name              as hotelName,
       p.project_name          as projectName,
       vm.violation_item       as violationItem,
       vm.penalty_score        as penaltyScore,
       vm.hotel_service_points as hotelServicePoints,
       vm.hotel_id as hotelId,
       vm.violation_type as violationType,
       h.HOTEL_GROUP as hotelGroup,
       vm.project_id as projectId,
       vm.violations_monitor_id as violationsMonitorId,
       vm.order_monitor_time as orderMonitorTime,
       vm.IS_PAYBACK_SCORE as isPaybackScore,
       vm.REMINDER_LEVEL as reminderLevel,
       vm.VIOLATIONS_ID as violationsId
      from htl_rfp.t_hotel_violations_monitor vm,
       htl_rfp.t_project                  p,
       htl_info.t_hotel                   h
       where vm.project_id = p.project_id
       and vm.hotel_id = h.hotelid
       and vm.violations_monitor_id = #{violationsMonitorId}
    </select>

    <select id="queryViolationMonitorIdByPaybackViolationMonitorId"
            resultType="java.lang.Long">
        select max(violations_monitor_id)
        from htl_rfp.t_hotel_violations_monitor
        where
             violations_monitor_id &lt; #{paybackViolationsMonitorId}
        and  project_id = #{projectId}
        and hotel_id = #{hotelId}
        and violation_type != 3
        and status = 1
        and IS_PAYBACK_SCORE = 1

    </select>
    <select id="queryHotelViolationsMonitors" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.t_hotel_violations_monitor
        where hotel_id = #{hotelId} and project_id=#{projectId}
        and status != 1 and CREATE_TIME >= TRUNC(sysdate-7)
        ORDER BY violations_monitor_id DESC
    </select>
    <select id="queryTodayHotelViolationsMonitorId" resultType="java.lang.Long">
        select max(violations_monitor_id)
        from htl_rfp.t_hotel_violations_monitor
        where hotel_id = #{hotelId} AND project_id=#{projectId}
         AND CREATE_TIME >= TRUNC(sysdate)
        ORDER BY VIOLATIONS_MONITOR_ID DESC
    </select>

    <select id="queryMaxHotelViolationsMonitorId" resultType="java.lang.Long">
        select max(violations_monitor_id)
        from htl_rfp.t_hotel_violations_monitor
        where hotel_id = #{hotelId} AND project_id=#{projectId}
    </select>

    <select id="queryMaxHotelViolationsMonitorDate" resultType="java.util.Date">
        select max(CREATE_TIME)
        from htl_rfp.t_hotel_violations_monitor
        where hotel_id = #{hotelId} AND project_id=#{projectId}
        group by hotel_id, project_id
    </select>

    <select id="queryViolationMonitorBySendStatus"
          resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.t_hotel_violations_monitor
        WHERE SEND_STATUS = 0 and violation_type =#{violationType} AND create_time BETWEEN sysdate-3 AND sysdate
    </select>

    <update id="updateViolationMonitorSendStatus">
      update htl_rfp.t_hotel_violations_monitor
      set SEND_STATUS = 1 , SEND_TIME=sysdate where violations_monitor_id = #{violationsMonitorId}
    </update>

    <select id="queryNeedSendEmailOrSmsViolationMonitor" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.t_hotel_violations_monitor
        WHERE CREATE_TIME >= TRUNC(sysdate-2)
        AND SEND_PAYBACK_STATUS = 1
        AND VIOLATION_TYPE = 3
        AND IS_PAYBACK_SCORE = 1
        <![CDATA[AND ROWNUM <= 50]]>
    </select>

    <select id="queryViolationMonitorGroupByHotelId" parameterType="com.fangcang.rfp.common.dto.request.QueryPerformanceInfoRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryPerformanceInfoResponse">
        SELECT
            pih.project_id AS projectId,
            pih.hotel_id AS hotelId,
            h.chn_name AS hotelName,
            bc.dataname AS cityName,
            NVL(vmStat.vmUpdateTime, pih.modify_time ) as updateTime,
            pih.HOTEL_SERVICE_POINTS as hotelServicePoints,
            h.city as cityCode,
            b.brandId AS brandId,
            b.brandName AS brandName,
            b.groupId AS groupId,
            b.hotelGroupName AS hotelGroupName,
            NVL(vmStat.violationsCount, 0) AS violationsCount,
            NVL(rn.totalProcurementVolume, 0 ) as totalProcurementVolume
        FROM
            htl_rfp.t_project_intent_hotel pih
            LEFT JOIN htl_info.t_hotel h ON pih.hotel_id = h.hotelid
            LEFT JOIN
                (SELECT
                    t1.brandid AS brandId,
                    t1.brandname AS brandName,
                    t1.groupid AS groupId,
                    t2.brandName AS hotelGroupName
                 FROM htl_info.T_BRAND t1 LEFT JOIN htl_info.T_BRAND t2 ON t1.GROUPID = t2.BRANDID AND t2.GROUPID = 0
                 ) b ON h.HOTELBRAND = b.brandid
            LEFT JOIN htl_base.t_areadata bc
            ON h.city = bc.datacode AND bc.datatype = 3
            AND bc.countrycode = 'CN'
            LEFT JOIN (
                SELECT
                    vm.project_id as projectId,
                    vm.hotel_id as hotelId,
                    count(vm.VIOLATIONS_MONITOR_ID) as violationsCount,
                    max(modify_time) AS vmUpdateTime
                FROM
                    htl_rfp.t_hotel_violations_monitor vm
                WHERE
                    vm.project_id = #{projectId,jdbcType=BIGINT}
                    <if test="hotelGroupHotelIdList != null and hotelGroupHotelIdList.size() >0">
                        AND vm.hotel_id IN
                        <foreach collection="hotelGroupHotelIdList" item="hotelGroupHotelId" separator="," open="(" close=")">
                            #{hotelGroupHotelId,jdbcType=BIGINT}
                        </foreach>
                    </if>
                    <if test="hotelId != null and hotelId >0">
                        AND vm.hotel_id = #{hotelId,jdbcType=BIGINT}
                    </if>
                    AND vm.VIOLATION_TYPE IN (1,2)
                    GROUP BY vm.project_id, vm.hotel_id
                ) vmStat ON vmStat.hotelId = h.hotelid
            LEFT JOIN (
                SELECT
                    t.hotel_id AS hotelId,
                    sum(nvl(t.room_night_count,0)) AS totalProcurementVolume
                FROM htl_rfp.T_DIS_HOTEL_DAILY_ORDER t
                WHERE
                    t.hotel_id IN  (SELECT hotel_id from htl_rfp.t_project_intent_hotel WHERE project_id = #{projectId,jdbcType=BIGINT} AND BID_STATE =3)
                <if test="hotelGroupHotelIdList != null and hotelGroupHotelIdList.size() >0">
                    AND t.hotel_id IN
                    <foreach collection="hotelGroupHotelIdList" item="hotelGroupHotelId" separator="," open="(" close=")">
                        #{hotelGroupHotelId,jdbcType=BIGINT}
                    </foreach>
                </if>
                <if test="hotelId != null and hotelId >0">
                    AND t.hotel_id = #{hotelId,jdbcType=BIGINT}
                </if>
                 <if test="distributorCodeList != null and distributorCodeList.size() > 0">
                     AND t.DISTRIBUTOR_CODE IN
                     <foreach collection="distributorCodeList" separator="," open="(" close=")" item="distributorCode">
                         #{distributorCode}
                     </foreach>
                 </if>
                AND to_date(#{startDate,jdbcType=VARCHAR},'yyyy-mm-dd') <![CDATA[   <=  ]]> BOOK_DATE AND  BOOK_DATE <![CDATA[   <=  ]]> to_date(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd')
                GROUP BY t.hotel_id
            ) rn ON rn.hotelId = h.hotelid
        WHERE
            pih.project_id = #{projectId,jdbcType=BIGINT}
            AND pih.BID_STATE =3
            <if test="cityCode != null and cityCode !=''">
                AND h.city = #{cityCode,jdbcType=VARCHAR}
            </if>
            <if test="hotelId != null">
                AND pih.hotel_id = #{hotelId,jdbcType=BIGINT}
            </if>
            <if test="hotelGroupHotelIdList != null and hotelGroupHotelIdList.size() >0">
                AND pih.hotel_id IN
                <foreach collection="hotelGroupHotelIdList" item="hotelGroupHotelId" separator="," open="(" close=")">
                    #{hotelGroupHotelId,jdbcType=BIGINT}
                </foreach>
            </if>
        <if test="brandId != null and brandId > 0">
            AND h.HOTELBRAND = #{brandId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId > 0">
            AND h.hotel_group = #{hotelGroupId}
        </if>
        <if test="orderBy != null and orderDirection != null">
            ORDER BY ${orderBy} ${orderDirection} , hotelId ASC
        </if>
    </select>

    <update id="updateViolationMonitorPaybackStatus">
        UPDATE htl_rfp.t_hotel_violations_monitor
        SET IS_PAYBACK_SCORE = 1,
            MODIFY_TIME = SYSDATE
        WHERE
            VIOLATIONS_MONITOR_ID = #{violationsMonitorId}
    </update>

    <update id="updateRecheckViolationMonitorResult" parameterType="com.fangcang.rfp.common.entity.HotelViolationsMonitor">
        UPDATE htl_rfp.t_hotel_violations_monitor
        SET IS_PAYBACK_SCORE = #{recheckHotelViolationsMonitor.isPaybackScore},
        <if test="recheckHotelViolationsMonitor.sendPaybackStatus != null and recheckHotelViolationsMonitor.sendPaybackStatus > 0">
            SEND_PAYBACK_STATUS = #{recheckHotelViolationsMonitor.sendPaybackStatus},
        </if>
        <if test="recheckHotelViolationsMonitor.processingInfo !=null and recheckHotelViolationsMonitor.processingInfo != ''">
            PROCESSING_INFO = #{recheckHotelViolationsMonitor.processingInfo},
        </if>
        <if test="recheckHotelViolationsMonitor.processingDate !=null">
            PROCESSING_DATE = #{recheckHotelViolationsMonitor.processingDate},
        </if>
            PENALTY_SCORE = #{recheckHotelViolationsMonitor.penaltyScore},
            FULL_PENALTY_SCORE = #{recheckHotelViolationsMonitor.fullPenaltyScore},
            UP_PENALTY_SCORE = #{recheckHotelViolationsMonitor.upPenaltyScore},
            STATUS = #{recheckHotelViolationsMonitor.status},
            HOTEL_SERVICE_POINTS = #{recheckHotelViolationsMonitor.hotelServicePoints},
            TOTAL_VIOLATION_DAY_COUNT = #{recheckHotelViolationsMonitor.totalViolationDayCount},
            RESTORE_PRICE_DAY_COUNT = #{recheckHotelViolationsMonitor.restorePriceDayCount},
            THE_SAME_LEVEL_DAY_COUNT = #{recheckHotelViolationsMonitor.theSameLevelDayCount},
            CLIENT_ROOM_CLOSED_DAY_COUNT = #{recheckHotelViolationsMonitor.clientRoomClosedDayCount},
            MODIFIER =  #{recheckHotelViolationsMonitor.modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            VIOLATIONS_MONITOR_ID = #{recheckHotelViolationsMonitor.violationsMonitorId}
    </update>
    <update id="updateRecheckViolationMonitorSendStatus" parameterType="com.fangcang.rfp.common.entity.HotelViolationsMonitor">
        UPDATE htl_rfp.t_hotel_violations_monitor
        SET SEND_PAYBACK_STATUS = #{hotelViolationsMonitor.sendPaybackStatus},
            MODIFY_TIME = SYSDATE
        WHERE
            VIOLATIONS_MONITOR_ID = #{hotelViolationsMonitor.violationsMonitorId}
    </update>

    <select id="queryHotelViolationCountStat" resultType="com.fangcang.rfp.common.dto.HotelViolationCountStatDto">
        SELECT hotel_id AS hotelId,
               count(1) AS violationCount
        FROM htl_rfp.t_hotel_violations_monitor
        WHERE
            project_id = #{projectId,jdbcType=BIGINT}
            AND hotel_id IN
            <foreach collection="hotelIdList" item="hotelId" separator="," open="(" close=")">
                #{hotelId,jdbcType=BIGINT}
            </foreach>
        AND VIOLATION_TYPE IN (1,2)
        GROUP BY hotel_id
    </select>


</mapper>