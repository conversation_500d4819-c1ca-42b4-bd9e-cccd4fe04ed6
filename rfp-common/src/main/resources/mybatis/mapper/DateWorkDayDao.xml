<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.fangcang.rfp.common.dao.DateWorkDayDao">

    <insert id="batchMergeYearWorkday" >
        MERGE INTO htl_rfp.T_DATE_WORKDAY T
        USING (
        <foreach collection="workDays" item="item" separator="UNION ALL">
            SELECT
            #{item.date} AS dates,
            #{item.workDay} AS workDay
            FROM dual
        </foreach>
        ) s
        ON (t.WORK_DATE = s.dates)
        WHEN MATCHED THEN
        UPDATE SET
                t.WORK_DAY = s.workDay,
                t.modifier = 'System',
                t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.WORK_DATE, t.WORK_DAY, t.creator, t.create_time, t.modifier, t.modify_time)
            values(s.dates,s.workDay, 'System', sysdate, 'System',sysdate)
    </insert>

    <select id="getWorkDay" resultType="java.lang.Integer">
        select
            WORK_DAY as workDay
        from htl_rfp.T_DATE_WORKDAY where WORK_DATE = #{dateString}
    </select>

</mapper>