<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelPriceGroupDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup">
    <id column="HOTEL_PRICE_GROUP_ID" jdbcType="DECIMAL" property="hotelPriceGroupId" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="DECIMAL" property="projectIntentHotelId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="ROOM_LEVEL_NO" jdbcType="INTEGER" property="roomLevelNo" />
    <result column="APPLICABLE_WEEKS" jdbcType="VARCHAR" property="applicableWeeks" />
    <result column="LRA" jdbcType="INTEGER" property="lra" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CANCEL_RESTRICT_TYPE" jdbcType="INTEGER" property="cancelRestrictType" />
    <result column="CANCEL_RESTRICT_DAY" jdbcType="INTEGER" property="cancelRestrictDay" />
    <result column="CANCEL_RESTRICT_TIME" jdbcType="VARCHAR" property="cancelRestrictTime" />
    <result column="LEVEL_TOTAL_ROOM_COUNT" jdbcType="INTEGER" property="levelTotalRoomCount" />
    <result column="LEVEL_ROOM_TYPE1_COUNT" jdbcType="INTEGER" property="levelRoomType1Count" />
    <result column="LEVEL_ROOM_TYPE2_COUNT" jdbcType="INTEGER" property="levelRoomType2Count" />
    <result column="LANYON_ROOM_DESC" jdbcType="VARCHAR" property="lanyonRoomDesc" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="IS_LOCKED" jdbcType="INTEGER" property="isLocked" />
  </resultMap>
  <sql id="Base_Column_List">
    HOTEL_PRICE_GROUP_ID, PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, ROOM_LEVEL_NO, APPLICABLE_WEEKS, LRA,
    REMARK, CANCEL_RESTRICT_TYPE, CANCEL_RESTRICT_DAY, CANCEL_RESTRICT_TIME,LEVEL_TOTAL_ROOM_COUNT,LANYON_ROOM_DESC,
    LEVEL_ROOM_TYPE1_COUNT, LEVEL_ROOM_TYPE2_COUNT, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME, IS_LOCKED
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_PRICE_GROUP
    where HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
  </select>

  <select id="selectInfoByProjectId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_PRICE_GROUP
    WHERE
        PROJECT_ID = #{projectId}
    ORDER BY ROOM_LEVEL_NO ASC, HOTEL_PRICE_GROUP_ID ASC
  </select>

  <select id="selectInfoByProjectGroupIds" resultMap="BaseResultMap" parameterType="java.lang.Long">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_PRICE_GROUP
    WHERE
    HOTEL_PRICE_GROUP_ID IN 
    <foreach collection="projectGroupIdList" open="(" close=")" separator="," item="projectGroupId">
        #{projectGroupId}
    </foreach>
  </select>
  <select id="selectInfoByProjectPriceGroup" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_PRICE_GROUP
    WHERE
        1= 1
        <if test="projectIntentHotelId != null">
          AND PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
        </if>
        <if test="projectId != null">
          AND PROJECT_ID = #{projectId,jdbcType=DECIMAL}
        </if>
        <if test="hotelId != null">
          AND  HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
        </if>
        <if test="roomLevelNo != null">
          AND ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
        </if>
        <if test="hotelPriceGroupId != null">
          AND HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
        </if>
        <if test="applicableWeeks != null">
          AND APPLICABLE_WEEKS = #{applicableWeeks,jdbcType=VARCHAR}
        </if>
        <if test="lra != null">
          AND LRA = #{lra,jdbcType=INTEGER}
        </if>
        <if test="cancelRestrictType != null">
          AND CANCEL_RESTRICT_TYPE = #{cancelRestrictType,jdbcType=INTEGER}
        </if>
          ORDER BY ROOM_LEVEL_NO ASC, HOTEL_PRICE_GROUP_ID ASC
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_PROJECT_HOTEL_PRICE_GROUP
    where HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectAndHotelId">
    delete from T_PROJECT_HOTEL_PRICE_GROUP
    where PROJECT_ID = #{projectId,jdbcType=DECIMAL}
      AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </delete>

  <select id="selectNextSequenceKey" resultType="java.lang.Long">
    select htl_rfp.SEQ_PROJECT_HOTEL_PRICE_GROUP.nextval from dual
  </select>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup">
    <selectKey keyProperty="hotelPriceGroupId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_PROJECT_HOTEL_PRICE_GROUP.nextval from dual
    </selectKey>
    insert into T_PROJECT_HOTEL_PRICE_GROUP (HOTEL_PRICE_GROUP_ID, PROJECT_INTENT_HOTEL_ID,
      PROJECT_ID, HOTEL_ID, ROOM_LEVEL_NO, APPLICABLE_WEEKS, LRA, REMARK, CANCEL_RESTRICT_TYPE,CANCEL_RESTRICT_DAY,
      CANCEL_RESTRICT_TIME,LANYON_ROOM_DESC,LEVEL_TOTAL_ROOM_COUNT,LEVEL_ROOM_TYPE1_COUNT,LEVEL_ROOM_TYPE2_COUNT,
      CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME)
    values (#{hotelPriceGroupId,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=DECIMAL},
      #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{roomLevelNo,jdbcType=VARCHAR}, #{applicableWeeks,jdbcType=VARCHAR},
      #{lra,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},#{cancelRestrictType,jdbcType=INTEGER},#{cancelRestrictDay,jdbcType=INTEGER},
    #{cancelRestrictTime,jdbcType=VARCHAR}, #{lanyonRoomDesc, jdbcType=VARCHAR}, #{levelTotalRoomCount,jdbcType=INTEGER},
    #{levelRoomType1Count,jdbcType=INTEGER},#{levelRoomType2Count,jdbcType=INTEGER},
    #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <insert id="insertWithId" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup">
    insert into T_PROJECT_HOTEL_PRICE_GROUP (HOTEL_PRICE_GROUP_ID, PROJECT_INTENT_HOTEL_ID,
    PROJECT_ID, HOTEL_ID, ROOM_LEVEL_NO, APPLICABLE_WEEKS, LRA, REMARK, CANCEL_RESTRICT_TYPE,CANCEL_RESTRICT_DAY,CANCEL_RESTRICT_TIME,
    LEVEL_TOTAL_ROOM_COUNT, LEVEL_ROOM_TYPE1_COUNT, LEVEL_ROOM_TYPE2_COUNT, LANYON_ROOM_DESC, CREATOR, CREATE_TIME,
    MODIFIER, MODIFY_TIME)
    values (#{hotelPriceGroupId,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=DECIMAL},
    #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{roomLevelNo,jdbcType=VARCHAR}, #{applicableWeeks,jdbcType=VARCHAR},
    #{lra,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR},#{cancelRestrictType,jdbcType=INTEGER},#{cancelRestrictDay,jdbcType=INTEGER},
    #{cancelRestrictTime,jdbcType=VARCHAR},#{levelTotalRoomCount,jdbcType=DECIMAL},
            #{levelRoomType1Count,jdbcType=DECIMAL},#{levelRoomType2Count,jdbcType=DECIMAL}, #{lanyonRoomDesc,jdbcType=VARCHAR},
            #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <update id="updateLevelRoomCount" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup">
    UPDATE T_PROJECT_HOTEL_PRICE_GROUP
    SET LEVEL_TOTAL_ROOM_COUNT = #{levelTotalRoomCount,jdbcType=INTEGER},
        LEVEL_ROOM_TYPE1_COUNT = #{levelRoomType1Count,jdbcType=INTEGER},
        LEVEL_ROOM_TYPE2_COUNT = #{levelRoomType2Count,jdbcType=INTEGER}
    WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
    AND ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
  </update>

  <update id="updateLevelLanyonRoomDesc">
    UPDATE T_PROJECT_HOTEL_PRICE_GROUP
    SET LANYON_ROOM_DESC = #{lanyonRoomDes,jdbcType=VARCHAR}
    WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
      AND ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.ProjectHotelPriceGroup">
    UPDATE T_PROJECT_HOTEL_PRICE_GROUP
    <set>
      <if test="projectIntentHotelId != null">
        PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL},
      </if>
      <if test="projectId != null">
        PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      </if>
      <if test="hotelId != null">
        HOTEL_ID = #{hotelId,jdbcType=DECIMAL},
      </if>
      <if test="roomLevelNo != null and roomLevelNo != ''">
        ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=VARCHAR},
      </if>
      <if test="levelTotalRoomCount != null">
        LEVEL_TOTAL_ROOM_COUNT = #{levelTotalRoomCount,jdbcType=INTEGER},
      </if>
      <if test="levelRoomType1Count != null">
        LEVEL_ROOM_TYPE1_COUNT = #{levelRoomType1Count,jdbcType=VARCHAR},
      </if>
      <if test="levelRoomType2Count != null">
        LEVEL_ROOM_TYPE2_COUNT = #{levelRoomType2Count,jdbcType=VARCHAR},
      </if>
      <if test="applicableWeeks != null and applicableWeeks != ''">
        APPLICABLE_WEEKS = #{applicableWeeks,jdbcType=VARCHAR},
      </if>
      <if test="lra != null">
        LRA = #{lra,jdbcType=DECIMAL},
      </if>
      <if test="cancelRestrictType != null">
        CANCEL_RESTRICT_TYPE = #{cancelRestrictType,jdbcType=DECIMAL},
      </if>
      <if test="cancelRestrictDay != null">
        CANCEL_RESTRICT_DAY = #{cancelRestrictDay,jdbcType=DECIMAL},
      </if>
      <if test="cancelRestrictTime != null and cancelRestrictTime != ''">
        CANCEL_RESTRICT_TIME = #{cancelRestrictTime,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      MODIFY_TIME = sysdate
    </set>
    WHERE HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
  </update>

  <select id="selectMaxRoomLevelNo" resultType="java.lang.Integer">
    SELECT
        MAX(ROOM_LEVEL_NO)
    FROM
        T_PROJECT_HOTEL_PRICE_GROUP
    WHERE
          1=1
        <if test="projectIntentHotelId != null">
          AND PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
        </if>
        <if test="projectId != null">
          AND PROJECT_ID = #{projectId,jdbcType=DECIMAL}
        </if>
        <if test="hotelId != null">
          AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
        </if>
  </select>

  <select id="selectMaxRoomLevelCount" resultType="java.lang.Integer">
    SELECT
        COUNT(DISTINCT ROOM_LEVEL_NO)
    FROM
        T_PROJECT_HOTEL_PRICE_GROUP
    WHERE
        1=1
    <if test="projectIntentHotelId != null">
      AND PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
    </if>
    <if test="projectId != null">
      AND PROJECT_ID = #{projectId,jdbcType=DECIMAL}
    </if>
    <if test="hotelId != null">
      AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
    </if>
  </select>

  <update id="updateLocked">
      UPDATE T_PROJECT_HOTEL_PRICE_GROUP
      SET IS_LOCKED = #{isLocked,jdbcType=INTEGER}
      WHERE HOTEL_PRICE_GROUP_ID = #{hotelPriceGroupId,jdbcType=DECIMAL}
  </update>

</mapper>