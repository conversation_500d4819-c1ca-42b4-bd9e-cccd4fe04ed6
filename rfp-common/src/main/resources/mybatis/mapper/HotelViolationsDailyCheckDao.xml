<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelViolationsDailyCheckDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelViolationsDailyCheck">
        <result column="VIOLATIONS_MONITOR_ID" jdbcType="DECIMAL" property="violationsMonitorId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId"/>
        <result column="BEFORE_SALE_PRICE" jdbcType="DECIMAL" property="beforeSalePrice"/>
        <result column="BEFORE_COMPARE_RESULT" jdbcType="DECIMAL" property="beforeCompareResult"/>
        <result column="BEFORE_PRICE_CODE" jdbcType="DECIMAL" property="beforePriceCode"/>
        <result column="BEFORE_DIF_PRICE" jdbcType="DECIMAL" property="beforeDifPrice"/>
        <result column="BEFORE_IS_VIOLATION" jdbcType="INTEGER" property="beforeIsViolation"/>
        <result column="SALE_DATE" jdbcType="TIMESTAMP" property="saleDate"/>
        <result column="SALE_PRICE" jdbcType="DECIMAL" property="salePrice"/>
        <result column="COMPARE_RESULT" jdbcType="DECIMAL" property="compareResult"/>
        <result column="PRICE_CODE" jdbcType="DECIMAL" property="priceCode"/>
        <result column="DIF_PRICE" jdbcType="DECIMAL" property="difPrice"/>
        <result column="IS_PASS_CHECK" jdbcType="INTEGER" property="isPassCheck"/>
        <result column="PASS_ROOM_INFO" jdbcType="VARCHAR" property="passRoomInfo"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CHECK_PASS_TYPE" jdbcType="INTEGER" property="checkPassType"/>
    </resultMap>

    <sql id="Base_Column_List">
      VIOLATIONS_MONITOR_ID, HOTEL_ID, PROJECT_ID,ROOM_TYPE_ID,BEFORE_SALE_PRICE,
      BEFORE_COMPARE_RESULT,BEFORE_PRICE_CODE,BEFORE_DIF_PRICE,BEFORE_IS_VIOLATION,SALE_DATE, SALE_PRICE,
      COMPARE_RESULT, PRICE_CODE, DIF_PRICE, IS_PASS_CHECK, PASS_ROOM_INFO, CREATOR, CREATE_TIME,CHECK_PASS_TYPE
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_HOTEL_VIOLATIONS_DAILY_CHECK
        (violations_monitor_id, hotel_id, project_id, room_type_id, sale_date,
         before_sale_price,before_compare_result, before_price_code, before_dif_price, before_is_violation,
         sale_price,compare_result, price_code, dif_price, IS_PASS_CHECK, PASS_ROOM_INFO,creator, create_time,CHECK_PASS_TYPE)

        <foreach collection="hotelViolationsDailyChecks" item="item" index="index" separator="union all">
            ( select #{item.violationsMonitorId,jdbcType=DECIMAL}, #{item.hotelId,jdbcType=DECIMAL}, #{item.projectId,jdbcType=DECIMAL}, #{item.roomTypeId,jdbcType=DECIMAL},
            #{item.saleDate,jdbcType=TIMESTAMP}, #{item.beforeSalePrice,jdbcType=DECIMAL}, #{item.beforeCompareResult,jdbcType=DECIMAL}, #{item.beforePriceCode,jdbcType=DECIMAL},
            #{item.beforeDifPrice,jdbcType=DECIMAL}, #{item.beforeIsViolation,jdbcType=INTEGER},
            #{item.salePrice,jdbcType=DECIMAL}, #{item.compareResult,jdbcType=INTEGER}, #{item.priceCode,jdbcType=DECIMAL},
            #{item.difPrice,jdbcType=DECIMAL}, #{item.isPassCheck,jdbcType=INTEGER}, #{item.passRoomInfo,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, sysdate, #{item.checkPassType,jdbcType=INTEGER} from dual)
        </foreach>

    </insert>

    <select id="queryHotelViolationsDailyCheck" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_VIOLATIONS_DAILY_CHECK
        where VIOLATIONS_MONITOR_ID=#{violationsMonitorId} order by SALE_DATE
    </select>

    <select id="selectRoomName" resultType="java.lang.String">
      select t.roomtypename from htl_info.t_roomtype t where t.roomtypeid= #{roomTypeId}
    </select>
</mapper>