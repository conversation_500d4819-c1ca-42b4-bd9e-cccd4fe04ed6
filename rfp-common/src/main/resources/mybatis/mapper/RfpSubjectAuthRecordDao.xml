<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.RfpSubjectAuthRecordDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.RfpSubjectAuthRecord">
    <id column="AUTH_TRANSACTION_ID" jdbcType="DECIMAL" property="authTransactionId" />
    <result column="SUBJECT_ID" jdbcType="DECIMAL" property="subjectId" />
    <result column="AUTH_CONTRACT_CODE" jdbcType="VARCHAR" property="authContractCode" />
    <result column="FADD_CUSTOMER_ID" jdbcType="VARCHAR" property="faddCustomerId" />
    <result column="SIGN_STATE" jdbcType="DECIMAL" property="signState" />
    <result column="AUTH_CONTRACT_URL" jdbcType="VARCHAR" property="authContractUrl" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    AUTH_TRANSACTION_ID, SUBJECT_ID, AUTH_CONTRACT_CODE, FADD_CUSTOMER_ID, SIGN_STATE, 
    AUTH_CONTRACT_URL, REMARK, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_RFP_SUBJECT_AUTH_RECORD
    where AUTH_TRANSACTION_ID = #{authTransactionId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_RFP_SUBJECT_AUTH_RECORD
    where AUTH_TRANSACTION_ID = #{authTransactionId,jdbcType=DECIMAL}
  </delete>

  <select id="querySeqRfpAuthContractCode" resultType="long">
  		select htl_rfp.seq_rfp_auth_contract_code.nextval from dual
  </select>
  
  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.RfpSubjectAuthRecord">
    <selectKey keyProperty="authTransactionId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_AUTH_TRANSACTION_ID.nextval from dual
    </selectKey>
    insert into T_RFP_SUBJECT_AUTH_RECORD (AUTH_TRANSACTION_ID, SUBJECT_ID, AUTH_CONTRACT_CODE, 
      FADD_CUSTOMER_ID, SIGN_STATE, AUTH_CONTRACT_URL, 
      REMARK, CREATOR, CREATE_TIME, 
      MODIFIER, MODIFY_TIME)
    values (#{authTransactionId,jdbcType=BIGINT}, #{subjectId,jdbcType=BIGINT}, 
    <!-- fun_gen_rfp_auth_contract_code(), -->
      #{authContractCode,jdbcType=VARCHAR},
      #{faddCustomerId,jdbcType=VARCHAR}, #{signState,jdbcType=INTEGER}, #{authContractUrl,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, SYSDATE,
      #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.RfpSubjectAuthRecord">
    update T_RFP_SUBJECT_AUTH_RECORD
    <set>
      MODIFY_TIME = SYSDATE
      <if test="authContractCode != null">
        ,AUTH_CONTRACT_CODE = #{authContractCode,jdbcType=VARCHAR}
      </if>
      <if test="signState != null">
        ,SIGN_STATE = #{signState,jdbcType=INTEGER}
      </if>
      <if test="authContractUrl != null">
        ,AUTH_CONTRACT_URL = #{authContractUrl,jdbcType=VARCHAR}
      </if>
      <if test="remark != null">
        ,REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="modifier != null">
        ,MODIFIER = #{modifier,jdbcType=VARCHAR}
      </if>
    </set>
    where AUTH_TRANSACTION_ID = #{authTransactionId}
  </update>

  <select id="selectAllAuthRecordBySubjectId" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_RFP_SUBJECT_AUTH_RECORD
    where SUBJECT_ID = #{subjectId}
    order by CREATE_TIME desc
  </select>

</mapper>