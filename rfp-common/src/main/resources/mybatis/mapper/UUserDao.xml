<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.UUserDao" >
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.common.UserDTO" >
    <id column="user_id" property="userId" jdbcType="INTEGER" />
    <result column="user_account" property="userAccount" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="INTEGER" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="nickname" property="nickname" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="mobile" property="mobile" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="qq" property="qq" jdbcType="VARCHAR" />
    <result column="wexin_openid" property="weXinOpenId" jdbcType="VARCHAR" />
    <result column="photo" property="photo" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="INTEGER" />
    <result column="approver" property="approver" jdbcType="VARCHAR" />
    <result column="approve_time" property="approveTime" jdbcType="TIMESTAMP" />
    <result column="out_account" property="outAccount" jdbcType="VARCHAR" />
    <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="depart_id"  property="departId" jdbcType="DECIMAL"/>
    <result column="CHANNEL_PARTNER_ID"  property="channelPartnerId" jdbcType="BIGINT"/>

  </resultMap>
  <sql id="Base_Column_List" >
    user_id, user_account, org_id, user_name, nickname, password, mobile, email, qq, 
    WEXIN_OPENID,photo, state,APPROVER,APPROVE_TIME, out_account, last_login_time, creator, create_time,
    modifier, modify_time,depart_id,CHANNEL_PARTNER_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    where user_id = #{userId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from htl_rfp.t_user
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.UUser" useGeneratedKeys="true" keyProperty="userId">
      <selectKey keyProperty="userId" resultType="_long" order="BEFORE">
          select htl_rfp.SEQ_RFP_USER_ID.nextval from dual
      </selectKey>
    insert into htl_rfp.t_user (user_id,user_account, org_id,
      user_name, password,
      mobile, email, qq, wexin_openid,
      photo, state, approver,APPROVE_TIME,
      out_account, last_login_time, creator, 
      create_time,depart_id,CHANNEL_PARTNER_ID
      )
    values (#{userId},#{userAccount,jdbcType=VARCHAR}, #{orgId,jdbcType=INTEGER},
      #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
      #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR},  #{weXinOpenId,jdbcType=VARCHAR},
      #{photo,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{approver,jdbcType=VARCHAR},#{approveTime,jdbcType=TIMESTAMP},
      #{outAccount,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
     sysdate,#{departId,jdbcType=DECIMAL},#{channelPartnerId,jdbcType=DECIMAL}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.UUser" >
    update htl_rfp.t_user
    <set >
      <if test="userName != null and userName !=''" >
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null  and password !=''" >
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null  and mobile !=''" >
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
        <if test="email != null  and email !=''" >
            email = #{email,jdbcType=VARCHAR},
        </if>
      <if test="state != null">
          state = #{state},
      </if>
      <if test="lastLoginTime != null" >
          last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgType != null and orgType == 3">
        depart_id = #{departId,jdbcType=INTEGER},
      </if>
      <if test="orgType != null and orgType == 1">
        CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=INTEGER},
      </if>
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = SYSDATE
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.UUser" >
    update htl_rfp.t_user
    set user_account = #{userAccount,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      qq = #{qq,jdbcType=VARCHAR},
    WEXIN_OPENID = #{weXinOpenId,jdbcType=VARCHAR},
      photo = #{photo,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
    APPROVER = #{approver,jdbcType=INTEGER},
    APPROVE_TIME = #{approveTime,jdbcType=TIMESTAMP},
      out_account = #{outAccount,jdbcType=VARCHAR},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      modifier = #{modifier,jdbcType=VARCHAR},
      modify_time = now()
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  
  
  <!-- 登录 -->
  <select id="login" resultMap="BaseResultMap" >
  	 select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    <trim prefix="where" prefixOverrides="and"> 
    	 state = 1
        <if test="mobile != null and mobile !=''">  
           and mobile = #{mobile,jdbcType=VARCHAR} 
        </if> 
        <if test="userAccount != null and userAccount !=''">  
            and user_account = #{userAccount,jdbcType=VARCHAR}
        </if>  
        <if test="password != null and password !=''">  
            and  password = #{password,jdbcType=VARCHAR}  
        </if>
        <if test="email != null and email !=''">
            and  email = #{email,jdbcType=VARCHAR}
        </if>
    </trim>
  </select>
  <!-- 根据邮箱查询 -->
  <select id="findUserByEmail" resultMap="BaseResultMap" parameterType="java.util.Map">
  	 select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
      <where>
          <choose>
              <when test="userId != null and userId !=''">
                  (email = #{email,jdbcType=VARCHAR} and user_id != #{userId,jdbcType=VARCHAR})
              </when>
              <otherwise>
                  email = #{email,jdbcType=VARCHAR}
              </otherwise>
          </choose>
      </where>
  </select>

    <select id="findUserCountByEmail" resultType="int">
        SELECT COUNT(*)
        FROM htl_rfp.t_user
        WHERE email = #{email,jdbcType=VARCHAR}
    </select>
  
  <!-- 根据帐号查询 -->
  <select id="findUserByAccount" resultMap="BaseResultMap" >
  	 select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    where   user_account = #{userAccount,jdbcType=VARCHAR}
  </select>
  
  <!-- 根据手机号查询 -->
  <select id="findUserByMobile" resultMap="BaseResultMap" >
  	 select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    where mobile = #{mobile,jdbcType=VARCHAR}
  </select>
  
  <select id="selectRoleByUserId" resultType="com.fangcang.rfp.common.dto.RoleDTOBo">
		select ur.role_id,
		       ur.role_name,
		       ur.role_type,
		       ifnull(uu.user_id, 0) marker,
		       uu.user_id userId
		  from htl_rfp.t_role ur
		  left join htl_rfp.t_user_role uur
		    on uur.role_id = ur.role_id
		  left join (select user_id
		               from htl_rfp.t_user
		              where user_id = #{id, jdbcType = INTEGER}) uu
		    on uu.user_id = uur.user_id
		 group by ur.role_id
  </select>

    <select id="selectRoleCodeByUserId" resultType="java.lang.String">
        select r.ROLE_CODE
        from htl_rfp.t_user_role ur
        left join htl_rfp.t_role r on ur.role_id = r.role_id
        WHERE ur.user_id = #{userId}
    </select>
  
  <!-- 根据外部id获得用户信息 -->
  <select id="getUserByOutAccountId" resultMap="BaseResultMap" >
  	 select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    where   out_account_id = #{outAccountId,jdbcType=VARCHAR}
  </select>

    <select id="getUserByUserName" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List" />
        FROM htl_rfp.t_user
        WHERE  org_id = #{orgId,jdbcType=INTEGER}
        AND LOWER(user_name) like  LOWER('%' || #{userName,jdbcType=VARCHAR} || '%')
    </select>
  
   <!-- 根据外部id获得用户信息 -->
  <select id="getUserByOutAccount" resultMap="BaseResultMap" >
    select u.* from htl_rfp.t_user u,htl_rfp.t_org o
	where u.org_id = o.org_id
	and u.out_account = #{0}
	and o.client_org_code = #{1}
  </select>
  
  <sql id="where_simple" >
    <trim prefix="where" prefixOverrides="and |or">
        <if test="mobile != null and mobile !=''">  
            and mobile = #{mobile,jdbcType=VARCHAR}  
        </if> 
        <if test="userAccount != null and userAccount !=''">  
            and user_account = #{userAccount,jdbcType=VARCHAR}
        </if>  
        <if test="nickname != null and nickname !=''">  
            and nickname = #{nickname,jdbcType=VARCHAR} 
        </if>
    </trim>
  </sql>
  
  <sql id="where_all" >
        <if test="mobile != null and mobile !=''">  
            and mobile = #{mobile,jdbcType=VARCHAR}  
        </if> 
        <!-- <if test="userAccount != null and userAccount !=''">  
            and user_account = #{userAccount,jdbcType=VARCHAR}
        </if>   -->
        <if test="orgId != null and orgId !=''">  
            and org_id = #{orgId,jdbcType=INTEGER}
        </if> 
        <if test="state != null and state !=''">  
            and state = #{state,jdbcType=INTEGER}
        </if> 
        <if test="roleId != null and roleId !=''">  
            and user_id in (select user_id from t_user_role where role_id = #{roleId,jdbcType=INTEGER})
        </if> 
        <!-- 关键字查询(员工的用户名、姓名、昵称综合查询) -->
        <if test="keyWord != null and keyWord !=''">  
            and (
			        LOWER(nickname) like  LOWER(CONCAT("%",#{keyWord,jdbcType=VARCHAR},"%")) or
			       AT("%" LOWER(user_name) like  LOWER(CONC,#{keyWord,jdbcType=VARCHAR},"%")) or
			        LOWER(user_account) like  LOWER(CONCAT("%",#{keyWord,jdbcType=VARCHAR},"%"))
		        )
        </if>
  </sql>
  
  <sql id="where" >
        <if test="mobile != null and mobile !=''">  
            and mobile = #{mobile,jdbcType=VARCHAR}  
        </if> 
        <if test="userAccount != null and userAccount !=''">  
            and user_account = #{userAccount,jdbcType=VARCHAR}
        </if>  
        <if test="orgId != null and orgId !=''">  
            and org_id = #{orgId,jdbcType=INTEGER}
        </if> 
        <if test="state != null and state !=''">  
            and state = #{state,jdbcType=INTEGER}
        </if> 
        <if test="nickname != null and nickname !=''">  
            and LOWER(nickname) like  LOWER(CONCAT("%",#{keyWord,jdbcType=VARCHAR},"%"))
        </if>
        <if test="userName != null and userName !=''">  
            and LOWER(user_name) like  LOWER(CONCAT("%",#{userName,jdbcType=VARCHAR},"%"))
        </if>
  </sql>
  
  <!-- 根据条件获得用户信息 -->
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.UUser" >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user
    <include refid="where_simple" />
  </select>
  
  <!-- 设置新密码 -->
  <update id="resetPassWord" parameterType="com.fangcang.rfp.common.entity.UUser" >
    update htl_rfp.t_user
    set 
      password = #{password,jdbcType=VARCHAR},
      modify_time = now()
    where  user_account = #{userAccount,jdbcType=VARCHAR}
  </update>
  
  <!-- 查询用户分页 start -->
  <select id="findCount" resultType="java.lang.Long">
 	 select count(user_id) from  htl_rfp.t_user where 1 = 1
  	<include refid="where_all" />
  </select>
  <!-- 根据条件查询用户列表 -->
  <select id="findAll" resultMap="BaseResultMap"  >
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user where 1 = 1
    <include refid="where_all" />
    order by user_id desc
  </select>
  <!-- 查询用户分页 end -->
  


  <sql id="Role_Column_List" >
    r.role_id, r.org_id, r.role_name, r.role_code, r.state, r.role_type
  </sql>
  
  <!-- 根据条件查询用户列表 -->
  <select id="findByCondition" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.entity.UUser">
    select 
    <include refid="Base_Column_List" />
    from htl_rfp.t_user where 1 = 1
    <include refid="where" />
  </select>
  
  <select id="getUserByOpenId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select  t.user_id, t.user_account, t.org_id, t.user_name, t.nickname, t.password, t.mobile, t.email, t.qq, t.WEXIN_OPENID,
    t.photo, t.state, t.APPROVER, t.APPROVE_TIME , t.out_account, t.last_login_time, t.creator, t.create_time,
    t.modifier, t.modify_time,t.titanjr_tfsuserid,t.login_count
    from t_user t,t_third_bind_info t1
    where t.user_id = t1.user_id
    	and t1.open_id  = #{openId,jdbcType=VARCHAR}
  </select>

    <select id="queryOrgManagerByOrgId" resultMap="BaseResultMap" parameterType="java.util.Map" >
       SELECT
            a.user_id userId,
            a.org_id orgId,
            a.user_name userName,
            a.out_account outAccount
       FROM
        htl_rfp.t_user a
       JOIN  (
           SELECT
                u.user_id,
                min(u.create_time) time,
                u.org_id orgId
           FROM
                htl_rfp.t_user u,
                htl_rfp.t_user_role ur
           WHERE ur.role_id = 1
           GROUP BY u.org_id
        ) b
       on a.user_id = b.user_id
       <if test="orgIdList != null and orgIdList.size() > 0">
           AND a.org_id in <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
       </if>
    </select>

    <!-- 批量更新泰坦金融用户Id -->
    <update id="batchUpdateOrgTitanjrUserId"  parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update htl_rfp.t_user
            <set>
                titanjr_tfsuserid = #{item.titanjrTfsuserid,jdbcType=INTEGER},
                modify_time = now()
            </set>
            where user_id = #{item.userId,jdbcType=INTEGER}
        </foreach>
    </update>

    <!-- 根据外部机构编码和手机号获得用户信息 -->
    <select id="getUserByClientOrgCodeAndMobile" resultMap="BaseResultMap">
        select u.* from htl_rfp.t_user u,htl_rfp.t_org o
        where u.org_id = o.org_id
          and u.mobile = #{1}
          and o.client_org_code = #{0}
    </select>

    <select id="queryEmployeeApplyList"  resultType="com.fangcang.rfp.common.dto.response.UserResponse" parameterType="com.fangcang.rfp.common.dto.request.EmployeeRequest">
        select tu.USER_ID userId,
               tu.USER_NAME userName,
               tu.mobile mobile,
               tr.role_id roleId,
               tr.ROLE_CODE roleCodeType,
               tr.ROLE_NAME roleName
        from htl_rfp.t_user tu
         left join htl_rfp.t_user_role tur on tu.USER_ID = tur.USER_ID
         left join htl_rfp.t_role tr on tur.ROLE_ID = tr.ROLE_ID
        where tu.STATE = 2 and tu.ORG_ID = #{orgId}
    </select>

    <select id="queryEmployeeByUserName" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.dto.request.EmployeeRequest">
        select u.USER_ID userId,
               u.USER_NAME userName
        from htl_rfp.t_user u
        join htl_rfp.t_user_role ur on u.user_id = ur.user_id
        join htl_rfp.t_role tr on ur.role_id=tr.role_id
        where  u.ORG_ID = #{orgId} and tr.role_code in ('EMPLOYEE','HEAD_ORGANIZATION')  and u.STATE = 1
          <if test="userName != null and userName != '' ">
              and u.USER_NAME like concat(concat('%',#{userName}),'%')
          </if>
        <if test="userIds != null">
            and u.user_id in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="channelPartnerId != null and channelPartnerId > 0">
            AND u.CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryPlatformEmployeeByUserName" resultType="com.fangcang.rfp.common.dto.common.UserDTO" parameterType="com.fangcang.rfp.common.dto.request.EmployeeRequest">
        select u.USER_ID userId,
        u.USER_NAME userName,
        u.CHANNEL_PARTNER_ID channelPartnerId,
        cp.PARTNER_NAME AS channelPartnerName
        from htl_rfp.t_user u
        join htl_rfp.t_user_role ur on u.user_id = ur.user_id
        join htl_rfp.t_role tr on ur.role_id=tr.role_id
        join htl_rfp.t_org o ON u.org_id = o.org_id
        left join htl_rfp.T_CHANNEL_PARTNER cp ON u.CHANNEL_PARTNER_ID = cp.CHANNEL_PARTNER_ID
        where o.ORG_TYPE = 1 and tr.role_code in ('EMPLOYEE')  and u.STATE = 1
        <if test="userName != null and userName != '' ">
            and u.USER_NAME like concat(concat('%',#{userName}),'%')
        </if>
        <if test="userIds != null">
            and u.user_id in
            <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                #{userId}
            </foreach>
        </if>
        <if test="channelPartnerId != null and channelPartnerId > 0">
            AND u.CHANNEL_PARTNER_ID = #{channelPartnerId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryEmployeePageByParams" parameterType="com.fangcang.rfp.common.dto.request.EmployeeRequest" resultType="com.fangcang.rfp.common.dto.response.UserResponse">
        select tu.user_name userName,
            tu.mobile mobile,
            tu.user_id userId,
            tu.CHANNEL_PARTNER_ID channelPartnerId,
            tu.org_id orgId,
            tu.state,
            tu.email email,
            tr.ROLE_NAME roleName,
            too.ORG_NAME orgName,
            too.ORG_TYPE orgType,
            tr.role_id roleId,
            tr.role_code roleCodeType,
            tu.depart_id as departId,
            cp.PARTNER_NAME AS channelPartnerName
        from htl_rfp.t_user tu
        left join htl_rfp.t_user_role tur on tu.USER_ID = tur.USER_ID
        left join htl_rfp.t_role tr on tur.ROLE_ID = tr.ROLE_ID
        left join htl_rfp.t_org too on tu.org_id = too.ORG_ID
        left join htl_rfp.T_CHANNEL_PARTNER cp ON tu.CHANNEL_PARTNER_ID = cp.CHANNEL_PARTNER_ID
        where tr.STATE = 1 and tu.STATE != 2
        <if test="state != null ">
            and tu.STATE = #{state}
        </if>
        <if test="userName != null and userName != '' ">
            and tu.user_name like CONCAT(CONCAT('%',#{userName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="mobile != null and mobile != '' ">
            and tu.mobile like CONCAT(CONCAT('%',#{mobile,jdbcType=VARCHAR}),'%')
        </if>
        <if test="orgName != null and orgName != '' ">
            and too.org_name like CONCAT(CONCAT('%',#{orgName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="orgId != null ">
            and tu.org_id = #{orgId}
        </if>
        <if test="orgType != null">
            and too.ORG_TYPE = #{orgType}
        </if>
        <if test="departIds != null">
            and tu.depart_id in
            <foreach collection="departIds" open="(" close=")" item="departId" separator="," index="index">
                #{departId}
            </foreach>
        </if>
        <if test="hotelId != null ">
            AND too.org_id IN (SELECT org_id FROM t_org_related_hotel WHERE hotel_id = #{hotelId})
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND too.org_id IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                #{userRelatedOrgId}
            </foreach>
        </if>
        order by tu.create_time desc,tu.USER_ID desc
    </select>

    <!-- 按机构类型统计用户数 -->
    <select id="queryUserCountByOrgType" resultType="java.lang.Long" parameterType="java.lang.Integer">
         select count(1) as userCount
           from htl_rfp.t_user u
          where u.state = 1
            and exists (select 1
                   from htl_rfp.t_org o
                  where u.org_id = o.org_id
                    and o.state = 1
                    and o.org_type = #{orgType})
    </select>

    <select id="queryUserByDepartId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from htl_rfp.t_user where state = 1 AND depart_id in
        <foreach collection="departIds" open="(" close=")" item="departId" separator="," index="index">
            #{departId}
        </foreach>
    </select>

    <select id="queryAllEmployeeByName" resultMap="BaseResultMap" parameterType="com.fangcang.rfp.common.dto.request.EmployeeRequest">
        select u.USER_ID userId,
        u.USER_NAME userName
        from htl_rfp.t_user u
        join htl_rfp.t_user_role ur on u.user_id = ur.user_id
        join htl_rfp.t_role tr on ur.role_id=tr.role_id
        where  u.ORG_ID = #{orgId} and u.STATE != 0
        <if test="state != null">
            and u.STATE = #{state}
        </if>
        <if test="userName != null and userName != '' ">
            and u.USER_NAME like concat(concat('%',#{userName}),'%')
        </if>
    </select>

    <update id="updateUserState">
        UPDATE htl_rfp.t_user SET STATE = #{state} WHERE MOBILE = #{mobile}
    </update>
</mapper>