<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.RfpContractSignRecordDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.RfpContractSignRecord">
    <id column="SIGN_TRANSACTION_ID" jdbcType="DECIMAL" property="signTransactionId" />
    <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode" />
    <result column="FADD_CUSTOMER_ID" jdbcType="VARCHAR" property="faddCustomerId" />
    <result column="SIGN_STATE" jdbcType="DECIMAL" property="signState" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    SIGN_TRANSACTION_ID, CONTRACT_CODE, FADD_CUSTOMER_ID, SIGN_STATE, REMARK, CREATOR, 
    CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_RFP_CONTRACT_SIGN_RECORD
    where SIGN_TRANSACTION_ID = #{signTransactionId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_RFP_CONTRACT_SIGN_RECORD
    where SIGN_TRANSACTION_ID = #{signTransactionId,jdbcType=DECIMAL}
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.RfpContractSignRecord">
    <selectKey keyProperty="signTransactionId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_SIGN_TRANSACTION_ID.nextval from dual
    </selectKey>
    insert into T_RFP_CONTRACT_SIGN_RECORD (SIGN_TRANSACTION_ID, CONTRACT_CODE,SUBJECT_ID,
      FADD_CUSTOMER_ID, SIGN_STATE, REMARK, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME)
    values (#{signTransactionId}, #{contractCode},#{subjectId},
      #{faddCustomerId,jdbcType=VARCHAR}, #{signState,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
      #{creator,jdbcType=VARCHAR},SYSDATE, #{modifier,jdbcType=VARCHAR},
      SYSDATE)
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.RfpContractSignRecord">
    update T_RFP_CONTRACT_SIGN_RECORD
    <set>
      MODIFY_TIME = SYSDATE
      <if test="signState != null">
        ,SIGN_STATE = #{signState,jdbcType=INTEGER}
      </if>
      <if test="remark != null">
        ,REMARK = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="modifier != null">
        ,MODIFIER = #{modifier,jdbcType=VARCHAR}
      </if>
    </set>
    where SIGN_TRANSACTION_ID = #{signTransactionId}
  </update>

  <select id="selectSignRecordBySubjectId" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_RFP_CONTRACT_SIGN_RECORD
    where SUBJECT_ID = #{subjectId}
    order by CREATE_TIME desc
  </select>

  <select id="selectSignRecordByContractCode" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_RFP_CONTRACT_SIGN_RECORD
    where CONTRACT_CODE = #{contractCode}
    order by CREATE_TIME desc
  </select>

  <select id="selectSignRecordList" parameterType="com.fangcang.rfp.common.entity.RfpContractSignRecord"
          resultType="com.fangcang.rfp.common.entity.RfpContractSignRecord" >
    select
    <include refid="Base_Column_List" />
    from T_RFP_CONTRACT_SIGN_RECORD
    where 1=1
    <if test="signState != null ">
      and SIGN_STATE = #{signState}
    </if>
    <if test="contractCode != null and contractCode != '' ">
      and CONTRACT_CODE = #{contractCode}
    </if>
  </select>

</mapper>