<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelHistoryDataDao">

    <insert id="batchMergeHistoryData" >
        MERGE INTO htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA T
        USING (
        <foreach collection="historyProjects" item="item" separator="UNION ALL">
            SELECT
            #{item.projectId} AS projectId,
            #{item.hotelId} AS hotelId,
            #{item.roomNightCount} AS roomNightCount,
            #{item.totalAmount} AS totalAmount,
            #{item.operator} AS operator
            FROM dual
        </foreach>
        ) s
        ON (t.PROJECT_ID = s.projectId AND t.hotel_id = s.hotelId)
        WHEN MATCHED THEN
        UPDATE SET
        t.ROOMNIGHT_COUNT = s.roomNightCount,
        t.TOTAL_AMOUNT = s.totalAmount,
        t.IS_UPLOADED = 1,
        t.modifier = s.operator,
        t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.PROJECT_ID, t.HOTEL_ID,t.ROOMNIGHT_COUNT,t.TOTAL_AMOUNT, t.IS_UPLOADED, t.creator, t.create_time, t.modifier, t.modify_time)
        values(s.projectId,s.hotelId, s.roomNightCount, s.totalAmount, 1, s.operator, sysdate, s.operator, sysdate)
    </insert>

    <insert id="batchMergeHistoryResponse" >
        MERGE INTO htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA T
        USING (
        <foreach collection="historyProjects" item="item" separator="UNION ALL">
            SELECT
            #{item.projectId, jdbcType=BIGINT} AS projectId,
            #{item.hotelId, jdbcType=BIGINT} AS hotelId,
            #{item.cityCode, jdbcType=VARCHAR} AS cityCode,
            #{item.roomNightCount,jdbcType=INTEGER} AS roomNightCount,
            #{item.totalAmount,jdbcType=DECIMAL} AS totalAmount,
            #{item.savedAmount,jdbcType=DECIMAL} AS savedAmount,
            #{item.savedAmountRate,jdbcType=DECIMAL} AS savedAmountRate,
            #{item.lowestPrice,jdbcType=DECIMAL} AS lowestPrice,
            #{item.adjustLowestPrice,jdbcType=DECIMAL} AS adjustLowestPrice,
            #{item.lowestPriceDate,jdbcType=DATE} AS lowestPriceDate,
            #{item.lowestPriceItemInfo,jdbcType=VARCHAR} AS lowestPriceItemInfo,
            #{item.isUploaded,jdbcType=INTEGER} AS isUploaded,
            #{item.creator,jdbcType=VARCHAR} AS creator
            FROM dual
        </foreach>
        ) s
        ON (t.PROJECT_ID = s.projectId AND t.hotel_id = s.hotelId)
        WHEN MATCHED THEN
        UPDATE SET
        t.ROOMNIGHT_COUNT = s.roomNightCount,
        t.TOTAL_AMOUNT = s.totalAmount,
        t.CITY_CODE = s.cityCode,
        t.SAVED_AMOUNT = s.savedAmount,
        t.SAVED_AMOUNT_RATE = s.savedAmountRate,
        t.LOWEST_PRICE = s.lowestPrice,
        t.LOWEST_PRICE_DATE = s.lowestPriceDate,
        t.ADJUST_LOWEST_PRICE = s.adjustLowestPrice,
        t.LOWEST_PRICE_ITEM_INFO = s.lowestPriceItemInfo,
        t.IS_UPLOADED = s.isUploaded,
        t.modifier = s.creator,
        t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.PROJECT_ID, t.HOTEL_ID,t.ROOMNIGHT_COUNT,t.TOTAL_AMOUNT, t.CITY_CODE, t.SAVED_AMOUNT, t.SAVED_AMOUNT_RATE,t.LOWEST_PRICE,
                t.LOWEST_PRICE_DATE, t.ADJUST_LOWEST_PRICE, t.LOWEST_PRICE_ITEM_INFO,t.IS_UPLOADED,
        t.creator, t.create_time, t.modifier, t.modify_time)
        values(s.projectId, s.hotelId, s.roomNightCount, s.totalAmount, s.cityCode,  s.savedAmount, s.savedAmountRate,
               s.lowestPrice, s.lowestPriceDate, s.adjustLowestPrice, s.lowestPriceItemInfo,s.isUploaded, s.creator, sysdate, s.creator, sysdate)
    </insert>
    <update id="editHistoryProject"
            parameterType="com.fangcang.rfp.common.dto.request.EditHistoryProjectRequest">
        update htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA
            set MODIFY_TIME = sysdate
        <if test="operator != null">
            ,modifier = #{operator,jdbcType=VARCHAR}
        </if>
        <if test="roomNightCount != null">
            ,ROOMNIGHT_COUNT = #{roomNightCount}
        </if>
        <if test="totalAmount !=null and totalAmount != ''">
            ,TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL}
        </if>
        where project_id = #{projectId}
            and hotel_id = #{hotelId}
    </update>

    <delete id="deleteHistoryProjectInfo">
        delete from htl_rfp.T_PROJECT_HOTEL_HISTORY_DATA
        where PROJECT_ID = #{projectId} AND hotel_id = #{hotelId}
    </delete>

    <update id="updateHistoryProjectDataStat" parameterType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        UPDATE htl_rfp.t_project_hotel_history_data
        SET CITY_ORDER = #{cityOrder,jdbcType=INTEGER},
            CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            SAVED_AMOUNT = #{savedAmount, jdbcType=DECIMAL},
            SAVED_AMOUNT_RATE = #{savedAmountRate, jdbcType=DECIMAL},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT} AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </update>

    <update id="updateHistoryProjectPoi" parameterType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        UPDATE htl_rfp.t_project_hotel_history_data
        SET POI_ID = #{poiId,jdbcType=BIGINT},
            POI_DISTANCE = #{poiDistance, jdbcType=DECIMAL},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT} AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </update>

    <select id="queryHotelRecommendLevel" resultType="com.fangcang.rfp.common.dto.response.HotelRecommendLevelResponse">
        SELECT HOTEL_ID as hotelId,
            RECOMMEND_LEVEL as recommendLevel
        FROM htl_rfp.t_project_hotel_history_data
        WHERE PROJECT_ID = #{projectId, jdbcType=BIGINT}
        AND HOTEL_ID IN
            <foreach collection="hotelIds" separator="," open="(" close=")" item="hotelId">
                #{hotelId,jdbcType=BIGINT}
            </foreach>
    </select>

    <select id="queryHistoryProjectInfoList"
            resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHistoryProjectInfoRequest">
        SELECT
        phis.project_id AS projectId,
        phis.hotel_id AS hotelId,
        phis.CITY_ORDER AS cityOrder,
        th.chn_name AS hotelName,--酒店名称
        th.city AS cityCode,
        bc.dataName AS cityName,--城市
        phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CREATE_TIME as createTime, -- 创建时间
        phis.CREATOR as creator, -- 创建人
        phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
        ig.groupId AS groupId,
        ig.brandname as hotelGroupName,-- 酒店集团
        ib.brandId AS brandId,
        ib.brandname as brandName -- 品牌
        FROM
        htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.t_project p on phis.project_id = p.project_id
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        WHERE
        phis.PROJECT_ID = #{projectId}
        AND phis.IS_UPLOADED = 1
        <if test="cityCode != null and cityCode !=''"> --城市
            AND th.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="hotelId != null"> -- 酒店
            AND phis.hotel_id = #{hotelId,jdbcType=BIGINT}
        </if>
        <if test="brandId != null and brandId > 0">  -- hotel表 HOTELBRAND 存的 brandId
            AND th.HOTELBRAND = #{brandId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId > 0"> -- 酒店集团
            AND th.hotel_group = #{hotelGroupId}
        </if>
        <if test="hotelIdList != null">
            AND phis.hotel_id  IN
            <foreach collection="hotelIdList" open="(" close=")" item="hotelId" separator="," index="index">
                #{hotelId}
            </foreach>
        </if>
        and th.isactive=1  and th.country='CN'
        ORDER BY ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryHistoryProjectHotelList"  resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        SELECT
        phis.PROJECT_ID AS projectId,
        phis.HOTEL_ID AS hotelId,
        phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CITY_ORDER AS cityOrder,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.POI_ID AS poiId,
        phis.POI_DISTANCE AS poiDistance,
        phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
        phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
        phis.TOTAL_VIOLATIONS_COUNT AS totalViolationsCount,
        phis.OTA_MIN_PRICE AS otaMinPrice,
        phis.OTA_MAX_PRICE AS otaMaxPrice,
        phis.MIN_MAX_OTA_PRICE_DATE AS minMaxOtaPriceDate,
        phis.LOWEST_PRICE AS lowestPrice,
        phis.LOWEST_PRICE_DATE AS lowestPriceDate,
        phis.SERVICE_POINT AS servicePoint,
        phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
        th.city AS cityCode,
        th.CHN_NAME AS hotelName,
        th.LNG_BAIDU AS lngBaidu,
        th.LAT_BAIDU AS latBaidu,
        th.RATING as rating,
        NVL(th.HOTEL_STAR,'') as hotelStar,
        th.PRACICE_DATE as praciceDate
        FROM
        htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        <if test="cityCode != null and cityCode != ''">
            AND th.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="hotelId != null and hotelId > 0">
            AND phis.hotel_id = #{hotelId,jdbcType=BIGINT}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="poiId != null and poiId > 0">
            AND phis.POI_ID = #{poiId,jdbcType=BIGINT}
        </if>
        and th.isactive=1  and th.country='CN'
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryHistoryProjectHotelIdList" resultType="java.lang.Long">
        SELECT
            HOTEL_ID
        FROM
            htl_rfp.t_project_hotel_history_data
        WHERE
            PROJECT_ID = #{projectId}
    </select>

    <select id="queryRecommendHistoryProjectHotelList"  resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        SELECT
            phis.PROJECT_ID AS projectId,
            phis.HOTEL_ID AS hotelId,
            phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
            phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
            phis.CITY_ORDER AS cityOrder,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.POI_ID AS poiId,
            phis.POI_DISTANCE AS poiDistance,
            phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
            phis.LOWEST_PRICE AS lowestPrice,
            phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
            th.city AS cityCode,
            th.LNG_BAIDU AS lngBaidu,
            th.LAT_BAIDU AS latBaidu,
            th.chn_name AS hotelName,
            th.hotel_star AS hotelStar,
            th.rating AS rating,
            rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            rh.REFERENCE_PRICE AS referencePrice
        FROM
            htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id
        WHERE
            phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        and rh.STATE = 1
        and th.isactive=1  and th.country='CN'
        AND rh.REQUIRED_ROOM_NIGHT is not null
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryAllRecommendHistoryProjectHotelList"  resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        SELECT
        phis.PROJECT_ID AS projectId,
        phis.HOTEL_ID AS hotelId,
        phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CITY_ORDER AS cityOrder,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.POI_ID AS poiId,
        phis.POI_DISTANCE AS poiDistance,
        phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
        phis.LOWEST_PRICE AS lowestPrice,
        phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
        th.city AS cityCode,
        th.LNG_BAIDU AS lngBaidu,
        th.LAT_BAIDU AS latBaidu,
        th.chn_name AS hotelName,
        th.hotel_star AS hotelStar,
        th.rating AS rating,
        rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
        rh.REFERENCE_PRICE AS referencePrice
        FROM
        htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id AND rh.STATE = 1 AND rh.REQUIRED_ROOM_NIGHT is not null
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        and th.isactive=1  and th.country='CN'
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>
    <select id="queryHistoryProjectHotelWithHotelRecommendList"  resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        SELECT
        phis.PROJECT_ID AS projectId,
        phis.HOTEL_ID AS hotelId,
        phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CITY_ORDER AS cityOrder,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.POI_ID AS poiId,
        phis.POI_DISTANCE AS poiDistance,
        phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
        phis.LOWEST_PRICE AS lowestPrice,
        phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
        th.city AS cityCode,
        th.LNG_BAIDU AS lngBaidu,
        th.LAT_BAIDU AS latBaidu,
        th.chn_name AS hotelName,
        th.hotel_star AS hotelStar,
        th.rating AS rating,
        rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight
        FROM
        htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id  and rh.STATE = 1  AND rh.REQUIRED_ROOM_NIGHT is not null
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        and th.isactive=1  and th.country='CN'
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>


    <select id="queryRecommendAndLowestPriceHistoryProjectHotelList"   resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse">
        SELECT
            phis.PROJECT_ID AS projectId,
            phis.HOTEL_ID AS hotelId,
            phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
            phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
            phis.CITY_ORDER AS cityOrder,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.POI_ID AS poiId,
            phis.POI_DISTANCE AS poiDistance,
            phis.RECOMMEND_LEVEL AS recommendLevel,-- 推荐等级
            phis.LOWEST_PRICE AS lowestPrice,
            phis.ADJUST_LOWEST_PRICE AS adjustLowestPrice,
            th.city AS cityCode,
            th.LNG_BAIDU AS lngBaidu,
            th.LAT_BAIDU AS latBaidu,
            th.chn_name AS hotelName,
            th.hotel_star AS hotelStar,
            th.rating AS rating,
            rh.REQUIRED_ROOM_NIGHT AS requiredRoomNight
        FROM
            htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_RECOMMEND_HOTEL rh on phis.hotel_id = rh.hotel_id
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode != ''">
            AND phis.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        AND (rh.REQUIRED_ROOM_NIGHT is not null OR phis.LOWEST_PRICE is not null)
        and rh.STATE = 1
        and th.isactive=1  and th.country='CN'
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <update id="clearHistoryProjectHotelRecommendLevel">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET RECOMMEND_LEVEL = null,
            PRICE_LEVEL_ROOM_NIGHT = null,
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateHistoryProjectHotelRecommendLevel">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET RECOMMEND_LEVEL = #{recommendLevel},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" item="hotelId" separator="," close=")" open="(">
            #{hotelId}
        </foreach>
    </update>

    <update id="updateRecommendLevelAndPriceLevelRoomNight">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET RECOMMEND_LEVEL = #{recommendLevel,jdbcType=INTEGER},
            PRICE_LEVEL_ROOM_NIGHT = #{priceLevelRoomNight,jdbcType=INTEGER},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
    </update>

    <update id="updateHistoryProjectHotelViolationsCount">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET TOTAL_VIOLATIONS_COUNT = #{violationsCount},
        modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" item="hotelId" separator="," close=")" open="(">
            #{hotelId}
        </foreach>
    </update>

    <update id="resetHistoryProjectHotelViolationsCount">
        UPDATE
        htl_rfp.t_project_hotel_history_data
        SET TOTAL_VIOLATIONS_COUNT = null,
        modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateHistoryProjectHotelServicePoint">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET SERVICE_POINT = #{servicePoint},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID IN
        <foreach collection="hotelIdList" item="hotelId" separator="," close=")" open="(">
            #{hotelId}
        </foreach>
    </update>

    <update id="resetHistoryProjectHotelServicePoint">
        UPDATE
        htl_rfp.t_project_hotel_history_data
        SET SERVICE_POINT = NULL,
        modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
    </update>

    <update id="updateHistoryProjectHotelMinMaxOtaPrice">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET OTA_MIN_PRICE = #{minPrice},
            OTA_MAX_PRICE = #{maxPrice},
            MIN_MAX_OTA_PRICE_DATE = #{minMaxOtaPriceDate},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </update>

    <update id="updateHistoryProjectHotelLowestPrice">
        UPDATE
            htl_rfp.t_project_hotel_history_data
        SET LOWEST_PRICE = #{lowestPrice},
            ADJUST_LOWEST_PRICE = #{adjustLowestPrice},
            LOWEST_PRICE_ITEM_INFO = #{lowestPriceItemInfo},
            LOWEST_PRICE_DATE = #{lowestPriceDate},
            modify_time = sysdate
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </update>

    <select id="queryNeedUpdateLowestPriceHotelIds" resultType="java.lang.Long">
        SELECT
            HOTEL_ID
        FROM
            htl_rfp.t_project_hotel_history_data
        WHERE
            PROJECT_ID = #{projectId}
        AND (LOWEST_PRICE IS NULL OR LOWEST_PRICE_DATE &lt; SYSDATE - INTERVAL '30' DAY)
    </select>

    <update id="updateLastStatReferenceNo">
        UPDATE "HTL_RFP"."T_PROJECT_HOTEL_HISTORY_DATA"
        SET LAST_STAT_REFERENCE_NO = #{statReferenceNo},
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE PROJECT_ID = #{projectId}
    </update>

    <sql id="queryProjectTotalRecommendHotelWhere">
        phis.PROJECT_ID = #{projectId}
        and th.isactive=1   and th.country='CN'
        <if test="cityCode != null and cityCode !=''"> --城市
            AND th.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="hotelName != null and hotelName != ''"> -- 酒店
            AND th.chn_name LIKE concat(concat('%',#{hotelName}),'%')
        </if>
        <if test="hotelBrandId != null and hotelBrandId > 0">  -- hotel表 HOTELBRAND 存的 brandId
            AND th.HOTELBRAND = #{hotelBrandId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId > 0"> -- 酒店集团
            AND th.hotel_group = #{hotelGroupId}
        </if>
        <if test="province != null and province != ''"> -- 省份
            AND th.PROVINCE = #{province}
        </if>
        <if test="hotelBidState != null and hotelBidState > 0"> -- 报价状态
            AND pih.BID_STATE = #{hotelBidState}
        </if>
        <if test="isInvited != null and isInvited == 1">
            AND pih.INVITE_STATUS = 1
        </if>
        <if test="isInvited != null and isInvited == 0">
            AND (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        </if>

    </sql>

    <select id="queryTotalRecommendNeedInvitedHotelIdList" resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            phis.hotel_id
        FROM
            htl_rfp.t_project_hotel_history_data phis
         inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
                     phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
                     phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
                     phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
                     phr.IS_AREA_GATHER_RECOMMEND = 1 OR
                     phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
                     phr.IS_SAVED_HOTEL_RECOMMEND = 1)
         left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
         left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
         left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
         WHERE
            (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
            phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
            phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
            phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
            phr.IS_AREA_GATHER_RECOMMEND = 1 OR
            phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
            phr.IS_SAVED_HOTEL_RECOMMEND = 1)
        </if>
    </select>


    <select id="queryTotalRecommendHotelIdNotIncldeAreaGaterList" resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            phis.hotel_id
        FROM
            htl_rfp.t_project_hotel_history_data phis
        INNER JOIN htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
        WHERE
            phis.PROJECT_ID = #{projectId}
            AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
            phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
            phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
            phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
            phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
            phr.IS_SAVED_HOTEL_RECOMMEND = 1)
    </select>

    <select id="queryTotalRecommendNeedInvitedHotelCount"  resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            COUNT(phis.hotel_id)
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
                     phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
                     phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
                     phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
                     phr.IS_AREA_GATHER_RECOMMEND = 1 OR
                     phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
                     phr.IS_SAVED_HOTEL_RECOMMEND = 1)
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
                left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
          (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
          AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
            phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
            phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
            phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
            phr.IS_AREA_GATHER_RECOMMEND = 1 OR
            phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
            phr.IS_SAVED_HOTEL_RECOMMEND = 1)
        </if>
    </select>

    <select id="queryFrequencyRecommendNeedInvitedHotelIdList"  resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            phis.hotel_id
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_FREQUENCY_RECOMMEND = 1
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
                left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND  phis.IS_UPLOADED = 1
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_FREQUENCY_RECOMMEND = 1
        </if>
    </select>

    <select id="queryFrequencyRecommendNeedInvitedHotelCount"  resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            COUNT(phis.hotel_id)
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_FREQUENCY_RECOMMEND = 1
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
                left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND  phis.IS_UPLOADED = 1
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_FREQUENCY_RECOMMEND = 1
        </if>
    </select>

    <select id="queryFrequencySameLevelRecommendNeedInvitedHotelIdList"  resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            phis.hotel_id
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
            left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
            left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
            left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
        </if>
    </select>

    <select id="queryFrequencySameLevelRecommendNeedInvitedHotelCount"  resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            COUNT(phis.hotel_id)
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
                left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
        </if>
    </select>

    <select id="queryNoPoiHotAreaRecommendNeedInvitedHotelIdList"  resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
        COUNT(phis.hotel_id)
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
        AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        </if>
    </select>

    <select id="queryPoiNearRecommendNeedInvitedHotelIdList"  resultType="java.lang.Long" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            phis.hotel_id
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND  phis.IS_UPLOADED = 1
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1
        </if>
    </select>

    <select id="queryPoiNearRecommendNeedInvitedHotelCount"  resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
            COUNT(phis.hotel_id)
        FROM
            htl_rfp.t_project_hotel_history_data phis
                inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
                AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
                left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
                left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND  phis.IS_UPLOADED = 1
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1
        </if>
    </select>

    <select id="queryNoPoiHotAreaRecommendInvitedHotelCount"  resultType="java.lang.Integer" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest">
        SELECT
        COUNT(phis.hotel_id)
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
        AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        WHERE
        (pih.INVITE_STATUS = 0 OR pih.INVITE_STATUS IS NULL)
        AND (pih.BID_STATE IS NULL OR pih.BID_STATE = 0)
        AND <include refid="queryProjectTotalRecommendHotelWhere"></include>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        </if>
    </select>



    <select id="queryProjectTotalRecommendHotelStat" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
            phis.project_id AS projectId,
            COUNT(phis.hotel_id) AS totalHotelCount,
            SUM(CASE WHEN nvl(phr.IS_FREQUENCY_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_SAME_LEVEL_FREQ_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_POI_NEAR_HOTEL_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_NO_POI_HOT_AREA_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_AREA_GATHER_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_HIGH_QUALITY_RECOMMEND,0) = 1 OR
                    nvl(phr.IS_SAVED_HOTEL_RECOMMEND,0) = 1
                THEN 1 ELSE 0 END) AS recommendHotelCount,
            SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
            SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
            htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
        AND (phr.IS_FREQUENCY = 1 OR
            phr.IS_SAME_LEVEL_FREQ = 1 OR
            phr.IS_POI_NEAR_HOTEL = 1 OR
            phr.IS_NO_POI_HOT_AREA = 1 OR
            phr.IS_AREA_GATHER = 1 OR
            phr.IS_HIGH_QUALITY = 1 OR
            phr.IS_SAVED_HOTEL = 1)
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
          <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
            phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
            phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
            phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
            phr.IS_AREA_GATHER_RECOMMEND = 1 OR
            phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
            phr.IS_SAVED_HOTEL_RECOMMEND = 1)
        </if>
        GROUP BY phis.project_id
    </select>

    <select id="queryProjectTotalRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectTotalRecommendHotelResponse">
       SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            nvl(phr.IS_FREQUENCY_RECOMMEND,0) AS isFrequencyRecommend,
            nvl(phr.IS_SAME_LEVEL_FREQ_RECOMMEND,0) AS isSameLevelFrequencyRecommend,
            nvl(phr.IS_POI_NEAR_HOTEL_RECOMMEND,0) AS isPoiNearHotelRecommend,
            nvl(phr.IS_NO_POI_HOT_AREA_RECOMMEND,0) AS isNoPoiHotAreaRecommend,
            nvl(phr.IS_AREA_GATHER_RECOMMEND,0) AS isAreaGatherRecommend,
            nvl(phr.IS_HIGH_QUALITY_RECOMMEND,0) AS isHighQualityRecommend,
            nvl(phr.IS_SAVED_HOTEL_RECOMMEND,0) AS isSavedHotelRecommend,
            phr.FREQUENCY_RECOMMENDS AS frequencyRecommends,
            phr.SAME_LEVEL_FREQ_RECOMMENDS AS sameLevelFreqRecommends,
            phr.POI_NEAR_HOTEL_RECOMMENDS AS poiNearHotelRecommends,
            phr.NO_POI_HOT_AREA_RECOMMENDS AS noPoiHotAreaRecommends,
            phr.AREA_GATHER_RECOMMENDS AS areaGatherRecommends,
            nvl(pih.BID_STATE,0) AS hotelBidState,
            nvl(pih.INVITE_STATUS,0) AS isInvited,
            pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            th.PROVINCE AS province,
            bcp.dataName AS provinceName,--省份
            th.city AS cityCode,
            th.TELEPHONE AS telePhone,
            bc.dataName AS cityName,--城市
            phis.ROOMNIGHT_COUNT AS salesRoomNight,
            phis.TOTAL_AMOUNT AS totalAmount,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.SAVED_AMOUNT_RATE AS savedAmountRate,
            phis.SERVICE_POINT AS servicePoint,
            phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
            phis.OTA_MIN_PRICE AS otaMinPrice,
            phis.OTA_MAX_PRICE AS otaMaxPrice,
            phis.LOWEST_PRICE AS lowestPrice,
            phis.RECOMMEND_LEVEL AS recommendLevel,
            th.PRACICE_DATE AS praciceDate,
            th.CHN_ADDRESS AS hotelAddress,
            th.CHN_NAME AS hotelName,
            th.HOTEL_STAR AS hotelStar,
            th.FITMENT_DATE AS fitmentDate,
            th.LAYER_COUNT AS roomCount,
            th.RATING AS rating,
            ig.brandid AS hotelGroupId,
            ig.BRANDNAME AS hotelGroupName,
            ib.brandid AS brandId,
            ib.BRANDNAME AS hotelBrandName,
            ch.BRIGHT_SPOT AS brightSpot,
            ch.BREAKFAST_NUM AS breakfastNum,
            ch.REFERENCE_PRICE AS referencePrice,
            ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
            ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            op.POI_NAME AS poiName,
            phis.POI_DISTANCE AS poiDistance,
            phis.CREATE_TIME as createTime, -- 创建时间
            phis.CREATOR as creator -- 创建人
        FROM
            htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID
        AND (phr.IS_FREQUENCY = 1 OR
            phr.IS_SAME_LEVEL_FREQ = 1 OR
            phr.IS_POI_NEAR_HOTEL = 1 OR
            phr.IS_NO_POI_HOT_AREA = 1 OR
            phr.IS_AREA_GATHER = 1 OR
            phr.IS_HIGH_QUALITY = 1 OR
            phr.IS_SAVED_HOTEL = 1)
        left join htl_rfp.T_ORG_POI op ON op.POI_ID = phis.POI_ID
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND (phr.IS_FREQUENCY_RECOMMEND = 1 OR
            phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1 OR
            phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1 OR
            phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1 OR
            phr.IS_AREA_GATHER_RECOMMEND = 1 OR
            phr.IS_HIGH_QUALITY_RECOMMEND = 1 OR
            phr.IS_SAVED_HOTEL_RECOMMEND = 1)
        </if>
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryProjectFrequencyRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectFrequencyRecommendHotelResponse">
        SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            nvl(phr.IS_FREQUENCY_RECOMMEND,0) AS isRecommend,
            phr.FREQUENCY_RECOMMENDS AS recommendString,
            nvl(pih.BID_STATE,0) AS hotelBidState,
            nvl(pih.INVITE_STATUS,0) AS isInvited,
            pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            th.PROVINCE AS province,
            bcp.dataName AS provinceName,--省份
            th.city AS cityCode,
            th.TELEPHONE AS telePhone,
            bc.dataName AS cityName,--城市
            phis.ROOMNIGHT_COUNT AS salesRoomNight,
            phis.TOTAL_AMOUNT AS totalAmount,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.SAVED_AMOUNT_RATE AS savedAmountRate,
            phis.SERVICE_POINT AS servicePoint,
            phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
            phis.OTA_MIN_PRICE AS otaMinPrice,
            phis.OTA_MAX_PRICE AS otaMaxPrice,
            phis.LOWEST_PRICE AS lowestPrice,
            phis.RECOMMEND_LEVEL AS recommendLevel,
            th.CHN_ADDRESS AS hotelAddress,
            th.CHN_NAME AS hotelName,
            th.HOTEL_STAR AS hotelStar,
            th.PRACICE_DATE AS praciceDate,
            th.FITMENT_DATE AS fitmentDate,
            th.LAYER_COUNT AS roomCount,
            th.RATING AS rating,
            ig.brandid AS hotelGroupId,
            ig.BRANDNAME AS hotelGroupName,
            ib.brandid AS brandId,
            ib.BRANDNAME AS hotelBrandName,
            ch.BRIGHT_SPOT AS brightSpot,
            ch.BREAKFAST_NUM AS breakfastNum,
            ch.REFERENCE_PRICE AS referencePrice,
            ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
            ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            op.POI_NAME AS poiName,
            phis.POI_DISTANCE AS poiDistance,
            phis.CREATE_TIME as createTime, -- 创建时间
            phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
            inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_FREQUENCY = 1
            left join htl_rfp.T_ORG_POI op ON op.POI_ID = phis.POI_ID
            left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
            left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
            left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
            left join htl_info.t_brand ig on th.hotel_group = ig.brandid
            left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
            left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
            left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_FREQUENCY_RECOMMEND = 1
        </if>
            AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryProjectFrequencyRecommendHotelStat" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
            phis.project_id AS projectId,
            COUNT(phis.hotel_id) AS totalHotelCount,
            SUM(nvl(phr.IS_FREQUENCY_RECOMMEND,0)) AS recommendHotelCount,
            SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5 THEN 1 ELSE 0 END) AS bidHotelCount,
            SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_FREQUENCY = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_FREQUENCY_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>

    <select id="queryProjectFrequencySameLevelRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectFrequencySameLevelRecommendHotelResponse">
        SELECT
        phis.project_id AS projectId,
        phis.hotel_id AS hotelId,
        nvl(phr.IS_SAME_LEVEL_FREQ_RECOMMEND,0) AS isRecommend,
        phr.SAME_LEVEL_FREQ_RECOMMENDS AS recommendString,
        phr.SAME_LEVEL_FREQ_HOTEL_ID AS sameLevelFreqHotelId,
        phr.SAME_LEVEL_FREQ_HOTEL_INFO AS sameLevelFreqHotelInfo,
        nvl(pih.BID_STATE,0) AS hotelBidState,
        nvl(pih.INVITE_STATUS,0) AS isInvited,
        pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
        th.PROVINCE AS province,
        bcp.dataName AS provinceName,--省份
        th.city AS cityCode,
        th.TELEPHONE AS telePhone,
        bc.dataName AS cityName,--城市
        phis.ROOMNIGHT_COUNT AS salesRoomNight,
        phis.TOTAL_AMOUNT AS totalAmount,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.SAVED_AMOUNT_RATE AS savedAmountRate,
        phis.SERVICE_POINT AS servicePoint,
        phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
        phis.OTA_MIN_PRICE AS otaMinPrice,
        phis.OTA_MAX_PRICE AS otaMaxPrice,
        phis.LOWEST_PRICE AS lowestPrice,
        phis.RECOMMEND_LEVEL AS recommendLevel,
        th.CHN_ADDRESS AS hotelAddress,
        th.CHN_NAME AS hotelName,
        th.HOTEL_STAR AS hotelStar,
        th.PRACICE_DATE AS praciceDate,
        th.FITMENT_DATE AS fitmentDate,
        th.LAYER_COUNT AS roomCount,
        th.RATING AS rating,
        ig.brandid AS hotelGroupId,
        ig.BRANDNAME AS hotelGroupName,
        ib.brandid AS brandId,
        ib.BRANDNAME AS hotelBrandName,
        ch.BRIGHT_SPOT AS brightSpot,
        ch.BREAKFAST_NUM AS breakfastNum,
        ch.REFERENCE_PRICE AS referencePrice,
        ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
        ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
        op.POI_NAME AS poiName,
        phis.POI_DISTANCE AS poiDistance,
        phis.CREATE_TIME as createTime, -- 创建时间
        phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_SAME_LEVEL_FREQ = 1
        left join htl_rfp.T_ORG_POI op ON op.POI_ID = phis.POI_ID
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
        </if>
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryProjectFrequencySameLevelRecommendHotelStat" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
        phis.project_id AS projectId,
        COUNT(phis.hotel_id) AS totalHotelCount,
        SUM(nvl(phr.IS_SAME_LEVEL_FREQ_RECOMMEND,0)) AS recommendHotelCount,
        SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
        SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_SAME_LEVEL_FREQ = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAME_LEVEL_FREQ_RECOMMEND = 1
        </if>
        GROUP BY phis.project_id
    </select>

    <select id="queryPoiNearHotelRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryPoiNearHotelRecommendHotelResponse">
        SELECT
        phis.project_id AS projectId,
        phis.hotel_id AS hotelId,
        nvl(phr.IS_POI_NEAR_HOTEL_RECOMMEND,0) AS isRecommend,
        phr.POI_NEAR_HOTEL_RECOMMENDS AS recommendString,
        phr.POI_NEAR_HOTEL_POI_ID AS poiId,
        nvl(pih.BID_STATE,0) AS hotelBidState,
        pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
        nvl(pih.INVITE_STATUS,0) AS isInvited,
        th.PROVINCE AS province,
        bcp.dataName AS provinceName,--省份
        th.city AS cityCode,
        th.TELEPHONE AS telePhone,
        bc.dataName AS cityName,--城市
        phis.ROOMNIGHT_COUNT AS salesRoomNight,
        phis.TOTAL_AMOUNT AS totalAmount,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.SAVED_AMOUNT_RATE AS savedAmountRate,
        phis.SERVICE_POINT AS servicePoint,
        phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
        phis.OTA_MIN_PRICE AS otaMinPrice,
        phis.OTA_MAX_PRICE AS otaMaxPrice,
        phis.LOWEST_PRICE AS lowestPrice,
        phis.RECOMMEND_LEVEL AS recommendLevel,
        th.CHN_ADDRESS AS hotelAddress,
        th.CHN_NAME AS hotelName,
        th.HOTEL_STAR AS hotelStar,
        th.PRACICE_DATE AS praciceDate,
        th.FITMENT_DATE AS fitmentDate,
        th.LAYER_COUNT AS roomCount,
        th.RATING AS rating,
        ig.brandid AS hotelGroupId,
        ig.BRANDNAME AS hotelGroupName,
        ib.brandid AS brandId,
        ib.BRANDNAME AS hotelBrandName,
        ch.BRIGHT_SPOT AS brightSpot,
        ch.BREAKFAST_NUM AS breakfastNum,
        ch.REFERENCE_PRICE AS referencePrice,
        ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
        ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
        op.POI_NAME AS poiName,
        pp.POI_HOTEL_STAT_3KM AS poiHotelStat3Km,
        pp.TOTAL_NIGHT_ROOM_COUNT AS totalNightRoomCount,
        phis.POI_DISTANCE AS poiDistance,
        phis.CREATE_TIME as createTime, -- 创建时间
        phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_POI_NEAR_HOTEL = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_rfp.T_PROJECT_POI pp ON phis.PROJECT_ID = pp.PROJECT_ID AND phr.POI_NEAR_HOTEL_POI_ID = pp.POI_ID
        left join htl_rfp.T_ORG_POI op ON op.POI_ID = pp.POI_ID
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>


    <select id="queryNoPoiHotAreaRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryNoPoiHotAreaRecommendHotelResponse">
        SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            nvl(phr.IS_NO_POI_HOT_AREA_RECOMMEND,0) AS isRecommend,
            phr.NO_POI_HOT_AREA_RECOMMENDS AS recommendString,
            phr.NO_POI_HOTEL_ID AS noPoiHotelId,
            phr.NO_POI_HOTEL_INFO AS noPoiHotelInfo,
            nvl(pih.BID_STATE,0) AS hotelBidState,
            pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            nvl(pih.INVITE_STATUS,0) AS isInvited,
            th.PROVINCE AS province,
            bcp.dataName AS provinceName,--省份
            th.city AS cityCode,
            th.TELEPHONE AS telePhone,
            bc.dataName AS cityName,--城市
            phis.ROOMNIGHT_COUNT AS salesRoomNight,
            phis.TOTAL_AMOUNT AS totalAmount,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.SAVED_AMOUNT_RATE AS savedAmountRate,
            phis.SERVICE_POINT AS servicePoint,
            phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
            phis.PRICE_LEVEL_ROOM_NIGHT AS priceLevelRoomNight,
            phis.OTA_MIN_PRICE AS otaMinPrice,
            phis.OTA_MAX_PRICE AS otaMaxPrice,
            phis.LOWEST_PRICE AS lowestPrice,
            phis.RECOMMEND_LEVEL AS recommendLevel,
            th.CHN_ADDRESS AS hotelAddress,
            th.CHN_NAME AS hotelName,
            th.HOTEL_STAR AS hotelStar,
            th.PRACICE_DATE AS praciceDate,
            th.FITMENT_DATE AS fitmentDate,
            th.LAYER_COUNT AS roomCount,
            th.RATING AS rating,
            ig.brandid AS hotelGroupId,
            ig.BRANDNAME AS hotelGroupName,
            ib.brandid AS brandId,
            ib.BRANDNAME AS hotelBrandName,
            ch.BRIGHT_SPOT AS brightSpot,
            ch.BREAKFAST_NUM AS breakfastNum,
            ch.REFERENCE_PRICE AS referencePrice,
            ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
            ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            phis.POI_DISTANCE AS poiDistance,
            phis.CREATE_TIME as createTime, -- 创建时间
            phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_NO_POI_HOT_AREA = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryAreaGatherRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryAreaGatherRecommendHotelResponse">
        SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            nvl(phr.IS_AREA_GATHER_RECOMMEND,0) AS isRecommend,
            phr.AREA_GATHER_RECOMMENDS AS recommendString,
            phr.AREA_GATHER_HOTEL_INFO AS areaGatherHotelInfo,
            nvl(pih.BID_STATE,0) AS hotelBidState,
            pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            nvl(pih.INVITE_STATUS,0) AS isInvited,
            th.PROVINCE AS province,
            bcp.dataName AS provinceName,--省份
            th.city AS cityCode,
            th.TELEPHONE AS telePhone,
            bc.dataName AS cityName,--城市
            phis.ROOMNIGHT_COUNT AS salesRoomNight,
            phis.TOTAL_AMOUNT AS totalAmount,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.SAVED_AMOUNT_RATE AS savedAmountRate,
            phis.SERVICE_POINT AS servicePoint,
            phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
            phis.OTA_MIN_PRICE AS otaMinPrice,
            phis.OTA_MAX_PRICE AS otaMaxPrice,
            phis.LOWEST_PRICE AS lowestPrice,
            phis.RECOMMEND_LEVEL AS recommendLevel,
            phis.PRICE_LEVEL_ROOM_NIGHT AS priceLevelRoomNight,
            th.CHN_ADDRESS AS hotelAddress,
            th.CHN_NAME AS hotelName,
            th.HOTEL_STAR AS hotelStar,
            th.PRACICE_DATE AS praciceDate,
            th.FITMENT_DATE AS fitmentDate,
            th.LAYER_COUNT AS roomCount,
            th.RATING AS rating,
            ig.brandid AS hotelGroupId,
            ig.BRANDNAME AS hotelGroupName,
            ib.brandid AS brandId,
            ib.BRANDNAME AS hotelBrandName,
            ch.BRIGHT_SPOT AS brightSpot,
            ch.BREAKFAST_NUM AS breakfastNum,
            ch.REFERENCE_PRICE AS referencePrice,
            ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
            ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            phis.POI_DISTANCE AS poiDistance,
            phis.CREATE_TIME as createTime, -- 创建时间
            phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_AREA_GATHER = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_AREA_GATHER_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="queryHighQualityRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryHighQualityRecommendHotelResponse">
        SELECT
        phis.project_id AS projectId,
        phis.hotel_id AS hotelId,
        nvl(phr.IS_HIGH_QUALITY_RECOMMEND,0) AS isRecommend,
        nvl(pih.BID_STATE,0) AS hotelBidState,
        pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
        nvl(pih.INVITE_STATUS,0) AS isInvited,
        th.PROVINCE AS province,
        bcp.dataName AS provinceName,--省份
        th.city AS cityCode,
        th.TELEPHONE AS telePhone,
        bc.dataName AS cityName,--城市
        phis.ROOMNIGHT_COUNT AS salesRoomNight,
        phis.TOTAL_AMOUNT AS totalAmount,
        phis.SAVED_AMOUNT AS savedAmount,
        phis.SAVED_AMOUNT_RATE AS savedAmountRate,
        phis.SERVICE_POINT AS servicePoint,
        phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
        phis.OTA_MIN_PRICE AS otaMinPrice,
        phis.OTA_MAX_PRICE AS otaMaxPrice,
        phis.LOWEST_PRICE AS lowestPrice,
        phis.RECOMMEND_LEVEL AS recommendLevel,
        phis.PRICE_LEVEL_ROOM_NIGHT AS priceLevelRoomNight,
        th.CHN_ADDRESS AS hotelAddress,
        th.CHN_NAME AS hotelName,
        th.HOTEL_STAR AS hotelStar,
        th.PRACICE_DATE AS praciceDate,
        th.FITMENT_DATE AS fitmentDate,
        th.LAYER_COUNT AS roomCount,
        th.RATING AS rating,
        ig.brandid AS hotelGroupId,
        ig.BRANDNAME AS hotelGroupName,
        ib.brandid AS brandId,
        ib.BRANDNAME AS hotelBrandName,
        ch.BRIGHT_SPOT AS brightSpot,
        ch.BREAKFAST_NUM AS breakfastNum,
        ch.REFERENCE_PRICE AS referencePrice,
        ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
        ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
        phis.POI_DISTANCE AS poiDistance,
        phis.CREATE_TIME as createTime, -- 创建时间
        phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_HIGH_QUALITY = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_HIGH_QUALITY_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>

    <select id="querySavedRecommendHotelList" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QuerySavedRecommendHotelResponse">
        SELECT
            phis.project_id AS projectId,
            phis.hotel_id AS hotelId,
            nvl(phr.IS_SAVED_HOTEL_RECOMMEND,0) AS isRecommend,
            nvl(pih.BID_STATE,0) AS hotelBidState,
            pih.PROJECT_INTENT_HOTEL_ID AS projectIntentHotelId,
            nvl(pih.INVITE_STATUS,0) AS isInvited,
            th.PROVINCE AS province,
            bcp.dataName AS provinceName,--省份
            th.city AS cityCode,
            th.TELEPHONE AS telePhone,
            bc.dataName AS cityName,--城市
            phis.ROOMNIGHT_COUNT AS salesRoomNight,
            phis.TOTAL_AMOUNT AS totalAmount,
            phis.SAVED_AMOUNT AS savedAmount,
            phis.SAVED_AMOUNT_RATE AS savedAmountRate,
            phis.SERVICE_POINT AS servicePoint,
            phis.TOTAL_VIOLATIONS_COUNT AS violationsCount,
            phis.OTA_MIN_PRICE AS otaMinPrice,
            phis.OTA_MAX_PRICE AS otaMaxPrice,
            phis.LOWEST_PRICE AS lowestPrice,
            phis.RECOMMEND_LEVEL AS recommendLevel,
            phis.PRICE_LEVEL_ROOM_NIGHT AS priceLevelRoomNight,
            th.CHN_ADDRESS AS hotelAddress,
            th.CHN_NAME AS hotelName,
            th.HOTEL_STAR AS hotelStar,
            th.PRACICE_DATE AS praciceDate,
            th.FITMENT_DATE AS fitmentDate,
            th.LAYER_COUNT AS roomCount,
            th.RATING AS rating,
            ig.brandid AS hotelGroupId,
            ig.BRANDNAME AS hotelGroupName,
            ib.brandid AS brandId,
            ib.BRANDNAME AS hotelBrandName,
            ch.BRIGHT_SPOT AS brightSpot,
            ch.BREAKFAST_NUM AS breakfastNum,
            ch.REFERENCE_PRICE AS referencePrice,
            ch.LAST_ROOM_AVAILABLE AS lastRoomAvailable,
            ch.REQUIRED_ROOM_NIGHT AS requiredRoomNight,
            phis.POI_DISTANCE AS poiDistance,
            phis.CREATE_TIME as createTime, -- 创建时间
            phis.CREATOR as creator -- 创建人
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_SAVED_HOTEL = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        left join htl_rfp.T_RECOMMEND_HOTEL ch on phis.hotel_id = ch.HOTEL_ID AND ch.STATE=1
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        left join htl_base.t_areadata bcp ON th.PROVINCE = bcp.datacode AND bcp.datatype = 2
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAVED_HOTEL_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        ORDER BY phis.ROOMNIGHT_COUNT DESC
    </select>
    <select id="queryNoPoiHotAreaRecommendHotelStat" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
        phis.project_id AS projectId,
        COUNT(phis.hotel_id) AS totalHotelCount,
        SUM(nvl(phr.IS_NO_POI_HOT_AREA_RECOMMEND,0)) AS recommendHotelCount,
        SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
        SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_NO_POI_HOT_AREA = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_NO_POI_HOT_AREA_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>

    <select id="queryPoiNearHotelRecommendHotelStat" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
            phis.project_id AS projectId,
            COUNT(phis.hotel_id) AS totalHotelCount,
            SUM(nvl(phr.IS_POI_NEAR_HOTEL_RECOMMEND,0)) AS recommendHotelCount,
            SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
            SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_POI_NEAR_HOTEL = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_POI_NEAR_HOTEL_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>

    <select id="queryAreaGatherRecommendHotelStat"  parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
        phis.project_id AS projectId,
        COUNT(phis.hotel_id) AS totalHotelCount,
        SUM(nvl(phr.IS_AREA_GATHER_RECOMMEND,0)) AS recommendHotelCount,
        SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
        SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_AREA_GATHER = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_AREA_GATHER_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>

    <select id="queryHighQualityRecommendHotelStat"  parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
        phis.project_id AS projectId,
        COUNT(phis.hotel_id) AS totalHotelCount,
        SUM(nvl(phr.IS_HIGH_QUALITY_RECOMMEND,0)) AS recommendHotelCount,
        SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
        SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_HIGH_QUALITY = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_HIGH_QUALITY_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>

    <select id="querySavedHotelRecommendHotelStat"  parameterType="com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest"
            resultType="com.fangcang.rfp.common.dto.response.QueryProjectRecommendHotelStatResponse">
        SELECT
        phis.project_id AS projectId,
        COUNT(phis.hotel_id) AS totalHotelCount,
        SUM(nvl(phr.IS_SAVED_HOTEL_RECOMMEND,0)) AS recommendHotelCount,
        SUM(CASE WHEN nvl(pih.BID_STATE,0) > 0 AND nvl(pih.BID_STATE,0) != 5  THEN 1 ELSE 0 END) AS bidHotelCount,
        SUM(nvl(pih.INVITE_STATUS,0)) AS invitedHotelCount
        FROM
        htl_rfp.t_project_hotel_history_data phis
        inner join htl_rfp.T_PROJECT_HISTORY_RECOMMEND phr ON phis.PROJECT_ID = phr.PROJECT_ID AND phis.HOTEL_ID = phr.HOTEL_ID AND phr.IS_SAVED_HOTEL = 1
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid
        left join htl_rfp.T_PROJECT_INTENT_HOTEL pih ON phis.PROJECT_ID = pih.PROJECT_ID AND phis.HOTEL_ID = pih.HOTEL_ID
        WHERE
        <include refid="queryProjectTotalRecommendHotelWhere"/>
        <if test="isOnlyViewRecommend != null and isOnlyViewRecommend == 1">
            AND phr.IS_SAVED_HOTEL_RECOMMEND = 1
        </if>
        AND phis.IS_UPLOADED = 1
        GROUP BY phis.project_id
    </select>


    <select id="queryLowestPriceItemInfo" resultType="java.lang.String">
        SELECT
            LOWEST_PRICE_ITEM_INFO
        FROM
            htl_rfp.t_project_hotel_history_data
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
    </select>

    <select id="queryProjectHotelHistoryRoomNightCount" resultType="java.lang.Integer">
        SELECT ROOMNIGHT_COUNT
        FROM htl_rfp.t_project_hotel_history_data
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </select>
</mapper>