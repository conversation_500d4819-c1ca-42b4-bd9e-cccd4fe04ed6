<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectLastYearCityStatDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectLastYearCityStat">
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode"/>
        <result column="TOTAL_ROOM_NIGHT" jdbcType="BIGINT" property="totalRoomNight"/>
        <result column="TOTAL_SALES_AMOUNT" jdbcType="DECIMAL" property="totalSalesAmount"/>
        <result column="TOTAL_ROOM_NIGHT_500" jdbcType="BIGINT" property="totalRoomNight500"/>
        <result column="TOTAL_ROOM_NIGHT_400_TO_500" jdbcType="BIGINT" property="totalRoomNight400To500"/>
        <result column="TOTAL_ROOM_NIGHT_300_TO_400" jdbcType="BIGINT" property="totalRoomNight300To400"/>
        <result column="TOTAL_ROOM_NIGHT_200_TO_300" jdbcType="BIGINT" property="totalRoomNight200To300"/>
        <result column="TOTAL_ROOM_NIGHT_200" jdbcType="BIGINT" property="totalRoomNight200"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROJECT_ID, CITY_CODE, TOTAL_ROOM_NIGHT, TOTAL_SALES_AMOUNT,
        TOTAL_ROOM_NIGHT_500,TOTAL_ROOM_NIGHT_400_TO_500,TOTAL_ROOM_NIGHT_300_TO_400,
        TOTAL_ROOM_NIGHT_200_TO_300,TOTAL_ROOM_NIGHT_200,
        CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <select id="selectByProjectIdAndCityCode" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT_LAST_YEAR_CITY_STAT
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
        AND CITY_CODE = #{cityCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByProjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT_LAST_YEAR_CITY_STAT
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>


    <update id="insertOrUpdate" parameterType="com.fangcang.rfp.common.entity.ProjectLastYearCityStat">
        MERGE INTO htl_rfp.T_PROJECT_LAST_YEAR_CITY_STAT dest
            USING (
                SELECT  #{projectId,jdbcType=BIGINT} AS projectId,
                        #{cityCode,jdbcType=VARCHAR} AS cityCode,
                        #{totalRoomNight,jdbcType=BIGINT} AS totalRoomNight,
                        #{totalSalesAmount,jdbcType=DECIMAL} AS totalSalesAmount,
                        #{totalRoomNight500,jdbcType=BIGINT} AS totalRoomNight500,
                        #{totalRoomNight400To500,jdbcType=BIGINT} AS totalRoomNight400To500,
                        #{totalRoomNight300To400,jdbcType=BIGINT} AS totalRoomNight300To400,
                        #{totalRoomNight200To300,jdbcType=BIGINT} AS totalRoomNight200To300,
                        #{totalRoomNight200,jdbcType=BIGINT} AS totalRoomNight200,
                        #{creator,jdbcType=VARCHAR} AS creator,
                        #{modifier,jdbcType=VARCHAR} AS modifier
                FROM dual
            ) src
            ON (dest.PROJECT_ID = src.projectId AND dest.CITY_CODE = src.cityCode)
            WHEN MATCHED THEN
                UPDATE SET
                    dest.TOTAL_ROOM_NIGHT = #{totalRoomNight,jdbcType=BIGINT},
                    dest.TOTAL_SALES_AMOUNT = #{totalSalesAmount,jdbcType=DECIMAL},
                    dest.TOTAL_ROOM_NIGHT_500 =  #{totalRoomNight500,jdbcType=BIGINT},
                    dest.TOTAL_ROOM_NIGHT_400_TO_500 = #{totalRoomNight400To500,jdbcType=BIGINT},
                    dest.TOTAL_ROOM_NIGHT_300_TO_400 = #{totalRoomNight300To400,jdbcType=BIGINT},
                    dest.TOTAL_ROOM_NIGHT_200_TO_300 = #{totalRoomNight200To300,jdbcType=BIGINT},
                    dest.TOTAL_ROOM_NIGHT_200 = #{totalRoomNight200,jdbcType=BIGINT},
                    dest.MODIFIER = #{modifier,jdbcType=VARCHAR},
                    dest.MODIFY_TIME = SYSDATE
            WHEN NOT MATCHED THEN
                INSERT (PROJECT_ID, CITY_CODE, TOTAL_ROOM_NIGHT, TOTAL_SALES_AMOUNT,
                        TOTAL_ROOM_NIGHT_500,TOTAL_ROOM_NIGHT_400_TO_500,TOTAL_ROOM_NIGHT_300_TO_400,
                        TOTAL_ROOM_NIGHT_200_TO_300,TOTAL_ROOM_NIGHT_200,
                        CREATOR, CREATE_TIME)
                    VALUES (src.projectId, src.cityCode, src.totalRoomNight, src.totalSalesAmount,
                            src.totalRoomNight500, src.totalRoomNight400To500, src.totalRoomNight300To400,
                            src.totalRoomNight200To300,src.totalRoomNight200,
                            src.creator, sysdate)
    </update>

</mapper>