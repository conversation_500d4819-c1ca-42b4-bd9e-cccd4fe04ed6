<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrgHotelGroupUserLogDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.dto.response.OrgHotelGroupUserLogResponse">
        <result column="CONTACT_USER_LOG_ID" jdbcType="BIGINT" property="contactUserLogId"/>
        <result column="ENTERPRISE_ORG_ID" jdbcType="BIGINT" property="enterpriseOrgId"/>
        <result column="HOTEL_GROUP_ORG_ID" jdbcType="BIGINT" property="hotelGroupOrgId"/>
        <result column="HOTEL_GROUP_USER_ID" jdbcType="BIGINT" property="hotelGroupUserId"/>
        <result column="OPERATE" jdbcType="VARCHAR" property="operate"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONTACT_USER_LOG_ID, ENTERPRISE_ORG_ID, HOTEL_GROUP_ORG_ID, HOTEL_GROUP_USER_ID, OPERATE, CREATOR, CREATE_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.OrgHotelGroupUserLog" keyProperty="contactUserLogId" useGeneratedKeys="true">
        <selectKey keyProperty="contactUserLogId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_CONTACT_USER_LOG_ID.nextval from dual
        </selectKey>
        insert into htl_rfp.T_ORG_HOTEL_GROUP_USER_LOG (CONTACT_USER_LOG_ID, ENTERPRISE_ORG_ID, HOTEL_GROUP_ORG_ID,HOTEL_GROUP_USER_ID,
        OPERATE, CREATOR, CREATE_TIME)
        values (#{contactUserLogId,jdbcType=BIGINT},
                #{enterpriseOrgId,jdbcType=BIGINT},
                #{hotelGroupOrgId,jdbcType=BIGINT},
                 #{hotelGroupUserId,jdbcType=BIGINT},
                #{operate,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR}, sysdate
            )
  </insert>

    <select id="queryLogList" resultMap="BaseResultMap">
        select * from
                     htl_rfp.T_ORG_HOTEL_GROUP_USER_LOG where ENTERPRISE_ORG_ID = #{enterpriseOrgId} and HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId}
        ORDER BY CONTACT_USER_LOG_ID DESC
    </select>

</mapper>