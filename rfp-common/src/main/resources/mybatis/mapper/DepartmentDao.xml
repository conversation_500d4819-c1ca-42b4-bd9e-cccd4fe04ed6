<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.DepartmentDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.Department">
        <result column="DEPART_ID" jdbcType="DECIMAL" property="departId"/>
        <result column="ORG_ID" jdbcType="DECIMAL" property="orgId"/>
        <result column="DEPART_NAME" jdbcType="VARCHAR" property="departName"/>
        <result column="PARENT_DEPART_ID" jdbcType="DECIMAL" property="parentDepartId"/>
        <result column="STATE" jdbcType="DECIMAL" property="state"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      DEPART_ID, ORG_ID, DEPART_NAME, PARENT_DEPART_ID, STATE, CREATOR,
      CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.Department" keyProperty="departId"
            useGeneratedKeys="true">

        <selectKey keyProperty="departId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_DEPARTMENT.nextval from dual
        </selectKey>
        insert into htl_rfp.T_DEPARTMENT (DEPART_ID, ORG_ID, DEPART_NAME,
        PARENT_DEPART_ID, STATE, CREATOR,
        CREATE_TIME, MODIFIER, MODIFY_TIME
        )
        values (#{departId,jdbcType=DECIMAL}, #{orgId,jdbcType=DECIMAL}, #{departName,jdbcType=VARCHAR},
        #{parentDepartId,jdbcType=DECIMAL},1, #{creator,jdbcType=VARCHAR},
        sysdate, #{modifier,jdbcType=VARCHAR}, sysdate
        )
    </insert>

    <update id="updateDepartment" parameterType="com.fangcang.rfp.common.dto.request.UpdateDepartmentDto">
        update htl_rfp.T_DEPARTMENT
        set MODIFIER = #{operator} , MODIFY_TIME=sysdate
        <if test="departName != null and departName !=''">
            ,DEPART_NAME = #{departName}
        </if>
        <if test="state != null">
            ,STATE = #{state}
        </if>
        where DEPART_ID = #{departId}
    </update>

    <select id="queryFirstLevelDepartmentByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_DEPARTMENT
        where ORG_ID = #{orgId} and PARENT_DEPART_ID is null and STATE = 1
    </select>

    <select id="queryDepartmentByParentDepartId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_DEPARTMENT
        where PARENT_DEPART_ID = #{parentDepartId} and STATE = 1
    </select>

    <select id="queryDepartmentByDepartId" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_DEPARTMENT
        where STATE = 1 and DEPART_ID in
        <foreach collection="departIds" open="(" close=")" item="departId" separator="," index="index">
            #{departId}
        </foreach>
    </select>
    <select id="queryDepartmentByName"  resultType="com.fangcang.rfp.common.dto.response.QueryDepartmentResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryDepartmentRequest">
        select
        DEPART_ID as departId, DEPART_NAME as departName
        from htl_rfp.T_DEPARTMENT
        where STATE = 1 and ORG_ID = #{orgId}
        <if test="departName != null and departName !=''">
        and DEPART_NAME like CONCAT(CONCAT('%',#{departName}),'%')
        </if>
    </select>
</mapper>