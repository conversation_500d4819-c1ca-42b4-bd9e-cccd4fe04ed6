<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelBidStrategyDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy">
    <id column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId" />
    <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId" />
    <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId" />
    <result column="SUPPORT_PAY_AT_HOTEL" jdbcType="INTEGER" property="supportPayAtHotel" />
    <result column="SUPPORT_CO_PAY" jdbcType="INTEGER" property="supportCoPay" />
    <result column="SUPPORT_NO_GUARANTEE" jdbcType="INTEGER" property="supportNoGuarantee" />
    <result column="SUPPORT_CHECKIN_INFO" jdbcType="INTEGER" property="supportCheckinInfo" />
    <result column="SUPPORT_PAY_EARLY_CHECKOUT" jdbcType="INTEGER" property="supportPayEarlyCheckout" />
    <result column="EARLY_CHECKIN_TIME" jdbcType="VARCHAR" property="earlyCheckinTime" />
    <result column="LATE_CHECKOUT_TIME" jdbcType="VARCHAR" property="lateCheckoutTime" />
    <result column="LATE_RESERVE_TIME" jdbcType="VARCHAR" property="lateReserveTime" />
    <result column="DO_AFTER_LATE_RESERVE_TIME" jdbcType="INTEGER" property="doAfterLateReserveTime" />
    <result column="SUPPORT_MONTHLY_BALANCE" jdbcType="INTEGER" property="supportMonthlyBalance" />
    <result column="SUPPORT_INCLUDE_COMMISSION" jdbcType="INTEGER" property="supportIncludeCommission" />
    <result column="TEND_COMMISSION" jdbcType="DECIMAL" property="tendCommission" />
    <result column="PROVIDE_INVOICE_TYPE" jdbcType="INTEGER" property="provideInvoiceType" />
    <result column="PROVIDE_INVOICE_TAX_RATE" jdbcType="DECIMAL" property="provideInvoiceTaxRate" />
    <result column="SUPPORT_WIFI" jdbcType="INTEGER" property="supportWifi" />
    <result column="SUPPORT_INCLUDE_TAX_SERVICE" jdbcType="INTEGER" property="supportIncludeTaxService" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, SUPPORT_PAY_AT_HOTEL, SUPPORT_CO_PAY, 
    SUPPORT_NO_GUARANTEE, SUPPORT_CHECKIN_INFO, SUPPORT_PAY_EARLY_CHECKOUT, EARLY_CHECKIN_TIME, 
    LATE_CHECKOUT_TIME, LATE_RESERVE_TIME, DO_AFTER_LATE_RESERVE_TIME, SUPPORT_MONTHLY_BALANCE, 
    SUPPORT_INCLUDE_COMMISSION, TEND_COMMISSION, PROVIDE_INVOICE_TYPE, PROVIDE_INVOICE_TAX_RATE,
    SUPPORT_WIFI, SUPPORT_INCLUDE_TAX_SERVICE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_BID_STRATEGY
    where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
  </select>

  <select id="selectByProjectIdAndHotelID" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_BID_STRATEGY
    where PROJECT_ID = #{projectId,jdbcType=BIGINT} and HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByProjectAndHotelId" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy">
    delete from T_PROJECT_HOTEL_BID_STRATEGY
    where PROJECT_ID = #{projectId,jdbcType=DECIMAL} AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
  </delete>


  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy">
    insert into T_PROJECT_HOTEL_BID_STRATEGY (PROJECT_INTENT_HOTEL_ID, PROJECT_ID, 
      HOTEL_ID, SUPPORT_PAY_AT_HOTEL, SUPPORT_CO_PAY, 
      SUPPORT_NO_GUARANTEE, SUPPORT_CHECKIN_INFO, 
      SUPPORT_PAY_EARLY_CHECKOUT, EARLY_CHECKIN_TIME, 
      LATE_CHECKOUT_TIME, LATE_RESERVE_TIME, DO_AFTER_LATE_RESERVE_TIME, 
      SUPPORT_MONTHLY_BALANCE, SUPPORT_INCLUDE_COMMISSION, 
      TEND_COMMISSION, PROVIDE_INVOICE_TYPE, PROVIDE_INVOICE_TAX_RATE,
      SUPPORT_WIFI, SUPPORT_INCLUDE_TAX_SERVICE,
      CREATOR, CREATE_TIME)
    values (#{projectIntentHotelId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT},
      #{hotelId,jdbcType=BIGINT}, #{supportPayAtHotel,jdbcType=DECIMAL}, #{supportCoPay,jdbcType=DECIMAL},
      #{supportNoGuarantee,jdbcType=DECIMAL}, #{supportCheckinInfo,jdbcType=DECIMAL}, 
      #{supportPayEarlyCheckout,jdbcType=DECIMAL}, #{earlyCheckinTime,jdbcType=VARCHAR}, 
      #{lateCheckoutTime,jdbcType=VARCHAR}, #{lateReserveTime,jdbcType=VARCHAR}, #{doAfterLateReserveTime,jdbcType=DECIMAL}, 
      #{supportMonthlyBalance,jdbcType=DECIMAL}, #{supportIncludeCommission,jdbcType=DECIMAL}, 
      #{tendCommission,jdbcType=DECIMAL}, #{provideInvoiceType,jdbcType=DECIMAL}, #{provideInvoiceTaxRate,jdbcType=DECIMAL},
      #{supportWifi,jdbcType=INTEGER},#{supportIncludeTaxService,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy">
    update T_PROJECT_HOTEL_BID_STRATEGY
    <set>
      <if test="supportPayAtHotel != null">
        SUPPORT_PAY_AT_HOTEL = #{supportPayAtHotel,jdbcType=DECIMAL},
      </if>
      <if test="supportCoPay != null">
        SUPPORT_CO_PAY = #{supportCoPay,jdbcType=DECIMAL},
      </if>
      <if test="supportNoGuarantee != null">
        SUPPORT_NO_GUARANTEE = #{supportNoGuarantee,jdbcType=DECIMAL},
      </if>
      <if test="supportCheckinInfo != null">
        SUPPORT_CHECKIN_INFO = #{supportCheckinInfo,jdbcType=DECIMAL},
      </if>
      <if test="supportPayEarlyCheckout != null">
        SUPPORT_PAY_EARLY_CHECKOUT = #{supportPayEarlyCheckout,jdbcType=DECIMAL},
      </if>
      <if test="earlyCheckinTime != null">
        EARLY_CHECKIN_TIME = #{earlyCheckinTime,jdbcType=VARCHAR},
      </if>
      <if test="lateCheckoutTime != null">
        LATE_CHECKOUT_TIME = #{lateCheckoutTime,jdbcType=VARCHAR},
      </if>
      <if test="lateReserveTime != null">
        LATE_RESERVE_TIME = #{lateReserveTime,jdbcType=VARCHAR},
      </if>
      <if test="doAfterLateReserveTime != null">
        DO_AFTER_LATE_RESERVE_TIME = #{doAfterLateReserveTime,jdbcType=DECIMAL},
      </if>
      <if test="supportMonthlyBalance != null">
        SUPPORT_MONTHLY_BALANCE = #{supportMonthlyBalance,jdbcType=DECIMAL},
      </if>
      <if test="supportIncludeCommission != null">
        SUPPORT_INCLUDE_COMMISSION = #{supportIncludeCommission,jdbcType=DECIMAL},
      </if>
      <if test="tendCommission != null">
        TEND_COMMISSION = #{tendCommission,jdbcType=DECIMAL},
      </if>
      <if test="provideInvoiceType != null">
        PROVIDE_INVOICE_TYPE = #{provideInvoiceType,jdbcType=DECIMAL},
      </if>
      <if test="provideInvoiceTaxRate != null">
        PROVIDE_INVOICE_TAX_RATE = #{provideInvoiceTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="supportWifi != null">
        SUPPORT_WIFI = #{supportWifi,jdbcType=INTEGER},
      </if>
      <if test="supportIncludeTaxService != null">
        SUPPORT_INCLUDE_TAX_SERVICE = #{supportIncludeTaxService,jdbcType=INTEGER},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
        MODIFY_TIME = SYSDATE
    </set>
    where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL}
  </update>

  <select id="selectByProjectIntentHotelIds" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_PROJECT_HOTEL_BID_STRATEGY
    where PROJECT_INTENT_HOTEL_ID IN
    <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
      #{projectIntentHotelId}
    </foreach>
  </select>

  <select id="selectByProjectHotelBidStrategy" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidStrategy" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_PROJECT_HOTEL_BID_STRATEGY
    WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
  </select>
</mapper>