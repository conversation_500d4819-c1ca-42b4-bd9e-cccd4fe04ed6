<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectInviteHotelDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectInviteHotel">
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROJECT_ID, HOTEL_ID, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <update id="insertOrUpdate" parameterType="com.fangcang.rfp.common.entity.ProjectInviteHotel">
        MERGE INTO htl_rfp.T_PROJECT_INVITE_HOTEL dest
            USING (
                SELECT  #{projectId,jdbcType=BIGINT} AS projectId,
                        #{hotelId,jdbcType=BIGINT} AS hotelId,
                        #{creator,jdbcType=VARCHAR} AS creator,
                        #{modifier,jdbcType=VARCHAR} AS modifier
                FROM dual
            ) src
            ON (dest.PROJECT_ID = src.projectId AND dest.HOTEL_ID = src.hotelId)
            WHEN MATCHED THEN
                UPDATE SET
                    dest.MODIFIER = #{modifier,jdbcType=VARCHAR},
                    dest.MODIFY_TIME = SYSDATE
            WHEN NOT MATCHED THEN
                INSERT (PROJECT_ID, HOTEL_ID, CREATOR, CREATE_TIME)
                    VALUES (src.projectId, src.hotelId, src.creator, sysdate)
    </update>

    <delete id="deleteProjectHotel" parameterType="com.fangcang.rfp.common.entity.ProjectInviteHotel">
        DELETE FROM
                 htl_rfp.T_PROJECT_INVITE_HOTEL
        WHERE "PROJECT_ID" = #{projectId,jdbcType=BIGINT}
        AND "HOTEL_ID" = #{hotelId,jdbcType=BIGINT}
    </delete>

    <select id="queryListByProject" resultType="com.fangcang.rfp.common.entity.ProjectInviteHotel">
        SELECT *
        FROM htl_rfp.T_PROJECT_INVITE_HOTEL
        WHERE "PROJECT_ID" = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="queryByProjectHotelId" resultType="com.fangcang.rfp.common.entity.ProjectInviteHotel">
        SELECT *
        FROM htl_rfp.T_PROJECT_INVITE_HOTEL
        WHERE "PROJECT_ID" = #{projectId,jdbcType=BIGINT} AND "HOTEL_ID" = #{hotelId,jdbcType=BIGINT}
    </select>
    <select id="queryHistoryProjectInfoList"
            resultType="com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHistoryProjectInfoRequest">
        SELECT
        phis.project_id AS projectId,
        phis.hotel_id AS hotelId,
        phis.CITY_ORDER AS cityOrder,
        th.chn_name AS hotelName,--酒店名称
        th.city AS cityCode,
        bc.dataName AS cityName,--城市
        phis.ROOMNIGHT_COUNT AS roomNightCount,--成交间夜数
        phis.TOTAL_AMOUNT AS totalAmount,-- 成交金额
        phis.CREATE_TIME as createTime, -- 创建时间
        phis.CREATOR as creator, -- 创建人
        ig.groupId AS groupId,
        ig.brandname as hotelGroupName,-- 酒店集团
        ib.brandId AS brandId,
        ib.brandname as brandName -- 品牌
        FROM
        htl_rfp.t_project_hotel_history_data phis
        left join htl_info.t_hotel th on phis.hotel_id = th.hotelid   and th.isactive=1   and th.country='CN'
        left join htl_rfp.t_project p on phis.project_id = p.project_id
        left join htl_info.t_brand ig on th.hotel_group = ig.brandid
        left join htl_info.t_brand ib on th.hotelbrand = ib.brandid
        left join htl_base.t_areadata bc ON th.city = bc.datacode AND bc.datatype = 3 AND bc.countrycode = 'CN'
        WHERE
        phis.PROJECT_ID = #{projectId}
        <if test="cityCode != null and cityCode !=''"> --城市
            AND th.city = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="hotelId != null"> -- 酒店
            AND phis.hotel_id = #{hotelId,jdbcType=BIGINT}
        </if>
        <if test="brandId != null and brandId > 0">  -- hotel表 HOTELBRAND 存的 brandId
            AND th.HOTELBRAND = #{brandId}
        </if>
        <if test="hotelGroupId != null and hotelGroupId > 0"> -- 酒店集团
            AND th.hotel_group = #{hotelGroupId}
        </if>
        <if test="hotelIdList != null">
            AND phis.hotel_id  IN
            <foreach collection="hotelIdList" open="(" close=")" item="hotelId" separator="," index="index">
                #{hotelId}
            </foreach>
        </if>
        ORDER BY ROOMNIGHT_COUNT DESC
    </select>
</mapper>