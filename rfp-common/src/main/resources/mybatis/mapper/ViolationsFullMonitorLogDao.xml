<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ViolationsFullMonitorLogDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ViolationsFullMonitorLog">
        <result column="VIOLATIONS_MONITOR_ID" jdbcType="BIGINT" property="violationsMonitorId"/>
        <result column="ROOM_TYPE_ID" jdbcType="BIGINT" property="roomTypeId"/>
        <result column="BREAKFAST_NUM" jdbcType="INTEGER" property="breakfastNum"/>
        <result column="SALE_DATE" jdbcType="TIMESTAMP" property="saleDate"/>
        <result column="SALE_PRICE" jdbcType="DECIMAL" property="salePrice"/>
        <result column="QUERY_DATE_TIME" jdbcType="TIMESTAMP" property="queryDateTime"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        VIOLATIONS_MONITOR_ID, ROOM_TYPE_ID,BREAKFAST_NUM, SALE_DATE,
      SALE_PRICE, QUERY_DATE_TIME, CREATE_TIME
    </sql>

    <select id="queryList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM
            T_VIOLATIONS_FULL_MONITOR_LOG
        WHERE
            VIOLATIONS_MONITOR_ID = #{violationMonitorId}
        ORDER BY QUERY_DATE_TIME ASC
    </select>

    <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.ViolationsFullMonitorLog">
        INSERT INTO T_VIOLATIONS_FULL_MONITOR_LOG(
            VIOLATIONS_MONITOR_ID,
            ROOM_TYPE_ID,
            BREAKFAST_NUM,
            SALE_DATE,
            SALE_PRICE,
            QUERY_DATE_TIME,
            CREATE_TIME
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            (SELECT #{item.violationsMonitorId,jdbcType=BIGINT},
            #{item.roomTypeId,jdbcType=BIGINT},
            #{item.breakfastNum,jdbcType=INTEGER},
            #{item.saleDate,jdbcType=TIMESTAMP},
            #{item.salePrice,jdbcType=DECIMAL},
            #{item.queryDateTime,jdbcType=TIMESTAMP},
            SYSDATE from dual)
        </foreach>
    </insert>

</mapper>