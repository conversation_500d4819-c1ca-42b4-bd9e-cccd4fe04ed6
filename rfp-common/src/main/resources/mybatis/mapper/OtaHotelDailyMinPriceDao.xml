<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OtaHotelDailyMinPriceDao">

    <select id="queryList" resultType="com.fangcang.rfp.common.dto.response.OtaHotelDailyMinPriceResponse">
        SELECT
            HOTEL_ID AS hotelId,
            SALES_DATE AS salesDate,
            MIN_PRICE AS minPrice
        FROM htl_order.T_OTA_HOTEL_DAILY_MIN_PRICE
        WHERE HOTEL_ID = #{hotelId}
          AND SALES_DATE >= TO_DATE(#{salesDateFrom,jdbcType=VARCHAR}, 'yyyy-mm-dd')  AND SALES_DATE &lt;= TO_DATE(#{salesDateTo,jdbcType=VARCHAR}, 'yyyy-mm-dd')
        ORDER BY SALES_DATE ASC
    </select>

    <select id="queryMinPriceList" resultType="java.math.BigDecimal">
        SELECT
            MIN_PRICE
        FROM htl_order.T_OTA_HOTEL_DAILY_MIN_PRICE
        WHERE HOTEL_ID = #{hotelId}
          AND SALES_DATE >= TO_DATE(#{salesDateFrom,jdbcType=VARCHAR}, 'yyyy-mm-dd')  AND SALES_DATE &lt;= TO_DATE(#{salesDateTo,jdbcType=VARCHAR}, 'yyyy-mm-dd')
        ORDER BY MIN_PRICE ASC
    </select>

    <select id="queryOtaHotelMinMaxPriceVOList" resultType="com.fangcang.rfp.common.dto.OtaHotelMinMaxPriceVO">
        SELECT
            HOTEL_ID AS hotelId,
            MIN(MIN_PRICE) AS minPrice,
            MAX(MIN_PRICE) AS maxPrice
        FROM htl_order.T_OTA_HOTEL_DAILY_MIN_PRICE
        WHERE HOTEL_ID IN
        <foreach collection="hotelIds" item="hotelId" open="(" close=")" separator=",">
            #{hotelId}
        </foreach>
        AND SALES_DATE >= TO_DATE(#{salesDateFrom,jdbcType=VARCHAR}, 'yyyy-mm-dd')  AND SALES_DATE &lt;= TO_DATE(#{salesDateTo,jdbcType=VARCHAR}, 'yyyy-mm-dd')
        GROUP BY HOTEL_ID
    </select>

</mapper>