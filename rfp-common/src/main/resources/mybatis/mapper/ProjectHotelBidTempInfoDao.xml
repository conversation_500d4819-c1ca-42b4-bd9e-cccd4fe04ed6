<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelBidTempInfoDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectHotelBidTempInfo">
        <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId"/>
        <result column="BID_INFO_JSON" jdbcType="VARCHAR" property="bidInfoJson" />
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROJECT_INTENT_HOTEL_ID, BID_INFO_JSON, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <select id="selectByProjectIntentHotelId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_HOTEL_BID_TEMP_INFO
        where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidTempInfo">
        INSERT INTO  htl_rfp.T_PROJECT_HOTEL_BID_TEMP_INFO(
        PROJECT_INTENT_HOTEL_ID,
        BID_INFO_JSON,
        CREATOR,
        CREATE_TIME,
        MODIFIER,
        MODIFY_TIME
        ) VALUES (
             #{projectIntentHotelId,jdbcType=BIGINT},
            #{bidInfoJson,jdbcType=VARCHAR},
            #{creator,jdbcType=VARCHAR},
            SYSDATE,
            #{modifier,jdbcType=VARCHAR},
            SYSDATE
        )
    </insert>
    <delete id="deleteByProjectIntentHotelId" parameterType="java.lang.Long">
        DELETE FROM T_PROJECT_HOTEL_BID_TEMP_INFO WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
    </delete>

    <update id="update" parameterType="com.fangcang.rfp.common.entity.ProjectHotelBidTempInfo">
        UPDATE htl_rfp.T_PROJECT_HOTEL_BID_TEMP_INFO
        SET BID_INFO_JSON = #{bidInfoJson,jdbcType=VARCHAR},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </update>


</mapper>