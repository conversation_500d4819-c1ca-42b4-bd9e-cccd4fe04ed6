<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.MonitorCallTmchubErrorDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.MonitorCallTmchubError">
        <result column="ORG_ID" jdbcType="BIGINT" property="orgId"/>
        <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId"/>
        <result column="ROOM_TYPE_ID" jdbcType="INTEGER" property="roomTypeId"/>
        <result column="ROOM_TYPE" jdbcType="VARCHAR" property="roomType"/>
        <result column="MONITOR_DATE" jdbcType="TIMESTAMP" property="monitorDate"/>
        <result column="VIOLATION_MONITOR_ID" jdbcType="BIGINT" property="violationMonitorId"/>
        <result column="MONITOR_ERROR_TYPE" jdbcType="INTEGER" property="monitorErrorType"/>
        <result column="ERROR_REASON" jdbcType="VARCHAR" property="errorReason"/>
        <result column="MONITOR_TIME" jdbcType="TIMESTAMP" property="monitorTime"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORG_ID, HOTEL_ID,ROOM_TYPE_ID, ROOM_TYPE,VIOLATION_MONITOR_ID,
        MONITOR_DATE, MONITOR_ERROR_TYPE, ERROR_REASON, MONITOR_TIME, CREATE_TIME
    </sql>

    <select id="queryList" resultType="com.fangcang.rfp.common.dto.response.MonitorCallTmchubErrorDto" parameterType="com.fangcang.rfp.common.dto.request.QueryMonitorCallTmchubErrorListRequest">
        SELECT
            t1.ORG_ID AS orgId,
            t1.HOTEL_ID AS hotelId,
            t1.ROOM_TYPE_ID AS roomTypeId,
            t1.ROOM_TYPE AS roomType,
            t1.VIOLATION_MONITOR_ID AS violationMonitorId,
            t1.MONITOR_DATE AS monitorDate,
            t1.ERROR_REASON AS errorReason,
            t1.MONITOR_ERROR_TYPE AS monitorErrorType,
            t1.MONITOR_TIME AS monitorTime,
            t1.CREATE_TIME AS createTime,
            o.org_name as orgName,
            h.chn_name as hotelName
        FROM
            htl_rfp.T_MONITOR_CALL_TMCHUB_ERROR t1,
            htl_rfp.t_org o,
            htl_info.t_hotel h
        WHERE
             t1.ORG_ID = o.org_id
        AND  t1.HOTEL_ID = h.hotelid
        AND t1.MONITOR_TIME >= TO_DATE(#{query.monitorDateFrom}, 'YYYY-MM-DD hh24:mi:ss')
        <if test="query.orgId != null and query.orgId > 0">
            AND t1.ORG_ID = #{query.orgId}
        </if>
        <if test="query.hotelId != null and query.hotelId > 0">
            AND t1.HOTEL_ID = #{query.hotelId}
        </if>
        <if test="query.monitorErrorType != null and query.monitorErrorType > 0">
            AND t1.MONITOR_ERROR_TYPE = #{query.monitorErrorType}
        </if>
        <if test="query.monitorDateTo != null and query.monitorDateTo != ''">
            AND t1.MONITOR_TIME &lt;= TO_DATE(#{query.monitorDateTo}, 'YYYY-MM-DD hh24:mi:ss') + 1
        </if>
        ORDER BY t1.MONITOR_TIME DESC
    </select>

    <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.MonitorCallTmchubError">
        INSERT INTO T_MONITOR_CALL_TMCHUB_ERROR(
            ORG_ID,
            HOTEL_ID,
            ROOM_TYPE_ID,
            ROOM_TYPE,
            VIOLATION_MONITOR_ID,
            MONITOR_DATE,
            MONITOR_ERROR_TYPE,
            ERROR_REASON,
            MONITOR_TIME,
            CREATE_TIME
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            ( select
            #{item.orgId,jdbcType=BIGINT},
            #{item.hotelId,jdbcType=BIGINT},
            #{item.roomTypeId,jdbcType=INTEGER},
            #{item.roomType,jdbcType=VARCHAR},
            #{item.violationMonitorId,jdbcType=BIGINT},
            #{item.monitorDate,jdbcType=TIMESTAMP},
            #{item.monitorErrorType,jdbcType=INTEGER},
            #{item.errorReason,jdbcType=VARCHAR},
            #{item.monitorTime,jdbcType=TIMESTAMP},
            SYSDATE from dual)
        </foreach>
    </insert>

</mapper>