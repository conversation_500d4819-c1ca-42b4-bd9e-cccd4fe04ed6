<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.BidTaskImportColumnDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.BidTaskImportColumn">
        <result column="COLUMN_INDEX" jdbcType="BIGINT" property="columnIndex"/>
        <result column="COLUMN_CODE" jdbcType="VARCHAR" property="columnCode"/>
        <result column="COLUMN_NAME" jdbcType="VARCHAR" property="columnName"/>
    </resultMap>

    <sql id="Base_Column_List">
        COLUMN_INDEX, COLUMN_CODE, COLUMN_NAME
    </sql>

    <select id="queryAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_BID_TASK_IMPORT_COLUMN
       ORDER BY COLUMN_INDEX ASC
    </select>


</mapper>