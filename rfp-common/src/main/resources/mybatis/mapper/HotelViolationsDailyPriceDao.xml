<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelViolationsDailyPriceDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelViolationsDailyPrice">
        <result column="VIOLATIONS_MONITOR_ID" jdbcType="DECIMAL" property="violationsMonitorId"/>
        <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId"/>
        <result column="SALE_DATE" jdbcType="TIMESTAMP" property="saleDate"/>
        <result column="SALE_PRICE" jdbcType="DECIMAL" property="salePrice"/>
        <result column="COMPARE_RESULT" jdbcType="DECIMAL" property="compareResult"/>
        <result column="PRICE_CODE" jdbcType="DECIMAL" property="priceCode"/>
        <result column="DIF_PRICE" jdbcType="DECIMAL" property="difPrice"/>
        <result column="IS_VIOLATION" jdbcType="INTEGER" property="isViolation"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="BASE_PRICE" jdbcType="DECIMAL" property="basePrice"/>
        <result column="LRA" jdbcType="INTEGER" property="lra"/>
    </resultMap>

    <sql id="Base_Column_List">
      VIOLATIONS_MONITOR_ID, HOTEL_ID, PROJECT_ID,ROOM_TYPE_ID, SALE_DATE, SALE_PRICE,
      COMPARE_RESULT, PRICE_CODE, DIF_PRICE, IS_VIOLATION, CREATE_TIME,MODIFY_TIME,BASE_PRICE,LRA
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_HOTEL_VIOLATIONS_DAILY_PRICE
        (violations_monitor_id, hotel_id, project_id, room_type_id, sale_date, sale_price,
        compare_result, price_code, dif_price, is_violation, create_time,MODIFY_TIME,BASE_PRICE,LRA)
        <foreach collection="hotelViolationsDailyPrices" item="item" index="index" separator="union all">
            ( select #{item.violationsMonitorId}, #{item.hotelId}, #{item.projectId}, #{item.roomTypeId},
            #{item.saleDate}, #{item.salePrice,jdbcType=DECIMAL}, #{item.compareResult}, #{item.priceCode},
            #{item.difPrice,jdbcType=DECIMAL}, #{item.isViolation,jdbcType=INTEGER}, sysdate,sysdate, #{item.basePrice,jdbcType=DECIMAL}, #{item.lra,jdbcType=INTEGER} from dual)
        </foreach>

    </insert>
    <update id="update" parameterType="com.fangcang.rfp.common.entity.HotelViolationsDailyPrice">
        UPDATE T_HOTEL_VIOLATIONS_DAILY_PRICE
        SET SALE_PRICE = #{hotelViolationsDailyPrice.salePrice},
            COMPARE_RESULT = #{hotelViolationsDailyPrice.compareResult},
            PRICE_CODE = #{hotelViolationsDailyPrice.priceCode},
            DIF_PRICE = #{hotelViolationsDailyPrice.difPrice},
            IS_VIOLATION = #{hotelViolationsDailyPrice.isViolation},
            MODIFY_TIME = SYSDATE
        WHERE
            VIOLATIONS_MONITOR_ID = #{hotelViolationsDailyPrice.violationsMonitorId}
        AND HOTEL_ID = #{hotelViolationsDailyPrice.hotelId}
        AND PROJECT_ID = #{hotelViolationsDailyPrice.projectId}
        AND ROOM_TYPE_ID = #{hotelViolationsDailyPrice.roomTypeId}
        AND SALE_DATE = #{hotelViolationsDailyPrice.saleDate}
    </update>

    <select id="queryHotelViolationsDailyPrice" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_VIOLATIONS_DAILY_PRICE
        where VIOLATIONS_MONITOR_ID=#{violationsMonitorId} order by SALE_DATE
    </select>
    <select id="selectRoomName" resultType="java.lang.String">
      select t.roomtypename from htl_info.t_roomtype t where t.roomtypeid= #{roomTypeId}
    </select>
</mapper>