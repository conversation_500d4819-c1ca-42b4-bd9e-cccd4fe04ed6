<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectWeightHotelGroupDao">

    <select id="query" resultType="com.fangcang.rfp.common.entity.ProjectWeightHotelGroup">
        SELECT
            PROJECT_WEIGHT_HOTEL_GROUP_ID AS projectWeightHotelGroupId,
            PROJECT_ID AS projectId,
            G<PERSON>UP_TYPE AS groupType,
            REFERENCE_TYPE AS referenceType,
            REFERENCE_ID AS referenceId,
            REFERENCE_NAME AS referenceName,
            CREATOR AS creator,
            CREATE_TIME AS createTime,
            MODIFIER AS modifier,
            MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_PROJECT_WEIGHT_HOTEL_GROUP
        WHERE PROJECT_ID = #{projectId}
          <if test="groupType != null">
            AND GROUP_TYPE = #{groupType}
        </if>
        <if test="brandId != null">
            AND REFERENCE_ID = #{brandId}
        </if>
        <if test="groupId != null">
            AND REFERENCE_ID = #{groupId}
        </if>
        ORDER BY createTime DESC
    </select>

    <select id="queryByRefId" resultType="com.fangcang.rfp.common.entity.ProjectWeightHotelGroup">
        SELECT
        PROJECT_WEIGHT_HOTEL_GROUP_ID AS projectWeightHotelGroupId,
        PROJECT_ID AS projectId,
        GROUP_TYPE AS groupType,
        REFERENCE_TYPE AS referenceType,
        REFERENCE_ID AS referenceId,
        REFERENCE_NAME AS referenceName,
        CREATOR AS creator,
        CREATE_TIME AS createTime,
        MODIFIER AS modifier,
        MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_PROJECT_WEIGHT_HOTEL_GROUP
        WHERE PROJECT_ID = #{projectId}
        <if test="groupType != null">
            AND GROUP_TYPE = #{groupType}
        </if>
        <if test="referenceId != null">
            AND REFERENCE_ID = #{referenceId}
        </if>
        ORDER BY createTime DESC

    </select>

    <delete id="delete" parameterType="java.lang.Long">
        DELETE FROM htl_rfp.T_PROJECT_WEIGHT_HOTEL_GROUP
        WHERE PROJECT_WEIGHT_HOTEL_GROUP_ID = #{projectWeightHotelGroupId}
    </delete>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectWeightHotelGroup">
        <selectKey keyProperty="projectWeightHotelGroupId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_P_WEIGHT_H_GROUP_ID.nextval from dual
        </selectKey>
            INSERT INTO htl_rfp.T_PROJECT_WEIGHT_HOTEL_GROUP
            (PROJECT_WEIGHT_HOTEL_GROUP_ID,
             PROJECT_ID,
             GROUP_TYPE,
             REFERENCE_TYPE,
             REFERENCE_ID,
             REFERENCE_NAME,
             CREATOR,
             CREATE_TIME)
            VALUES (#{projectWeightHotelGroupId},
                    #{projectWeightHotelGroup.projectId,jdbcType=BIGINT},
                    #{projectWeightHotelGroup.groupType,jdbcType=INTEGER},
                    #{projectWeightHotelGroup.referenceType,jdbcType=INTEGER},
                    #{projectWeightHotelGroup.referenceId,jdbcType=BIGINT},
                    #{projectWeightHotelGroup.referenceName,jdbcType=VARCHAR},
                    #{projectWeightHotelGroup.creator,jdbcType=VARCHAR},
                    SYSDATE
            )
    </insert>

</mapper>