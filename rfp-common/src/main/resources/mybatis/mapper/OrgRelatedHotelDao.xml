<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fangcang.rfp.common.dao.OrgRelatedHotelDao" >

    <insert id="insertSelective" parameterType="com.fangcang.rfp.common.entity.OrgRelatedHotel" >
        <selectKey keyProperty="orgRelatedHotelId" resultType="_long" order="BEFORE">
          select htl_rfp.SEQ_ORG_RELATED_HOTEL_ID.nextval from dual
        </selectKey>
        insert into T_ORG_RELATED_HOTEL
        <trim prefix="(" suffix=")" suffixOverrides="," >
            ORG_RELATED_HOTEL_ID,
            ORG_ID,
            HOTEL_ID,
            creator,
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            #{orgRelatedHotelId},
            #{orgId,jdbcType=BIGINT},
            #{hotelId,jdbcType=BIGINT},
            #{creator,jdbcType=VARCHAR},
            SYSDATE
        </trim>
    </insert>
    <update id="deleteOrgRelatedHotelByOrgId">
        DELETE FROM htl_rfp.t_org_related_hotel
                             where ORG_ID = #{orgId}
    </update>

    <update id="deleteOrgRelatedHotelById">
        DELETE FROM htl_rfp.t_org_related_hotel
        where ORG_RELATED_HOTEL_ID = #{orgRelatedHotelId}
    </update>

    <select id="selectById" resultType="com.fangcang.rfp.common.entity.OrgRelatedHotel">
        SELECT
            ORG_RELATED_HOTEL_ID AS orgRelatedHotelId,
            ORG_ID AS orgId,
            HOTEL_ID AS hotelId,
            CREATOR AS creator,
            CREATE_TIME AS createTime
        FROM
            htl_rfp.t_org_related_hotel
        WHERE
            ORG_RELATED_HOTEL_ID = #{orgRelatedHotelId,jdbcType=BIGINT}
    </select>

    <select id="selectCountByOrgId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
            htl_rfp.t_org_related_hotel
        WHERE ORG_ID = #{orgId,jdbcType=BIGINT}
    </select>

    <select id="selectCountByHotelId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
            htl_rfp.t_org t1
        INNER JOIN htl_rfp.t_org_related_hotel t2 ON t1.org_id = t2.org_id
        WHERE t1.STATE = 1 AND t2.HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </select>

    <select id="queryOrgRelatedHotelList" resultType="com.fangcang.rfp.common.dto.common.OrgRelatedHotelDTO" parameterType="com.fangcang.rfp.common.dto.request.OrgRelatedHotelRequest">
        SELECT
            t.ORG_RELATED_HOTEL_ID AS orgRelatedHotelId,
            t.ORG_ID AS orgId,
            t.HOTEL_ID AS hotelId,
            h.CHN_NAME AS hotelName,
            b.brandName AS brandName,
            b.hotelGroupName AS hotelGroupName,
            bc.dataname AS cityName
        FROM htl_rfp.t_org_related_hotel t,
            htl_info.t_hotel h
        LEFT JOIN
             (SELECT
                  t1.brandid as brandid,
                  t1.brandname AS brandName,
                  t1.groupid AS groupId,
                  t2.brandName AS hotelGroupName
              FROM htl_info.T_BRAND t1 LEFT JOIN htl_info.T_BRAND t2 ON t1.GROUPID = t2.BRANDID AND t2.GROUPID = 0
             ) b ON h.HOTELBRAND = b.brandid
         LEFT JOIN htl_base.t_areadata bc
                   ON h.city = bc.datacode AND bc.datatype = 3
                       AND bc.countrycode = 'CN'
        WHERE t.hotel_id = h.hotelid
        AND t.ORG_ID = #{orgId,jdbcType=BIGINT}
        ORDER BY orgRelatedHotelId ASC
    </select>

    <select id="queryOrgIdByHotelId" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT max(t1.ORG_ID)
        FROM
            htl_rfp.t_org t1
        INNER JOIN htl_rfp.t_org_related_hotel t2 ON t1.org_id = t2.org_id AND t1.ORG_TYPE = 2
        WHERE t1.STATE = 1 AND t2.HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </select>

    <select id="queryHotelOrgByHotelIds" resultType="com.fangcang.rfp.common.dto.HotelOrgVO">
        SELECT o.ORG_NAME AS orgName,
               o.CONTACT_NAME AS contactName,
               o.CONTACT_MOBILE AS contactMobile,
               o.CONTACT_EMAIL AS contactEmail,
               t.hotelId AS hotelId,
               t.orgId AS orgId
        FROM (
            SELECT
                t2.HOTEL_ID AS hotelId,
                max(t1.ORG_ID) AS orgId
            FROM
            htl_rfp.t_org t1
            INNER JOIN htl_rfp.t_org_related_hotel t2 ON t1.org_id = t2.org_id AND t1.ORG_TYPE = 2
            WHERE t1.STATE = 1 AND t2.HOTEL_ID IN
            <foreach collection="hotelIds" item="hotelId" open="(" close=")" separator=",">
                #{hotelId,jdbcType=BIGINT}
            </foreach>
            GROUP BY t2.HOTEL_ID
        ) t
        INNER JOIN  htl_rfp.t_org o  ON o.org_id = t.orgId
    </select>

    <select id="queryHotelIdByHotelOrgId" resultType="java.lang.Long" parameterType="java.lang.Long">
         SELECT
             HOTEL_ID
         FROM
             htl_rfp.t_org_related_hotel
         WHERE
             ORG_ID = #{hotelOrgId}
    </select>

</mapper>
