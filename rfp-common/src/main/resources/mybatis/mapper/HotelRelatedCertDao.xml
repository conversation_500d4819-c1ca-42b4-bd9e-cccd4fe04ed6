<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelRelatedCertDao">

    <select id="queryListByHotelIds" resultType="com.fangcang.rfp.common.entity.HotelRelatedCert">
        SELECT
            HOTEL_RELATED_CERT_ID AS hotelRelatedCertId,
            HOTEL_ID AS hotelId,
            CERT_TYPE AS certType,
            CERT_URL AS certUrl,
            IS_DELETED AS isDeleted,
            CREATOR AS creator,
            CREATE_TIME AS createTime,
            MODIFIER AS modifier,
            MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_HOTEL_RELATED_CERT
        WHERE HOTEL_ID IN
        <foreach collection="hotelIds" separator="," item="hotelId" open="(" close=")">
            #{hotelId}
        </foreach>
        AND IS_DELETED = 0
    </select>

    <select id="queryListByHotelRelatedCertIds" resultType="com.fangcang.rfp.common.entity.HotelRelatedCert">
        SELECT
            HOTEL_RELATED_CERT_ID AS hotelRelatedCertId,
            HOTEL_ID AS hotelId,
            CERT_TYPE AS certType,
            CERT_URL AS certUrl,
            IS_DELETED AS isDeleted,
            CREATOR AS creator,
            CREATE_TIME AS createTime,
            MODIFIER AS modifier,
            MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_HOTEL_RELATED_CERT
        WHERE HOTEL_RELATED_CERT_ID IN
        <foreach collection="hotelRelatedCertIds" separator="," item="hotelRelatedId" open="(" close=")">
            #{hotelRelatedId}
        </foreach>
    </select>

    <update id="deleteHotelCert">
        UPDATE htl_rfp.T_HOTEL_RELATED_CERT
        SET IS_DELETED = HOTEL_RELATED_CERT_ID,
            MODIFY_TIME = SYSDATE
        WHERE HOTEL_RELATED_CERT_ID = #{hotelRelatedCertId}
    </update>

    <insert id="insertHotelCert" parameterType="com.fangcang.rfp.common.entity.HotelRelatedCert">
        <selectKey keyProperty="hotelRelatedCertId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_HOTEL_RELATED_CERT.nextval from dual
        </selectKey>
        INSERT INTO htl_rfp.T_HOTEL_RELATED_CERT(
            HOTEL_RELATED_CERT_ID,
            HOTEL_ID,
            CERT_TYPE,
            CERT_URL,
            IS_DELETED,
            CREATOR,
            CREATE_TIME,
            MODIFIER,
            MODIFY_TIME
        ) VALUES (
            #{hotelRelatedCertId},
            #{hotelId},
            #{certType},
            #{certUrl},
             0,
            #{creator},
            SYSDATE,
            #{modifier},
            SYSDATE
        )
    </insert>

</mapper>