<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrderMonitorConfigDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.OrderMonitorConfig">
    <result column="ORG_ID" jdbcType="DECIMAL" property="orgId" />
    <result column="DISTRIBUTOR_CODE" jdbcType="VARCHAR" property="distributorCode" />
    <result column="STATE" jdbcType="INTEGER" property="state" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="DISTRIBUTOR_NAME" jdbcType="VARCHAR" property="distributorName" />
  </resultMap>

  <sql id="Base_Coumn_List">
    ORG_ID,DISTRIBUTOR_CODE,STATE,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME,DISTRIBUTOR_NAME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.OrderMonitorConfig">
    insert into htl_rfp.T_ORDER_MONITOR_CONFIG (ORG_ID, DISTRIBUTOR_CODE, STATE,
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME, DISTRIBUTOR_NAME)
    values (#{orgId,jdbcType=DECIMAL}, #{distributorCode,jdbcType=VARCHAR}, #{state,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
            SYSDATE, #{distributorName,jdbcType=VARCHAR})
  </insert>

  <update id="update" parameterType="com.fangcang.rfp.common.entity.OrderMonitorConfig">
    update htl_rfp.T_ORDER_MONITOR_CONFIG
    set STATE = #{state},
        MODIFIER = #{modifier},
        MODIFY_TIME = SYSDATE
    where ORG_ID = #{orgId} and DISTRIBUTOR_CODE = #{distributorCode}
  </update>

  <select id="selectInfoByOrgIdAndDistributorCode"  parameterType="com.fangcang.rfp.common.entity.OrderMonitorConfig" resultMap="BaseResultMap">
    select <include refid="Base_Coumn_List"/>
    from htl_rfp.T_ORDER_MONITOR_CONFIG
    where ORG_ID = #{orgId} and DISTRIBUTOR_CODE = #{distributorCode}
  </select>

  <select id="selectOrderMonitorConfigList" parameterType="com.fangcang.rfp.common.dto.request.OrderMonitorConfigRequest"
          resultType="com.fangcang.rfp.common.dto.response.OrderMonitorConfigResponse">
    select t.orgId,
           t.count count,
           o.ORG_NAME orgName
    from (
      select t.org_id orgId,
          count(1) count
      from htl_rfp.T_ORDER_MONITOR_CONFIG t
      where t.STATE = 1
      group by t.org_id
    )t
    INNER JOIN htl_rfp.T_ORG o on t.orgId = o.ORG_ID
    <if test="orgName != null and orgName != '' ">
      and o.ORG_NAME like concat('%',concat(#{orgName},'%'))
    </if>
  </select>

  <select id="selectDistributorCodeList" parameterType="com.fangcang.rfp.common.dto.request.OrderMonitorConfigRequest"
          resultType="com.fangcang.rfp.common.dto.response.OrderMonitorConfigResponse">
    select t.ORG_ID orgId,
           t.DISTRIBUTOR_CODE distributorCode,
           t.DISTRIBUTOR_NAME distributorName,
           t.MODIFIER modifier,
           t.MODIFY_TIME modifyTime
    from htl_rfp.T_ORDER_MONITOR_CONFIG t
    where t.STATE = 1 and t.ORG_ID = #{orgId}
    <if test="distributorCode != null and distributorCode != '' ">
        and t.DISTRIBUTOR_CODE like concat('%',concat(#{distributorCode},'%'))
    </if>
    <if test="distributorName != null and distributorName != '' ">
      and t.DISTRIBUTOR_NAME like concat('%',concat(#{distributorName},'%'))
    </if>
  </select>

  <select id="queryOrderMonitorConfigOrgList" parameterType="java.lang.String"
          resultType="com.fangcang.rfp.common.dto.response.QueryOrderMonitorConfigOrgResponse">
    select
        o.ORG_NAME as orgName,
        t.ORG_ID orgId,
        t.DISTRIBUTOR_CODE distributorCode,
        t.DISTRIBUTOR_NAME distributorName,
        t.MODIFIER modifier,
        t.MODIFY_TIME modifyTime
    from htl_rfp.T_ORDER_MONITOR_CONFIG t
    inner join htl_rfp.T_ORG o ON t.ORG_ID = o.ORG_ID AND  t.STATE = 1
    where t.STATE = 1 AND
      t.DISTRIBUTOR_CODE IN
    <foreach collection="distributorCodeList" item="distributorCode" open="(" close=")" separator=",">
      #{distributorCode}
    </foreach>

  </select>

    <select id="queryOrderMonitorConfig" resultType="com.fangcang.rfp.common.dto.response.QueryOrderMonitorConfigResponse">
        select c.ORG_ID as orgId,
          c.DISTRIBUTOR_CODE as distributorCode,
          c.STATE as state,
          c.DISTRIBUTOR_NAME as distributorName,
          p.project_id as projectId
        from htl_rfp.T_ORDER_MONITOR_CONFIG c,
          htl_rfp.t_project p where c.ORG_ID = p.tender_org_id
         and c.STATE=1
          and p.project_id in
      <foreach collection="projectIds" open="(" close=")" item="projectId" separator=","
               index="index">
        #{projectId}
      </foreach>
</select>

</mapper>