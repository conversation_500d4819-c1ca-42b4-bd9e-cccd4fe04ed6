<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectPoiDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectPoi">
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="POI_ID" jdbcType="BIGINT" property="poiId"/>
        <result column="TOTAL_NIGHT_ROOM_COUNT" jdbcType="BIGINT" property="totalNightRoomCount"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="CITY_NIGHT_ROOM_RISK" jdbcType="DECIMAL" property="cityNightRoomRisk"/>
        <result column="TOTAL_NIGHT_ROOM_COUNT_5KM" jdbcType="INTEGER" property="totalNightRoomCount5Km"/>
        <result column="TOTAL_AMOUNT_5KM" jdbcType="DECIMAL" property="totalAmount5Km"/>
        <result column="CITY_NIGHT_ROOM_RISK_5KM" jdbcType="DECIMAL" property="cityNightRoomRisk5Km"/>
        <result column="TOTAL_NIGHT_ROOM_COUNT_10KM" jdbcType="INTEGER" property="totalNightRoomCount10Km"/>
        <result column="TOTAL_AMOUNT_10KM" jdbcType="DECIMAL" property="totalAmount10Km"/>
        <result column="CITY_NIGHT_ROOM_RISK_10KM" jdbcType="DECIMAL" property="cityNightRoomRisk10Km"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      PROJECT_ID, POI_ID, TOTAL_NIGHT_ROOM_COUNT, CITY_NIGHT_ROOM_RISK, TOTAL_AMOUNT,
                TOTAL_NIGHT_ROOM_COUNT_5KM, CITY_NIGHT_ROOM_RISK_5KM, TOTAL_AMOUNT_5KM,
                TOTAL_NIGHT_ROOM_COUNT_10KM, CITY_NIGHT_ROOM_RISK_10KM, TOTAL_AMOUNT_10KM,
                CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

    <insert id="batchInsertProjectPoi" parameterType="com.fangcang.rfp.common.entity.ProjectPoi">
        insert into htl_rfp.T_PROJECT_POI (PROJECT_ID, POI_ID, CREATOR,
        CREATE_TIME, MODIFIER, MODIFY_TIME
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            (select #{item.projectId}, #{item.poiId}, #{item.creator,jdbcType=VARCHAR},
            sysdate, #{item.modifier,jdbcType=VARCHAR}, sysdate from dual
            )
        </foreach>
    </insert>

    <select id="selectByProjectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT_POI
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>


    <select id="selectByProjectAndPoiId" resultType="com.fangcang.rfp.common.dto.response.ProjectPoiInfoResponse">
        SELECT pp.project_id as projectId ,pp.poi_id as poiId, pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount,
            pp.TOTAL_AMOUNT as totalAmount, op.city_name as cityName,op.poi_name as poiName ,
            op.poi_address as poiAddress ,op.lng_baidu lngBaiDu, op.lat_baidu latBaiDu, op.CITY_CODE as cityCode, pp.CITY_NIGHT_ROOM_RISK as cityRisk,
               pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km, pp.TOTAL_AMOUNT_5KM as totalAmount5Km, pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
               pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km, pp.TOTAL_AMOUNT_10KM as totalAmount10Km, pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        FROM htl_rfp.t_project_poi pp ,htl_rfp.t_org_poi op
        WHERE pp.poi_id = op.poi_id and pp.project_id = #{projectId,jdbcType=BIGINT}
        AND pp.poi_id = #{poiId,jdbcType=BIGINT}
        ORDER BY
        <if test="distance == 5">
            pp.TOTAL_NIGHT_ROOM_COUNT_5KM  DESC,
        </if>
        <if test="distance == 10">
            pp.TOTAL_NIGHT_ROOM_COUNT_10KM  DESC,
        </if>
        <if test="distance == 0 or distance == 3">
            pp.TOTAL_NIGHT_ROOM_COUNT  DESC,
        </if>
        poiId ASC
    </select>

    <delete id="deleteByPoiIdAndProject" parameterType="java.lang.Long">
    delete from htl_rfp.t_project_poi
    where POI_ID = #{poiId,jdbcType=BIGINT} and PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </delete>

    <select id="selectProjectPoiInfo" parameterType="java.lang.Long" resultType="com.fangcang.rfp.common.dto.response.ProjectPoiInfoResponse">
        select pp.project_id as projectId ,pp.poi_id as poiId , pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount,
               pp.TOTAL_AMOUNT as totalAmount, op.city_name as cityName,op.poi_name as poiName ,
              op.poi_address as poiAddress ,op.lng_baidu lngBaiDu, op.lat_baidu latBaiDu,op.CITY_CODE as cityCode,
               pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km, pp.TOTAL_AMOUNT_5KM as totalAmount5Km, pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
               pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km, pp.TOTAL_AMOUNT_10KM as totalAmount10Km, pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        from htl_rfp.t_project_poi pp ,htl_rfp.t_org_poi op where pp.poi_id = op.poi_id and pp.project_id = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectMapProjectPoiInfo" resultType="com.fangcang.rfp.common.dto.response.ProjectPoiInfoResponse">
        select pp.project_id as projectId ,pp.poi_id as poiId, pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount, pp.CITY_NIGHT_ROOM_RISK AS cityRisk,
               pp.TOTAL_AMOUNT as totalAmount, op.city_name as cityName,op.poi_name as poiName ,
               op.poi_address as poiAddress ,op.lng_baidu lngBaiDu, op.lat_baidu latBaiDu,op.CITY_CODE as cityCode,
        pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km, pp.TOTAL_AMOUNT_5KM as totalAmount5Km, pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
        pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km, pp.TOTAL_AMOUNT_10KM as totalAmount10Km, pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        from htl_rfp.t_project_poi pp ,htl_rfp.t_org_poi op
        where pp.poi_id = op.poi_id and pp.project_id = #{projectId,jdbcType=BIGINT}
        <if test="poiName != null and poiName != ''">
            AND op.poi_name = CONCAT(CONCAT('%',#{poiName}),'%')
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND op.CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        ORDER BY
        <if test="distance == 5">
            pp.TOTAL_NIGHT_ROOM_COUNT_5KM  DESC,
        </if>
        <if test="distance == 10">
            pp.TOTAL_NIGHT_ROOM_COUNT_10KM  DESC,
        </if>
        <if test="distance == 0 or distance == 3">
            pp.TOTAL_NIGHT_ROOM_COUNT  DESC,
        </if>
        poiId ASC
    </select>
    <select id="selectMapProjectPoiInfoByPoiIdList" resultType="com.fangcang.rfp.common.dto.response.ProjectPoiInfoResponse">
        select pp.project_id as projectId ,pp.poi_id as poiId, pp.TOTAL_NIGHT_ROOM_COUNT as totalNightRoomCount, pp.CITY_NIGHT_ROOM_RISK AS cityRisk,
        pp.TOTAL_AMOUNT as totalAmount, op.city_name as cityName,op.poi_name as poiName ,
        op.poi_address as poiAddress ,op.lng_baidu lngBaiDu, op.lat_baidu latBaiDu,op.CITY_CODE as cityCode,
        pp.TOTAL_NIGHT_ROOM_COUNT_5KM as totalNightRoomCount5Km, pp.TOTAL_AMOUNT_5KM as totalAmount5Km, pp.CITY_NIGHT_ROOM_RISK_5KM as cityRisk5Km,
        pp.TOTAL_NIGHT_ROOM_COUNT_10KM as totalNightRoomCount10Km, pp.TOTAL_AMOUNT_10KM as totalAmount10Km, pp.CITY_NIGHT_ROOM_RISK_10KM as cityRisk10Km
        from htl_rfp.t_project_poi pp ,htl_rfp.t_org_poi op
        where pp.poi_id = op.poi_id and pp.project_id = #{projectId,jdbcType=BIGINT}
        AND pp.poi_id IN
        <foreach collection="poiIdList" item="poiId" open="(" close=")" separator=",">
            #{poiId}
        </foreach>
        ORDER BY totalNightRoomCount DESC, poiId ASC
    </select>


    <select id="selectCityProjectPoiLastYearRoomSum" parameterType="java.lang.Long" resultType="com.fangcang.rfp.common.dto.response.ProjectCityPoiRoomCountResponse">
        SELECT op.CITY_CODE AS cityCode, SUM(pp.TOTAL_NIGHT_ROOM_COUNT) AS roomCount
        FROM htl_rfp.t_project_poi pp ,htl_rfp.t_org_poi op
        WHERE pp.poi_id = op.poi_id AND pp.project_id = #{projectId,jdbcType=BIGINT}
        GROUP BY op.CITY_CODE
    </select>

    <update id="updateProjectPoiStatInfo" parameterType="com.fangcang.rfp.common.entity.ProjectPoi">
        UPDATE htl_rfp.t_project_poi
        SET TOTAL_NIGHT_ROOM_COUNT = #{totalNightRoomCount},
            TOTAL_AMOUNT = #{totalAmount},
            CITY_NIGHT_ROOM_RISK = #{cityNightRoomRisk},
            TOTAL_NIGHT_ROOM_COUNT_5KM = #{totalNightRoomCount5Km},
            TOTAL_AMOUNT_5KM = #{totalAmount5Km},
            CITY_NIGHT_ROOM_RISK_5KM = #{cityNightRoomRisk5Km},
            TOTAL_NIGHT_ROOM_COUNT_10KM = #{totalNightRoomCount10Km},
            TOTAL_AMOUNT_10KM = #{totalAmount10Km},
            CITY_NIGHT_ROOM_RISK_10KM = #{cityNightRoomRisk10Km},
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_ID = #{projectId}
        AND
            POI_ID = #{poiId}
    </update>

    <update id="updateProjectPoi3KmStatInfo" parameterType="com.fangcang.rfp.common.entity.ProjectPoi">
        UPDATE htl_rfp.t_project_poi
        SET POI_HOTEL_STAT_3KM = #{poiHotelStat3Km},
            MODIFIER = #{modifier},
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_ID = #{projectId}
          AND
            POI_ID = #{poiId}
    </update>


    <update id="clearCityPoiStat" parameterType="java.lang.Long">
        UPDATE htl_rfp.t_project_poi
        SET TOTAL_NIGHT_ROOM_COUNT = 0,
            TOTAL_AMOUNT = 0,
            CITY_NIGHT_ROOM_RISK = 0,
            TOTAL_NIGHT_ROOM_COUNT_5KM = 0,
            TOTAL_AMOUNT_5KM = 0,
            CITY_NIGHT_ROOM_RISK_5KM = 0,
            TOTAL_NIGHT_ROOM_COUNT_10KM = 0,
            TOTAL_AMOUNT_10KM = 0,
            CITY_NIGHT_ROOM_RISK_10KM = 0,
            MODIFY_TIME = SYSDATE
        WHERE
            PROJECT_ID = #{projectId}
    </update>
</mapper>