<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectAddedHotelDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectAddedHotel">
      <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
      <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
      <result column="NOTIFY_TYPE" jdbcType="INTEGER" property="notifyType" />
      <result column="CONTACT_USER_NAME" jdbcType="VARCHAR" property="contactUserName" />
      <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile" />
      <result column="CONTACT_EMAIL" jdbcType="VARCHAR" property="contactEmail" />
      <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
      <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
      <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
      <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    PROJECT_ID,HOTEL_ID,NOTIFY_TYPE,CONTACT_USER_NAME,CONTACT_MOBILE,CONTACT_EMAIL,MODIFIER,MODIFY_TIME,CREATOR,CREATE_TIME
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectAddedHotel">
    INSERT INTO T_PROJECT_ADDED_HOTEL (PROJECT_ID,HOTEL_ID,NOTIFY_TYPE,CONTACT_USER_NAME,CONTACT_MOBILE,CONTACT_EMAIL,CREATOR,CREATE_TIME)
    VALUES (#{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL},#{notifyType,jdbcType=INTEGER},
            #{contactUserName,jdbcType=VARCHAR},#{contactMobile,jdbcType=VARCHAR}, #{contactEmail,jdbcType=VARCHAR},
            #{creator,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <select id="selectByProjectIdAndHotelId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from HTL_RFP.T_PROJECT_ADDED_HOTEL t
    where t.PROJECT_ID = #{projectId} and t.HOTEL_ID = #{hotelId}
  </select>

  <insert id="batchInsert" parameterType="com.fangcang.rfp.common.entity.ProjectAddedHotel">
        insert into HTL_RFP.T_PROJECT_ADDED_HOTEL(PROJECT_ID,HOTEL_ID,NOTIFY_TYPE,CONTACT_USER_NAME,CONTACT_MOBILE,CONTACT_EMAIL,CREATOR,CREATE_TIME)
        <foreach collection="list" index="index" item="item" separator="union all">
          (
              select #{item.projectId,jdbcType=BIGINT},#{item.hotelId,jdbcType=BIGINT},#{item.notifyType,jdbcType=INTEGER},#{item.contactUserName,jdbcType=VARCHAR},
                #{item.contactMobile,jdbcType=VARCHAR}, #{item.contactEmail,jdbcType=VARCHAR},
             #{item.creator},SYSDATE
            from dual
          )
        </foreach>
  </insert>

    <insert id="batchInsertOrUpdate">
        MERGE INTO htl_rfp.T_PROJECT_ADDED_HOTEL T
        USING (
        <foreach collection="addedHotels" item="item" separator="UNION ALL">
            SELECT
            #{item.projectId,jdbcType=BIGINT},#{item.hotelId,jdbcType=BIGINT},#{item.notifyType,jdbcType=INTEGER},#{item.contactUserName,jdbcType=VARCHAR},
            #{item.contactMobile,jdbcType=VARCHAR}, #{item.contactEmail,jdbcType=VARCHAR},
            #{item.creator},SYSDATE
            FROM dual
        </foreach>
        ) s
        ON (t.PROJECT_ID = s.projectId AND t.hotel_id = s.hotelId)
        WHEN MATCHED THEN
        UPDATE SET
        t.NOTIFY_TYPE = s.notifyType,
        t.CONTACT_USER_NAME = s.contactUserName,
        t.CONTACT_MOBILE = s.contactMobile,
        t.CONTACT_EMAIL = s.contactEmail,
        t.modifier = s.creator,
        t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.PROJECT_ID, t.HOTEL_ID,t.NOTIFY_TYPE,t.CONTACT_USER_NAME, t.CONTACT_MOBILE, t.CONTACT_EMAIL, t.creator, t.create_time)
        values(s.projectId, s.hotelId, s.notifyType, s.contactUserName, s.contactMobile,  s.contactEmail, s.creator, sysdate)
    </insert>

    <update id="update" parameterType="com.fangcang.rfp.common.entity.ProjectAddedHotel">
        UPDATE HTL_RFP.T_PROJECT_ADDED_HOTEL t
        SET
            t.NOTIFY_TYPE = #{notifyType,jdbcType=INTEGER},
            t.CONTACT_USER_NAME = #{contactUserName,jdbcType=VARCHAR},
            t.CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
            t.CONTACT_EMAIL =#{contactEmail,jdbcType=VARCHAR},
            t.modifier = #{creator,jdbcType=VARCHAR},
            t.modify_time = sysdate
        WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId}
    </update>

  <delete id="delete" parameterType="com.fangcang.rfp.common.entity.ProjectAddedHotel">
    DELETE HTL_RFP.T_PROJECT_ADDED_HOTEL t
    WHERE t.PROJECT_ID = #{projectId} AND t.HOTEL_ID = #{hotelId}
  </delete>

  <select id="selectProjectHotelHotelList" parameterType="com.fangcang.rfp.common.dto.request.ProjectAddedHotelRequest"
          resultType="com.fangcang.rfp.common.dto.response.ProjectAddedHotelResponse">
    SELECT
      pah.PROJECT_ID AS projectId,
      pah.HOTEL_ID AS hotelId,
      pah.NOTIFY_TYPE AS notifyType,
      pah.CONTACT_USER_NAME AS contactUserName,
      pah.CONTACT_MOBILE AS contactMobile,
      pah.CONTACT_EMAIL AS contactEmail,
      pah.MODIFIER AS modifier,
      pah.MODIFY_TIME AS modifyTime,
      pah.CREATOR AS creator,
      pah.CREATE_TIME AS createTime,
      h.CHN_NAME AS hotelName,
      h.PROVINCE AS province,
      h.CITY AS city,
      h.HOTEL_GROUP AS hotelGroup,
      h.HOTELBRAND AS hotelBrand
     FROM
       HTL_RFP.T_PROJECT_ADDED_HOTEL pah
     INNER JOIN HTL_INFO.T_HOTEL h ON pah.HOTEL_ID = h.HOTELID
     WHERE pah.PROJECT_ID = #{projectId}
    <if test="hotelId != null and hotelId > 0">
        AND pah.HOTEL_ID = #{hotelId}
    </if>
     <if test="province != null and province != ''">
        AND h.PROVINCE = #{province}
     </if>
    <if test="city != null and city != ''">
        AND h.CITY = #{city}
    </if>
    <if test="hotelGroup != null and hotelGroup != ''">
        AND h.HOTEL_GROUP = #{hotelGroup}
    </if>
    <if test="hotelBrand != null and hotelBrand != ''">
        AND h.HOTELBRAND = #{hotelBrand}
    </if>
     <if test="notifyType != null and notifyType > 0">
         AND pah.NOTIFY_TYPE = #{notifyType}
     </if>
    ORDER BY pah.CREATE_TIME DESC, pah.HOTEL_ID DESC
  </select>


</mapper>