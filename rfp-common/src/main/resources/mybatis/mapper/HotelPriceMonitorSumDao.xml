<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelPriceMonitorSumDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelPriceMonitorSum">
    <result column="ORG_ID" jdbcType="BIGINT" property="orgId" />
    <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId" />
    <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId" />
    <result column="OP_HAVE_ROOM_PCT" jdbcType="INTEGER" property="opHaveRoomPct" />
    <result column="DP_HAVE_ROOM_PCT" jdbcType="INTEGER" property="dpHaveRoomPct" />
    <result column="UP_HAVE_ROOM_PCT" jdbcType="INTEGER" property="upHaveRoomPct" />
    <result column="FULL_ROOM_PCT" jdbcType="INTEGER" property="fullRoomPct" />
    <result column="MONITOR_DAY_TYPE" jdbcType="INTEGER" property="monitorDayType" />
    <result column="MONITOR_YEAR" jdbcType="INTEGER" property="monitorYear" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
  </resultMap>

  <sql id="Base_Column_List">
    ORG_ID,PROJECT_ID,HOTEL_ID,OP_HAVE_ROOM_PCT,DP_HAVE_ROOM_PCT,UP_HAVE_ROOM_PCT,FULL_ROOM_PCT,
    MONITOR_DAY_TYPE,MONITOR_YEAR,CREATOR,CREATE_TIME,MODIFIER,MODIFY_TIME,CITY_CODE
  </sql>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorSum">
    insert into htl_rfp.T_HOTEL_PRICE_MONITOR_SUM (ORG_ID, PROJECT_ID, HOTEL_ID,
      OP_HAVE_ROOM_PCT, DP_HAVE_ROOM_PCT, UP_HAVE_ROOM_PCT, 
      FULL_ROOM_PCT, MONITOR_DAY_TYPE, MONITOR_YEAR, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME, CITY_CODE)
    values (#{orgId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, 
      #{opHaveRoomPct,jdbcType=DECIMAL}, #{dpHaveRoomPct,jdbcType=DECIMAL}, #{upHaveRoomPct,jdbcType=DECIMAL}, 
      #{fullRoomPct,jdbcType=DECIMAL}, #{monitorDayType,jdbcType=DECIMAL}, #{monitorYear,jdbcType=DECIMAL}, 
      #{creator,jdbcType=VARCHAR}, SYSDATE, #{modifier,jdbcType=VARCHAR},
      SYSDATE, #{cityCode,jdbcType=VARCHAR})
  </insert>

  <update id="update" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorSum">
    update htl_rfp.T_HOTEL_PRICE_MONITOR_SUM
    set OP_HAVE_ROOM_PCT = #{opHaveRoomPct},
        DP_HAVE_ROOM_PCT = #{dpHaveRoomPct},
        UP_HAVE_ROOM_PCT = #{upHaveRoomPct},
        FULL_ROOM_PCT = #{fullRoomPct},
        MONITOR_YEAR = #{monitorYear},
        MODIFIER = #{modifier},
        MODIFY_TIME = SYSDATE
    where PROJECT_ID = #{projectId} and HOTEL_ID = #{hotelId} and MONITOR_DAY_TYPE = #{monitorDayType}
  </update>

  <select id="selectByUnique" parameterType="com.fangcang.rfp.common.entity.HotelPriceMonitorSum" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from htl_rfp.T_HOTEL_PRICE_MONITOR_SUM
    where PROJECT_ID = #{projectId} and HOTEL_ID = #{hotelId} and MONITOR_DAY_TYPE = #{monitorDayType}
  </select>

  <select id="selectHotelPriceMonitorSumList" resultType="com.fangcang.rfp.common.dto.response.count.HotelPriceMonitorSumResponse"
          parameterType="com.fangcang.rfp.common.dto.request.count.HotelPriceMonitorSumRequest">
    select p.project_name projectName,
           p.TENDER_TYPE tenderType,
           h.chn_name hotelName,
           o1.ORG_NAME orgName,
           t.ORG_ID orgId,
           t.PROJECT_ID projectId,
           t.HOTEL_ID hotelId,
           t.MODIFY_TIME modifyTime,
           t.OP_HAVE_ROOM_PCT opHaveRoomPct,
           t.DP_HAVE_ROOM_PCT dpHaveRoomPct,
           t.UP_HAVE_ROOM_PCT upHaveRoomPct,
           t.FULL_ROOM_PCT fullRoomPct,
           h.HOTEL_STAR hotelStar,
           h.HOTELBRAND brandId,
           h.HOTEL_GROUP hotelGroupId,
           h.province province,
           ph.BID_CONTACT_NAME AS bidContactName,
           ph.BID_CONTACT_MOBILE AS bidContactMobile,
           ph.BID_CONTACT_EMAIL AS bidContactEmail,
           CASE WHEN ofh.HOTEL_ID IS NULL THEN 0 ELSE 1 END AS isFollowHotel,
        <choose>
          <when test="hotelGroupOrgId != null">
              0 AS hotelServicePoints
          </when>
          <otherwise>
              ph.hotel_service_points AS hotelServicePoints
          </otherwise>
      </choose>
    from HTL_RFP.T_HOTEL_PRICE_MONITOR_SUM t
        left join HTL_RFP.T_PROJECT p on t.project_id = p.project_id
        left join HTL_RFP.T_ORG o1 on t.ORG_ID = o1.ORG_ID
        left join HTL_INFO.T_HOTEL h on t.HOTEL_ID = h.HOTELID
        left join HTL_RFP.T_ORG_FOLLOW_HOTEL ofh ON ofh.ORG_ID = p.TENDER_ORG_ID AND ofh.HOTEL_ID = t.HOTEL_ID
        <choose>
            <when test="hotelGroupOrgId != null">
                inner join HTL_RFP.T_PROJECT_INTENT_HOTEL_GROUP ph on t.project_id = ph.project_id and ph.HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId} AND ph.IS_ACTIVE = 1
            </when>
            <otherwise>
                left join HTL_RFP.t_project_intent_hotel ph on t.project_id = ph.project_id and t.hotel_id = ph.hotel_id
            </otherwise>
        </choose>
    where t.MONITOR_DAY_TYPE = #{monitorDayType}
            and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.PRICE_MONITOR_START_DATE and p.PRICE_MONITOR_END_DATE
    <if test="isQueryLastThreeDay == 0 or isQueryLastThreeDay == 10">
        AND t.MODIFY_TIME >= TRUNC(SYSDATE) -3
    </if>
    <if test="orgId != null">
        and t.ORG_ID = #{orgId}
    </if>
    <if test="projectId != null">
        and t.PROJECT_ID = #{projectId}
    </if>
    <if test="hotelId != null">
        and t.HOTEL_ID = #{hotelId}
    </if>
    <if test="isFollowHotel != null and isFollowHotel > 0">
        and ofh.HOTEL_ID IS NOT NULL
    </if>
    <if test="province != null and province != ''">
        AND h.PROVINCE = #{province}
    </if>
    <if test="hotelIdList != null and hotelIdList.size() > 0">
        and t.HOTEL_ID IN
        <foreach collection="hotelIdList" item="hotelIdItem" open="(" close=")" separator=",">
            #{hotelIdItem}
        </foreach>
    </if>
    <if test="projectName != null and projectName != '' ">
        and p.PROJECT_NAME like concat('%',concat(#{projectName},'%'))
    </if>
    <if test="cityCode != null and cityCode != '' ">
        and t.CITY_CODE = #{cityCode}
    </if>
    <if test="userIds != null">
        <choose>
            <when test="hotelGroupOrgId != null ">
                and ph.HOTEL_GROUP_CONTACT_UID IN
                <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                    #{userId}
                </foreach>
            </when>
            <otherwise>
                and ph.distributor_contact_uid IN
                <foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">
                    #{userId}
                </foreach>
            </otherwise>
        </choose>
    </if>
    <if test="hotelGroupId != null">
        and t.HOTEL_ID IN (SELECT HOTELID FROM HTL_INFO.T_HOTEL WHERE HOTEL_GROUP = #{hotelGroupId} AND ISACTIVE = 1)
    </if>
    <if test="brandId != null">
        and h.hotelBrand = #{brandId}
    </if>
    <if test="hotelGroupBrandIdList != null and hotelGroupBrandIdList.size() > 0">
        AND h.hotelBrand IN
        <foreach collection="hotelGroupBrandIdList" item="hotelGroupBrandId" separator="," open="(" close=")">
            #{hotelGroupBrandId}
        </foreach>
    </if>
      <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
          AND p.TENDER_ORG_ID IN
          <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
              #{userRelatedOrgId}
          </foreach>
      </if>
    <if test="sortColumn != null and sortType != null">
        <if test="sortColumn == 0 and sortType == 0">
            order by t.OP_HAVE_ROOM_PCT,
        </if>
        <if test="sortColumn == 0 and sortType == 1">
            order by t.OP_HAVE_ROOM_PCT desc,
        </if>
        <if test="sortColumn == -1 and sortType == 0">
            order by t.DP_HAVE_ROOM_PCT,
        </if>
        <if test="sortColumn == -1 and sortType == 1">
            order by t.DP_HAVE_ROOM_PCT desc,
        </if>
        <if test="sortColumn == 1 and sortType == 0">
            order by t.UP_HAVE_ROOM_PCT,
        </if>
        <if test="sortColumn == 1 and sortType == 1">
            order by t.UP_HAVE_ROOM_PCT desc,
        </if>
        <if test="sortColumn == 2 and sortType == 0">
            order by t.FULL_ROOM_PCT,
        </if>
        <if test="sortColumn == 2 and sortType == 1">
            order by t.FULL_ROOM_PCT desc,
        </if>
        t.PROJECT_ID desc, t.HOTEL_ID
    </if>
  </select>

    <select id="selectDpUpFullPctList" parameterType="com.fangcang.rfp.common.dto.request.count.HotelPriceMonitorSumRequest"
            resultType="com.fangcang.rfp.common.dto.response.TransactionRankingMonitoringProportionResponse">
        select *
        from (
            select
                t.PROJECT_ID projectId,
                t.HOTEL_ID hotelId,
                h.CHN_NAME hotelName
            <if test="sortColumn == -1">
                ,t.DP_HAVE_ROOM_PCT oneThreePct
            </if>
            <if test="sortColumn == 1">
                ,t.UP_HAVE_ROOM_PCT oneThreePct
            </if>
            <if test="sortColumn == 2">
                ,t.FULL_ROOM_PCT oneThreePct
            </if>
            from HTL_RFP.T_HOTEL_PRICE_MONITOR_SUM t
            left join HTL_INFO.T_HOTEL h on t.HOTEL_ID = h.HOTELID
            where 1=1
               and exists (
                    select 1
                    from HTL_RFP.T_PROJECT p
                    where t.PROJECT_ID = p.PROJECT_ID
                    and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.PRICE_MONITOR_START_DATE and p.PRICE_MONITOR_END_DATE
                )
            <if test="monitorDayType != null">
                and t.MONITOR_DAY_TYPE = #{monitorDayType}
            </if>
            <if test="orgId != null">
                and t.ORG_ID = #{orgId}
            </if>
            <if test="projectId != null">
                and t.PROJECT_ID = #{projectId}
            </if>
            <if test="hotelId != null">
                and t.HOTEL_ID = #{hotelId}
            </if>
            <if test="sortColumn == -1">
                order by t.DP_HAVE_ROOM_PCT desc,
            </if>
            <if test="sortColumn == 1">
                order by t.UP_HAVE_ROOM_PCT desc,
            </if>
            <if test="sortColumn == 2">
                order by t.FULL_ROOM_PCT desc,
            </if>
            t.PROJECT_ID,t.HOTEL_ID
        )t
        where rownum &lt;= 10

    </select>

    <select id="selectCityPriceMonitorList" parameterType="com.fangcang.rfp.common.dto.request.count.CityPriceMonitorRequest"
            resultType="com.fangcang.rfp.common.dto.response.count.CityPriceMonitorSumResponse">
        select t.cityCode,
               a.DATANAME cityName,
               t.orgId,
               o.ORG_NAME orgName,
               t.opHaveRoomPct,
               t.dpHaveRoomPct,
               t.upHaveRoomPct,
               t.fullRoomPct
        from (
            select
                t.CITY_CODE cityCode,
                t.org_id orgId,
                round( sum(t.op_have_room_pct) / count(t.hotel_id),2)  as opHaveRoomPct,
                round( sum(t.dp_have_room_pct) / count(t.hotel_id),2) as dpHaveRoomPct,
                round( sum(t.up_have_room_pct) / count(t.hotel_id) ,2) as upHaveRoomPct,
                round( sum(t.full_room_pct) / count(t.hotel_id) ,2) as fullRoomPct
            from htl_rfp.t_hotel_price_monitor_sum t
            where 1=1
            and exists (
                select 1
                from htl_rfp.T_PROJECT p
                where p.PROJECT_ID = t.PROJECT_ID
                and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.PRICE_MONITOR_START_DATE and p.PRICE_MONITOR_END_DATE
                <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                    AND p.TENDER_ORG_ID IN
                    <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                        #{userRelatedOrgId}
                    </foreach>
                </if>
            )
            <if test="monitorYear != null ">
                and t.monitor_year = #{monitorYear}
            </if>
            <if test="monitorDayType != null ">
                and t.monitor_day_type = #{monitorDayType}
            </if>
            <if test="orgId != null ">
                and t.org_id = #{orgId}
            </if>
            <if test="cityCode != null and cityCode != '' ">
                and t.CITY_CODE = #{cityCode}
            </if>
            group by  t.org_id,t.CITY_CODE
            <if test="sortColumn == null and sortType == null">
                order by fullRoomPct desc,
            </if>
            <if test="sortColumn != null and sortType != null">
                <if test="sortColumn == 0 and sortType == 0">
                    order by opHaveRoomPct,
                </if>
                <if test="sortColumn == 0 and sortType == 1">
                    order by opHaveRoomPct desc,
                </if>
                <if test="sortColumn == -1 and sortType == 0">
                    order by dpHaveRoomPct,
                </if>
                <if test="sortColumn == -1 and sortType == 1">
                    order by dpHaveRoomPct desc,
                </if>
                <if test="sortColumn == 1 and sortType == 0">
                    order by upHaveRoomPct,
                </if>
                <if test="sortColumn == 1 and sortType == 1">
                    order by upHaveRoomPct desc,
                </if>
                <if test="sortColumn == 2 and sortType == 0">
                    order by fullRoomPct,
                </if>
                <if test="sortColumn == 2 and sortType == 1">
                    order by fullRoomPct desc,
                </if>
            </if>
            t.org_id,t.CITY_CODE
        )t
        left join htl_rfp.T_ORG o on t.orgId = o.ORG_ID
        left join HTL_BASE.T_AREADATA a on t.cityCode = a.DATACODE  and a.DATATYPE = 3 and  a.COUNTRYCODE = 'CN'

    </select>

    <select id="selectPoiPriceMonitorList" parameterType="com.fangcang.rfp.common.dto.request.count.PoiPriceMonitorRequest"
            resultType="com.fangcang.rfp.common.dto.response.count.PoiPriceMonitorSumResponse">
        select k.poiId poiId,
               k.hotelCount hotelCount,
               k.opHaveRoomPctByCity opHaveRoomPct,
               k.dpHaveRoomPctByCity dpHaveRoomPct,
               k.upHaveRoomPctByCity upHaveRoomPct,
               k.fullRoomPctByCity fullRoomPct,
               poi.CITY_CODE cityCode,
               k.org_id orgId,
               o.ORG_NAME orgName,
               poi.city_name cityName,
               poi.poi_name poiName
        from (select a.poi_id poiId,
                     a.org_id,
                     count(a.hotel_id) hotelCount,
                     round( sum(a.op_have_room_pct) / count(a.hotel_id),2)  as opHaveRoomPctByCity,
                     round( sum(a.dp_have_room_pct) / count(a.hotel_id),2) as dpHaveRoomPctByCity,
                     round( sum(a.up_have_room_pct) / count(a.hotel_id),2) as upHaveRoomPctByCity,
                     round( sum(a.full_room_pct) / count(a.hotel_id),2) as fullRoomPctByCity
              from (select th1.poi_id,
                           t.op_have_room_pct,
                           t.dp_have_room_pct,
                           t.up_have_room_pct,
                           t.full_room_pct,
                           t.hotel_id,
                           t.org_id
                    from htl_rfp.t_hotel_price_monitor_sum t
                             left join htl_rfp.t_poi_hotel th1 on th1.hotel_id = t.hotel_id and th1.org_id = t.org_id
                    where 1=1 and exists  (
                        select 1
                        from htl_rfp.T_PROJECT p
                        where p.PROJECT_ID = t.PROJECT_ID
                        and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between p.PRICE_MONITOR_START_DATE and p.PRICE_MONITOR_END_DATE
                        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
                            AND p.TENDER_ORG_ID IN
                            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                                #{userRelatedOrgId}
                            </foreach>
                        </if>
                    )
                    <if test="monitorYear != null">
                    and  t.monitor_year = #{monitorYear}
                    </if>
                    <if test="monitorDayType != null">
                      and t.monitor_day_type = #{monitorDayType}
                    </if>
                    <if test="orgId != null">
                      and t.org_id = #{orgId}
                    </if>
                    <if test="cityCode != null and cityCode != '' ">
                    and t.CITY_CODE = #{cityCode}
                    </if>
                      and exists (
                                select 1
                                from htl_rfp.t_poi_hotel th
                                left join htl_rfp.t_org_poi op on th.POI_ID = op.POI_ID
                                where th.hotel_id = t.hotel_id and th.org_id = t.org_id
                                <if test="cityCode != null and cityCode != '' ">
                                    and op.CITY_CODE = #{cityCode}
                                </if>
                                )
                  ) a
              group by a.org_id,a.poi_id) k
                 left join htl_rfp.t_org_poi poi on k.poiId = poi.poi_id
                 left join htl_rfp.T_ORG o on k.org_id = o.ORG_ID
                 left join HTL_BASE.T_AREADATA a on a.DATACODE = poi.CITY_CODE and a.DATATYPE = 3 and a.COUNTRYCODE = 'CN'
        where 1=1
        <if test="poiName != null and poiName != '' ">
            and poi.poi_name like concat('%',concat(#{poiName},'%'))
        </if>
        <if test="sortColumn == null and sortType == null">
            order by opHaveRoomPctByCity desc,
        </if>
        <if test="sortColumn != null and sortType != null">
            <if test="sortColumn == 0 and sortType == 0">
                order by opHaveRoomPctByCity,
            </if>
            <if test="sortColumn == 0 and sortType == 1">
                order by opHaveRoomPctByCity desc,
            </if>
            <if test="sortColumn == -1 and sortType == 0">
                order by dpHaveRoomPctByCity,
            </if>
            <if test="sortColumn == -1 and sortType == 1">
                order by dpHaveRoomPctByCity desc,
            </if>
            <if test="sortColumn == 1 and sortType == 0">
                order by upHaveRoomPctByCity,
            </if>
            <if test="sortColumn == 1 and sortType == 1">
                order by upHaveRoomPctByCity desc,
            </if>
            <if test="sortColumn == 2 and sortType == 0">
                order by fullRoomPctByCity,
            </if>
            <if test="sortColumn == 2 and sortType == 1">
                order by fullRoomPctByCity desc,
            </if>
        </if>
        k.org_id,k.poiId
    </select>


    <select id="selectPoiManageMonitorHotelList" resultType="com.fangcang.rfp.common.dto.response.count.PoiManageMonitorHotelResponse"
            parameterType="com.fangcang.rfp.common.dto.request.count.PoiPriceMonitorRequest">
        select o.POI_ID poiId,
               o.POI_NAME poiName,
               o.POI_ADDRESS poiAddress,
               o.ORG_ID orgId,
               r.ORG_NAME orgName,
               o.CITY_CODE cityCode,
               o.CITY_NAME cityName,
               count(p.HOTEL_ID) hotelCount
        from HTL_RFP.T_ORG_POI o
        left join HTL_RFP.T_POI_HOTEL p  on o.POI_ID = p.POI_ID
        left join HTL_RFP.T_ORG r on r.ORG_ID = o.ORG_ID
        where o.STATE = 1 and r.STATE = 1 and r.ORG_TYPE = 3
        <if test="orgId != null">
            and r.ORG_ID = #{orgId}
        </if>
        <if test="cityCode != null and cityCode != '' ">
            and o.CITY_CODE = #{cityCode}
        </if>
        <if test="poiName != null and poiName != '' ">
            and o.POI_NAME like concat('%',concat(#{poiName},'%'))
        </if>
        group by o.POI_ID,
                 o.POI_NAME,
                 o.POI_ADDRESS,
                 o.ORG_ID,
                 r.ORG_NAME,
                 o.CITY_CODE,
                 o.CITY_NAME
    </select>



</mapper>