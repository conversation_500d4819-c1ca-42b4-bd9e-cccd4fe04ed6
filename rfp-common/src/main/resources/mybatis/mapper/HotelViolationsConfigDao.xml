<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.HotelViolationsConfigDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.HotelViolationsConfig">
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="NO_LRA_SO_DAYS_NOTICE" jdbcType="DECIMAL" property="noLraSoDaysNotice"/>
        <result column="NO_LRA_SO_DAYS" jdbcType="DECIMAL" property="noLraSoDays"/>
        <result column="NO_LRA_SO_DAYS_OTHER" jdbcType="DECIMAL" property="noLraSoDaysOther"/>
        <result column="NO_LRA_SO_PENALTY_SCORE" jdbcType="DECIMAL" property="noLraSoPenaltyScore"/>
        <result column="NO_LRA_PI_DAYS_NOTICE" jdbcType="DECIMAL" property="noLraPiDaysNotice"/>
        <result column="NO_LRA_PI_DAYS" jdbcType="DECIMAL" property="noLraPiDays"/>
        <result column="NO_LRA_PI_DAYS_AVG" jdbcType="DECIMAL" property="noLraPiDaysAvg"/>
        <result column="NO_LRA_PI_PENALTY_SCORE" jdbcType="DECIMAL" property="noLraPiPenaltyScore"/>
        <result column="LRA_SO_DAYS_NOTICE" jdbcType="DECIMAL" property="lraSoDaysNotice"/>
        <result column="LRA_SO_DAYS" jdbcType="DECIMAL" property="lraSoDays"/>
        <result column="LRA_SO_DAYS_OTHER" jdbcType="DECIMAL" property="lraSoDaysOther"/>
        <result column="LRA_SO_PENALTY_SCORE" jdbcType="DECIMAL" property="lraSoPenaltyScore"/>
        <result column="LRA_PI_DAYS_NOTICE" jdbcType="DECIMAL" property="lraPiDaysNotice"/>
        <result column="LRA_PI_DAYS" jdbcType="DECIMAL" property="lraPiDays"/>
        <result column="LRA_PI_DAYS_AVG" jdbcType="DECIMAL" property="lraPiDaysAvg"/>
        <result column="LRA_PI_PENALTY_SCORE" jdbcType="DECIMAL" property="lraPiPenaltyScore"/>
        <result column="ORDER_PI_NOTICE" jdbcType="DECIMAL" property="orderPiNotice"/>
        <result column="ORDER_PI_AVG" jdbcType="DECIMAL" property="orderPiAvg"/>
        <result column="ORDER_PI_PENALTY_SCORE" jdbcType="DECIMAL" property="orderPiPenaltyScore"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      PROJECT_ID, NO_LRA_SO_DAYS_NOTICE, NO_LRA_SO_DAYS, NO_LRA_SO_DAYS_OTHER, NO_LRA_SO_PENALTY_SCORE,
      NO_LRA_PI_DAYS_NOTICE, NO_LRA_PI_DAYS, NO_LRA_PI_DAYS_AVG,NO_LRA_PI_PENALTY_SCORE, LRA_SO_DAYS_NOTICE, LRA_SO_DAYS,
      LRA_SO_DAYS_OTHER, LRA_SO_PENALTY_SCORE, LRA_PI_DAYS_NOTICE,LRA_PI_DAYS, LRA_PI_DAYS_AVG, LRA_PI_PENALTY_SCORE,
      ORDER_PI_NOTICE, ORDER_PI_AVG, ORDER_PI_PENALTY_SCORE,CREATOR, CREATE_TIME, MODIFIER,MODIFY_TIME
    </sql>

    <update id="saveOrModifyViolationConfig" parameterType="com.fangcang.rfp.common.entity.HotelViolationsConfig">
       MERGE INTO htl_rfp.T_HOTEL_VIOLATIONS_CONFIG dest
    USING (
      SELECT  #{projectId,jdbcType=DECIMAL} AS projectId,
             #{noLraSoDaysNotice,jdbcType=DECIMAL} AS noLraSoDaysNotice,
             #{noLraSoDays,jdbcType=DECIMAL} AS noLraSoDays,
            #{noLraSoDaysOther,jdbcType=DECIMAL} AS noLraSoDaysOther,
             #{noLraSoPenaltyScore,jdbcType=DECIMAL} AS noLraSoPenaltyScore,
            #{noLraPiDaysNotice,jdbcType=DECIMAL} AS noLraPiDaysNotice,
            #{noLraPiDays,jdbcType=DECIMAL} AS noLraPiDays,
            #{noLraPiDaysAvg,jdbcType=DECIMAL} AS noLraPiDaysAvg,
            #{noLraPiPenaltyScore,jdbcType=DECIMAL} AS noLraPiPenaltyScore,
            #{lraSoDaysNotice,jdbcType=DECIMAL} AS lraSoDaysNotice,
            #{lraSoDays,jdbcType=DECIMAL} AS lraSoDays,
            #{lraSoDaysOther,jdbcType=DECIMAL} AS lraSoDaysOther,
            #{lraSoPenaltyScore,jdbcType=DECIMAL} AS lraSoPenaltyScore,
            #{lraPiDaysNotice,jdbcType=DECIMAL} AS lraPiDaysNotice,
            #{lraPiDays,jdbcType=DECIMAL} AS lraPiDays,
            #{lraPiDaysAvg,jdbcType=DECIMAL} AS lraPiDaysAvg,
            #{lraPiPenaltyScore,jdbcType=DECIMAL} AS lraPiPenaltyScore,
            #{orderPiNotice,jdbcType=DECIMAL} AS orderPiNotice,
            #{orderPiAvg,jdbcType=DECIMAL} AS orderPiAvg,
            #{orderPiPenaltyScore,jdbcType=DECIMAL} AS orderPiPenaltyScore,
            #{creator,jdbcType=VARCHAR} AS creator,
            #{modifier,jdbcType=VARCHAR} AS modifier
      FROM dual
    ) src
    ON (dest.project_id = src.projectId)
    WHEN MATCHED THEN
      UPDATE SET
        dest.no_lra_so_days_notice = src.noLraSoDaysNotice,
        dest.no_lra_so_days = src.noLraSoDays,
        dest.no_lra_so_days_other = src.noLraSoDaysOther,
        dest.no_lra_so_penalty_score = src.noLraSoPenaltyScore,
        dest.no_lra_pi_days_notice = src.noLraPiDaysNotice,
        dest.no_lra_pi_days = src.noLraPiDays,
        dest.no_lra_pi_days_avg = src.noLraPiDaysAvg,
        dest.no_lra_pi_penalty_score = src.noLraPiPenaltyScore,
        dest.lra_so_days_notice = src.lraSoDaysNotice,
        dest.lra_so_days = src.lraSoDays,
        dest.lra_so_days_other = src.lraSoDaysOther,
        dest.lra_so_penalty_score = src.lraSoPenaltyScore,
        dest.lra_pi_days_notice = src.lraPiDaysNotice,
        dest.lra_pi_days = src.lraPiDays,
        dest.lra_pi_days_avg = src.lraPiDaysAvg,
        dest.lra_pi_penalty_score = src.lraPiPenaltyScore,
        dest.order_pi_notice = src.orderPiNotice,
        dest.order_pi_avg = src.orderPiAvg,
        dest.order_pi_penalty_score = src.orderPiPenaltyScore,
        dest.modifier = modifier,
        dest.modify_time = sysdate
    WHEN NOT MATCHED THEN
      INSERT (project_id, no_lra_so_days_notice, no_lra_so_days, no_lra_so_days_other,
              no_lra_so_penalty_score, no_lra_pi_days_notice, no_lra_pi_days,
              no_lra_pi_days_avg, no_lra_pi_penalty_score, lra_so_days_notice,
              lra_so_days, lra_so_days_other, lra_so_penalty_score, lra_pi_days_notice,
              lra_pi_days, lra_pi_days_avg, lra_pi_penalty_score, order_pi_notice,
              order_pi_avg, order_pi_penalty_score, creator, create_time, modifier, modify_time)
      VALUES (src.projectId, src.noLraSoDaysNotice, src.noLraSoDays, src.noLraSoDaysOther,
              src.noLraSoPenaltyScore, src.noLraPiDaysNotice, src.noLraPiDays,
              src.noLraPiDaysAvg, src.noLraPiPenaltyScore, src.lraSoDaysNotice,
              src.lraSoDays, src.lraSoDaysOther, src.lraSoPenaltyScore, src.lraPiDaysNotice,
              src.lraPiDays, src.lraPiDaysAvg, src.lraPiPenaltyScore, src.orderPiNotice,
              src.orderPiAvg, src.orderPiPenaltyScore, src.creator, sysdate, src.modifier, sysdate)

    </update>

    <select id="queryHotelViolationConfig" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_HOTEL_VIOLATIONS_CONFIG
        where project_id=#{projectId}
    </select>
    <select id="queryNeedViolationsMonitorProject" resultType="java.lang.Long">
        select c.project_id
        from htl_rfp.T_HOTEL_VIOLATIONS_CONFIG c join
        htl_rfp.t_project p on c.project_id = p.project_id
        where (to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between PRICE_MONITOR_START_DATE and PRICE_MONITOR_END_DATE) and
         (c.no_lra_so_days_notice = 1 or c.no_lra_pi_days_notice = 1 or c.lra_so_days_notice = 1 or c.lra_pi_days_notice = 1 or
          c.order_pi_notice = 1)
    </select>

    <select id="queryNeedOrderViolationsMonitorProject" resultType="java.lang.Long">
        select c.project_id
        from htl_rfp.T_HOTEL_VIOLATIONS_CONFIG c join
        htl_rfp.t_project p on c.project_id = p.project_id
        where (to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between PRICE_MONITOR_START_DATE and PRICE_MONITOR_END_DATE) and
         c.order_pi_notice = 1
    </select>
</mapper>