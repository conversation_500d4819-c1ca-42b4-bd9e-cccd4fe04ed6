<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectLanyonViewKeysDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ProjectLanyonViewKeys">
        <result column="PROJECT_LANYON_VIEW_KEYS_ID" jdbcType="BIGINT" property="projectLanyonViewKeysId"/>
        <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="BASE_INFO" jdbcType="VARCHAR" property="baseInfo"/>
        <result column="HOTEL_VERIFY" jdbcType="VARCHAR" property="hotelVerify"/>
        <result column="HOTEL_FACILITIES" jdbcType="VARCHAR" property="hotelFacilities"/>
        <result column="BID_INFO" jdbcType="VARCHAR" property="bidInfo"/>
        <result column="MEETING_ROOM_BID_INFO" jdbcType="VARCHAR" property="meetingRoomBidInfo"/>
        <result column="LONG_BID_INFO" jdbcType="VARCHAR" property="longBidInfo"/>
        <result column="HOTEL_SERVICE" jdbcType="VARCHAR" property="hotelService"/>
        <result column="USER_DEFINED" jdbcType="VARCHAR" property="userDefined"/>
        <result column="MTG_USER_DEFINED" jdbcType="VARCHAR" property="mtgUserDefined"/>
        <result column="LRA_UN_APPLICABLE_DAY_INFO" jdbcType="VARCHAR" property="larUnApplicableDayInfo"/>
        <result column="BASE_SERVICE_FEE" jdbcType="VARCHAR" property="baseServiceFee"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        PROJECT_LANYON_VIEW_KEYS_ID, PROJECT_ID, BASE_INFO, HOTEL_VERIFY, HOTEL_FACILITIES, BID_INFO,
        MEETING_ROOM_BID_INFO, LONG_BID_INFO, HOTEL_SERVICE, USER_DEFINED, MTG_USER_DEFINED, LRA_UN_APPLICABLE_DAY_INFO, BASE_SERVICE_FEE, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.fangcang.rfp.common.entity.ProjectLanyonViewKeys" keyProperty="projectLanyonViewKeysId"
            useGeneratedKeys="true">
        <selectKey keyProperty="projectLanyonViewKeysId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_PROJECT_LANYON_VIEW_KEYS.nextval from dual
        </selectKey>
        insert into htl_rfp.T_PROJECT_LANYON_VIEW_KEYS (PROJECT_LANYON_VIEW_KEYS_ID, PROJECT_ID, BASE_INFO, HOTEL_VERIFY, HOTEL_FACILITIES, BID_INFO,
        MEETING_ROOM_BID_INFO, LONG_BID_INFO, HOTEL_SERVICE, USER_DEFINED, MTG_USER_DEFINED, LRA_UN_APPLICABLE_DAY_INFO, BASE_SERVICE_FEE,
        CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
        )
        values (#{projectLanyonViewKeysId}, #{projectId,jdbcType=BIGINT},
        #{baseInfo,jdbcType=VARCHAR},  #{hotelVerify,jdbcType=VARCHAR}, #{hotelFacilities,jdbcType=VARCHAR},
        #{bidInfo,jdbcType=VARCHAR}, #{meetingRoomBidInfo,jdbcType=VARCHAR},#{longBidInfo,jdbcType=VARCHAR},
        #{hotelService,jdbcType=VARCHAR}, #{userDefined,jdbcType=VARCHAR}, #{mtgUserDefined,jdbcType=VARCHAR},
        #{larUnApplicableDayInfo,jdbcType=VARCHAR}, #{baseServiceFee,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR}, sysdate, #{modifier,jdbcType=VARCHAR}, sysdate
        )
    </insert>

    <select id="getByPorjectId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT_LANYON_VIEW_KEYS
        WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.ProjectLanyonViewKeys">
        UPDATE htl_rfp.T_PROJECT_LANYON_VIEW_KEYS
        SET BASE_INFO = #{baseInfo,jdbcType=VARCHAR},
            HOTEL_VERIFY = #{hotelVerify,jdbcType=VARCHAR},
            HOTEL_FACILITIES = #{hotelFacilities,jdbcType=VARCHAR},
            BID_INFO = #{bidInfo,jdbcType=VARCHAR},
            MEETING_ROOM_BID_INFO = #{meetingRoomBidInfo,jdbcType=VARCHAR},
            LONG_BID_INFO = #{longBidInfo,jdbcType=VARCHAR},
            HOTEL_SERVICE = #{hotelService,jdbcType=VARCHAR},
            USER_DEFINED = #{userDefined,jdbcType=VARCHAR},
            MTG_USER_DEFINED = #{mtgUserDefined,jdbcType=VARCHAR},
            LRA_UN_APPLICABLE_DAY_INFO = #{larUnApplicableDayInfo,jdbcType=VARCHAR},
            BASE_SERVICE_FEE = #{baseServiceFee,jdbcType=VARCHAR},
            MODIFIER = #{modifier,jdbcType=VARCHAR},
            MODIFY_TIME = sysdate
        WHERE PROJECT_LANYON_VIEW_KEYS_ID = #{projectLanyonViewKeysId,jdbcType=BIGINT}
    </update>

</mapper>