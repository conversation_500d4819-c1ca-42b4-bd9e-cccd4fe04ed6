<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.DisHotelOrderMonitorDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.DisHotelOrderMonitor">
    <result column="DISTRIBUTOR_CODE" jdbcType="VARCHAR" property="distributorCode" />
    <result column="HOTEL_ID" jdbcType="INTEGER" property="hotelId" />
    <result column="CITY_CODE" jdbcType="VARCHAR" property="cityCode" />
    <result column="BOOK_DATE" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="BOOK_COUNT" jdbcType="INTEGER" property="bookCount" />
    <result column="UNDER_FIVE_MIN_COUNT" jdbcType="INTEGER" property="underFiveMinCount" />
    <result column="FIVE_TO_THIRTY_MIN_COUNT" jdbcType="INTEGER" property="fiveToThirtyMinCount" />
    <result column="OVER_THIRTY_MIN_COUNT" jdbcType="INTEGER" property="overThirtyMinCount" />
    <result column="REFUSED_COUNT" jdbcType="INTEGER" property="refusedCount" />
    <result column="PROJECT_ID" jdbcType="INTEGER" property="projectId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.DisHotelOrderMonitor">
    insert into HTL_RFP.T_DIS_HOTEL_ORDER_MONITOR (DISTRIBUTOR_CODE, HOTEL_ID, CITY_CODE,
      BOOK_DATE, BOOK_COUNT, UNDER_FIVE_MIN_COUNT, FIVE_TO_THIRTY_MIN_COUNT,
      OVER_THIRTY_MIN_COUNT, REFUSED_COUNT, PROJECT_ID,
      CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
      )
    values (#{distributorCode,jdbcType=VARCHAR}, #{hotelId,jdbcType=INTEGER}, #{cityCode,jdbcType=VARCHAR},
      #{bookDate,jdbcType=TIMESTAMP}, #{bookCount,jdbcType=INTEGER}, #{underFiveMinCount,jdbcType=INTEGER},
      #{fiveToThirtyMinCount,jdbcType=INTEGER}, #{overThirtyMinCount,jdbcType=INTEGER},
      #{refusedCount,jdbcType=INTEGER}, #{projectId,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>

  <select id="selectHotelOrderMonitorStatList" parameterType="com.fangcang.rfp.common.dto.request.SelectHotelOrderMonitorStatRequest"
          resultType="com.fangcang.rfp.common.dto.response.SelectHotelOrderMonitorStatResponse">
      SELECT
      orgId AS orgId,
      hotelId AS hotelId,
      CASE WHEN sum(totalCount) > 0 THEN ROUND(sum(underFiveMinCount) / sum(totalCount),3) ELSE 0 END AS underFiveMinCountRate,
      CASE WHEN sum(totalCount) > 0 THEN ROUND(sum(fiveToThirtyMinCount) / sum(totalCount),3) ELSE 0 END AS fiveToThirtyMinCountRate,
      CASE WHEN sum(totalCount) > 0 THEN ROUND(sum(overThirtyMinCount) / sum(totalCount),3) ELSE 0 END AS overThirtyMinCountRate,
      CASE WHEN sum(totalCount) > 0 THEN ROUND(sum(refusedCount) / sum(totalCount),3) ELSE 0 END AS refusedCountRate,
      hotelGroupId AS hotelGroupId,
      sum(roomNightCount) AS roomNightCount,
      isFollowHotel AS isFollowHotel
      FROM
      (
        SELECT
                o.HOTEL_ID AS hotelId,
                SUM(o.UNDER_FIVE_MIN_COUNT) AS underFiveMinCount,
                SUM(o.FIVE_TO_THIRTY_MIN_COUNT) AS fiveToThirtyMinCount,
                SUM(o.OVER_THIRTY_MIN_COUNT) AS overThirtyMinCount,
                SUM(o.REFUSED_COUNT) AS refusedCount,
                hdo.HOTEL_GROUP AS hotelGroupId,
                NVL(SUM(hdo.ROOM_NIGHT_COUNT),0) AS roomNightCount,
                SUM( o.BOOK_COUNT ) AS totalCount,
                omc.ORG_ID as orgId,
                MAX(CASE WHEN ofh.HOTEL_ID IS NULL THEN 0 ELSE 1 END ) AS isFollowHotel
        FROM
              HTL_RFP.T_DIS_HOTEL_ORDER_MONITOR o
        LEFT JOIN HTL_RFP.T_DIS_HOTEL_DAILY_ORDER hdo ON hdo.HOTEL_ID = o.HOTEL_ID AND hdo.BOOK_DATE = o.BOOK_DATE AND hdo.DISTRIBUTOR_CODE = o.DISTRIBUTOR_CODE
        LEFT JOIN HTL_RFP.T_ORDER_MONITOR_CONFIG omc ON o.DISTRIBUTOR_CODE = omc.DISTRIBUTOR_CODE
        LEFT JOIN HTL_RFP.T_ORG_FOLLOW_HOTEL ofh ON ofh.ORG_ID = omc.ORG_ID AND ofh.HOTEL_ID = o.HOTEL_ID
        WHERE o.BOOK_DATE >= TO_DATE(#{statDateFrom}, 'yyyy-mm-dd') AND o.BOOK_DATE <![CDATA[   <=  ]]> TO_DATE(#{statDateTo}, 'yyyy-mm-dd')
            <if test="cityCode != null and cityCode != ''">
                AND o.CITY_CODE = #{cityCode}
            </if>
          <if test="provinceId != null and provinceId > 0">
              AND o.CITY_CODE IN (
                  SELECT DATACODE FROM HTL_BASE.T_AREADATA WHERE DATATYPE = 3 AND PARENTID  = #{provinceId}
              )
          </if>
            <if test="hotelGroupId != null and hotelGroupId != 0">
                AND hdo.HOTEL_GROUP = #{hotelGroupId}
            </if>
            <if test="hotelId != null and hotelId != 0">
                AND o.HOTEL_ID = #{hotelId}
            </if>
            <if test="orgId != null and orgId != 0">
                AND omc.ORG_ID = #{orgId}
            </if>
            <if test="isFollowHotel != null and isFollowHotel > 0">
                AND ofh.HOTEL_ID IS NOT NULL
            </if>
          <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
              AND omc.org_id IN
              <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" open="(" close=")" separator=",">
                  #{userRelatedOrgId}
              </foreach>
          </if>
          GROUP BY o.HOTEL_ID,hdo.HOTEL_GROUP,omc.ORG_ID) t
      GROUP BY
            t.orgId,
              t.hotelId,
              t.hotelGroupId,
               t.isFollowHotel
      <if test="sortColumn == 0 and sortType == 0">
          order by roomNightCount ASC, orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 0 and sortType == 1">
          order by roomNightCount DESC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 1 and sortType == 0">
          order by underFiveMinCountRate ASC,  orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 1 and sortType == 1">
          order by underFiveMinCountRate DESC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 2 and sortType == 0">
          order by fiveToThirtyMinCountRate ASC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 2 and sortType == 1">
          order by fiveToThirtyMinCountRate DESC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 3 and sortType == 0">
          order by overThirtyMinCountRate ASC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 3 and sortType == 1">
          order by overThirtyMinCountRate DESC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 4 and sortType == 0">
          order by refusedCountRate ASC , orgId ASC, hotelId ASC
      </if>
      <if test="sortColumn == 4 and sortType == 1">
          order by refusedCountRate DESC , orgId ASC, hotelId ASC
      </if>
  </select>

   <select id="selectHotelOrderMonitorStatDetail" parameterType="com.fangcang.rfp.common.dto.request.SelectHotelOrderMonitorStatRequest"
           resultType="com.fangcang.rfp.common.dto.response.SelectHotelOrderMonitorStatResponse">
       SELECT
       o.HOTEL_ID AS hotelId,
       o.BOOK_DATE AS bookDate,
       o.BOOK_COUNT AS bookieCount,
       o.UNDER_FIVE_MIN_COUNT AS underFiveMinCount,
       o.FIVE_TO_THIRTY_MIN_COUNT AS fiveToThirtyMinCount,
       o.OVER_THIRTY_MIN_COUNT AS overThirtyMinCount,
       o.REFUSED_COUNT AS refusedCount
       FROM
        HTL_RFP.T_DIS_HOTEL_ORDER_MONITOR o
       WHERE o.HOTEL_ID = #{hotelId}
       AND o.BOOK_DATE >= TO_DATE(#{statDateFrom}, 'yyyy-mm-dd') AND o.BOOK_DATE <![CDATA[   <=  ]]> TO_DATE(#{statDateTo}, 'yyyy-mm-dd')
           AND exists ( SELECT 1
                   FROM HTL_RFP.T_ORDER_MONITOR_CONFIG c
                   WHERE o.DISTRIBUTOR_CODE = c.distributor_code
                   AND c.STATE = 1
            <if test="orgId != null and orgId != 0">
                   AND c.org_id = #{orgId}
            </if>
                )

       ORDER BY o.BOOK_DATE ASC
   </select>
</mapper>
