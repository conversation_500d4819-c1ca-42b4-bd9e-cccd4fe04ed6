<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PriceUnapplicableDayDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PriceUnapplicableDay">
    <id column="UNAPPLICABLE_DAY_ID" jdbcType="DECIMAL" property="unapplicableDayId" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="BIGINT" property="projectIntentHotelId" />
    <result column="PROJECT_ID" jdbcType="BIGINT" property="projectId" />
    <result column="HOTEL_ID" jdbcType="BIGINT" property="hotelId" />
    <result column="START_DATE" jdbcType="TIMESTAMP" property="startDate" />
    <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    UNAPPLICABLE_DAY_ID, PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, START_DATE, END_DATE, 
    CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>

  <select id="selectPriceUnapplicableDayList" parameterType="com.fangcang.rfp.common.dto.request.ProjectHotelBidStrategyRequest" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PRICE_UNAPPLICABLE_DAY
    where 1 =1
      <if test="projectIntentHotelId != null">
        and  PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
      </if>
      <if test="projectId != null">
        and PROJECT_ID = #{projectId}
      </if>
      <if test="hotelId != null">
        and HOTEL_ID = #{hotelId}
      </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_PRICE_UNAPPLICABLE_DAY
    where UNAPPLICABLE_DAY_ID = #{unapplicableDayId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectIdAndHotelId" parameterType="java.lang.Long">
    delete from T_PRICE_UNAPPLICABLE_DAY
    where PROJECT_ID = #{projectId,jdbcType=BIGINT} AND HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByPriceUnapplicableDay" parameterType="com.fangcang.rfp.common.entity.PriceUnapplicableDay">
    delete from T_PRICE_UNAPPLICABLE_DAY
    where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT} and
          PROJECT_ID = #{projectId,jdbcType=BIGINT} and
          HOTEL_ID = #{hotelId,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.PriceUnapplicableDay">
    <selectKey keyProperty="unapplicableDayId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_UNAPPLICABLE_DAY_ID.nextval from dual
    </selectKey>
    insert into htl_rfp.T_PRICE_UNAPPLICABLE_DAY (
    UNAPPLICABLE_DAY_ID,
    PROJECT_INTENT_HOTEL_ID,
    PROJECT_ID,
    HOTEL_ID,
    START_DATE,
    END_DATE,
    CREATOR,
    CREATE_TIME
    )
    values (#{unapplicableDayId,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=BIGINT},#{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL},
     #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP},
    #{creator,jdbcType=VARCHAR}, SYSDATE)
  </insert>


  <insert id="insertBatch" parameterType="list">
    insert into htl_rfp.T_PRICE_UNAPPLICABLE_DAY (
      UNAPPLICABLE_DAY_ID,
      PROJECT_INTENT_HOTEL_ID,
      PROJECT_ID,
      HOTEL_ID,
      START_DATE,
      END_DATE,
      CREATOR,
      CREATE_TIME
   )
    select htl_rfp.SEQ_RFP_UNAPPLICABLE_DAY_ID.nextval, t.* from(
    <foreach collection="list" item="item" index="index" separator="union all">
      select #{item.projectIntentHotelId,jdbcType=BIGINT},
             #{item.projectId,jdbcType=BIGINT},
             #{item.hotelId,jdbcType=BIGINT},
             #{item.startDate,jdbcType=TIMESTAMP},
             #{item.endDate,jdbcType=TIMESTAMP},
             #{item.creator,jdbcType=VARCHAR},
             SYSDATE
      from dual
    </foreach>)t
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.PriceUnapplicableDay">
    update T_PRICE_UNAPPLICABLE_DAY
    set
      START_DATE = #{startDate,jdbcType=TIMESTAMP},
      END_DATE = #{endDate,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = SYSDATE
    where UNAPPLICABLE_DAY_ID = #{unapplicableDayId}
  </update>

  <select id="queryPriceUnapplicableDayByHotelIdAndProjectId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from htl_rfp.T_PRICE_UNAPPLICABLE_DAY
    where project_id = #{projectId}
    and hotel_id in
    <foreach collection="hotelIds" open="(" close=")" item="hotelId" separator="," index="index">
      #{hotelId}
    </foreach>
  </select>

  <select id="selectPriceUnapplicableDayListByIntentHotelId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from htl_rfp.T_PRICE_UNAPPLICABLE_DAY
    where PROJECT_INTENT_HOTEL_ID in
    <foreach collection="intentHotelIds" open="(" close=")" item="intentHotelId" separator="," index="index">
      #{intentHotelId}
    </foreach>
  </select>

  <delete id="deleteByProjectIntentHotelId">
    delete
    from htl_rfp.T_PRICE_UNAPPLICABLE_DAY
    where PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
  </delete>
</mapper>