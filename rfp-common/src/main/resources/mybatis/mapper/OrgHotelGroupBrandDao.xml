<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.OrgHotelGroupBrandDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.OrgHotelGroupBrand">
        <result column="ORG_HOTEL_GROUP_BRAND_ID" jdbcType="BIGINT" property="orgHotelGroupBrandId"/>
        <result column="ORG_ID" jdbcType="BIGINT" property="orgId"/>
        <result column="HOTEL_GROUP_ID" jdbcType="BIGINT" property="hotelGroupId"/>
        <result column="BRAND_ID" jdbcType="BIGINT" property="brandId"/>
        <result column="HAS_PERMISSION" jdbcType="INTEGER" property="hasPermission"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORG_ID, HOTEL_GROUP_ID, BRAND_ID, HAS_PERMISSION, CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <update id="insertOrUpdate" parameterType="com.fangcang.rfp.common.entity.OrgHotelGroupBrand">
        <selectKey keyProperty="orgHotelGroupBrandId" resultType="_long" order="BEFORE">
            select htl_rfp.SEQ_ORG_RELATED_BRAND_ID.nextval from dual
        </selectKey>
        MERGE INTO htl_rfp.T_ORG_HOTEL_GROUP_BRAND dest
            USING (
                SELECT
                        #{orgId,jdbcType=BIGINT} AS orgId,
                        #{hotelGroupId,jdbcType=BIGINT} AS hotelGroupId,
                        #{brandId,jdbcType=BIGINT} AS brandId,
                        #{hasPermission,jdbcType=INTEGER} AS hasPermission,
                        #{creator,jdbcType=VARCHAR} AS creator,
                        #{modifier,jdbcType=VARCHAR} AS modifier
                FROM dual
            ) src
            ON (dest.ORG_ID = src.orgId AND dest.BRAND_ID = src.brandId)
            WHEN MATCHED THEN
                UPDATE SET
                    dest.HAS_PERMISSION = #{hasPermission,jdbcType=INTEGER},
                    dest.MODIFIER = #{modifier,jdbcType=VARCHAR},
                    dest.MODIFY_TIME = SYSDATE
            WHEN NOT MATCHED THEN
                INSERT (ORG_HOTEL_GROUP_BRAND_ID, ORG_ID, HOTEL_GROUP_ID, BRAND_ID, HAS_PERMISSION,
                        CREATOR, CREATE_TIME)
                    VALUES (#{orgHotelGroupBrandId}, src.orgId, src.hotelGroupId, src.brandId, src.hasPermission, src.creator, sysdate)
    </update>


    <select id="queryOrgHotelGroupBrand" resultType="com.fangcang.rfp.common.dto.OrgHotelGroupBrandDto"
            parameterType="com.fangcang.rfp.common.dto.OrgHotelGroupBrandDto">
        SELECT
            t1.ORG_ID AS orgId,
            t1.BRAND_ID AS brandId,
            t1.HOTEL_GROUP_ID AS hotelGroupId,
            t1.HAS_PERMISSION AS  hasPermission,
            t2.BRANDNAME AS brandName
        FROM htl_rfp.T_ORG_HOTEL_GROUP_BRAND t1
        INNER JOIN htl_info.T_BRAND t2 ON t1.brand_id = t2.BRANDID
        WHERE
            t2.GROUPID > 0
            <if test="orgId != null">
                AND t1.ORG_ID = #{orgId}
            </if>
            <if test="hotelGroupId != null">
                AND t1.HOTEL_GROUP_ID = #{hotelGroupId}
            </if>
            <if test="brandId != null">
                AND t1.BRAND_ID = #{brandId}
            </if>
            <if test="hasPermission != null">
                AND t1.HAS_PERMISSION = #{hasPermission}
            </if>
        ORDER BY t1.BRAND_ID ASC
    </select>

    <delete id="deleteByOrgId" parameterType="java.lang.Long">
        DELETE FROM htl_rfp.T_ORG_HOTEL_GROUP_BRAND WHERE ORG_ID = #{orgId,jdbcType=BIGINT}
    </delete>

    <select id="queryHotelGroupBrandIds" resultType="java.lang.Long">
        SELECT
            BRAND_ID
        FROM htl_rfp.T_ORG_HOTEL_GROUP_BRAND
        WHERE ORG_ID IN
        <foreach collection="orgIds" item="orgId" close=")" open="(" separator=",">
            #{orgId}
        </foreach>
        AND HAS_PERMISSION = 1
    </select>

    <select id="queryOrgHotelGroupBrandByBrandIdList" resultType="com.fangcang.rfp.common.dto.OrgHotelGroupBrandDto" >
        SELECT
            t1.ORG_ID AS orgId,
            t1.BRAND_ID AS brandId,
            t1.HOTEL_GROUP_ID AS hotelGroupId,
            t1.HAS_PERMISSION AS  hasPermission,
            t2.BRANDNAME AS brandName,
            o.ORG_NAME AS orgName,
            o.CONTACT_NAME AS contactName,
            o.CONTACT_MOBILE AS contactMobile,
            o.CONTACT_EMAIL AS contactEmail
        FROM htl_rfp.T_ORG_HOTEL_GROUP_BRAND t1
        INNER JOIN htl_info.T_BRAND t2 ON t1.brand_id = t2.BRANDID
        LEFT JOIN htl_rfp.T_ORG o ON t1.ORG_ID = o.ORG_ID
        WHERE
            t2.GROUPID > 0
            AND t1.BRAND_ID IN
            <foreach collection="brandIdList" item="brandId" close=")" open="(" separator=",">
                #{brandId}
            </foreach>
            AND t1.HAS_PERMISSION = 1
        ORDER BY t1.CREATE_TIME DESC
    </select>
</mapper>