<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectHotelWeightDao">

    <select id="queryByProjectHotelId" resultType="com.fangcang.rfp.common.entity.ProjectHotelWeight">
        SELECT
            PROJECT_ID AS projectId,
            HOTEL_ID AS hotelId,
            CATEGORY_CODE AS categoryCode,
            WEIGHT_TYPE AS weightType,
            SCORE AS score,
            SCORE_STANDARDS AS scoreStandards,
            CREATOR AS creator,
            CREATE_TIME AS createTime,
            MODIFIER AS modifier,
            MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_PROJECT_HOTEL_WEIGHT
        WHERE PROJECT_ID = #{projectId}
        AND HOTEL_ID = #{hotelId}
    </select>

    <insert id="batchInsertOrUpdate">
        MERGE INTO htl_rfp.T_PROJECT_HOTEL_WEIGHT T
        USING (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.projectId,jdbcType=BIGINT} AS projectId,
            #{item.hotelId,jdbcType=BIGINT} AS hotelId,
            #{item.categoryCode,jdbcType=VARCHAR} AS categoryCode,
            #{item.weightType,jdbcType=VARCHAR} AS weightType,
            #{item.score,jdbcType=DECIMAL} AS score,
            #{item.scoreStandards,jdbcType=VARCHAR} AS scoreStandards,
            #{item.creator} AS creator, #{item.modifier} AS modifier
            FROM dual
        </foreach>
        ) s
        ON (t.PROJECT_ID = s.projectId AND t.HOTEL_ID = s.hotelId)
        WHEN MATCHED THEN
        UPDATE SET
        t.SCORE = s.score,
        t.SCORE_STANDARDS = s.scoreStandards,
        t.MODIFIER = s.modifier,
        t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.PROJECT_ID,t.HOTEL_ID, t.CATEGORY_CODE,t.WEIGHT_TYPE,t.SCORE, t.SCORE_STANDARDS, t.CREATOR, t.CREATE_TIME)
        values(s.projectId, s.hotelId, s.categoryCode, s.weightType, s.score, s.scoreStandards, s.creator, sysdate)
    </insert>

    <delete id="deleteByProjectHotelId">
        DELETE FROM htl_rfp.T_PROJECT_HOTEL_WEIGHT
        WHERE PROJECT_ID = #{projectId}
          AND HOTEL_ID = #{hotelId}
    </delete>

</mapper>