<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.PriceApplicableRoomDao">
  <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.PriceApplicableRoom">
    <id column="APPLICABLE_ROOM_ID" jdbcType="DECIMAL" property="applicableRoomId" />
    <result column="PROJECT_INTENT_HOTEL_ID" jdbcType="DECIMAL" property="projectIntentHotelId" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="HOTEL_ID" jdbcType="DECIMAL" property="hotelId" />
    <result column="PRICE_CODE" jdbcType="DECIMAL" property="priceCode" />
    <result column="ROOM_TYPE_ID" jdbcType="DECIMAL" property="roomTypeId" />
    <result column="ROOM_LEVEL_NO" jdbcType="INTEGER" property="roomLevelNo" />
    <result column="DISPLAY_ORDER" jdbcType="INTEGER" property="displayOrder" />
    <result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    APPLICABLE_ROOM_ID, PROJECT_INTENT_HOTEL_ID, PROJECT_ID, HOTEL_ID, PRICE_CODE, ROOM_TYPE_ID,ROOM_LEVEL_NO,DISPLAY_ORDER,IS_DELETED,
    CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_PRICE_APPLICABLE_ROOM
    where APPLICABLE_ROOM_ID = #{applicableRoomId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_PRICE_APPLICABLE_ROOM
    where APPLICABLE_ROOM_ID = #{applicableRoomId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectHotelId">
    delete from T_PRICE_APPLICABLE_ROOM
    where PROJECT_ID = #{projectId,jdbcType=DECIMAL}
    AND HOTEL_ID = #{hotelId,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByPriceCode" parameterType="java.lang.Long">
    delete from T_PRICE_APPLICABLE_ROOM
    where PRICE_CODE = #{priceCode,jdbcType=DECIMAL}
  </delete>

  <delete id="deleteByProjectHotelRoomLevelNo">
    DELETE FROM T_PRICE_APPLICABLE_ROOM
    WHERE PROJECT_ID = #{projectId,jdbcType=BIGINT} AND HOTEL_ID = #{hotelId,jdbcType=BIGINT} AND ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
  </delete>

  <update id="updateDeleteByProjectIntentHotelId" parameterType="java.lang.Long">
    UPDATE T_PRICE_APPLICABLE_ROOM SET IS_DELETED = APPLICABLE_ROOM_ID
    WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
  </update>

  <insert id="insert" parameterType="com.fangcang.rfp.common.entity.PriceApplicableRoom">
    <selectKey keyProperty="applicableRoomId" resultType="_long" order="BEFORE">
      select htl_rfp.SEQ_RFP_APPLICABLE_ROOM_ID.nextval from dual
    </selectKey>
    insert into T_PRICE_APPLICABLE_ROOM (APPLICABLE_ROOM_ID, PROJECT_INTENT_HOTEL_ID, 
      PROJECT_ID, HOTEL_ID, PRICE_CODE, ROOM_LEVEL_NO, DISPLAY_ORDER,
      ROOM_TYPE_ID, CREATOR, CREATE_TIME,
      MODIFIER, MODIFY_TIME)
    values (#{applicableRoomId,jdbcType=DECIMAL}, #{projectIntentHotelId,jdbcType=DECIMAL}, 
      #{projectId,jdbcType=DECIMAL}, #{hotelId,jdbcType=DECIMAL}, #{priceCode,jdbcType=DECIMAL},
      #{roomLevelNo,jdbcType=INTEGER}, #{displayOrder,jdbcType=INTEGER},
      #{roomTypeId,jdbcType=DECIMAL}, #{creator,jdbcType=VARCHAR}, SYSDATE,
      #{modifier,jdbcType=VARCHAR}, SYSDATE)
  </insert>

  <insert id="insertBatch" parameterType="list">
    INSERT INTO htl_rfp.T_PRICE_APPLICABLE_ROOM (APPLICABLE_ROOM_ID, PROJECT_INTENT_HOTEL_ID,
    PROJECT_ID, HOTEL_ID, ROOM_LEVEL_NO, DISPLAY_ORDER,
    ROOM_TYPE_ID, CREATOR, CREATE_TIME,
    MODIFIER, MODIFY_TIME
    )
    SELECT htl_rfp.SEQ_RFP_APPLICABLE_ROOM_ID.nextval AS applicableRoomId, t.* FROM(
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT
      #{item.projectIntentHotelId,jdbcType=DECIMAL} AS projectIntentHotelId,
      #{item.projectId,jdbcType=DECIMAL} AS projectId,
      #{item.hotelId,jdbcType=DECIMAL} AS hotelId,
      #{item.roomLevelNo,jdbcType=INTEGER} AS roomLevelNo,
      #{item.displayOrder,jdbcType=INTEGER} AS displayOrder,
      #{item.roomTypeId,jdbcType=DECIMAL} AS roomTypeId,
      #{item.creator,jdbcType=VARCHAR} AS creator,
      SYSDATE AS createTime,
      #{item.modifier,jdbcType=VARCHAR} AS modifier,
      SYSDATE AS modifyTime
      FROM dual
    </foreach>) t
  </insert>

  <insert id="insertBatchWithId" parameterType="list">
    INSERT INTO htl_rfp.T_PRICE_APPLICABLE_ROOM (APPLICABLE_ROOM_ID, PROJECT_INTENT_HOTEL_ID,
    PROJECT_ID, HOTEL_ID, ROOM_LEVEL_NO, DISPLAY_ORDER,
    ROOM_TYPE_ID, CREATOR, CREATE_TIME,
    MODIFIER, MODIFY_TIME
    )
    SELECT t.* FROM(
    <foreach collection="list" item="item" index="index" separator="union all">
      SELECT
      #{item.applicableRoomId,jdbcType=DECIMAL} AS applicableRoomId,
      #{item.projectIntentHotelId,jdbcType=DECIMAL} AS projectIntentHotelId,
      #{item.projectId,jdbcType=DECIMAL} AS projectId,
      #{item.hotelId,jdbcType=DECIMAL} AS hotelId,
      #{item.roomLevelNo,jdbcType=INTEGER} AS roomLevelNo,
      #{item.displayOrder,jdbcType=INTEGER} AS displayOrder,
      #{item.roomTypeId,jdbcType=DECIMAL} AS roomTypeId,
      #{item.creator,jdbcType=VARCHAR} AS creator,
      SYSDATE AS createTime,
      #{item.modifier,jdbcType=VARCHAR} AS modifier,
      SYSDATE AS modifyTime
      FROM dual
    </foreach>) t
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.PriceApplicableRoom">
    update T_PRICE_APPLICABLE_ROOM
    <set>
      <if test="projectIntentHotelId != null">
        PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL},
      </if>
      <if test="projectId != null">
        PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      </if>
      <if test="hotelId != null">
        HOTEL_ID = #{hotelId,jdbcType=DECIMAL},
      </if>
      <if test="priceCode != null">
        PRICE_CODE = #{priceCode,jdbcType=DECIMAL},
      </if>
      <if test="roomLevelNo != null">
        ROOM_LEVEL_NO = #{roomLevelNo}
      </if>
      <if test="displayOrder != null">
        DISPLAY_ORDER = #{displayOrder}
      </if>
      <if test="roomTypeId != null">
        ROOM_TYPE_ID = #{roomTypeId,jdbcType=DECIMAL},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
        MODIFY_TIME = sysdate,
    </set>
    where APPLICABLE_ROOM_ID = #{applicableRoomId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcang.rfp.common.entity.PriceApplicableRoom">
    update T_PRICE_APPLICABLE_ROOM
    set PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=DECIMAL},
      PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      HOTEL_ID = #{hotelId,jdbcType=DECIMAL},
      PRICE_CODE = #{priceCode,jdbcType=DECIMAL},
      ROOM_TYPE_ID = #{roomTypeId,jdbcType=DECIMAL},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
    where APPLICABLE_ROOM_ID = #{applicableRoomId,jdbcType=DECIMAL}
  </update>


  <select id="selectOldPriceApplicableRoom" parameterType="com.fangcang.rfp.common.entity.PriceApplicableRoom" resultType="com.fangcang.rfp.common.dto.response.PriceApplicableRoomResponse">
    select p.ROOM_TYPE_ID roomTypeId,
           r.ROOMTYPENAME  roomtypename
    from HTL_RFP.T_PRICE_APPLICABLE_ROOM p
           left join HTL_INFO.T_ROOMTYPE r on p.ROOM_TYPE_ID = r.ROOMTYPEID
    where p.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId}
      and  p.PROJECT_ID = #{projectId}
      and p.HOTEL_ID = #{hotelId}
      and  p.PRICE_CODE = #{priceCode}
      and p.ROOM_LEVEL_NO IS NULL order by p.APPLICABLE_ROOM_ID
  </select>

  <select id="selectPriceApplicableRoom" parameterType="com.fangcang.rfp.common.entity.PriceApplicableRoom" resultType="com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse">
    SELECT
      p.APPLICABLE_ROOM_ID as applicableRoomId,
      p.PROJECT_INTENT_HOTEL_ID as projectIntentHotelId,
      p.PROJECT_ID as projectId,
      p.HOTEL_ID as hotelId,
      p.ROOM_TYPE_ID as roomTypeId,
      r.roomtypename as roomTypeName,
      p.ROOM_LEVEL_NO as roomLevelNo,
      p.DISPLAY_ORDER as displayOrder,
      p.IS_DELETED AS isDeleted,
      p.CREATOR as creator,
      p.CREATE_TIME as createTime,
      p.MODIFIER as modifier,
      p.MODIFY_TIME as modifyTime
    FROM HTL_RFP.T_PRICE_APPLICABLE_ROOM p
    LEFT JOIN HTL_INFO.T_ROOMTYPE r ON p.ROOM_TYPE_ID = r.ROOMTYPEID
    WHERE
        p.IS_DELETED = 0
    <if test="projectIntentHotelId != null">
      AND p.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </if>
    <if test="projectId != null">
      AND p.PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </if>
    <if test="hotelId != null">
      AND p.HOTEL_ID = #{hotelId,jdbcType=BIGINT}
    </if>
    <if test="roomLevelNo != null">
        AND p.ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
    </if>
        AND p.ROOM_LEVEL_NO IS NOT NULL
      ORDER BY p.ROOM_LEVEL_NO ASC, p.DISPLAY_ORDER ASC
  </select>


  <select id="queryPriceApplicableRoomInfoByProjectIntentHotelId" resultType="com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse">
    select
    p.APPLICABLE_ROOM_ID as applicableRoomId,
    p.PROJECT_INTENT_HOTEL_ID as projectIntentHotelId,
    p.PROJECT_ID as projectId,
    p.HOTEL_ID as hotelId,
    p.ROOM_TYPE_ID as roomTypeId,
    r.roomtypename as roomTypeName,
    p.ROOM_LEVEL_NO as roomLevelNo,
    p.DISPLAY_ORDER as displayOrder,
    p.IS_DELETED AS isDeleted,
    p.CREATOR as creator,
    p.CREATE_TIME as createTime,
    p.MODIFIER as modifier,
    p.MODIFY_TIME as modifyTime
    FROM htl_rfp.t_price_applicable_room p,htl_info.t_roomtype r
    WHERE p.room_type_id = r.roomtypeid
    <if test="projectIntentHotelId != null">
      AND p.PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
    </if>
    <if test="projectId != null">
      AND p.PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </if>
    <if test="roomLevelNo != null">
      AND p.ROOM_LEVEL_NO = #{roomLevelNo,jdbcType=INTEGER}
    </if>
      AND IS_DELETED = 0
      AND p.ROOM_LEVEL_NO IS NOT NULL
    ORDER BY ROOM_LEVEL_NO ASC, DISPLAY_ORDER ASC
  </select>

  <select id="selectPriceApplicableRoomByPriceCode" parameterType="long" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" />
        from HTL_RFP.T_PRICE_APPLICABLE_ROOM
        where PRICE_CODE = #{priceCode} order by APPLICABLE_ROOM_ID
  </select>

  <select id="selectAllPriceApplicableRoom" parameterType="long" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" />
    FROM HTL_RFP.T_PRICE_APPLICABLE_ROOM
    WHERE PROJECT_INTENT_HOTEL_ID = #{projectIntentHotelId,jdbcType=BIGINT}
  </select>

  <select id="selectNextApplicableRoomId" resultType="java.lang.Long">
    SELECT htl_rfp.SEQ_RFP_APPLICABLE_ROOM_ID.nextval FROM dual
  </select>

  <select id="queryPriceApplicableRoomInfoByPriceCode" resultType="com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse">
    select
    p.APPLICABLE_ROOM_ID as applicableRoomId, p.PROJECT_INTENT_HOTEL_ID as projectIntentHotelId, p.PROJECT_ID as projectId,
    p.HOTEL_ID as hotelId, p.PRICE_CODE as priceCode, p.ROOM_TYPE_ID as roomTypeId,
    p.CREATOR as creator, p.CREATE_TIME as createTime, p.MODIFIER as modifier, p.MODIFY_TIME as modifyTime, r.roomtypename as roomTypeName
    from htl_rfp.t_price_applicable_room p,htl_info.t_roomtype r
    where
    p.room_type_id = r.roomtypeid
    and p.price_code in
    <foreach collection="priceCodeSet" open="(" close=")" item="priceCode" separator="," index="index">
      #{priceCode}
    </foreach>
  </select>

  <select id="queryPriceApplicableRoomInfoByProjectIntentHotelIds" resultType="com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse">
    select
      p.APPLICABLE_ROOM_ID as applicableRoomId, p.PROJECT_INTENT_HOTEL_ID as projectIntentHotelId, p.PROJECT_ID as projectId,
      p.HOTEL_ID as hotelId, p.PRICE_CODE as priceCode, p.ROOM_TYPE_ID as roomTypeId,p.ROOM_LEVEL_NO as roomLevelNo,
      p.CREATOR as creator, p.CREATE_TIME as createTime, p.MODIFIER as modifier, p.MODIFY_TIME as modifyTime, r.roomtypename as roomTypeName
    from htl_rfp.t_price_applicable_room p,htl_info.t_roomtype r
    where
    p.room_type_id = r.roomtypeid
    and p.PROJECT_INTENT_HOTEL_ID in
    <foreach collection="projectIntentHotelIds" open="(" close=")" item="projectIntentHotelId" separator="," index="index">
      #{projectIntentHotelId}
    </foreach>
    AND IS_DELETED = 0
    AND p.ROOM_LEVEL_NO IS NOT NULL
    ORDER BY DISPLAY_ORDER ASC
  </select>
</mapper>