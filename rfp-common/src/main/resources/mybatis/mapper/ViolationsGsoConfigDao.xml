<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ViolationsGsoConfigDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.ViolationsGSOConfig">
        <result column="VIOLATIONS_GSO_CONFIG_ID" jdbcType="DECIMAL" property="violationsGSOConfigId"/>
        <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="HOTEL_GROUP_ORG_ID" jdbcType="DECIMAL" property="hotelGroupOrgId"/>
        <result column="RECEIVE_MSG_USER_NAME" jdbcType="VARCHAR" property="receiveMsgUserName"/>
        <result column="REMINDER_TYPE_ID" jdbcType="DECIMAL" property="reminderTypeId"/>
        <result column="IS_SEND_EMAIL" jdbcType="DECIMAL" property="isSendEmail"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="IS_SEND_SMS" jdbcType="DECIMAL" property="isSendSms"/>
        <result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted"/>
        <result column="MOBILE_NO" jdbcType="VARCHAR" property="mobileNo"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        VIOLATIONS_GSO_CONFIG_ID, PROJECT_ID, PROJECT_NAME,HOTEL_GROUP_ORG_ID, RECEIVE_MSG_USER_NAME,
      REMINDER_TYPE_ID, IS_SEND_EMAIL, EMAIL,IS_SEND_SMS, IS_DELETED, MOBILE_NO,
      CREATOR, CREATE_TIME, MODIFIER, MODIFY_TIME
    </sql>

    <select id="queryNextId" resultType="java.lang.Long">
        SELECT htl_rfp.SEQ_VIOLATIONS_GSO_CONFIG.nextval FROM dual
    </select>

    <insert id="insert" parameterType="com.fangcang.rfp.common.dto.request.AddViolationsGSOConfigDto">
        insert into htl_rfp.T_VIOLATIONS_GSO_CONFIG (VIOLATIONS_GSO_CONFIG_ID, PROJECT_ID, PROJECT_NAME,
        HOTEL_GROUP_ORG_ID, RECEIVE_MSG_USER_NAME,REMINDER_TYPE_ID,IS_SEND_EMAIL, EMAIL,
        IS_SEND_SMS, IS_DELETED, MOBILE_NO, CREATOR, CREATE_TIME, MODIFIER,MODIFY_TIME)
        values (#{violationsGSOConfigId,jdbcType=DECIMAL}, #{projectId,jdbcType=DECIMAL}, #{projectName,jdbcType=VARCHAR},
        #{hotelGroupOrgId,jdbcType=DECIMAL}, #{receiveMsgUserName,jdbcType=VARCHAR},
        #{reminderTypeId,jdbcType=DECIMAL}, #{isSendEmail,jdbcType=DECIMAL}, #{email,jdbcType=VARCHAR},
        #{isSendSms,jdbcType=DECIMAL}, #{isDeleted,jdbcType=DECIMAL}, #{mobileNo,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},  #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR},  #{modifyTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="delete">
        UPDATE htl_rfp.T_VIOLATIONS_GSO_CONFIG
        SET
            IS_DELETED = 1,
            MODIFY_TIME = sysdate,
            MODIFIER = #{modifier,jdbcType=VARCHAR}
        WHERE
            VIOLATIONS_GSO_CONFIG_ID = #{violationsGsoConfigId,jdbcType=DECIMAL}
    </update>

    <select id="queryViolationGsoConfigById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM htl_rfp.T_VIOLATIONS_GSO_CONFIG
        WHERE VIOLATIONS_GSO_CONFIG_ID = #{violationsGsoConfigId,jdbcType=DECIMAL}
    </select>

    <select id="queryHotelGroupViolationGsoConfig"
            resultType="com.fangcang.rfp.common.dto.response.QueryHotelGroupViolationGsoConfigResponse"
            parameterType="com.fangcang.rfp.common.dto.request.QueryHotelGroupViolationGsoConfigRequest">
        SELECT
            VIOLATIONS_GSO_CONFIG_ID as violationsGsoConfigId,
            PROJECT_ID as projectId,
            PROJECT_NAME as projectName,
            HOTEL_GROUP_ORG_ID as hotelGroupOrgId,
            RECEIVE_MSG_USER_NAME as receiveMsgUserName,
            REMINDER_TYPE_ID as reminderTypeId,
            IS_SEND_EMAIL as isSendEmail,
            EMAIL as email,
            IS_SEND_SMS as isSendSms,
            IS_DELETED as isDeleted,
            MOBILE_NO as mobileNo,
            CREATOR as creator,
            CREATE_TIME as createTime,
            MODIFIER as modifier,
            MODIFY_TIME as modifyTime
        FROM htl_rfp.T_VIOLATIONS_GSO_CONFIG
        WHERE
            IS_DELETED = 0
        <if test="projectId != null">
            AND PROJECT_ID = #{projectId}
        </if>
        <if test="hotelGroupOrgId != null">
            AND HOTEL_GROUP_ORG_ID = #{hotelGroupOrgId}
        </if>
        <if test="reminderTypeIdList != null and reminderTypeIdList.size() > 0">
            AND REMINDER_TYPE_ID IN
            <foreach collection="reminderTypeIdList" close=")" open="(" separator="," item="reminderTypeId">
                #{reminderTypeId}
            </foreach>
        </if>
        <if test="hotelGroupOrgIdList != null and hotelGroupOrgIdList.size() > 0">
            AND HOTEL_GROUP_ORG_ID IN
            <foreach collection="hotelGroupOrgIdList" close=")" open="(" separator="," item="orgId">
                #{orgId}
            </foreach>
        </if>
        <if test="violationsGSOConfigId != null">
            AND VIOLATIONS_GSO_CONFIG_ID = #{violationsGSOConfigId}
        </if>
        <if test="projectName != null and projectName !=''">
            AND PROJECT_NAME LIKE '%' || #{projectName} || '%'
        </if>
        <if test="userName != null and userName !=''">
            AND RECEIVE_MSG_USER_NAME LIKE '%' || #{userName} || '%'
        </if>
        order by CREATE_TIME desc
    </select>
    <update id="updateViolationsGsoConfig" parameterType="com.fangcang.rfp.common.dto.request.UpdateViolationsGSOConfigDto">
      UPDATE
          htl_rfp.T_VIOLATIONS_GSO_CONFIG
      SET
          RECEIVE_MSG_USER_NAME = #{receiveMsgUserName,jdbcType=VARCHAR},
          REMINDER_TYPE_ID = #{reminderTypeId,jdbcType=DECIMAL},
          IS_SEND_EMAIL = #{isSendEmail,jdbcType=DECIMAL},
          EMAIL = #{email,jdbcType=VARCHAR},
          IS_SEND_SMS = #{isSendSms, jdbcType=DECIMAL},
          MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
          MODIFIER = #{modifier,jdbcType=VARCHAR},
          MODIFY_TIME = sysDate
      WHERE VIOLATIONS_GSO_CONFIG_ID = #{violationsGsoConfigId,jdbcType=DECIMAL}
    </update>

</mapper>