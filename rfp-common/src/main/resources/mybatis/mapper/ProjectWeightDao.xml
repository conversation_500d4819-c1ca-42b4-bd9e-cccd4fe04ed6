<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectWeightDao">

    <select id="queryByProjectId" resultType="com.fangcang.rfp.common.dto.ProjectWeightVO">
        SELECT
            PROJECT_ID AS projectId,
            CATEGORY_CODE AS categoryCode,
            WEIGHT_TYPE AS weightType,
            MAX_WEIGHT AS maxWeight,
            MIN_WEIGHT AS minWeight,
            SCORE_STANDARDS AS scoreStandards,
            DISPLAY_ORDER AS displayOrder,
            IS_ENABLED AS isEnabled,
            CREATOR AS creator,
            CREATE_TIME AS createTime,
            MODIFIER AS modifier,
            MODIFY_TIME AS modifyTime
        FROM htl_rfp.T_PROJECT_WEIGHT
        WHERE PROJECT_ID = #{projectId}
    </select>

    <insert id="batchInsertOrUpdate">
        MERGE INTO htl_rfp.T_PROJECT_WEIGHT T
        USING (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT
            #{item.projectId,jdbcType=BIGINT} AS projectId,
            #{item.categoryCode,jdbcType=VARCHAR} AS categoryCode,
            #{item.weightType,jdbcType=VARCHAR} AS weightType,
            #{item.maxWeight,jdbcType=DECIMAL} AS maxWeight,
            #{item.minWeight,jdbcType=DECIMAL} AS minWeight,
            #{item.scoreStandards,jdbcType=VARCHAR} AS scoreStandards,
            #{item.displayOrder,jdbcType=INTEGER} AS displayOrder,
            #{item.isEnabled,jdbcType=INTEGER} AS isEnabled,
            #{item.creator} AS creator, #{item.modifier} AS modifier
            FROM dual
        </foreach>
        ) s
        ON (t.PROJECT_ID = s.projectId AND t.WEIGHT_TYPE = s.weightType)
        WHEN MATCHED THEN
        UPDATE SET
        t.SCORE_STANDARDS = s.scoreStandards,
        t.MAX_WEIGHT = s.maxWeight,
        t.MIN_WEIGHT = s.minWeight,
        t.DISPLAY_ORDER = s.displayOrder,
        t.IS_ENABLED = s.isEnabled,
        t.MODIFIER = s.modifier,
        t.modify_time = sysdate
        WHEN NOT MATCHED THEN
        INSERT (t.PROJECT_ID, t.CATEGORY_CODE,t.WEIGHT_TYPE,t.MAX_WEIGHT,t.MIN_WEIGHT,t.SCORE_STANDARDS, t.DISPLAY_ORDER, t.IS_ENABLED, t.CREATOR, t.CREATE_TIME)
        values(s.projectId, s.categoryCode, s.weightType, s.maxWeight, s.minWeight, s.scoreStandards, s.displayOrder,  s.isEnabled, s.creator, sysdate)
    </insert>

</mapper>
