<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcang.rfp.common.dao.ProjectDao">
    <resultMap id="BaseResultMap" type="com.fangcang.rfp.common.entity.Project">
        <id column="PROJECT_ID" jdbcType="BIGINT" property="projectId"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="PROJECT_TYPE" jdbcType="INTEGER" property="projectType"/>
        <result column="TENDER_ORG_ID" jdbcType="BIGINT" property="tenderOrgId"/>
        <result column="TENDER_TYPE" jdbcType="INTEGER" property="tenderType"/>
        <result column="CONTACT_NAME" jdbcType="VARCHAR" property="contactName"/>
        <result column="CONTACT_MOBILE" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="ENROLL_START_TIME" jdbcType="TIMESTAMP" property="enrollStartTime"/>
        <result column="ENROLL_END_TIME" jdbcType="TIMESTAMP" property="enrollEndTime"/>
        <result column="FIRST_BID_START_TIME" jdbcType="TIMESTAMP" property="firstBidStartTime"/>
        <result column="FIRST_BID_END_TIME" jdbcType="TIMESTAMP" property="firstBidEndTime"/>
        <result column="SECOND_BID_START_TIME" jdbcType="TIMESTAMP" property="secondBidStartTime"/>
        <result column="SECOND_BID_END_TIME" jdbcType="TIMESTAMP" property="secondBidEndTime"/>
        <result column="THIRD_BID_START_TIME" jdbcType="TIMESTAMP" property="thirdBidStartTime"/>
        <result column="THIRD_BID_END_TIME" jdbcType="TIMESTAMP" property="thirdBidEndTime"/>
        <result column="TENDER_COUNT" jdbcType="INTEGER" property="tenderCount"/>
        <result column="DIFF_MIN_AMOUNT" jdbcType="DECIMAL" property="diffMinAmount"/>
        <result column="DIFF_MAX_AMOUNT" jdbcType="DECIMAL" property="diffMaxAmount"/>
        <result column="BUDGET_TOTAL_AMOUNT" jdbcType="DECIMAL" property="budgetTotalAmount"/>
        <result column="CONTRACT_TEMPLATE_TYPE" jdbcType="INTEGER" property="contractTemplateType"/>
        <result column="TEMPLATE_ID" jdbcType="BIGINT" property="templateId"/>
        <result column="CO_PAYER_TYPE" jdbcType="INTEGER" property="coPayerType"/>
        <result column="CO_PAYER_ORG_ID" jdbcType="BIGINT" property="coPayerOrgId"/>
        <result column="PROJECT_STATE" jdbcType="INTEGER" property="projectState"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFIER" jdbcType="VARCHAR" property="modifier"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="INTRODUCTION" jdbcType="CLOB" property="introduction"/>
        <result column="PRICE_MONITOR_START_DATE" jdbcType="TIMESTAMP" property="priceMonitorStartDate"/>
        <result column="PRICE_MONITOR_END_DATE" jdbcType="TIMESTAMP" property="priceMonitorEndDate"/>
        <result column="NEED_ONLINE_CONTRACTS" jdbcType="INTEGER" property="needOnlineContracts"/>
        <result column="RELATED_PROJECT_ID" jdbcType="BIGINT" property="relatedProjectId"/>
        <result column="DISPLAY_ORDER" jdbcType="INTEGER" property="displayOrder"/>
        <result column="IS_AUTO_CONFIG_GO_ONLINE" jdbcType="INTEGER" property="isAutoConfigGoOnline"/>
        <result column="ONLINE_DISTRIBUTOR_CODE" jdbcType="VARCHAR" property="onlineDistributorCode"/>
        <result column="ONLINE_RECEIVE_ORDER_METHOD" jdbcType="INTEGER" property="onlineReceiveOrderMethod"/>
        <result column="ONLINE_INVOICE_SET" jdbcType="INTEGER" property="onlineInvoiceSet"/>
        <result column="ONLINE_SERVICE_FEE_SET" jdbcType="INTEGER" property="onlineServiceFeeSet"/>
        <result column="ONLINE_SERVICE_FEE_TYPE" jdbcType="INTEGER" property="onlineServiceFeeType"/>
        <result column="ONLINE_SERVICE_FEE" jdbcType="DECIMAL" property="onlineServiceFee"/>
        <result column="ONLINE_HOTEL_BID_SET" jdbcType="INTEGER" property="onlineHotelBidSet"/>
        <result column="BID_STATE_UPDATED_NOTIFY_MODE" jdbcType="INTEGER" property="bidStateUpdatedNotifyMode"/>
        <result column="TOTAL_WEIGHT" jdbcType="DECIMAL" property="totalWeight"/>
    </resultMap>

    <sql id="Base_Column_List">
    PROJECT_ID, PROJECT_NAME, PROJECT_TYPE, TENDER_ORG_ID, TENDER_TYPE, CONTACT_NAME, 
    CONTACT_MOBILE, ENROLL_START_TIME, ENROLL_END_TIME, FIRST_BID_START_TIME, FIRST_BID_END_TIME, 
    SECOND_BID_START_TIME, SECOND_BID_END_TIME, THIRD_BID_START_TIME, THIRD_BID_END_TIME, 
    TENDER_COUNT, DIFF_MIN_AMOUNT, DIFF_MAX_AMOUNT, BUDGET_TOTAL_AMOUNT, CONTRACT_TEMPLATE_TYPE, 
    TEMPLATE_ID, CO_PAYER_TYPE, CO_PAYER_ORG_ID, PROJECT_STATE, INTRODUCTION, CREATOR, CREATE_TIME,
    MODIFIER, MODIFY_TIME,PRICE_MONITOR_START_DATE,PRICE_MONITOR_END_DATE,NEED_ONLINE_CONTRACTS, RELATED_PROJECT_ID,DISPLAY_ORDER,
    IS_AUTO_CONFIG_GO_ONLINE,ONLINE_DISTRIBUTOR_CODE,ONLINE_RECEIVE_ORDER_METHOD,ONLINE_INVOICE_SET,ONLINE_SERVICE_FEE_SET,ONLINE_SERVICE_FEE_TYPE,
    ONLINE_SERVICE_FEE,ONLINE_HOTEL_BID_SET,BID_STATE_UPDATED_NOTIFY_MODE,TOTAL_WEIGHT
  </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="selectByProjectIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM htl_rfp.T_PROJECT
        WHERE PROJECT_ID IN
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
    </select>

    <select id="selectRelatedProjectInfo" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        r.PROJECT_ID,
        r.PROJECT_NAME
        FROM
        T_PROJECT r
        WHERE
        r.PROJECT_ID = ( SELECT t.RELATED_PROJECT_ID FROM T_PROJECT t WHERE t.PROJECT_ID = #{projectId,jdbcType=BIGINT} )
    </select>

    <insert id="insertProject" parameterType="com.fangcang.rfp.common.entity.Project" keyProperty="projectId"
            useGeneratedKeys="true">

        <selectKey keyProperty="projectId" resultType="_long" order="BEFORE">
            select htl_rfp.seq_rfp_project_id.nextval from dual
        </selectKey>

        insert into htl_rfp.T_PROJECT (PROJECT_ID, PROJECT_NAME, PROJECT_TYPE,
        TENDER_ORG_ID, TENDER_TYPE, CONTACT_NAME,
        CONTACT_MOBILE, ENROLL_START_TIME, ENROLL_END_TIME,
        FIRST_BID_START_TIME, FIRST_BID_END_TIME,
        SECOND_BID_START_TIME, SECOND_BID_END_TIME,
        THIRD_BID_START_TIME, THIRD_BID_END_TIME,
        TENDER_COUNT, DIFF_MIN_AMOUNT, DIFF_MAX_AMOUNT,
        BUDGET_TOTAL_AMOUNT, CONTRACT_TEMPLATE_TYPE,
        TEMPLATE_ID, CO_PAYER_TYPE, CO_PAYER_ORG_ID,
        PROJECT_STATE, CREATOR, CREATE_TIME,
        MODIFIER, MODIFY_TIME,
        PRICE_MONITOR_START_DATE,PRICE_MONITOR_END_DATE,NEED_ONLINE_CONTRACTS,DISPLAY_ORDER,
        IS_AUTO_CONFIG_GO_ONLINE,ONLINE_DISTRIBUTOR_CODE,ONLINE_RECEIVE_ORDER_METHOD,ONLINE_INVOICE_SET,
        ONLINE_SERVICE_FEE_SET,ONLINE_SERVICE_FEE_TYPE, ONLINE_SERVICE_FEE,ONLINE_HOTEL_BID_SET,BID_STATE_UPDATED_NOTIFY_MODE
        )
        values (#{projectId}, #{projectName,jdbcType=VARCHAR}, #{projectType,jdbcType=INTEGER},
        #{tenderOrgId,jdbcType=BIGINT}, #{tenderType,jdbcType=INTEGER}, #{contactName,jdbcType=VARCHAR},
        #{contactMobile,jdbcType=VARCHAR}, #{enrollStartTime,jdbcType=TIMESTAMP}, #{enrollEndTime,jdbcType=TIMESTAMP},
        #{firstBidStartTime,jdbcType=TIMESTAMP}, #{firstBidEndTime,jdbcType=TIMESTAMP},
        #{secondBidStartTime,jdbcType=TIMESTAMP}, #{secondBidEndTime,jdbcType=TIMESTAMP},
        #{thirdBidStartTime,jdbcType=TIMESTAMP}, #{thirdBidEndTime,jdbcType=TIMESTAMP},
        #{tenderCount,jdbcType=INTEGER}, #{diffMinAmount,jdbcType=DECIMAL}, #{diffMaxAmount,jdbcType=DECIMAL},
        #{budgetTotalAmount,jdbcType=DECIMAL}, #{contractTemplateType,jdbcType=INTEGER},
        #{templateId,jdbcType=BIGINT}, #{coPayerType,jdbcType=INTEGER}, #{coPayerOrgId,jdbcType=BIGINT},
        0, #{creator,jdbcType=VARCHAR}, sysdate,
        #{modifier,jdbcType=VARCHAR}, sysdate,
        #{priceMonitorStartDate,jdbcType=TIMESTAMP},#{priceMonitorEndDate,jdbcType=TIMESTAMP},#{needOnlineContracts,jdbcType=INTEGER},
        #{displayOrder,jdbcType=INTEGER},
        #{isAutoConfigGoOnline,jdbcType=INTEGER},
        #{onlineDistributorCode,jdbcType=VARCHAR},
        #{onlineReceiveOrderMethod,jdbcType=INTEGER},
        #{onlineInvoiceSet,jdbcType=INTEGER},
        #{onlineServiceFeeSet,jdbcType=INTEGER},
        #{onlineServiceFeeType,jdbcType=INTEGER},
        #{onlineServiceFee,jdbcType=DECIMAL},
        #{onlineHotelBidSet,jdbcType=INTEGER},
        #{bidStateUpdatedNotifyMode,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.fangcang.rfp.common.entity.Project">
        update htl_rfp.T_PROJECT
        <set>
            <if test="projectName != null">
                PROJECT_NAME = #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="projectType != null">
                PROJECT_TYPE = #{projectType,jdbcType=INTEGER},
            </if>
            <if test="tenderOrgId != null">
                TENDER_ORG_ID = #{tenderOrgId,jdbcType=BIGINT},
            </if>
            <if test="tenderType != null">
                TENDER_TYPE = #{tenderType,jdbcType=INTEGER},
            </if>
            <if test="contactName != null">
                CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null">
                CONTACT_MOBILE = #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="enrollStartTime != null">
                ENROLL_START_TIME = #{enrollStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="enrollEndTime != null">
                ENROLL_END_TIME = #{enrollEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="firstBidStartTime != null">
                FIRST_BID_START_TIME = #{firstBidStartTime,jdbcType=TIMESTAMP},
            </if>
            FIRST_BID_END_TIME = #{firstBidEndTime,jdbcType=TIMESTAMP},
            SECOND_BID_START_TIME = #{secondBidStartTime,jdbcType=TIMESTAMP},
            SECOND_BID_END_TIME = #{secondBidEndTime,jdbcType=TIMESTAMP},
            THIRD_BID_START_TIME = #{thirdBidStartTime,jdbcType=TIMESTAMP},
            THIRD_BID_END_TIME = #{thirdBidEndTime,jdbcType=TIMESTAMP},
            <if test="bidStateUpdatedNotifyMode != null">
                BID_STATE_UPDATED_NOTIFY_MODE =  #{bidStateUpdatedNotifyMode,jdbcType=INTEGER},
            </if>
            <if test="tenderCount != null">
                TENDER_COUNT = #{tenderCount,jdbcType=INTEGER},
            </if>
            <if test="diffMinAmount != null">
                DIFF_MIN_AMOUNT = #{diffMinAmount,jdbcType=DECIMAL},
            </if>
            <if test="diffMaxAmount != null">
                DIFF_MAX_AMOUNT = #{diffMaxAmount,jdbcType=DECIMAL},
            </if>
            BUDGET_TOTAL_AMOUNT = #{budgetTotalAmount,jdbcType=DECIMAL},
            <if test="contractTemplateType != null">
                CONTRACT_TEMPLATE_TYPE = #{contractTemplateType,jdbcType=INTEGER},
            </if>
            <if test="templateId != null">
            TEMPLATE_ID = #{templateId,jdbcType=BIGINT},
            </if>
            <if test="coPayerType != null">
                CO_PAYER_TYPE = #{coPayerType,jdbcType=INTEGER},
            </if>
            CO_PAYER_ORG_ID = #{coPayerOrgId,jdbcType=BIGINT},
            <if test="modifier != null">
                MODIFIER = #{modifier,jdbcType=VARCHAR},
            </if>
            <if test="priceMonitorStartDate != null ">
                PRICE_MONITOR_START_DATE = #{priceMonitorStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="priceMonitorEndDate != null ">
                PRICE_MONITOR_END_DATE = #{priceMonitorEndDate,jdbcType=TIMESTAMP},
            </if>
            DISPLAY_ORDER = #{displayOrder,jdbcType=INTEGER},
            IS_AUTO_CONFIG_GO_ONLINE = #{isAutoConfigGoOnline,jdbcType=INTEGER},
            ONLINE_DISTRIBUTOR_CODE = #{onlineDistributorCode,jdbcType=VARCHAR},
            ONLINE_RECEIVE_ORDER_METHOD = #{onlineReceiveOrderMethod,jdbcType=INTEGER},
            ONLINE_INVOICE_SET = #{onlineInvoiceSet,jdbcType=INTEGER},
            ONLINE_SERVICE_FEE_SET = #{onlineServiceFeeSet,jdbcType=INTEGER},
            ONLINE_SERVICE_FEE_TYPE = #{onlineServiceFeeType,jdbcType=INTEGER},
            ONLINE_SERVICE_FEE = #{onlineServiceFee,jdbcType=DECIMAL},
            ONLINE_HOTEL_BID_SET = #{onlineHotelBidSet,jdbcType=INTEGER},
            MODIFY_TIME = sysdate,
            NEED_ONLINE_CONTRACTS=#{needOnlineContracts,jdbcType=INTEGER},
        </set>
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </update>

    <update id="updateIntroductionByProjectId" parameterType="com.fangcang.rfp.common.entity.Project">
        update htl_rfp.T_PROJECT set introduction = #{introduction,jdbcType=CLOB} , modify_time = sysdate
        <if test="modifier != null">
            ,MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </update>

    <select id="selectProjectBasicInformation" parameterType="java.lang.Long"
            resultType="com.fangcang.rfp.common.dto.response.ProjectBasicInfoResponse">
        select p.project_id as projectId, p.project_name as projectName, p.project_type as projectType, p.tender_org_id as tenderOrgId,
        p.tender_type as tenderType, p.contact_name as contactName,
    p.contact_mobile as contactMobile, p.enroll_start_time as enrollStartTime, p.enroll_end_time as enrollEndTime,
    p.first_bid_start_time as firstBidStartTime, p.first_bid_end_time as firstBidEndTime,
    p.second_bid_start_time as secondBidStartTime, p.second_bid_end_time as secondBidEndTime, p.third_bid_start_time as thirdBidStartTime, p.third_bid_end_time as thirdBidEndTime,
    p.tender_count as tenderCount, p.diff_min_amount as diffMinAmount, p.diff_max_amount as diffMaxAmount, p.budget_total_amount as budgetTotalAmount, p.contract_template_type as contractTemplateType,
    p.template_id as templateId, p.co_payer_type as coPayerType, p.co_payer_org_id as coPayerOrgId, p.project_state as projectState,o.org_name as orgName,
    p.PRICE_MONITOR_START_DATE priceMonitorStartDate,p.PRICE_MONITOR_END_DATE priceMonitorEndDate,p.NEED_ONLINE_CONTRACTS as needOnlineContracts,
    p.DISPLAY_ORDER as displayOrder,
    p.IS_AUTO_CONFIG_GO_ONLINE AS isAutoConfigGoOnline,
    p.ONLINE_DISTRIBUTOR_CODE AS onlineDistributorCode,
    p.ONLINE_RECEIVE_ORDER_METHOD AS onlineReceiveOrderMethod,
    p.ONLINE_INVOICE_SET AS onlineInvoiceSet,
    p.ONLINE_SERVICE_FEE_SET AS onlineServiceFeeSet,
    p.ONLINE_SERVICE_FEE_TYPE AS onlineServiceFeeType,
    p.ONLINE_SERVICE_FEE AS onlineServiceFee,
    p.ONLINE_HOTEL_BID_SET AS  onlineHotelBidSet,
    p.BID_STATE_UPDATED_NOTIFY_MODE AS bidStateUpdatedNotifyMode,
    p.TOTAL_WEIGHT AS totalWeight
    from htl_rfp.t_project p , htl_rfp.t_org o where p.tender_org_id = o.org_id and p.project_id = #{projectId}
    </select>

    <select id="selectIntroductionByProjectId" parameterType="java.lang.Long" resultType="java.lang.String">
        select introduction from  htl_rfp.t_project  where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </select>

    <select id="enterpriseAndPlatformQueryProjectInfo"
            parameterType="com.fangcang.rfp.common.dto.request.ProjectManagementQueryRequest"
            resultType="com.fangcang.rfp.common.dto.response.ProjectManagementQueryResponse">
        select
        p.project_id as projectId,
        p.project_name as projectName,
        o.org_name as orgName,
        p.tender_type as tenderType,
        p.project_type as projectType,
        p.tender_count as tenderCount,
        p.diff_min_amount as diffMinAmount,
        p.diff_max_amount as diffMaxAmount,
        p.enroll_start_time as enrollStartTime,
        p.enroll_end_time as enrollEndTime,
        p.first_bid_start_time as firstBidStartTime,
        p.first_bid_end_time as firstBidEndTime,
        p.second_bid_start_time as secondBidStartTime,
        p.second_bid_end_time as secondBidEndTime,
        p.third_bid_start_time as thirdBidStartTime,
        p.third_bid_end_time as thirdBidEndTime,
        p.display_order as displayOrder,
        p.creator as creator,
        p.create_time as createTime,
        cn.cityName as cityName
        from htl_rfp.t_project p
        left join htl_rfp.t_org o on p.tender_org_id=o.org_id
        left join (select pi.project_id, listagg(oi.city_name, '、') within
        group(order by oi.city_name) cityName from htl_rfp.t_project_poi pi ,htl_rfp.t_org_poi oi where
        pi.poi_id=oi.poi_id group by pi.project_id) cn on p.project_id = cn.project_id
        where p.project_state = #{projectState}
        <if test="orgName != null and orgName !=''">
            and o.org_name like concat(concat('%',#{orgName}),'%')
        </if>
        <if test="projectName != null and projectName !=''">
            and p.project_name like concat(concat('%',#{projectName}),'%')
        </if>
        <if test="createName != null and createName !=''">
            and p.creator like concat(concat('%',#{createName}),'%')
        </if>
        <if test="orgType == 3">
            and p.tender_org_id = #{userOrgId}
            <!--<if test="roleCodeType != null and roleCodeType != 'ADMIN'">-->
                <!--and (select count(1) from htl_rfp.t_project_intent_hotel ph where p.project_id =ph.project_id and-->
                <!--ph.distributor_contact_uid IN-->
                <!--<foreach collection="userIds" open="(" close=")" item="userId" separator="," index="index">-->
                <!--#{userId}-->
                <!--</foreach> )>0-->
            <!--</if>-->
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size > 0">
            AND p.tender_org_id IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" separator="," open="(" close=")">
                #{userRelatedOrgId}
            </foreach>
        </if>
        order by p.display_order desc NULLS LAST, p.create_time desc
    </select>

    <update id="updateProjectState" parameterType="com.fangcang.rfp.common.dto.request.UpdateProjectStateDto">
        update htl_rfp.T_PROJECT set project_state = #{projectState} , modify_time = sysdate
        <if test="operator != null">
            ,MODIFIER = #{operator,jdbcType=VARCHAR}
        </if>
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </update>

    <select id="selectProjectInfoByOrgIdAndMonitorTime" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT
        where TENDER_ORG_ID = #{tenderOrgId,jdbcType=BIGINT} and to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd') between PRICE_MONITOR_START_DATE and PRICE_MONITOR_END_DATE
    </select>

    <select id="selectProjectInfoByOrgId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT
        where TENDER_ORG_ID = #{tenderOrgId,jdbcType=BIGINT} and PROJECT_STATE != 3 order by CREATE_TIME desc
    </select>

    <update id="bindHistoryProject" parameterType="com.fangcang.rfp.common.dto.request.BindHistoryProjectRequest">
        update htl_rfp.T_PROJECT set RELATED_PROJECT_ID = #{relatedProjectId} ,
                                     modify_time = sysdate
        <if test="operator != null">
            ,MODIFIER = #{operator,jdbcType=VARCHAR}
        </if>
        where PROJECT_ID = #{projectId,jdbcType=BIGINT}
    </update>

    <select id="queryHistoryProjectInfo" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from htl_rfp.T_PROJECT
        where TENDER_ORG_ID = #{tenderOrgId,jdbcType=BIGINT} and PROJECT_STATE in (1,2)
        and PROJECT_ID != #{projectId,jdbcType=BIGINT}
        order by PROJECT_NAME desc
    </select>

    <select id="queryProjectName" resultType="com.fangcang.rfp.common.entity.Project" parameterType="com.fangcang.rfp.common.dto.request.QueryProjectNameRequest">
        select projectId,projectName
        from (select
                    PROJECT_ID  as projectId,
                    PROJECT_NAME projectName
                from htl_rfp.T_PROJECT
        where  project_state = 1
        <if test="orgId != null">
            and TENDER_ORG_ID = #{orgId}
        </if>
        <if test="queryName != null and queryName != '' ">
            and PROJECT_NAME like CONCAT(CONCAT('%',#{queryName}),'%')
            order by UTL_MATCH.edit_distance_similarity(LTRIM(rTRIM(#{queryName}))  ,PROJECT_NAME) desc
        </if>
        <if test="userRelatedOrgIdList != null and userRelatedOrgIdList.size() > 0">
            AND TENDER_ORG_ID IN
            <foreach collection="userRelatedOrgIdList" item="userRelatedOrgId" separator="," open="(" close=")">
                #{userRelatedOrgId}
            </foreach>
        </if>
        ) temp  where  rownum &lt;= 10
    </select>

    <update id="updateProjectWeight" parameterType="com.fangcang.rfp.common.entity.Project">
        update htl_rfp.T_PROJECT set TOTAL_WEIGHT = #{totalWeight},
            MODIFY_TIME = sysdate,
            MODIFIER = #{modifier}
        where PROJECT_ID = #{projectId}
    </update>
</mapper>
