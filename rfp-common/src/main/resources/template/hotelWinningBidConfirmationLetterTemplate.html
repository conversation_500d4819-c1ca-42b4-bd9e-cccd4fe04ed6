<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>酒店中签确认函</title>
    <style>
        body {
            font-family: 'SimSun', "宋体", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 40px auto;
            padding: 30px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #005695;
            padding-bottom: 20px;
        }
        .title {
            font-size: 28px;
            color: #005695;
            margin-bottom: 10px;
        }
        .number {
            font-size: 16px;
            color: #666;
        }
        .content {
            line-height: 1.8;
        }
        .section-title {
            background-color: #f5f5f5;
            border-left: 4px solid #005695;
            padding: 10px 15px;
            margin: 20px 0 10px;
            font-weight: bold;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
        }
        .row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .signature {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
        }
        .signature-item {
            text-align: center;
            margin: 0 20px;
        }
        .line {
            width: 200px;
            height: 1px;
            background-color: #000;
            margin: 10px 0;
        }
        .date {
            margin-top: 10px;
            font-size: 14px;
        }
        .highlight {
            color: #005695;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <div class="title">酒店中签确认函</div>
        <div class="number">编号：<span th:text="${contractNo}"></span></DIV>
    </div>

    <div class="content">
        <p>致：<span th:text="${hotelName}"></span></p>
        <p>根据双方协商，现就<span th:text="${projectName}"></span>项目达成以下协议，特此确认：</p>

        <div class="section-title">一、协议基本信息</div>
        <table>
            <tr>
                <th>签约项目名称</th>
                <td th:text="${projectName}"></td>
            </tr>
            <tr>
                <th>报价酒店名称</th>
                <td th:text="${hotelName}"></td>
            </tr>
            <tr>
                <th>报价日期</th>
                <td th:text="${bidDate}"></td>
            </tr>
            <tr>
                <th>协议价有效期</th>
                <td th:text="${validPeriod}"></td>
            </tr>
            <tr>
                <th>Season1价有效期</th>
                <td th:text="${season1ValidPeriod}"></td>
            </tr>
            <tr>
                <th>Season2价有效期</th>
                <td th:text="${season2ValidPeriod}"></td>
            </tr>
        </table>

        <div class="section-title">二、产品不适用日期</div>
        <table>
            <tr>
                <td th:text="${unapplicableDay1}">1</td>
                <td th:text="${unapplicableDay1}">2</td>
                <td th:text="${unapplicableDay1}">2</td>
            </tr>
            <tr>
                <td th:text="${unapplicableDay1}"></td>
                <td th:text="${unapplicableDay1}"></td>
                <td th:text="${unapplicableDay1}"></td>
            </tr>
            <tr>
                <td th:text="${unapplicableDay1}"></td>
                <td th:text="${unapplicableDay1}"></td>
                <td th:text="${unapplicableDay1}"></td>
            </tr>
            <tr>
                <td th:text="${unapplicableDay10}"></td>
                <td colspan="2"></td>
            </tr>
        </table>

        <div class="section-title">三、房型报价明细</div>
        
        <div th:each="roomType : ${roomTypes}">
            <p><strong>房档<span th:text="${roomType.roomTypeIndex}">一</span>: <span th:text="${roomType.name}">Standard1(未绑定)、Standard2(未绑定)、Standard3(未绑定)</span></strong></p>
            <table>
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>基础协议价</th>
                        <th>Season1价</th>
                        <th>Season2价</th>
                        <th>适用星期</th>
                        <th>退改条款</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="rate : ${roomType.rates}">
                        <td th:text="${rate.type}">单早</td>
                        <td th:text="${rate.basePrice}">957.78</td>
                        <td th:text="${rate.season1Price}">418.56</td>
                        <td th:text="${rate.season2Price}">627.84</td>
                        <td th:text="${rate.weekDays}">二,三,四,五,六,日</td>
                        <td th:text="${rate.cancelPolicy}">提前0天18:00点之前可免费退改，之后收取首晚房费的退订费</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section-title">四、酒店服务承诺</div>
        <table>
            <tr>
                <th>支持员工到店付</th>
                <td th:text="${supportPayAtHotel}"></td>
            </tr>
            <tr>
                <th>支持公司统一支付</th>
                <td th:text="${supportCoPay}"></td>
            </tr>
            <tr>
                <th>到店付免担保</th>
                <td th:text="${supportNoGuarantee}"></td>
            </tr>
            <tr>
                <th>酒店须支持提供入住明细信息</th>
                <td th:text="${supportCheckinInfo}"></td>
            </tr>
            <tr>
                <th>酒店须支持提前离店按实际入住金额收款</th>
                <td th:text="${supportPayEarlyCheckout}"></td>
            </tr>
            <tr>
                <th>酒店报价是否包含税费和服务费</th>
                <td th:text="${supportIncludeTaxService}"></td>
            </tr>
            <tr>
                <th>酒店是否支持wifi</th>
                <td th:text="${supportWifi}"></td>
            </tr>
        </table>

        <div class="section-title">五、入离政策</div>
        <table>
            <tr>
                <th>最早入住时间</th>
                <td th:text="${earlyCheckinTime}">无</td>
            </tr>
            <tr>
                <th>最晚退房时间</th>
                <td th:text="${lateCheckoutTime}">无</td>
            </tr>
            <tr>
                <th>到店付免担保最晚保留时间</th>
                <td th:text="${lateCheckoutTime}">无</td>
            </tr>
            <tr>
                <th>超出最晚保留时间情况</th>
                <td th:text="${lateCheckoutTime}">无</td>
            </tr>
        </table>

        <div class="section-title">六、结算信息</div>
        <table>
            <tr>
                <th>是否支持月结</th>
                <td th:text="${settlement1}">1</td>
            </tr>
            <tr>
                <th>所有报价是否含佣金</th>
                <td th:text="${settlement2}">2</td>
            </tr>
            <tr>
                <th>所有报价佣金率</th>
                <td th:text="${settlement3}">3</td>
            </tr>
            <tr>
                <th>发票类型</th>
                <td th:text="${settlement4}">4</td>
            </tr>
        </table>

        <div class="section-title">七、联系人信息</div>
        <table>
            <tr>
                <th>联系人</th>
                <td th:text="${contactName}"></td>
            </tr>
            <tr>
                <th>联系人电话</th>
                <td th:text="${contactMobile}"></td>
            </tr>
            <tr>
                <th>联系人邮箱</th>
                <td th:text="${contactEmail}"></td>
            </tr>
        </table>

        <div class="signature">
            <div class="signature-item">
                <div class="line"></div>
                <div class="date">企业代表签字/盖章</div>
            </div>
            <div class="signature-item">
                <div class="line"></div>
                <div class="date">日期</div>
            </div>
            <div class="signature-item">
                <div class="line"></div>
                <div class="date">酒店代表签字/盖章</div>
            </div>
            <div class="signature-item">
                <div class="line"></div>
                <div class="date">日期</div>
            </div>
        </div>

        <div class="footer">
            <p>本确认函一式两份，双方各执一份，具有同等法律效力。</p>
            <p>本确认函自双方签字盖章之日起生效。</p>
        </div>
    </div>
</div>
</body>
</html>