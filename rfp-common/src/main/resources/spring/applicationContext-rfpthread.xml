<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
        http://www.springframework.org/schema/beans 
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context 
        http://www.springframework.org/schema/context/spring-context.xsd"
       default-autowire="byName" default-lazy-init="false">

    <bean id="rfpCommonExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="6"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="10"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="5000"/>  <!-- 队列大小 -->
    </bean>

    <!-- 记录ELK日志线程池 -->
    <bean id="logDataExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="5"/>
        <property name="keepAliveSeconds" value="3000"/>
        <property name="maxPoolSize" value="40"/>
        <property name="queueCapacity" value="2000"/>
    </bean>

    <bean id="queryBidHotelInfoExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="60"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="300"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="5000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="hotelContractGenerateExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="hotelCoPayContractGenerateExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="updateCacheExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="consumerHotelIdMonitorExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="consumerPriceViolationMonitorExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <bean id="consumerOrderViolationMonitorExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>

    <!--异步操作违规监控 -->
    <bean id="violationMonitorExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="5"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>
    <!--异步生成酒店六边形坐标热力图 -->
    <bean id="hotelHexagonGenerateExecutor"
          class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="5"/> <!--核心线程数 -->
        <property name="keepAliveSeconds" value="60"/>  <!--某线程空闲超过这个时间，就回收该线程 -->
        <property name="maxPoolSize" value="100"/>      <!--最大线程数 -->
        <property name="queueCapacity" value="2000"/>  <!-- 队列大小 -->
    </bean>


</beans>