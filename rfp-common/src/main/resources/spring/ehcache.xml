<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="ehcache.xsd" 
    updateCheck="true"
    monitoring="autodetect" 
    dynamicConfig="true">

    <diskStore path="java.io.tmpdir" />
    
    <cache name="cachedProjectManager.getById"
        maxEntriesLocalHeap="1000"
        maxEntriesLocalDisk="1000" 
        eternal="false" 
        diskSpoolBufferSizeMB="20"
        timeToIdleSeconds="60" timeToLiveSeconds="60"
        memoryStoreEvictionPolicy="LFU" 
        transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>
    <cache name="cachedHotelManager.selectHotelInfo"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           timeToIdleSeconds="600" timeToLiveSeconds="600"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cachedProjectIntentHotelManager.queryProjectBidHotelInfo"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           timeToIdleSeconds="60" timeToLiveSeconds="60"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cachedProjectManager.queryProjectWhiteHotelIdList"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           timeToIdleSeconds="60" timeToLiveSeconds="60"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cachedPriceMonitorConfigManager.getByProjectId"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           timeToIdleSeconds="120" timeToLiveSeconds="120"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cachedProjectManager.queryProjectEmailAttachmentList"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="200"
           timeToIdleSeconds="60" timeToLiveSeconds="60"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cachedProjectManager.queryProjectCategoryWeightList"
           maxEntriesLocalHeap="1000"
           maxEntriesLocalDisk="1000"
           eternal="false"
           diskSpoolBufferSizeMB="200"
           timeToIdleSeconds="5" timeToLiveSeconds="5"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>




</ehcache>