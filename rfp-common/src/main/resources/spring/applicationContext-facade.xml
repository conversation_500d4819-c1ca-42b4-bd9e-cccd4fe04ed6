<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- 调用hessian时使用的接口 -->

    <!--邮件推送-->
    <bean id="emailSendService" class="com.fangcang.rpc.stub.hessian.FangCangHessianProxyFactoryBean">
        <property name="serviceUrl" value="${message.server}/emailSendService?appName=${appName}"/>
        <property name="serviceInterface" value="com.fangcang.message.email.service.EmailSendService"/>
        <property name="readTimeout" value="120000"/> <!-- 120s -->
        <property name="overloadEnabled" value="true"/>
    </bean>

    <!--短信推送-->
    <bean id="messageSendService" class="com.fangcang.rpc.stub.hessian.FangCangHessianProxyFactoryBean">
        <property name="serviceUrl" value="${message.server}/messageSendService?appName=${appName}"/>
        <property name="serviceInterface" value="com.fangcang.message.sms.service.MessageSendService"/>
        <property name="overloadEnabled" value="true"/>
    </bean>

    <!-- 分销商-->
    <bean id="companyFacade" class="com.fangcang.rpc.stub.hessian.FangCangHessianProxyFactoryBean">
        <property name="serviceUrl" value="${agent.server}/companyFacade"/>
        <property name="serviceInterface" value="com.fangcang.hotel.agent.facade.CompanyFacade"/>
        <property name="overloadEnabled" value="true"/>
    </bean>

    <!-- 供应商 -->
    <bean id="supplyOrgFacade" class="com.fangcang.rpc.stub.hessian.FangCangHessianProxyFactoryBean">
        <property name="serviceUrl" value="${supply.server}/supplyOrgFacade"/>
        <property name="serviceInterface" value="com.fangcang.hotel.supply.facade.SupplyOrgFacade"/>
        <property name="overloadEnabled" value="true"/>
    </bean>

</beans>