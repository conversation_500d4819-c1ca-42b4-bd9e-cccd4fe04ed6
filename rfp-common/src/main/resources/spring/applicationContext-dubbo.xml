<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd">
    <!--服务提供者应用名称-->
    <dubbo:application name="rfp-server"></dubbo:application>
    <dubbo:registry protocol="zookeeper" address="${zookeeperaddress}"></dubbo:registry>

    <dubbo:protocol name="dubbo" port="20880"/>

    <dubbo:consumer check="false"/>

    <!--酒店基本信息服务-->
    <dubbo:reference id="hotelInfoFacade" interface="com.fangcang.hotel.base.api.facade.HotelInfoFacade"  protocol="dubbo" timeout="60000"></dubbo:reference>
    <dubbo:reference id="roomInfoFacade" interface="com.fangcang.hotel.base.api.facade.RoomInfoFacade"  protocol="dubbo" timeout="60000"></dubbo:reference>
    <dubbo:reference id="imageInfoFacade" interface="com.fangcang.hotel.base.api.facade.ImageInfoFacade"  protocol="dubbo" timeout="60000"></dubbo:reference>


</beans>