<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jee="http://www.springframework.org/schema/jee"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="
        http://www.springframework.org/schema/beans 
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/jee
		http://www.springframework.org/schema/jee/spring-jee.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx.xsd"
       default-lazy-init="true">
    <!-- JNDI 数据源 -->
    <jee:jndi-lookup id="htl_rfp" jndi-name="jdbc/htl_rfp"/>

    <!-- 声明使用注解式事务 -->
    <tx:annotation-driven transaction-manager="htlrfp_transactionManager"/>

    <bean id="htlrfp_transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="htl_rfp"/>
        <qualifier value="htlrfp"/>
    </bean>

    <bean id="htlrfpSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="htl_rfp"/>
        <property name="configLocation" value="classpath:mybatis/htlrfp_config.xml"/>
    </bean>


    <!-- DAO接口所在包名，Spring会自动查找其下的类-->
    <bean id="htlrfpMapper" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.fangcang.rfp.common.dao"/>
        <property name="sqlSessionFactoryBeanName" value="htlrfpSqlSessionFactory"/>
    </bean>

</beans>