overloadEnabled=true
recordException=true
connectTimeOut=3000
readTimeOut=40000
appName=tmc-hub
#to enable method intercept
methodInterceptEnabled=true
#split by '#'(english) if multi
common.service.fail.mailTo=<EMAIL>\#<EMAIL>
#appNameæ ç¤ºåºç¨åï¼ä½¿ç¨æ¶æ¢æèªå·±åºç¨å
#recordExceptionä¸ºtrueè¡¨ç¤ºè°ç¨åºéåé®ä»¶
#appNameè¡¨ç¤ºè°ç¨ç«¯åºç¨å
#common.service.fail.mailToè¡¨ç¤ºåºéå¤çäººï¼å¤ä¸ªç¨#åå²
#methodInterceptEnabledæ ç¤ºå¼å¯æå³é­æ¥å£æ¦æªï¼å¦æè¦åç¬æ¦æªæäºæ¥å£åæ¹æ³ï¼æ¹åæ°å¿é¡»è®¾ç½®ä¸ºtrue,ä¸éè¿FangCangHessianProxyFactoryBeanæ³¨å¥è¦æ¦æªçæ¹æ³ï¼å¦1æç¤ºï¼