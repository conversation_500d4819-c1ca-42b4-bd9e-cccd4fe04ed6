/**
 * Copyright (c) 2006-2015 Fangcang Ltd. All Rights Reserved.
 * <p>
 * This code is the confidential and proprietary information of
 * Fangcang. You shall not disclose such Confidential Information
 * and shall use it only in accordance with the terms of the agreements
 * you entered into with Fangcang,http://www.fangcang.com.
 */
package com.fangcang.rfp.common.service.config;

import com.ctol.fangcang.common.ParamServiceImpl;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
public class CustomPropertyConfigurer extends PropertyPlaceholderConfigurer {

    @Override
    protected String resolvePlaceholder(String placeholder, Properties props) {
        String value = super.resolvePlaceholder(placeholder, props);
        value = value == null ? value : value.trim();
        return ParamServiceImpl.getInstance().getConfValue(value);
    }
}
 