package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.QueryProjectRecommendHotelRequest;
import com.fangcang.rfp.common.dto.response.QueryHistoryProjectInfoResponse;
import com.fangcang.rfp.common.entity.Project;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

public interface ProjectHotelHistoryDataRecommendService {

    // 生成推荐等级
    Future<Response> generateRecommendLevel(UserDTO userDTO, String statReferenceNo, Long projectId, CountDownLatch countDownLatch);

    // 更新去年违规次数
    Future<Response> updateLastYearViolationsCount(UserDTO userDTO, String statReferenceNo, Project project, List<Long> hotelIdList, CountDownLatch countDownLatch);

    // 更新去年服务分
    Future<Response> updateLastYearServicePoint(UserDTO userDTO, String statReferenceNo, Project project, List<Long> hotelIdList, CountDownLatch countDownLatch);

    // 更新去年OTA价格
    Future<Response> updateLastYearOtaPrice(UserDTO userDTO, String statReferenceNo, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch);

    // 更新商旅价格
    Future<Response> updateLowestPrice(UserDTO userDTO, String statReferenceNo, Project project, CountDownLatch countDownLatch);

    // 异步生成高频预订邀约推荐
    Future<Response> generateFrequencyRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch);

    // 异步生成POI周边酒店推荐
    Future<Response> generatePoiNearHotelRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch);

    // 异步生成非POI热订区域邀约推荐
    Future<Response> generateNoPoiHotAreaRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch);

    // 异步生成散布聚量邀约推荐
    Future<Response> generateAreaGatherRecommend(String statReferenceNo, UserDTO userDTO, Project project);

    // 查询可邀约酒店总数
    Response queryProjectTotalRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 导出推荐列表
    InputStream exportProjectRecommendHotel(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest, HttpServletResponse response) throws Exception;

    // 查询可邀约酒店总数统计
    Response queryProjectRecommendHotelStat(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询高频推荐酒店
    Response queryProjectFrequencyRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询高频同档推荐酒店
    Response queryProjectFrequencySameLevelRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询POI周边优质酒店邀约推荐
    Response queryPoiNearHotelRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查非POI热订区域邀约推荐:
    Response queryNoPoiHotelAreaRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询散布聚量邀约推荐
    Response queryAreaGatherRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询优质酒店邀约推荐
    Response queryHighQualityRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询节省明星酒店邀约推荐
    Response querySavedRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 查询最近30天商旅价格详情
    Response queryLowestPriceItemInfo(Long projectId, Long hotelId);

    // 查询最近一年OTA最低价格
    Response queryHotelOtaMinPriceList(Long projectId, Long hotelId);

    // 查询项目需要邀约酒店数量
    Response queryProjectRecommendNeedInvitedHotelCount(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest);

    // 一键邀约项目所有酒店
    Response addProjectAllRecommendNeedInvitedHotel(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest, UserDTO userDTO);

}
