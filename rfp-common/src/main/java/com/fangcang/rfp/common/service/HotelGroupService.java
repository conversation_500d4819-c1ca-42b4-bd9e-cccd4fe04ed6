package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.QueryProjectOverviewResponse;
import com.fangcang.rfp.common.entity.HotelGroupDefaultCustomStrategy;
import com.fangcang.rfp.common.entity.HotelGroupDefaultCustomStrategyOption;
import com.fangcang.rfp.common.entity.ProjectIntentHotelGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/14 15:57
 */
public interface HotelGroupService {

    /**
     * 项目报价总览
     *
     * @param queryProjectOverviewRequest
     * @return
     */
    Response queryProjectOverview(QueryProjectOverviewRequest queryProjectOverviewRequest, UserDTO userDTO);

    /**
     * 查询酒店集团意向报价酒店
     * @param queryTendProjectInviteHotelInfoRequest
     * @return
     */
    Response queryTendProjectInviteHotelInfo(QueryTendProjectInviteHotelInfoRequest queryTendProjectInviteHotelInfoRequest);

    /**
     * 保存酒店集团默认报价
     *
     * @param saveHotelGroupDefaultPriceDto
     * @return
     */
    Response saveHotelGroupDefaultPrice(SaveHotelGroupDefaultPriceDto saveHotelGroupDefaultPriceDto);

    /**
     * 查询酒店默认报价
     *
     * @param projectIntentHotelGroupId
     * @param orgId
     * @return
     */
    Response queryHotelGroupDefaultPrice(Long projectIntentHotelGroupId, Long orgId) throws Exception;

    /**
     * 修改酒店集团销售联系人
     *
     * @param modifyHotelGroupContactDto
     * @return
     */
    Response modifyHotelGroupContact(ModifyHotelGroupContactDto modifyHotelGroupContactDto);

    /**
     * 选酒店报价
     *
     * @param groupId
     * @param hotelId
     * @return
     */
    Response queryHotelToPrice(Long groupId, Long hotelId);

    /**
     * 需要合同去报价校验机构信息
     *
     * @param orgId
     * @param projectId
     * @return
     */
    Response checkOrgInfo(Long orgId, Long projectId);

    /**
     * 查询酒店集团所有报价（包括单体酒店）
     *
     * @param queryAllHotelGroupPriceRequest
     * @return
     */
    Response queryAllHotelGroupPrice(QueryAllHotelGroupPriceRequest queryAllHotelGroupPriceRequest, UserDTO userDTO);

    /**
     * 查询酒店集团代酒店报价（不包括单体酒店）
     *
     * @param queryHotelGroupBidPriceRequest
     * @return
     */
    Response queryHotelGroupBidPriceByHotelOrgId(QueryHotelGroupBidPriceRequest queryHotelGroupBidPriceRequest, UserDTO userDTO);



    /**
     * 查询酒店集团项目基本信息
     */
    List<QueryProjectOverviewResponse> queryProjectOverviewInfo(QueryProjectOverviewRequest queryProjectOverviewRequest);

    /**
     * 更新项目酒店集团审核开关
     * @param projectIntentHotelGroup
     * @return
     */

    Response updateOpenGroupApprove(ProjectIntentHotelGroup projectIntentHotelGroup);

    List<HotelGroupDefaultCustomStrategy> queryHotelGroupDefaultCustomStrategy(Long projectIntentHotelGroupId);
}
