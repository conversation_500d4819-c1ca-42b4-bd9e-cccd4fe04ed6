package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.entity.EmployeeOrg;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**

 * @简单描述: 机构关注酒店配置

 */
public interface OrgFollowHotelService
{

	/**
	 * 新增员工机构
	 * @return
	 * @throws Exception
	 */
	Response addOrgFollowHotel(AddOrgFollowHotelDto addOrgFollowHotelDto);

	/**
	 * 删除更新机构
	 */
	Response deleteOrgFollowHotel(DeleteOrgFollowHotelDto deleteOrgFollowHotelDto);

	/**
	 * 查询机构关注酒店
	 */
	Response queryOrgFollowHotelList(QueryOrgFollowHotelRequest queryOrgFollowHotelRequest);

	/**
	 * 下载机构关注酒店模板
	 */
	void downloadOrgFollowHotelTemplate(HttpServletResponse httpServletResponse) throws Exception;



	/**
	 *  导入机构关注酒店
	 */
	Response importOrgFollowHotel(MultipartFile uploadFile, UserDTO userDTO) throws Exception;



}
