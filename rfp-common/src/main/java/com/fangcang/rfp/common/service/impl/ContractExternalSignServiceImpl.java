package com.fangcang.rfp.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcang.exception.ServiceException;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.config.FaddConfig;
import com.fangcang.rfp.common.dao.ContractExternalDao;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ContractExternalDto;
import com.fangcang.rfp.common.dto.response.AttachmentInfoResponse;
import com.fangcang.rfp.common.entity.ContractExternal;
import com.fangcang.rfp.common.entity.OrgSubject;
import com.fangcang.rfp.common.entity.OrgSubjectFaddAccount;
import com.fangcang.rfp.common.entity.RfpContractSignRecord;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.fadd.api.FaddApiManager;
import com.fangcang.rfp.common.fadd.api.OrgSubjectFaddAccountService;
import com.fangcang.rfp.common.fadd.api.RfpContractSignRecordService;
import com.fangcang.rfp.common.service.ContractExternalSignService;
import com.fangcang.rfp.common.service.ElkLogService;
import com.fangcang.rfp.common.service.OrgSubjectService;
import com.fangcang.rfp.common.util.ELKUtil;
import com.fangcang.rfp.common.util.StringUtils;
import com.fangcang.tmc.delivery.api.constant.FaddContractStatusEnum;
import com.fangcang.tmc.delivery.api.constant.FaddReturnCodeEnum;
import com.fangcang.tmc.delivery.api.constant.FaddSignPositionTypeEnum;
import com.fangcang.tmc.delivery.api.remote.common.FaddResponse;
import com.fangcang.tmc.delivery.api.remote.request.ContractSignConfig;
import com.fangcang.tmc.delivery.api.remote.request.ContractSignRequest;
import com.fangcang.tmc.delivery.api.remote.request.ExtsignAutoRequest;
import com.fangcang.tmc.delivery.api.remote.request.UuploaddocsRequest;
import com.fangcang.tmc.delivery.api.remote.response.ContractExternalResponse;
import com.fangcang.tmc.delivery.api.remote.response.ContractStatusResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 合同签署服务
 *
 * <AUTHOR>
 * @since 2024/3/2
 */
@Service
public class ContractExternalSignServiceImpl implements ContractExternalSignService {

    private final static Logger logger = LoggerFactory.getLogger(ContractExternalSignServiceImpl.class);
    @Autowired
    private ContractExternalDao contractExternalDao;
    @Autowired
    private FaddApiManager faddApiManager;

    @Autowired
    private RfpContractSignRecordService rfpContractSignRecordService;
    @Autowired
    private OrgSubjectService orgSubjectService;

    @Autowired
    private OrgSubjectFaddAccountService orgSubjectFaddAccountService;

    public static final String creator = "System";

    @Autowired
    private ElkLogService elkLogService;

    @Override
    public ContractExternalResponse queryContract(String contractCode) {
        ContractExternal contractExternal = contractExternalDao.selectByContractCode(contractCode);
        if (contractExternal == null) {
            return null;
        }
        if (ContractStateEnum.SIGNING.key == contractExternal.getContractState()) {
            // 还在签章中，尝试主动归档
            logger.info("合同仍在签章中，尝试主动归档, contractCode: {}", contractCode);
            contractFiling(contractCode, creator);
            contractExternal = contractExternalDao.selectByContractCode(contractCode);
        }

        ContractExternalResponse response = new ContractExternalResponse();
        BeanUtils.copyProperties(contractExternal, response);
        return response;
    }

    @Override
    public boolean submitSignTask(ContractSignRequest request) {
        // 参数校验
        checkContractSignRequest(request);
        // 签约主体校验
        checkSignSubject(BaseConfig.getExternalContractSubjectId());

        ContractExternal contractExternal = contractExternalDao.selectByContractCode(request.getContractCode());
        if (contractExternal == null) {
            insertContract(request);
        } else {
            Integer contractState = contractExternal.getContractState();
            if (ContractStateEnum.SIGN_FAIL.key == contractState) {
                ContractExternalDto contractDto = new ContractExternalDto();
                contractDto.setContractId(contractExternal.getContractId());
                contractDto.setContractState(ContractStateEnum.NOT_SIGN.key);
                contractDto.setModifier(creator);
                contractExternalDao.updateContractById(contractDto);
                logger.info("重新签署合同，contractCode: {}", contractExternal.getContractCode());
            } else if (ContractStateEnum.FILING_FAIL.key == contractState) {
                boolean filingRes = contractFiling(contractExternal.getContractCode(), creator);
                if (!filingRes) {
                    throw new ServiceException("合同归档失败");
                }
            } else {
                throw new ServiceException("合同已存在，请勿重复提交");
            }

        }
        return true;
    }

    private void checkContractSignRequest(ContractSignRequest request) throws IllegalArgumentException {
        Assert.notNull(request.getSignConfig(), "签章配置不能为空");
        Assert.hasText(request.getFileUrl(), "合同文件地址不能为空");
        Assert.hasText(request.getContractCode(), "合同编码不能为空");
        ContractSignConfig signConfig = request.getSignConfig();
        Assert.notNull(signConfig.getSignPositionType(), "定位类型不能为空");
        if (Objects.equals(signConfig.getSignPositionType(), FaddSignPositionTypeEnum.KEYWORD.type)) {
            Assert.hasText(signConfig.getSignKeyword(), "定位关键字不能为空");
        } else if (Objects.equals(signConfig.getSignPositionType(), FaddSignPositionTypeEnum.LOCATION.type)){
            Assert.notEmpty(signConfig.getSignaturePositions(), "定位坐标不能为空");
        } else {
            throw new IllegalArgumentException("错误的定位类型");
        }
    }


    private void checkSignSubject(Long subjectId) throws ServiceException {
        Response subjectResponse = orgSubjectService.selectByPrimaryKey(subjectId);
        OrgSubject queryObject = (OrgSubject)subjectResponse.getData();
        if (queryObject == null || StateEnum.Invalid.key == queryObject.getState()) {
            throw new ServiceException("签约主体无效, 请先确认签约主体已正确配置");
        }
        if (AuthorizeStateEnum.FINISH.key.intValue() != queryObject.getAuthorizeState().intValue()) {
            throw new ServiceException("签约主体未授权成功, 请先确认签约主体已正确配置");
        }
        Response accountResponse = orgSubjectFaddAccountService.selectByPrimaryKey(subjectId);
        if (accountResponse.getData() == null){
            throw new ServiceException("签约主体无法大大账号, 请先确认签约主体已正确配置");
        }

    }

    private void insertContract(ContractSignRequest request) {
        ContractExternal contract = new ContractExternal();
        contract.setContractUrl(request.getFileUrl());
        contract.setContractCode(request.getContractCode());
        contract.setContractState(ContractStateEnum.NOT_SIGN.key);
        contract.setSignMethod(SignMethodEnum.ELECTRIC.key);
        contract.setCreator(creator);
        contract.setModifier(creator);
        contract.setSignConfig(JSON.toJSONString(request.getSignConfig()));
        contractExternalDao.insertContract(contract);
    }

    @Override
    public void onlineSignature(ContractExternal contract) throws Exception {
        Long subjectId = BaseConfig.getExternalContractSubjectId();

        Response accountResponse = orgSubjectFaddAccountService.selectByPrimaryKey(subjectId);
        String faddCustomerId = ((OrgSubjectFaddAccount) accountResponse.getData()).getFaddCustomerId();

//        List<RfpContractSignRecord> rfpContractSignRecords = rfpContractSignRecordService.selectSignRecordByContractCode(contract.getContractCode());

        // 新增合同签章记录
        RfpContractSignRecord rfpContractSignRecord = new RfpContractSignRecord();
        rfpContractSignRecord.setSubjectId(subjectId);
        rfpContractSignRecord.setContractCode(contract.getContractCode());
        rfpContractSignRecord.setSignState(SignStateEnum.NOT_SIGN.key);
        rfpContractSignRecord.setFaddCustomerId(faddCustomerId);
        rfpContractSignRecord.setCreator(creator);
        rfpContractSignRecord.setModifier(creator);
        rfpContractSignRecordService.insert(rfpContractSignRecord);


        // 判断合同上传状态是否已上传
        if (UploadStateEnum.UPLOAD.key != contract.getUploadState()) {

            // 合同未上传,上传合同
            UuploaddocsRequest uuploaddocsRequest = new UuploaddocsRequest();
            uuploaddocsRequest.setContractId(contract.getContractCode());
            uuploaddocsRequest.setDocTitle(contract.getContractCode());

            // 主动推文件流
            String uploaddocs;
            URL url = new URL(contract.getContractUrl());
            URLConnection urlConnection = url.openConnection();
            urlConnection.connect();
            try (InputStream inputStream = urlConnection.getInputStream()) {
                uuploaddocsRequest.setFile(inputStream);
                uploaddocs = faddApiManager.uploaddocs(uuploaddocsRequest);
            }

            FaddResponse faddResponse = JSON.parseObject(uploaddocs, FaddResponse.class);
            // 防止上传成功，但更新合同状态失败情况
            if (!"1000".equals(faddResponse.getCode()) && !faddResponse.getMsg().contains("合同编号重复，此编号已存在")){
                logger.error("上传外部合同失败，合同code：" + contract.getContractCode());

                ContractExternalDto contractDto = new ContractExternalDto();
                contractDto.setContractId(contract.getContractId());
                contractDto.setContractState(ContractStateEnum.SIGN_FAIL.key);
                contractDto.setModifier(creator);
                contractDto.setRemark("上传合同失败");
                contractExternalDao.updateContractById(contractDto);
                elkLogService.saveLogData(
                        ELKUtil.buildElkLogData(new Date(),
                                "onlineSignature-uploadContract",
                                contract.getContractCode(),
                                JSON.toJSONString(uuploaddocsRequest),
                                uploaddocs,
                                String.valueOf(ContractStateEnum.SIGN_FAIL.key)));
                return;
            }

            // 上传成功，更新合同上传状态
            ContractExternalDto contractDto = new ContractExternalDto();
            contractDto.setContractId(contract.getContractId());
            contractDto.setUploadState(UploadStateEnum.UPLOAD.key);
            contractDto.setModifier(creator);
            contractExternalDao.updateContractById(contractDto);
        }
        ContractSignConfig signConfig = JSON.parseObject(contract.getSignConfig(), ContractSignConfig.class);

        // 开始签章
        boolean signRes = callExtsignAuto(contract.getContractCode(), signConfig, rfpContractSignRecord.getSignTransactionId(), faddCustomerId);
        ContractExternalDto contractDto = new ContractExternalDto();
        contractDto.setContractId(contract.getContractId());
        contractDto.setModifier(creator);
        if (signRes) {
            // 更新合同为签章中
            contractDto.setContractState(ContractStateEnum.SIGNING.key);
        } else {
            contractDto.setContractState(ContractStateEnum.SIGN_FAIL.key);
            contractDto.setRemark("自动签章失败");
        }
        contractExternalDao.updateContractById(contractDto);

    }


    /**
     * 封装调用签章逻辑
     */
    private boolean callExtsignAuto(String contractCode, ContractSignConfig signConfig, Long signTransactionId, String faddCustomerId) throws Exception {
        // 调甲方或乙方自动签章
        ExtsignAutoRequest extsignAutoRequest = new ExtsignAutoRequest();
        extsignAutoRequest.setTransactionId(String.valueOf(signTransactionId));
        extsignAutoRequest.setContractId(contractCode);
        extsignAutoRequest.setCustomerId(faddCustomerId);
        extsignAutoRequest.setDocTitle(contractCode);
        extsignAutoRequest.setPositionType(String.valueOf(signConfig.getSignPositionType()));
        if (Objects.equals(FaddSignPositionTypeEnum.KEYWORD.type, signConfig.getSignPositionType())) {
            extsignAutoRequest.setSignKeyword(signConfig.getSignKeyword());
            if (signConfig.getKeywordStrategy() != null) {
                extsignAutoRequest.setKeywordStrategy(String.valueOf(signConfig.getKeywordStrategy()));
            }
        } else {
            extsignAutoRequest.setSignaturePositions(JSON.toJSONString(signConfig.getSignaturePositions()));
        }
        extsignAutoRequest.setNotifyUrl(FaddConfig.getExternalSignNotifyUrl());

        RfpContractSignRecord rfpContractSignRecord = new RfpContractSignRecord();
        rfpContractSignRecord.setSignTransactionId(signTransactionId);

        String s = faddApiManager.extsignAuto(extsignAutoRequest);
        FaddResponse faddResponse = JSON.parseObject(s, FaddResponse.class);
        if (FaddReturnCodeEnum.OPERATOR_SUCCESS.code.equals(faddResponse.getCode())){
            rfpContractSignRecord.setSignState(SignStateEnum.DONE.key);
            rfpContractSignRecordService.updateByPrimaryKeySelective(rfpContractSignRecord);
            return true;
        } else {
            logger.error("法大大签章失败，contractCode: {}, signTransactionId: {}, 失败原因：{}", contractCode, signTransactionId, faddResponse.getMsg());
            rfpContractSignRecord.setSignState(SignStateEnum.SIGN_FAIL.key);
            rfpContractSignRecordService.updateByPrimaryKeySelective(rfpContractSignRecord);

            elkLogService.saveLogData(
                    ELKUtil.buildElkLogData(new Date(),
                            "onlineSignature-callExtsignAuto",
                            contractCode,
                            JSON.toJSONString(extsignAutoRequest),
                            s,
                            String.valueOf(ContractStateEnum.SIGN_FAIL.key)));

            return false;
        }
    }


//    @Transactional
//    public void insertContract(Contract contract) {
//        Response response = new Response();
//
//        try {
//            // PROJECT_ID, PROJECT_BUSSINESS_ID, CONTRACT_BIZ_TYPE 唯一性校验
//            int count = contractDao.checkContractIsRepeat(contract);
//            if (count > 0) {
//                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
//                response.setMsg("数据重复");
//                return response;
//            }
//            contractDao.insertContract(contract);
//            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
//            response.setMsg(ReturnResultEnum.SUCCESS.message);
//
//        } catch (Exception e) {
//            logger.error("新增合同失败", e);
//            response.setResult(ReturnResultEnum.FAILED.errorNo);
//            response.setMsg(ReturnResultEnum.FAILED.message);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//        }
//        return response;
//    }


    @Override
    public boolean contractFiling(String contractCode,String modifier) {
        try {
            Response response = fcContractFiling(contractCode, modifier);

            // 合同归档失败
            if (!Objects.equals(response.getResult(), ReturnResultEnum.SUCCESS.errorNo)) {
                ContractExternalDto contractDto = new ContractExternalDto();
                contractDto.setContractCode(contractCode);
                contractDto.setContractState(ContractStateEnum.FILING_FAIL.key);
                contractDto.setRemark(response.getMsg());
                contractDto.setModifier(modifier);
                updateContractByCode(contractDto);
            } else {
                logger.info("合同归档成功, contractCode: {}", contractCode);
            }
        } catch (Exception e) {
            logger.error("合同归档失败, contractCode: {}", contractCode, e);
            return false;
//            throw new ServiceException("合同归档失败");
        }
        return true;
    }

    private Response fcContractFiling(String contractCode,String modifier) throws Exception {

        Response response = new Response();

        // 如果已完成签章则跳过
        ContractExternal contractExternal = contractExternalDao.selectByContractCode(contractCode);
        if (ContractStateEnum.DONE.key == contractExternal.getContractState()) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg("签章成功");
            return response;
        }


        String contractStatus = faddApiManager.contractStatus(contractCode);
        ContractStatusResponse contractStatusResponse = JSON.parseObject(contractStatus, ContractStatusResponse.class);

        if (!FaddReturnCodeEnum.OPERATOR_SUCCESS.code.equals(contractStatusResponse.getCode())){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(contractStatusResponse.getCode() + contractStatusResponse.getMsg());

            elkLogService.saveLogData(
                    ELKUtil.buildElkLogData(new Date(),
                            "onlineSignature-getFaddContractStatus",
                            contractCode,
                            contractCode,
                            contractStatus,
                            String.valueOf(ContractStateEnum.FILING_FAIL.key)));
            return response;
        }

        if (FaddContractStatusEnum.REFUSE.key.equals(contractStatusResponse.getContractStatus())){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("合同已拒签");

            elkLogService.saveLogData(
                    ELKUtil.buildElkLogData(new Date(),
                            "onlineSignature-getFaddContractStatus",
                            contractCode,
                            "合同已拒签",
                            contractStatus,
                            String.valueOf(ContractStateEnum.FILING_FAIL.key)));

            return response;
        }

        // 合同状态是待签署，先调归档
        if (FaddContractStatusEnum.WAIT.key.equals(contractStatusResponse.getContractStatus())){
            // 合同归档
            String contractFiling = faddApiManager.contractFiling(contractCode);
            FaddResponse faddResponse = JSON.parseObject(contractFiling, FaddResponse.class);

            if (!FaddReturnCodeEnum.OPERATOR_SUCCESS.code.equals(faddResponse.getCode()) && !"该合同不存在或已归档迁移".equals(faddResponse.getMsg())){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("合同归档失败");

                elkLogService.saveLogData(
                        ELKUtil.buildElkLogData(new Date(),
                                "onlineSignature-FaddContractFiling",
                                contractCode,
                                contractCode,
                                contractFiling,
                                String.valueOf(ContractStateEnum.FILING_FAIL.key)));

                return response;
            }
        }

        // 合同下载
        String url = faddApiManager.downLoadContract(contractCode);

        // 上传到本地服务器
        Response uploadToFTP = faddApiManager.downloadFileAndUploadToFTP(url, modifier, 2);

        if (ReturnResultEnum.SUCCESS.errorNo.intValue() != uploadToFTP.getResult().intValue()){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("合同下载并上传到服务器失败");

            elkLogService.saveLogData(
                    ELKUtil.buildElkLogData(new Date(),
                            "onlineSignature-downloadFileAndUploadToFTP",
                            contractCode,
                            url,
                            JSON.toJSONString(uploadToFTP),
                            String.valueOf(ContractStateEnum.FILING_FAIL.key)));
            return response;
        }
        AttachmentInfoResponse attachmentInfoResponse = (AttachmentInfoResponse)uploadToFTP.getData();

        // 若配置了其他域名访问，则替换域名
        String signedContractUrl;
        if (StringUtils.isNotBlank(BaseConfig.getExternalContractSignedDomain())) {
            signedContractUrl = BaseConfig.getExternalContractSignedDomain() + removeDomain(attachmentInfoResponse.getFileUrl());
        } else {
            signedContractUrl = attachmentInfoResponse.getFileUrl();
        }

        // 更新本地合同已签章地址、合同状态
        ContractExternalDto contractDto = new ContractExternalDto();
        contractDto.setContractCode(contractCode);
        contractDto.setContractState(ContractStateEnum.DONE.key);
        contractDto.setSignedContractUrl(signedContractUrl);
        contractDto.setModifier(modifier);
        contractDto.setRemark("签章成功");
        contractExternalDao.updateContractByCode(contractDto);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("签章成功");

        return response;
    }

    @Override
    public void updateContractByCode(ContractExternalDto contractDto) {
        contractExternalDao.updateContractByCode(contractDto);
    }

    public static String removeDomain(String url) {
        String regex = "^https?://[^/]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        return matcher.replaceFirst("");
    }

    public void setRfpContractSignRecordService(RfpContractSignRecordService rfpContractSignRecordService) {
        this.rfpContractSignRecordService = rfpContractSignRecordService;
    }
}
