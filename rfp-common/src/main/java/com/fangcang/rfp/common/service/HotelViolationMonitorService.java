package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.HotelOrderMonitorResponse;
import com.fangcang.rfp.common.entity.HotelViolationsMonitor;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/22 19:30
 */
public interface HotelViolationMonitorService {

    /**
     * 查询酒店违规监控信息
     *
     * @param queryHotelViolationMonitorRequest
     * @return
     */
    Response queryHotelViolationMonitor(QueryHotelViolationMonitorRequest queryHotelViolationMonitorRequest);

    /**
     * 查询酒店违规监控信息 状态数量统计
     *
     * @param statusCountRequest
     * @return
     */
    Response queryHotelViolationMonitorStatusCount(HotelViolationMonitorStatusCountRequest statusCountRequest);


    /**
     * 查询酒店履约统计
     *
     * @param statRequest
     * @return
     */
    Response queryHotelViolationMonitorStat(QueryHotelViolationMonitorStatRequest statRequest);

    /**
     * 查询酒店违规复核统计
     *
     * @param queryHotelViolationCheckPassStatRequest
     * @return
     */
    Response queryHotelViolationCheckPassStat(QueryHotelViolationCheckPassStatRequest queryHotelViolationCheckPassStatRequest);

    /**
     * 导出酒店违规复核统计
     *
     * @param queryHotelViolationCheckPassStatRequest
     * @return
     */
    void exportHotelViolationCheckPassStat(QueryHotelViolationCheckPassStatRequest queryHotelViolationCheckPassStatRequest, HttpServletResponse response) throws Exception;

    /**
     * 查询酒店履约 标签处理 记录
     *
     * @param recordsRequest
     * @return
     */
    Response queryViolationMonitorStatLabelRecords(QueryHotelViolationMonitorStatLabelRecordsRequest recordsRequest);

    /**
     * 查询酒店履约统计 导出
     *
     * @param statRequest
     * @return
     */
    void exportHotelViolationStat(QueryHotelViolationMonitorStatRequest statRequest, HttpServletResponse response) throws Exception;

    /**
     * 标记处理
     *
     * @param labelProcessDto
     * @return
     */
    Response labelProcess(LabelProcessDto labelProcessDto, Integer orgType);

    /**
     * 酒店履约统计标记处理
     *
     * @param statLabelProcessDto
     * @return
     */
    Response monitorStatLabelProcess(MonitorStatLabelProcessDto statLabelProcessDto);


    /**
     * 查询服务分扣分明细
     *
     * @param queryServiceDeductionDetailsRequest
     * @return
     */
    Response queryServiceDeductionDetails(QueryServiceDeductionDetailsRequest queryServiceDeductionDetailsRequest);

    /**
     * 查询报价监控明细
     *
     * @param violationsMonitorId
     * @return
     */
    Response queryHotelViolationsDailyPrice(Long violationsMonitorId) throws Exception;

    /**
     * 查询订单监控明细
     *
     * @param violationsMonitorId
     * @return
     */
    Response queryHotelViolationsOrderDetail(Long violationsMonitorId);

    /**
     * 导出酒店违规信息
     *
     * @param queryHotelViolationMonitorRequest
     */
    void exportHotelViolations(QueryHotelViolationMonitorRequest queryHotelViolationMonitorRequest, HttpServletResponse httpServletResponse) throws Exception;

    /**
     * 初始化酒店报价违规监控数据
     */
    void initHotelPriceViolationsMonitor() throws Exception;

    /**
     * 消费酒店报价违规监控数据
     *
     * @param projectHotelId
     */
    String consumeHotelPriceViolationsMonitor(String projectHotelId, String modifier);

    /**
     * 消费酒店报价违规监控数据 (复核)
     *
     * @param projectHotelId
     */
    String consumeHotelPriceViolationsRepeatMonitor(String projectHotelId, String modifier) throws Exception;


    /**
     * 初始化发送酒店报价违规监控数据
     */
    void initSendPriceViolationsMonitor() throws Exception;

    /**
     * 消费发送酒店报价违规监控数据
     *
     * @param violationsMonitorId
     */
    void consumeSendPriceViolationsMonitor(Long violationsMonitorId) throws Exception;

    /**
     * 消费发送复核酒店报价违规监控数据
     *
     */
    void consumeSendRecheckPriceViolationsMonitor(HotelViolationsMonitor hotelViolationsMonitor) throws Exception;


    /**
     * 初始化酒店订单违规监控数据
     */
    void initHotelOrderViolationsMonitor() throws Exception;

    /**
     * 消费酒店订单违规监控数据
     *
     * @param hotelOrderMonitorResponse
     */
    void consumeHotelOrderViolationsMonitor(HotelOrderMonitorResponse hotelOrderMonitorResponse);


    /**
     * 初始化发送酒店订单违规监控数据
     */
    void initSendOrderViolationsMonitor() throws Exception;

    /**
     * 消费发送酒店订单违规监控数据
     *
     * @param violationsMonitorId
     */
    void consumeSendOrderViolationsMonitor(Long violationsMonitorId) throws Exception;

    /**
     * 增加服务分奖励及补偿功能
     *
     * @param addHotelViolationMonitorDto
     * @return
     */
    Response addHotelViolationMonitor(AddHotelViolationMonitorDto addHotelViolationMonitorDto);

    /**
     * 解决最近7天不处理任务
     */
    Response resolvedNotCooperationViolation();
}
