package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.OrgPoiRequest;
import com.fangcang.rfp.common.entity.OrgPoi;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 机构Poi业务
 * @auther chenjianhua
 * @date 2022/9/1
*/
public interface OrgPoiService {

    /**
     * 分页查询机构POI
     * @param orgPoiRequest
     * @return
     */
    Response queryOrgPoiPage(OrgPoiRequest orgPoiRequest);

    /**
     * 新增机构POI
     * @param orgPoi
     */
    Response addOrgPoi(OrgPoi orgPoi) ;

    /**
     * 更新机构POI
     * @param orgPoi
     */
    Response updateOrgPoi(OrgPoi orgPoi);

    /**
     * 删除机构POI
     * @param orgPoi
     */
    Response deleteOrgPoi(OrgPoi orgPoi);

    /**
     * POI名称和机构id(按照匹配度排序)
     * @param orgPoiRequest
     * @return
     */
    Response queryPoiLikeName(OrgPoiRequest orgPoiRequest);

    /**
     * 下载机构POI导入模板
     */
    void downloadOrgPoiTemplate(HttpServletResponse httpServletResponse);

    /**
     * 机构POI导入
     */
    public Response importOrgPoi(MultipartFile uploadFile, UserDTO userDTO) throws Exception;
}

