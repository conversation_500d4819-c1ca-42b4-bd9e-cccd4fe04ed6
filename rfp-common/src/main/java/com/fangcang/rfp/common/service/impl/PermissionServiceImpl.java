package com.fangcang.rfp.common.service.impl;


import com.fangcang.rfp.common.dao.UPermissionDao;
import com.fangcang.rfp.common.dao.URolePermissionDao;
import com.fangcang.rfp.common.dao.UUserDao;
import com.fangcang.rfp.common.dao.UUserRoleDao;
import com.fangcang.rfp.common.dto.Permission;
import com.fangcang.rfp.common.dto.PermissionBo;
import com.fangcang.rfp.common.dto.RolePermission;
import com.fangcang.rfp.common.entity.UPermission;
import com.fangcang.rfp.common.service.PermissionService;
import com.fangcang.rfp.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class PermissionServiceImpl implements PermissionService {

	@Autowired
	UPermissionDao permissionDao;

	@Autowired
	UUserDao userDao;

	@Autowired
	URolePermissionDao rolePermissionDao;

	@Autowired
	UUserRoleDao userRoleDao;
	
	@Override
	public int deleteByPrimaryKey(Long id) {
		return permissionDao.deleteByPrimaryKey(id);
	}

	@Override
	public Permission insertSelective(Permission record) {
		//添加权限
		permissionDao.insertSelective(record);
		//每添加一个权限，都往【系统管理员 	888888】里添加一次。保证系统管理员有最大的权限
		executePermission(new Long(1), String.valueOf(record.getPermissionId()));
		return record;
	}

	@Override
	public UPermission selectByPrimaryKey(Long id) {
		return permissionDao.selectByPrimaryKey(id);
	}

	@Override
	public int updateByPrimaryKey(Permission record) {
		return permissionDao.updateByPrimaryKey(record);
	}

	@Override
	public int updateByPrimaryKeySelective(Permission record) {
		return permissionDao.updateByPrimaryKeySelective(record);
	}

	@Override
	public Map<String, Object> deletePermissionById(String ids) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			int successCount=0,errorCount=0;
			String resultMsg ="删除%s条，失败%s条";
			String[] idArray = new String[]{};
			if(StringUtils.contains(ids, ",")){
				idArray = ids.split(",");
			}else{
				idArray = new String[]{ids};
			}
			
			c:for (String idx : idArray) {
				Long id = new Long(idx);
				List<RolePermission> rolePermissions= rolePermissionDao.findRolePermissionByPid(id);
				if (null != rolePermissions && rolePermissions.size() > 0)
				{
					errorCount += rolePermissions.size();
					continue c;
				}
				else
				{
					successCount += this.deleteByPrimaryKey(id);
				}
			}
			resultMap.put("status", 200);
			//如果有成功的，也有失败的，提示清楚。
			if(successCount > 0 && errorCount > 0){
				resultMsg = String.format(resultMsg, successCount,errorCount);
			}else{
				resultMsg = "操作成功";
			}
			resultMap.put("resultMsg", resultMsg);
		} catch (Exception e) {
			resultMap.put("status", 500);
			resultMap.put("message", "删除出现错误，请刷新后再试！");
		}
		return resultMap;
	}

	@Override
	public List<PermissionBo> selectPermissionById(Long id) {
		return permissionDao.selectPermissionById(id);
	}

	@Override
	public Map<String, Object> addPermission2Role(Long roleId, String ids) {
		//先删除原有的。
		rolePermissionDao.deleteByRid(roleId);
		return executePermission(roleId, ids);
	}
	/**
	 * 处理权限 
	 * @param roleId
	 * @param ids
	 * @return
	 */
	private Map<String, Object> executePermission(Long roleId, String ids){
		Map<String,Object> resultMap = new HashMap<String, Object>();
		int count = 0;
		try {
			//如果ids,permission 的id 有值，那么就添加。没值象征着：把这个角色（roleId）所有权限取消。
			if(StringUtils.isNotBlank(ids)){
				String[] idArray = null;
				
				//这里有的人习惯，直接ids.split(",") 都可以，我习惯这么写。清楚明了。
				if(StringUtils.contains(ids, ",")){
					idArray = ids.split(",");
				}else{
					idArray = new String[]{ids};
				}
				//添加新的。
				for (String pid : idArray) {
					//这里严谨点可以判断，也可以不判断。这个{@link StringUtils 我是重写了的} 
					if(StringUtils.isNotBlank(pid)){
						RolePermission entity = new RolePermission(roleId,new Long(pid));
						count += rolePermissionDao.insertSelective(entity);
					}
				}
			}
			resultMap.put("status", 200);
			resultMap.put("message", "操作成功");
		} catch (Exception e) {
			resultMap.put("status", 200);
			resultMap.put("message", "操作失败，请重试！");
		}
		//清空拥有角色Id为：roleId 的用户权限已加载数据，让权限数据重新加载
		List<Long> userIds = userRoleDao.findUserIdByRoleId(roleId);
		
//		TokenManager.clearUserAuthByUserId(userIds);
		resultMap.put("count", count);
		return resultMap;
		
	}

	@Override
	public Map<String, Object> deleteByRids(String roleIds) {
		Map<String,Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap.put("roleIds", roleIds);
			rolePermissionDao.deleteByRids(resultMap);
			resultMap.put("status", 200);
			resultMap.put("message", "操作成功");
		} catch (Exception e) {
			resultMap.put("status", 200);
			resultMap.put("message", "操作失败，请重试！");
		}
		return resultMap;
	}

	@Override
	public Set<String> findPermissionByUserId(Long userId) {
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("userId", userId);
		return permissionDao.findPermissionByUserId(map);
	}

	
}
