package com.fangcang.rfp.common.exception;

import com.fangcang.rfp.common.enums.ReturnResultEnum;

/**
 * <AUTHOR>
 * @ClassName ParameterException
 * @Description 参数异常类
 * @createTime 2022-08-30 16:57:16
 * @Param
 * @return
 */
@SuppressWarnings("serial")
public class ParameterException extends RuntimeException {

    /**
     * 异常编码
     */
    private ReturnResultEnum errorCode;

    public ReturnResultEnum getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(ReturnResultEnum errorCode) {
        this.errorCode = errorCode;
    }

    public ParameterException() {
        super();
    }

    public ParameterException(String message) {
        super(message);
    }

    public ParameterException(String message, Throwable cause) {
        super(message, cause);
    }

    public ParameterException(Throwable cause) {
        super(cause);
    }

    public ParameterException(ReturnResultEnum errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public ParameterException(ReturnResultEnum errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

}
