package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.HotelCertVO;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.entity.HotelRelatedCert;
import com.fangcang.rfp.common.entity.ProjectIntentHotel;

import java.util.List;

public interface HotelRelatedCertService {

    public List<HotelRelatedCert> queryListByHotelId(Long hotelId);

    public int insertHotelRelatedCert(HotelRelatedCert hotelRelatedCert);

    public Response updateHotelRelatedCert(Long projectIntentId, Long hotelId, HotelCertVO hotelCertVO, String creator);

    public List<HotelRelatedCert> queryListByHotelRelatedCertIds(List<Long> hotelRelatedCertIds);

    public boolean setBidHotelLastCertInfo(ProjectIntentHotel projectIntentHotel);

    public boolean setBidHotelCertInfo(ProjectIntentHotel projectIntentHotel);


}
