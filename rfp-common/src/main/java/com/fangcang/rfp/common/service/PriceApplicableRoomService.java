package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse;
import com.fangcang.rfp.common.dto.response.RoomInfoResponse;
import com.fangcang.rfp.common.entity.PriceApplicableRoom;
import com.fangcang.rfp.common.entity.ProjectIntentHotel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PriceApplicableRoomService {

    List<PriceApplicableRoom> selectPriceApplicableRoomByPriceCode(Long priceCode);

    List<PriceApplicableRoomInfoResponse> selectPriceApplicableRoom(@Param("projectIntentHotelId") Long projectIntentHotelId, @Param("roomLevelNo")Integer roomLevelNo);

    Response validateRoomType(Long projectIntentHotelId, Integer roomLevelNo, List<PriceApplicableRoomInfoResponse> priceApplicableRoomResponseList);

    Response updatePriceLevelRoom(Long projectIntentHotelId, Integer roomLevelNo, List<PriceApplicableRoomInfoResponse> priceApplicableRoomResponseList, String creator,
                                  Integer totalRoomCount, Integer levelRoomType1Count, Integer levelRoomType2Count);

    void uploadPriceLevelRoom(UserDTO userDTO, ProjectIntentHotel projectIntentHotel, Integer roomLevelNo,
                              List<PriceApplicableRoom> priceApplicableRoomList, Map<Long, RoomInfoResponse> roomInfoMap);


    Long selectNextApplicableRoomId();
}
