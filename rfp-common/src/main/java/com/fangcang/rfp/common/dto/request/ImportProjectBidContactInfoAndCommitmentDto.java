package com.fangcang.rfp.common.dto.request;

import com.fangcang.rfp.common.entity.ProjectCustomBidStrategy;


import java.util.List;

/**
 * 导入批量更新联系信息及承诺信息DTO
 */

public class ImportProjectBidContactInfoAndCommitmentDto {

    /**
     * 房仓酒店 id
     */
    private Long hotelId;

    /**
     * 项目意向酒店id
     */
    private Long projectIntentHotelId;

    //酒店投标联系人姓名
    private String bidContactName;

    //酒店投标联系人电话
    private String bidContactMobile;

    //酒店投标联系人邮箱
    private String bidContactEmail;

    //酒店集团投标联系人姓名
    private String hotelGroupBidContactName;

    //酒店集团投标联系人电话
    private String hotelGroupBidContactMobile;

    //酒店集团投标联系人邮箱
    private String hotelGroupBidContactEmail;

    /**
     * 酒店是否支持员工到店付款：1-承诺支持，0-不承诺
     */
    private Integer supportPayAtHotel;

    /**
     * 酒店是否支持公司统一支付：1-承诺支持，0-不承诺
     */
    private Integer supportCoPay;

    /**
     * 酒店是否支持到店付免担保：1-承诺支持，0-不承诺
     */
    private Integer supportNoGuarantee;

    /**
     * 酒店是否支持提供入住明细信息：1-承诺支持，0-不承诺
     */
    private Integer supportCheckinInfo;

    /**
     * 酒店是否支持提前离店按实际入住金额收款：1-承诺支持，0-不承诺
     */
    private Integer supportPayEarlyCheckout;

    /**
     * 酒店是否支持wifi (1: 承若支持 0：不承若)
     */
    private Integer supportWifi;

    /**
     * 酒店报价是否包含税费和服务费 （1：承若 0：不承若）
     */
    private Integer supportIncludeTaxService;

    /**
     * 项目自定义采购报价策略列表
     */
    private List<ProjectCustomBidStrategy> projectCustomBidStrategies;


    public Long getHotelId() {
        return hotelId;
    }

    public void setHotelId(Long hotelId) {
        this.hotelId = hotelId;
    }

    public Long getProjectIntentHotelId() {
        return projectIntentHotelId;
    }

    public void setProjectIntentHotelId(Long projectIntentHotelId) {
        this.projectIntentHotelId = projectIntentHotelId;
    }

    public String getBidContactName() {
        return bidContactName;
    }

    public void setBidContactName(String bidContactName) {
        this.bidContactName = bidContactName;
    }

    public String getBidContactMobile() {
        return bidContactMobile;
    }

    public void setBidContactMobile(String bidContactMobile) {
        this.bidContactMobile = bidContactMobile;
    }

    public String getBidContactEmail() {
        return bidContactEmail;
    }

    public void setBidContactEmail(String bidContactEmail) {
        this.bidContactEmail = bidContactEmail;
    }

    public String getHotelGroupBidContactName() {
        return hotelGroupBidContactName;
    }

    public void setHotelGroupBidContactName(String hotelGroupBidContactName) {
        this.hotelGroupBidContactName = hotelGroupBidContactName;
    }

    public String getHotelGroupBidContactMobile() {
        return hotelGroupBidContactMobile;
    }

    public void setHotelGroupBidContactMobile(String hotelGroupBidContactMobile) {
        this.hotelGroupBidContactMobile = hotelGroupBidContactMobile;
    }

    public String getHotelGroupBidContactEmail() {
        return hotelGroupBidContactEmail;
    }

    public void setHotelGroupBidContactEmail(String hotelGroupBidContactEmail) {
        this.hotelGroupBidContactEmail = hotelGroupBidContactEmail;
    }

    public Integer getSupportPayAtHotel() {
        return supportPayAtHotel;
    }

    public void setSupportPayAtHotel(Integer supportPayAtHotel) {
        this.supportPayAtHotel = supportPayAtHotel;
    }

    public Integer getSupportCoPay() {
        return supportCoPay;
    }

    public void setSupportCoPay(Integer supportCoPay) {
        this.supportCoPay = supportCoPay;
    }

    public Integer getSupportNoGuarantee() {
        return supportNoGuarantee;
    }

    public void setSupportNoGuarantee(Integer supportNoGuarantee) {
        this.supportNoGuarantee = supportNoGuarantee;
    }

    public Integer getSupportCheckinInfo() {
        return supportCheckinInfo;
    }

    public void setSupportCheckinInfo(Integer supportCheckinInfo) {
        this.supportCheckinInfo = supportCheckinInfo;
    }

    public Integer getSupportPayEarlyCheckout() {
        return supportPayEarlyCheckout;
    }

    public void setSupportPayEarlyCheckout(Integer supportPayEarlyCheckout) {
        this.supportPayEarlyCheckout = supportPayEarlyCheckout;
    }

    public Integer getSupportWifi() {
        return supportWifi;
    }

    public void setSupportWifi(Integer supportWifi) {
        this.supportWifi = supportWifi;
    }

    public Integer getSupportIncludeTaxService() {
        return supportIncludeTaxService;
    }

    public void setSupportIncludeTaxService(Integer supportIncludeTaxService) {
        this.supportIncludeTaxService = supportIncludeTaxService;
    }

    public List<ProjectCustomBidStrategy> getProjectCustomBidStrategies() {
        return projectCustomBidStrategies;
    }

    public void setProjectCustomBidStrategies(List<ProjectCustomBidStrategy> projectCustomBidStrategies) {
        this.projectCustomBidStrategies = projectCustomBidStrategies;
    }
}