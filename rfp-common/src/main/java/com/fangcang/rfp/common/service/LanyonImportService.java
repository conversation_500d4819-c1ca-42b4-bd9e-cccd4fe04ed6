package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.entity.LanyonImportColumn;
import com.fangcang.rfp.common.entity.LanyonImportData;
import com.fangcang.rfp.common.entity.ProjectLanyonViewKeys;

import java.util.List;

public interface LanyonImportService {

    public List<LanyonImportColumn> selectLanyonImportColumns();


    public List<LanyonImportColumn> selectLanyonImportColumnsByDisplayOrder();


    public Response insertLanyonViewKeys(Long projectId,  // 基础信息
                                         String baseInfo,
                                        String hotelVerify,
                                        String hotelFacilities,
                                        String bidInfo,
                                        String meetingRoomBidInfo,
                                        String longBidInfo,
                                        String hotelService,
                                        String userDefined,
                                        String mtgUserDefined,
                                        String larUnApplicableDayInfo,
                                        String baseServiceFee, String operator);

    public Response updateLanyonViewKeys(Long lanyonViewKeysIs, Long projectId,  String baseInfo,
                                         String hotelVerify,
                                         String hotelFacilities,
                                         String bidInfo,
                                         String meetingRoomBidInfo,
                                         String longBidInfo,
                                         String hotelService,
                                         String userDefined,
                                         String mtgUserDefined,
                                         String larUnApplicableDayInfo,
                                         String baseServiceFee, String operator);

    public ProjectLanyonViewKeys getProjectLanyonViewKeys(Long projectId);

    public LanyonImportData insertOrUpdateLanyonImportData(LanyonImportData lanyonImportData);

    public LanyonImportData getLanyonImportData(Long projectId, Long hotelId, Integer dataType);


}
