package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.OrgHotelGroupBrandDto;
import com.fangcang.rfp.common.dto.common.OrgDTO;
import com.fangcang.rfp.common.dto.common.OrgRelatedHotelDTO;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.AddOrgRelatedHotelRequest;
import com.fangcang.rfp.common.dto.request.DeleteOrgRelatedHotelRequest;
import com.fangcang.rfp.common.dto.request.OrgRelatedHotelRequest;
import com.fangcang.rfp.common.dto.request.OrgRequest;
import com.fangcang.rfp.common.entity.Org;
import com.fangcang.rfp.common.entity.OrgRelatedHotel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @类    名:OrgService
 * @简单描述: 机构信息业务
 * @详细描述：
 * @作    者:yangjunjun
 * @日    期：2016年12月16日下午2:57:36
 *
 */
public interface OrgService
{

	/**
	 * 新增或者更新机构
	 * @param orgDTO
	 * @return
	 * @throws Exception
	 */
	Response editOrg(OrgDTO orgDTO);

	/**
	 * 新增机构
	 * @param orgDTO
	 * @throws Exception
	 */
	Response addOrg(OrgDTO orgDTO) ;

	/**
	 * 更新机构
	 * @param orgDTO
	 * @return
	 * @throws Exception
	 */
	Response updateOrg(OrgDTO orgDTO);

	/**
	 * 参数校验 0：新增  1：更新
	 * @param orgDTO
	 * @param response
	 * @param type
	 * @return
	 */
	boolean isOrgParamValid(OrgDTO orgDTO, Response response, int type);

	/**
	 * 根据酒店ID查询机构(每个酒店只能注册绑定一个机构)
	 * @param hotelId
	 * @return
	 */
	Org getOrgByHotelId(Long hotelId);

	/**
	 * 根据酒店集团ID查询机构(每个酒店只能注册绑定一个机构)
	 * @param groupId
	 * @return
	 */
	List<Org>  getHotelGroupOrgByGroupId(Long groupId);


	/**
	 * 平台管理删除机构信息
	 * @param orgDTO
	 * @return
	 */
	Response deleteOrg(OrgDTO orgDTO);


	/**
	 * 通过机构名称、当前登录用户联想搜索机构列表
	 * @param orgRequest
	 * @param orgRequest
	 * @return
	 */
	Response queryOrgByOrgName(OrgRequest orgRequest) ;

	/**
	 * @[简要描述]查询机构
	 * @[详细描述]
	 * @作    者:quanhb
	 * @日    期：2017年9月25日
	 */
	Org selectByPrimaryKey(Long orgId);
	/**
	 * 平台端分页查询机构信息列表
	 * @param orgRequest
	 * @return
	 * @throws Exception
	 */
	Response selectOrgInfoPage(UserDTO userDTO, OrgRequest orgRequest) ;

	/**
	 * 企业和酒店管理员才可查看单个机构信息
	 * @param orgId
	 * @return
	 * @throws Exception
	 */
	Response selectOrgInfo(UserDTO userDTO, Long orgId);

	/**
	 *  统计不同结构类型机构数
	 * @return
	 */
	Response getOrgCount();

	/**
	 * 批量注册酒店机构
	 * @param uploadFile
	 * @param userDTO
	 * @return
	 */
	Response batchRegistHotelOrg(MultipartFile uploadFile, UserDTO userDTO) throws Exception;

	/**
	 * 下载注册酒店机构模板
	 * @param httpServletResponse
	 */
	void downloadRegistHotelOrgTemplate(HttpServletResponse httpServletResponse) throws Exception;

	/**
	 * 查询酒店机构关联酒店信息
	 * @return
	 */
	Response queryOrgRelatedHotelList(OrgRelatedHotelRequest request);

	/**
	 * 查询酒店机构关联酒店信息
	 * @return
	 */
	List<OrgRelatedHotelDTO> queryOrgRelatedHotelListByOrgId(Long orgId);


	/**
	 * 新增酒店机构关联酒店信息
	 * @return
	 */
	Response addOrgRelatedHotel(AddOrgRelatedHotelRequest request, UserDTO userDTO);

	Response deleteOrgRelatedHotel(DeleteOrgRelatedHotelRequest request, UserDTO userDTO);

	/**
	 * 通过合作伙伴机构id查找机构用户
	 * @return
	 */
	OrgDTO selectByPartner(String partnerChannelCode, String partnerOrgId);

	/**
	 * 查询酒店集团机构品牌信息
	 * @param orgId
	 * @return
	 */
	List<OrgHotelGroupBrandDto> queryHotelGroupBrandList(Long orgId);

	void deleteCacheByOrgId(Long orgId);

	/**
	 * 根据合作商channel id机构
	 * @param channelPartnerId
	 * @return
	 */
	List<OrgDTO> queryChannelPartnerRelatedDistributorOrgList(Long channelPartnerId);

	/**
	 * 设置机构证书信息
	 */
	void setOrgRelatedCertInfo(List<OrgRelatedHotelDTO> orgRelatedHotelDTOS);

}
