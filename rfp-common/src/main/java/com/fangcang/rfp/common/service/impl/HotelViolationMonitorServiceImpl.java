package com.fangcang.rfp.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcang.enums.BookTypeEnum;
import com.fangcang.enums.RoomStateEnum;
import com.fangcang.hotel.delivery.tmc.common.tmchub.api.request.SignatureHelpRequestDto;
import com.fangcang.hotel.delivery.tmc.common.tmchub.api.service.TmcHubApiManager;
import com.fangcang.hotel.sync.common.api.dto.mapping.RoomMappingDto;
import com.fangcang.rfp.common.cache.CachedPriceMonitorConfigManager;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.constants.RedisLockConstant;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.CompareFullRoomClientPriceResult;
import com.fangcang.rfp.common.dto.HotelPriceMonitorCheckInfo;
import com.fangcang.rfp.common.dto.OrgHotelGroupBrandDto;
import com.fangcang.rfp.common.dto.QueryTmcHubResult;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.dto.response.count.HotelViolationMonitorStatusCountResponse;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.*;
import com.fangcang.tmc.hub.api.request.product.ProductDetailRequest;
import com.fangcang.tmc.hub.api.response.product.*;
import com.fangcang.util.DateUtil;
import com.fangcang.util.JsonUtil;
import com.fangcang.util.ListUtil;
import com.fangcang.util.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/1/22 19:30
 */
@Service
public class HotelViolationMonitorServiceImpl implements HotelViolationMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(HotelViolationMonitorServiceImpl.class);

    @Autowired
    private HotelViolationsMonitorDao hotelViolationsMonitorDao;

    @Autowired
    private HotelViolationsDailyPriceDao hotelViolationsDailyPriceDao;

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;

    @Autowired
    private PriceApplicableDayService priceApplicableDayService;

    @Autowired
    private HotelViolationsConfigDao hotelViolationsConfigDao;

    @Autowired
    private HotelPriceMonitorDao hotelPriceMonitorDao;

    @Autowired
    private TmcHubApiManager tmcHubApiManager;

    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private DisHotelDailyOrderDao disHotelDailyOrderDao;

    @Autowired
    private ViolationsRemindConfigDao violationsRemindConfigDao;

    @Autowired
    private PriceMonitorConfigDao priceMonitorConfigDao;

    @Autowired
    private OrderMonitorConfigDao orderMonitorConfigDao;

    @Autowired
    private SendHtmlService sendHtmlService;

    @Autowired
    private HotelViolationOrderDetailDao hotelViolationOrderDetailDao;

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private ViolationsFullMonitorLogDao violationsFulMonitorLogDao;

    @Autowired
    private HotelViolationsDailyCheckDao hotelViolationsDailyCheckDao;

    @Autowired
    private MonitorCallTmchubErrorDao monitorCallTmchubErrorDao;

    @Autowired
    private ProjectIntentHotelService projectIntentHotelService;

    @Autowired
    private Executor violationMonitorExecutor;

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;

    @Autowired
    private ProjectHotelPriceGroupDao hotelPriceGroupDao;

    @Autowired
    private ProjectHotelWhiteDao projectHotelWhiteDao;

    @Autowired
    private WorkDateService workDateService;

    @Autowired
    private HotelDao hotelDao;

    @Autowired
    private CachedPriceMonitorConfigManager cachedPriceMonitorConfigManager;

    @Autowired
    private OrderExcludeSupConfigDao orderExcludeSupConfigDao;

    @Autowired
    private ProjectAddedHotelDao projectAddedHotelDao;

    @Autowired
    private OrgHotelGroupBrandDao orgHotelGroupBrandDao;

    @Autowired
    private ProjectAddedHotelService projectAddedHotelService;

    @Autowired
    private OrgDao orgDao;

    @Override
    public Response queryHotelViolationMonitor(QueryHotelViolationMonitorRequest queryHotelViolationMonitorRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (queryHotelViolationMonitorRequest.getStartViolationDate() != null) {
            queryHotelViolationMonitorRequest.setStartViolationDate(DateUtil.dateToString(DateUtil.stringToDate(queryHotelViolationMonitorRequest.getStartViolationDate())));
        }
        if (queryHotelViolationMonitorRequest.getEndViolationDate() != null) {
            queryHotelViolationMonitorRequest.setEndViolationDate(DateUtil.dateToString(DateUtil.getDate(DateUtil.stringToDate(queryHotelViolationMonitorRequest.getEndViolationDate()), 1, 0)));
        }

        if (queryHotelViolationMonitorRequest.isPage()) {
            PageHelper.startPage(queryHotelViolationMonitorRequest.getCurrentPage(), queryHotelViolationMonitorRequest.getPageSize());
        }
        List<QueryHotelViolationMonitorResponse> queryHotelViolationMonitorResponses = hotelViolationsMonitorDao.queryHotelViolationMonitorInfo(queryHotelViolationMonitorRequest);
        if (CollectionUtils.isNotEmpty(queryHotelViolationMonitorResponses)) {
            List<Long> hotelIdList = new ArrayList<>(queryHotelViolationMonitorResponses.stream().map(QueryHotelViolationMonitorResponse::getHotelId).collect(Collectors.toSet()));
            Map<Long, HotelWithGroupBrandInfoResponse> hotelWithGroupBrandInfoResponseMap = hotelDao.selectHotelWithGroupBrandInfoList(hotelIdList).stream().collect(Collectors.toMap(HotelWithGroupBrandInfoResponse::getHotelId, Function.identity()));
            queryHotelViolationMonitorResponses.forEach(item -> {
                Date createTime = item.getCreateTime();
                Date processingDate = item.getProcessingDate();
                if (createTime != null && processingDate != null) {
                    long minute = DateUtil.getMinute(createTime, processingDate);
                    item.setDuration(minute / 60 + "小时" + minute % 60 + "分钟");
                }
                HotelWithGroupBrandInfoResponse hotelWithGroupBrandInfoResponse = hotelWithGroupBrandInfoResponseMap.get(item.getHotelId());
                if(hotelWithGroupBrandInfoResponse != null) {
                    item.setCityCode(hotelWithGroupBrandInfoResponse.getCityCode());
                    item.setCityName(hotelWithGroupBrandInfoResponse.getCityName());
                    item.setProvince(hotelWithGroupBrandInfoResponse.getProvince());
                    item.setProvinceName(hotelWithGroupBrandInfoResponse.getProvinceName());
                    item.setBrandId(hotelWithGroupBrandInfoResponse.getBrandId());
                    item.setBrandName(hotelWithGroupBrandInfoResponse.getBrandName());
                }
            });

            // 监控id集合
            List<Long> monitorIdList = queryHotelViolationMonitorResponses.stream().map(QueryHotelViolationMonitorResponse::getViolationsMonitorId).collect(Collectors.toList());
            // 获取 处理标签 集合  分批处理
            Integer partialLimit = 1000;
            int limit = (monitorIdList.size()+partialLimit -1)/partialLimit;
            List<List<Long>> mglist = new ArrayList<>();
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                mglist.add(monitorIdList.stream().skip(i * partialLimit).limit(partialLimit).collect(Collectors.toList()));
            });
            List<LabelProcessRecordResponse> processRecords = new ArrayList<>();
            for (List idList : mglist) {
                List<LabelProcessRecordResponse> records = hotelViolationsMonitorDao.getLabelProcessRecordsByIds(idList);
                processRecords.addAll(records);
            }
            // 对 处理标签集合 进行分组
            Map<Long, List<LabelProcessRecordResponse>> processRecordsGroup = processRecords.stream().collect(Collectors.groupingBy(LabelProcessRecordResponse::getViolationsMonitorId));
            // 循环赋值 给 queryHotelViolationMonitorResponses 对象
            for (QueryHotelViolationMonitorResponse queryHotelViolationMonitorRespons : queryHotelViolationMonitorResponses) {
                queryHotelViolationMonitorRespons.setProcessRecords(processRecordsGroup.get(queryHotelViolationMonitorRespons.getViolationsMonitorId()));
            }
        }
        response.setData(PageUtil.makePageResult(queryHotelViolationMonitorResponses));
        return response;
    }

    @Override
    public Response queryHotelViolationMonitorStatusCount(HotelViolationMonitorStatusCountRequest statusCountRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (statusCountRequest.getStartViolationDate() != null) {
            statusCountRequest.setStartViolationDate(DateUtil.dateToString(DateUtil.stringToDate(statusCountRequest.getStartViolationDate())));
        }
        if (statusCountRequest.getEndViolationDate() != null) {
            statusCountRequest.setEndViolationDate(DateUtil.dateToString(DateUtil.getDate(DateUtil.stringToDate(statusCountRequest.getEndViolationDate()), 1, 0)));
        }
        HotelViolationMonitorStatusCountResponse statusCountResult = hotelViolationsMonitorDao.queryHotelViolationMonitorStatusCount(statusCountRequest);
        if (statusCountResult == null) {
            statusCountResult = new HotelViolationMonitorStatusCountResponse();
            statusCountResult.setRepliedCount(0);
            statusCountResult.setUnsettledCount(0);
            statusCountResult.setPendingStatusCount(0);
        }

        response.setData(statusCountResult);
        return response;
    }

    @Override
    public Response queryHotelViolationMonitorStat(QueryHotelViolationMonitorStatRequest statRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        if (statRequest.isPage()) {
            PageHelper.startPage(statRequest.getCurrentPage(), statRequest.getPageSize());
        }
        // 配合前端特殊处理
        if (statRequest.getProcessStatus() != null && statRequest.getProcessStatus().intValue() == 0) {
            statRequest.setProcessStatus(null);
        }
        List<QueryHotelViolationMonitorStatResponse> violationMonitorStatResponses = hotelViolationsMonitorDao.queryHotelViolationMonitorStat(statRequest);
        response.setData(PageUtil.makePageResult(violationMonitorStatResponses));
        return response;
    }


    @Override
    public Response queryHotelViolationCheckPassStat(QueryHotelViolationCheckPassStatRequest queryHotelViolationCheckPassStat) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        if (queryHotelViolationCheckPassStat.isPage()) {
            PageHelper.startPage(queryHotelViolationCheckPassStat.getCurrentPage(), queryHotelViolationCheckPassStat.getPageSize());
        }
        List<QueryHotelViolationCheckPassStatResponse> violationCheckPassStatResponses = hotelViolationsMonitorDao.queryHotelViolationCheckPassStat(queryHotelViolationCheckPassStat);

        // 查询酒店，酒店集团，品牌信息
        Set<Long> hotelIdSet = violationCheckPassStatResponses.stream().map(QueryHotelViolationCheckPassStatResponse::getHotelId).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(hotelIdSet)) {
            List<HotelWithGroupBrandInfoResponse> hotelWithGroupBrandInfoResponses = hotelDao.selectHotelWithGroupBrandInfoList(new ArrayList<>(hotelIdSet));
            Map<Long, HotelWithGroupBrandInfoResponse> hotelWithGroupBrandInfoResponseMap = hotelWithGroupBrandInfoResponses.stream().collect(Collectors.toMap(HotelWithGroupBrandInfoResponse::getHotelId, Function.identity()));
            violationCheckPassStatResponses.forEach(item -> {
                HotelWithGroupBrandInfoResponse hotelWithGroupBrandInfoResponse = hotelWithGroupBrandInfoResponseMap.get(item.getHotelId());
                item.setHotelName(hotelWithGroupBrandInfoResponse.getHotelName());
                item.setCityCode(hotelWithGroupBrandInfoResponse.getCityCode());
                item.setCityName(hotelWithGroupBrandInfoResponse.getCityName());
                item.setBrandId(hotelWithGroupBrandInfoResponse.getBrandId());
                item.setBrandName(hotelWithGroupBrandInfoResponse.getBrandName());
                item.setGroupId(hotelWithGroupBrandInfoResponse.getGroupId());
                item.setHotelGroupName(hotelWithGroupBrandInfoResponse.getHotelGroupName());
                item.setProvince(hotelWithGroupBrandInfoResponse.getProvince());
                item.setProvinceName(hotelWithGroupBrandInfoResponse.getProvinceName());
            });
        }

        // 设置比例
        violationCheckPassStatResponses.forEach(item -> {
            int totalPassDailyCount = item.getRestorePriceDayCount() + item.getTheSameLevelDayCount() + item.getClientRoomClosedDayCount();
            item.setSolvedRisk(item.getTotalViolationCount() == 0 ? BigDecimal.ZERO : new BigDecimal(item.getSolvedCount()).divide(new BigDecimal(item.getTotalViolationCount()),3, RoundingMode.HALF_UP ));
            item.setRestorePriceDayRisk(totalPassDailyCount == 0 ? BigDecimal.ZERO : new BigDecimal(item.getRestorePriceDayCount()).divide(new BigDecimal(totalPassDailyCount),3, RoundingMode.HALF_UP ));
            item.setTheSameLevelDayRisk(totalPassDailyCount == 0 ? BigDecimal.ZERO : new BigDecimal(item.getTheSameLevelDayCount()).divide(new BigDecimal(totalPassDailyCount),3, RoundingMode.HALF_UP ));
            item.setClientRoomClosedDayRisk(totalPassDailyCount == 0 ? BigDecimal.ZERO : new BigDecimal(item.getClientRoomClosedDayCount()).divide(new BigDecimal(totalPassDailyCount),3, RoundingMode.HALF_UP ));
            item.setTotalPassDailyCount(item.getRestorePriceDayCount() + item.getTheSameLevelDayCount() + item.getClientRoomClosedDayCount());
            item.setTotalPassDailyRisk(item.getTotalViolationDayCount() == 0 ? BigDecimal.ZERO : new BigDecimal(item.getTotalPassDailyCount()).divide(new BigDecimal(item.getTotalViolationDayCount()),3, RoundingMode.HALF_UP ));
        });

        response.setData(PageUtil.makePageResult(violationCheckPassStatResponses));

        return response;
    }


    @Override
    public void exportHotelViolationCheckPassStat(QueryHotelViolationCheckPassStatRequest queryHotelViolationCheckPassStatRequest, HttpServletResponse httpServletResponse) throws Exception {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("省份");
            headerRow.createCell(1).setCellValue("城市");
            headerRow.createCell(2).setCellValue("酒店名称");
            headerRow.createCell(3).setCellValue("是否关注");
            headerRow.createCell(4).setCellValue("项目名称");
            headerRow.createCell(5).setCellValue("酒店集团名称");
            headerRow.createCell(6).setCellValue("品牌");
            headerRow.createCell(7).setCellValue("项目期内累计间夜");
            headerRow.createCell(8).setCellValue("酒店服务分");
            headerRow.createCell(9).setCellValue("违规次数");
            headerRow.createCell(10).setCellValue("已解决次数");
            headerRow.createCell(11).setCellValue("解决率");
            headerRow.createCell(12).setCellValue("违规日历天数");
            headerRow.createCell(13).setCellValue("恢复原价可订天数");
            headerRow.createCell(14).setCellValue("恢复原价可订天数比例");
            headerRow.createCell(15).setCellValue("同档其他房型可订天数");
            headerRow.createCell(16).setCellValue("同档其他房型可订天数比例");
            headerRow.createCell(17).setCellValue("关闭C端房态天数");
            headerRow.createCell(18).setCellValue("关闭C端房态天数比例");
            headerRow.createCell(19).setCellValue("日历总复核通过天数");
            headerRow.createCell(20).setCellValue("日历总复核通过比例");

            int currentPage = 1;
            int pageSize = 1000;
            boolean dataExists = true;

            while (dataExists) {
                queryHotelViolationCheckPassStatRequest.setCurrentPage(currentPage);
                queryHotelViolationCheckPassStatRequest.setPageSize(pageSize);
                Response response = queryHotelViolationCheckPassStat(queryHotelViolationCheckPassStatRequest);
                if (response.getData() == null) {
                    break;
                }

                PageResult<QueryHotelViolationCheckPassStatResponse> pageResult = (PageResult<QueryHotelViolationCheckPassStatResponse>) response.getData();
                if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }

                assembleCheckPassStatExcel(pageResult.getList(), sheet);

                currentPage++;

                if (currentPage > pageResult.getTotalPage()) {
                    dataExists = false;
                }
            }
            String fileName = "酒店自动复核统计" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("导出自动复核统计异常：" + JSON.toJSONString(queryHotelViolationCheckPassStatRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }


    @Override
    public Response queryViolationMonitorStatLabelRecords(QueryHotelViolationMonitorStatLabelRecordsRequest recordsRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (recordsRequest.isPage()) {
            PageHelper.startPage(recordsRequest.getCurrentPage(), recordsRequest.getPageSize());
        }
        List<StatLabelProcessRecordResponse> statProcessRecords =  hotelViolationsMonitorDao.getStatLabelProcessRecord(recordsRequest.getProjectIntentHotelId());
        response.setData(PageUtil.makePageResult(statProcessRecords));
        return response;
    }

    @Override
    public void exportHotelViolationStat(QueryHotelViolationMonitorStatRequest statRequest, HttpServletResponse httpServletResponse) throws Exception {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("省份");
            headerRow.createCell(1).setCellValue("城市");
            headerRow.createCell(2).setCellValue("酒店名称");
            headerRow.createCell(3).setCellValue("是否关注");
            headerRow.createCell(4).setCellValue("项目名称");
            headerRow.createCell(5).setCellValue("酒店集团名称");
            headerRow.createCell(6).setCellValue("品牌");
            headerRow.createCell(7).setCellValue("已成交间夜");
            headerRow.createCell(8).setCellValue("酒店服务分");
            headerRow.createCell(9).setCellValue("违规次数");
            headerRow.createCell(10).setCellValue("提醒等级");
            headerRow.createCell(11).setCellValue("处理状态");
            headerRow.createCell(12).setCellValue("备注回复");
            headerRow.createCell(13).setCellValue("平台履约跟进人");
            headerRow.createCell(14).setCellValue("操作人");
            headerRow.createCell(15).setCellValue("操作人所属机构类型");
            headerRow.createCell(16).setCellValue("操作时间");
            headerRow.createCell(17).setCellValue("最后违规时间");

            int currentPage = 1;
            int pageSize = 1000;
            boolean dataExists = true;

            while (dataExists) {
                statRequest.setCurrentPage(currentPage);
                statRequest.setPageSize(pageSize);
                Response response = queryHotelViolationMonitorStat(statRequest);
                if (response.getData() == null) {
                    break;
                }

                PageResult<QueryHotelViolationMonitorStatResponse> pageResult = (PageResult<QueryHotelViolationMonitorStatResponse>) response.getData();
                if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }

                assembleStatExcel(pageResult.getList(), sheet);

                currentPage++;

                if (currentPage > pageResult.getTotalPage()) {
                    dataExists = false;
                }
            }
            String fileName = "酒店履约统计信息" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("导出酒店履约统计信息异常：" + JSON.toJSONString(statRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    @Override
    public Response labelProcess(LabelProcessDto labelProcessDto, Integer orgType) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (labelProcessDto.getViolationsMonitorId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        // 如果是平台 企业 校验状态 是否符合 待解决 未解决 已解决三个状态
        if (orgType.intValue() == OrgTypeEnum.PLATFORM.key.intValue() || orgType.intValue() == OrgTypeEnum.DISTRIBUTOR.key.intValue()) {
            Integer processingStatus = labelProcessDto.getProcessingStatus();
            if (processingStatus == null || HotelViolationMonitorEnum.getEnumByKey(processingStatus) == null) {
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
                return response;
            }
        } else {
            // 如果是酒店 酒店集团 标签处理，则默认给状态 已回复
            labelProcessDto.setProcessingStatus(ViolationProcessStatusEnum.REPLYED.key);
        }
        // 更新 T_HOTEL_VIOLATIONS_MONITOR 表 状态及回复信息
        hotelViolationsMonitorDao.labelProcess(labelProcessDto);
        // 新增 记录 到 T_VERIFICATION_INFO 表 记录会话 及状态
        LabelProcessRecordDto recordDto = new LabelProcessRecordDto(labelProcessDto);
        recordDto.setOrgType(orgType);
        hotelViolationsMonitorDao.labelProcessRecord(recordDto);
        return response;
    }

    @Override
    public Response monitorStatLabelProcess(MonitorStatLabelProcessDto statLabelProcessDto) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (statLabelProcessDto.getProjectIntentHotelId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        // 如果不是平台 企业 则报错  此功能只能 平台 企业
        if (statLabelProcessDto.getOrgType().intValue() != OrgTypeEnum.PLATFORM.key.intValue() && statLabelProcessDto.getOrgType().intValue() != OrgTypeEnum.DISTRIBUTOR.key.intValue()) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        // 更新 t_project_intent_hotel 表 状态
        hotelViolationsMonitorDao.monitorStatLabelProcess(statLabelProcessDto);
        // 新增 记录 到 T_HOTEL_VIOLATION_STAT_PROCESS 表 记录会话 及状态
        hotelViolationsMonitorDao.labelViolationStatProcessRecord(statLabelProcessDto);
        return response;
    }


    @Override
    public Response queryServiceDeductionDetails(QueryServiceDeductionDetailsRequest queryServiceDeductionDetailsRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        if (CollectionUtils.isEmpty(queryServiceDeductionDetailsRequest.getHotelIdList()) && queryServiceDeductionDetailsRequest.getHotelId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if (queryServiceDeductionDetailsRequest.isPage()) {
            PageHelper.startPage(queryServiceDeductionDetailsRequest.getCurrentPage(), queryServiceDeductionDetailsRequest.getPageSize());
        }
        if (queryServiceDeductionDetailsRequest.getProjectId() != null) {
            List<QueryServiceDeductionDetailsResponse> queryServiceDeductionDetails = hotelViolationsMonitorDao.queryServiceDeductionDetails(queryServiceDeductionDetailsRequest);
            response.setData(PageUtil.makePageResult(queryServiceDeductionDetails));
        } else {
            Integer status = queryServiceDeductionDetailsRequest.getStatus();
            if (status != null && HotelViolationMonitorEnum.getEnumByKey(status) == null) {
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
                return response;
            }
            //酒店角色查询扣分明细
            List<QueryServiceDeductionDetailsResponse> queryServiceDeductionDetails = hotelViolationsMonitorDao.queryServiceDeductionDetailsByHotel(queryServiceDeductionDetailsRequest);
            response.setData(PageUtil.makePageResult(queryServiceDeductionDetails));
        }
        return response;
    }

    @Override
    public Response queryHotelViolationsDailyPrice(Long violationsMonitorId) throws Exception {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        QueryViolationsDailyPriceResponse queryViolationsDailyPriceResponse = hotelViolationsMonitorDao.queryViolationMonitorDetail(violationsMonitorId);
        if (queryViolationsDailyPriceResponse == null) {
            return response;
        }

        // 查询违规监控记录ID
        Long orgViolationsMonitorId = null;
        if(queryViolationsDailyPriceResponse.getViolationType() == ViolationTypeEnum.REWARDS_OR_COMPENSATION.key &&
                queryViolationsDailyPriceResponse.getIsPaybackScore() == RfpConstant.constant_1
        ){
            orgViolationsMonitorId = queryViolationsDailyPriceResponse.getViolationsId();
            logger.info("查询违规监控ID orgViolationsMonitorId:" + orgViolationsMonitorId);
        }
        // 自动复核记录通过违规ID查询违规监控信息
        if(orgViolationsMonitorId != null){
            violationsMonitorId = orgViolationsMonitorId;
        }

        List<HotelViolationsDailyPrice> hotelViolationsDailyPrices = hotelViolationsDailyPriceDao.queryHotelViolationsDailyPrice(violationsMonitorId);
        if (CollectionUtils.isEmpty(hotelViolationsDailyPrices)) {
            logger.error("查询每日报价为空violationsMonitorId:" + violationsMonitorId);
            return response;
        }

        // 获取LRA
        Integer lraValue = null;
        for(HotelViolationsDailyPrice hotelViolationsDailyPrice : hotelViolationsDailyPrices){
            if(hotelViolationsDailyPrice != null){
                lraValue = hotelViolationsDailyPrice.getLra();
                break;
            }
        }
        if(lraValue == null){
            logger.info("查询每日报价LRA 为空violationsMonitorId: {}", JSON.toJSONString(hotelViolationsDailyPrices.get(0)));
        }
        List<DailyPriceDto> dailyPriceDtos = CommonUtil.copyList(hotelViolationsDailyPrices, DailyPriceDto.class);

        Long priceCode = hotelViolationsDailyPrices.get(0).getPriceCode();
        Long roomTypeId = hotelViolationsDailyPrices.get(0).getRoomTypeId();
        ProjectHotelPrice projectHotelPrice = projectHotelPriceDao.selectByPrimaryKey(priceCode);
        if (projectHotelPrice == null) {
            logger.error("查询报价为空：priceCode：" + priceCode + ",violationsMonitorId:" + violationsMonitorId);
            return response;
        }

        String roomName = hotelViolationsDailyPriceDao.selectRoomName(roomTypeId);
        queryViolationsDailyPriceResponse.setRoomName(roomName);

        List<PriceApplicableDay> priceApplicableDays = priceApplicableDayService.selectPriceApplicableDayListByPriceCode(priceCode);
        if (CollectionUtils.isNotEmpty(priceApplicableDays)) {
            StringBuilder applicableDay = new StringBuilder();
            for (PriceApplicableDay priceApplicableDay : priceApplicableDays) {
                applicableDay.append(DateUtil.dateToString(priceApplicableDay.getStartDate())).append("至").append(DateUtil.dateToString(priceApplicableDay.getEndDate())).append(",");
            }
            queryViolationsDailyPriceResponse.setApplicableDay(applicableDay.toString().substring(0, applicableDay.toString().length() - 1));
        }

        // 查询满房监控记录
        List<ViolationsFullMonitorLog> violationsFulMonitorLogList = violationsFulMonitorLogDao.queryList(violationsMonitorId);
        List<ViolationFullMonitorLogDto> violationFullMonitorLogDtoList = new ArrayList<>();
        for(ViolationsFullMonitorLog log : violationsFulMonitorLogList){
            ViolationFullMonitorLogDto dto = new ViolationFullMonitorLogDto();
            dto.setHotelName(queryViolationsDailyPriceResponse.getHotelName());
            if(Objects.equals(log.getRoomTypeId(), roomTypeId)){
                dto.setRoomType(roomName);
            } else {
                dto.setRoomType(hotelViolationsDailyPriceDao.selectRoomName(log.getRoomTypeId()));
            }
            dto.setSalePrice(log.getSalePrice());
            dto.setSaleDate(log.getSaleDate());
            dto.setQueryDateTime(log.getQueryDateTime());
            dto.setBreakfast(BreakfastNumEnum.getValueByKey(log.getBreakfastNum()));
            violationFullMonitorLogDtoList.add(dto);
        }
        queryViolationsDailyPriceResponse.setViolationFullMonitorLogDtoList(violationFullMonitorLogDtoList);


        // 查询复核对记录
        List<HotelViolationsDailyCheck> hotelViolationsDailyCheckList = hotelViolationsDailyCheckDao.queryHotelViolationsDailyCheck(violationsMonitorId);
        hotelViolationsDailyCheckList = hotelViolationsDailyCheckList.stream().map(this::convertHotelViolationsDailyCheck).collect(Collectors.toList());
        queryViolationsDailyPriceResponse.setHotelViolationsDailyCheckList(hotelViolationsDailyCheckList);
        queryViolationsDailyPriceResponse.setBreakfastNum(BreakfastNumEnum.getValueByKey(projectHotelPrice.getBreakfastNum()));
        queryViolationsDailyPriceResponse.setLra(lraValue);
        queryViolationsDailyPriceResponse.setContractPrice(projectHotelPrice.getBasePrice());
        queryViolationsDailyPriceResponse.setDailyPriceDtos(dailyPriceDtos);
        response.setData(queryViolationsDailyPriceResponse);
        return response;
    }

    private HotelViolationsDailyCheck convertHotelViolationsDailyCheck(HotelViolationsDailyCheck hotelViolationsDailyCheck){
        if(StringUtil.isEmpty(hotelViolationsDailyCheck.getPassRoomInfo())){
            hotelViolationsDailyCheck.setHotelPriceMonitorCheckInfos(new ArrayList<>());
        } else {
            hotelViolationsDailyCheck.setHotelPriceMonitorCheckInfos(JsonUtil.jsonToList(hotelViolationsDailyCheck.getPassRoomInfo(), HotelPriceMonitorCheckInfo.class));
        }
        return hotelViolationsDailyCheck;
    }

    @Override
    public Response queryHotelViolationsOrderDetail(Long violationsMonitorId) {

        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        QueryViolationsDailyPriceResponse queryViolationsDailyPriceResponse = hotelViolationsMonitorDao.queryViolationMonitorDetail(violationsMonitorId);
        if (queryViolationsDailyPriceResponse == null) {
            return response;
        }
        List<OrderDetailDto> orderDetailDtos = hotelViolationOrderDetailDao.queryByViolationsMonitorId(violationsMonitorId);
        if (CollectionUtils.isEmpty(orderDetailDtos)) {
            return response;
        }

        QueryViolationsOrderDetailResponse queryViolationsOrderDetailResponse = new QueryViolationsOrderDetailResponse();
        queryViolationsOrderDetailResponse.setProjectId(queryViolationsDailyPriceResponse.getProjectId());
        queryViolationsOrderDetailResponse.setHotelId(queryViolationsDailyPriceResponse.getHotelId());
        queryViolationsOrderDetailResponse.setHotelName(queryViolationsDailyPriceResponse.getHotelName());
        queryViolationsOrderDetailResponse.setHotelGroup(queryViolationsDailyPriceResponse.getHotelGroup());
        queryViolationsOrderDetailResponse.setProjectName(queryViolationsDailyPriceResponse.getProjectName());
        queryViolationsOrderDetailResponse.setViolationItem(queryViolationsDailyPriceResponse.getViolationItem());
        queryViolationsOrderDetailResponse.setPenaltyScore(queryViolationsDailyPriceResponse.getPenaltyScore());
        queryViolationsOrderDetailResponse.setHotelServicePoints(queryViolationsDailyPriceResponse.getHotelServicePoints());
        queryViolationsOrderDetailResponse.setOrderMonitorTime(queryViolationsDailyPriceResponse.getOrderMonitorTime());
        queryViolationsOrderDetailResponse.setViolationsMonitorId(violationsMonitorId);
        queryViolationsOrderDetailResponse.setReminderLevel(queryViolationsDailyPriceResponse.getReminderLevel());
        for (OrderDetailDto orderDetailDto : orderDetailDtos) {
            orderDetailDto.setHotelName(queryViolationsDailyPriceResponse.getHotelName());
            orderDetailDto.setBreakfastNum(BreakfastNumEnum.getValueByKey(orderDetailDto.getBreakfastnum()));
            List<Long> otaPriceIds = new ArrayList<>();
            for (String otaPriceId : orderDetailDto.getOtaPriceIds().split(",")) {
                otaPriceIds.add(Long.valueOf(otaPriceId));
            }
            List<OtaOrderPriceDto> otaOrderPriceDtos = hotelViolationOrderDetailDao.queryOtaOrderPrices(otaPriceIds);
            Collections.sort(otaOrderPriceDtos);
            StringBuffer otaOrderPriceString = new StringBuffer();
            for (OtaOrderPriceDto otaOrderPriceDto : otaOrderPriceDtos) {
                otaOrderPriceString.append(DateUtil.dateToString(otaOrderPriceDto.getRoomDate(), RfpConstant.dateFormat2)).append("(").append(otaOrderPriceDto.getRoomPrice().toString()).append(")").append("，");
            }
            orderDetailDto.setMonitorDailyPrice(otaOrderPriceString.length() > 0 ? otaOrderPriceString.substring(0,otaOrderPriceString.length()-1) : "");
        }
        queryViolationsOrderDetailResponse.setOrderDetailDtos(orderDetailDtos);
        response.setData(queryViolationsOrderDetailResponse);
        return response;
    }

    @Override
    public void exportHotelViolations(QueryHotelViolationMonitorRequest queryHotelViolationMonitorRequest, HttpServletResponse httpServletResponse) throws Exception {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("违规日期");
            headerRow.createCell(1).setCellValue("机构名称");
            headerRow.createCell(2).setCellValue("项目名称");
            headerRow.createCell(3).setCellValue("省份");
            headerRow.createCell(4).setCellValue("城市");
            headerRow.createCell(5).setCellValue("酒店ID");
            headerRow.createCell(6).setCellValue("酒店名称");
            headerRow.createCell(7).setCellValue("关注酒店");
            headerRow.createCell(8).setCellValue("集团名称");
            headerRow.createCell(9).setCellValue("近30日我的采购量(N)");
            headerRow.createCell(10).setCellValue("剩余服务分");
            headerRow.createCell(11).setCellValue("本次扣分");
            headerRow.createCell(12).setCellValue("违规项");
            headerRow.createCell(13).setCellValue("违规类型");
            headerRow.createCell(14).setCellValue("酒店回复信息");
            headerRow.createCell(15).setCellValue("酒店回复时间");
            headerRow.createCell(16).setCellValue("提醒等级");
            headerRow.createCell(17).setCellValue("耗时");
            headerRow.createCell(18).setCellValue("处理信息");
            headerRow.createCell(19).setCellValue("处理时间");
            headerRow.createCell(20).setCellValue("状态");

            int currentPage = 1;
            int pageSize = 1000;
            boolean dataExists = true;

            while (dataExists) {
                queryHotelViolationMonitorRequest.setCurrentPage(currentPage);
                queryHotelViolationMonitorRequest.setPageSize(pageSize);
                Response response = queryHotelViolationMonitor(queryHotelViolationMonitorRequest);
                if (response.getData() == null) {
                    break;
                }

                PageResult<QueryHotelViolationMonitorResponse> pageResult = (PageResult<QueryHotelViolationMonitorResponse>) response.getData();
                if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }

                assembleExcel(pageResult.getList(), sheet);

                currentPage++;

                if (currentPage > pageResult.getTotalPage()) {
                    dataExists = false;
                }
            }
            String fileName = "酒店违规监控信息" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("导出酒店违规信息异常：" + JSON.toJSONString(queryHotelViolationMonitorRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    @Override
    public void initHotelPriceViolationsMonitor() throws Exception {
        List<Long> projectIds = hotelViolationsConfigDao.queryNeedViolationsMonitorProject();
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }

        Set<String> projectHotelIds = new HashSet<>();
        Map<String, List<ProjectHotelWhite>> projectHotelWhiteMap = new HashMap<>(); // <ProjectId, ProjectHotelWhiteList>
        Date nowDate = new Date();
        boolean todayIsMonday;
        boolean todayIsHoliday;
        int weekDay = cn.hutool.core.date.DateUtil.dayOfWeek(nowDate);
        List<Integer> hotelWhiteTypeList = new ArrayList<>();
        if(weekDay != Calendar.MONDAY){ //星期一
            todayIsMonday = false;
            hotelWhiteTypeList.add(HotelWhitTypeEnum.MONDAY_MONITOR.key);
        } else {
            todayIsMonday = true;
        }

        int workDay = workDateService.getWorkDateFlag(DateUtil.dateToString(new Date(), "yyyyMMdd"));
        if(workDay == 2){ // 节假日
            todayIsHoliday = true;
            hotelWhiteTypeList.add(HotelWhitTypeEnum.HOLIDAY_NO_MONITOR.key);
        } else {
            todayIsHoliday = false;
            if (workDay == 0){ // 未找到数据
                logger.error("workDateService.getWorkDateFlag is error {}, {}", DateUtil.dateToString(new Date(), "yyyyMMdd"), workDay);
            }
        }
        for (Set<Long> projectIdList : CommonUtil.splitSet(new HashSet<>(projectIds), 1000)) {
            List<String> projectHotelIdList = hotelPriceMonitorDao.queryHotelIdsByProject(projectIdList);
            if(CollectionUtils.isNotEmpty(hotelWhiteTypeList)) {
                List<ProjectHotelWhite> projectHotelWhiteList = projectHotelWhiteDao.queryMonitorProjectHotelWhiteDetailList(new ArrayList<>(projectIdList), hotelWhiteTypeList);
                projectHotelWhiteMap = projectHotelWhiteList.stream().collect(Collectors.groupingBy(o -> o.getProjectId() + "_" + o.getHotelId()));
            }
            if(!projectHotelWhiteMap.isEmpty()) {
                Map<String, List<ProjectHotelWhite>> finalProjectHotelWhiteMap = projectHotelWhiteMap;
                projectHotelIdList = projectHotelIdList.stream().filter(o -> whiteTypeFilter(o, todayIsMonday, todayIsHoliday, finalProjectHotelWhiteMap)).collect(Collectors.toList());
            }
            if (projectHotelIdList != null && !projectHotelIdList.isEmpty()) {
                projectHotelIds.addAll(projectHotelIdList);
            }
        }
        if (projectHotelIds.isEmpty()) {
            return;
        }
        RedisService.sadd(RedisConstant.HOTEL_PRICE_VIOLATIONS_MONITOR, projectHotelIds.toArray(new String[projectHotelIds.size()]));

    }

    private boolean whiteTypeFilter(String projectHotelIdStr,
                                                boolean todayIsMonday,
                                                boolean todayIsHoliday,
                                                Map<String, List<ProjectHotelWhite>>projectHotelWhiteMap){
        if(!projectHotelWhiteMap.containsKey(projectHotelIdStr)){
            return true;
        }
        List<ProjectHotelWhite> projectHotelWhiteList = projectHotelWhiteMap.get(projectHotelIdStr);
        boolean isMonitor = true;
        for(ProjectHotelWhite projectHotelWhite : projectHotelWhiteList){
            // 节假日不监控
            if(todayIsHoliday && projectHotelWhite.getHotelWhiteType() == HotelWhitTypeEnum.HOLIDAY_NO_MONITOR.key){
                isMonitor = false;
                // 只周一监控
            } else if(!todayIsMonday && projectHotelWhite.getHotelWhiteType() == HotelWhitTypeEnum.MONDAY_MONITOR.key){
                isMonitor = false;
            }
        }
        // 周一是节假日，而且配置了节假日不监控
        if(projectHotelWhiteList.size() == 2 && todayIsHoliday && todayIsMonday){
            isMonitor = false;
        }
        return isMonitor;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String consumeHotelPriceViolationsMonitor(String projectHotelId, String modifier) {
        String[] split = projectHotelId.split(RfpConstant.UNDERLINE);
        Long projectId = Long.valueOf(split[0]);
        Long hotelId = Long.valueOf(split[1]);
        HotelPriceMonitor queryHotelPriceMonitor = new HotelPriceMonitor();
        queryHotelPriceMonitor.setProjectId(projectId);
        queryHotelPriceMonitor.setHotelId(hotelId);
        queryHotelPriceMonitor.setSaleDate(DateUtil.stringToDate(LocalDate.now().toString(), "yyyy-MM-dd"));
        List<HotelPriceMonitor> hotelPriceMonitors = hotelPriceMonitorDao.selectHotelPriceMonitorList(queryHotelPriceMonitor);
        if (CollectionUtils.isEmpty(hotelPriceMonitors)) {
            return "未找到酒店违规监控";
        }

        HotelViolationsConfig hotelViolationsConfig = hotelViolationsConfigDao.queryHotelViolationConfig(projectId);
        if (hotelViolationsConfig == null) {
            return "酒店违规监控配置为空";
        }
        //校验酒店违规配置是否需要发送通知  lra承诺：1-是，0-否
        Integer noLraSoDaysNotice = hotelViolationsConfig.getNoLraSoDaysNotice(); // NO LRA 满房超过天数
        Integer noLraPiDaysNotice = hotelViolationsConfig.getNoLraPiDaysNotice(); // NO LRA 涨价超过天数
        Integer lraSoDaysNotice = hotelViolationsConfig.getLraSoDaysNotice(); // LRA 满房超过天数
        Integer lraPiDaysNotice = hotelViolationsConfig.getLraPiDaysNotice(); // LRA 涨价超过天数
        if (noLraSoDaysNotice.intValue() == RfpConstant.constant_0
                && noLraPiDaysNotice.intValue() == RfpConstant.constant_0
                && lraSoDaysNotice.intValue() == RfpConstant.constant_0
                && lraPiDaysNotice.intValue() == RfpConstant.constant_0) {
            return "LRA 配置都为0";
        }

        PriceMonitorConfig priceMonitorConfig = priceMonitorConfigDao.selectPriceMonitorConfigByProject(projectId);
        if (priceMonitorConfig == null) {
            logger.error("无报价监控配置信息，projectId:" + projectId + ",hotelId:" + hotelId);
            return "无报价监控配置信息";
        }

        // 报价组
        Map<Long, ProjectHotelPriceGroup> projectHotelPriceGroupMap = new HashMap<>();

        //从订单监控中获取分销商编码
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(projectId);
        List<QueryOrderMonitorConfigResponse> orderMonitorConfigs = orderMonitorConfigDao.queryOrderMonitorConfig(projectIds);
        if (CollectionUtils.isEmpty(orderMonitorConfigs)) {
            logger.error("无分销商信息，projectId:" + projectId + ",hotelId:" + hotelId);
            return "无分销商信息";
        }
        List<String> distributorCodes = new ArrayList();
        for (QueryOrderMonitorConfigResponse queryOrderMonitorConfigResponse : orderMonitorConfigs) {
            distributorCodes.add(queryOrderMonitorConfigResponse.getDistributorCode());
        }

        // 查询间夜数
        List<ViolationsRemindConfig> violationsRemindConfigs = violationsRemindConfigDao.queryViolationsRemindConfig(projectId);
        QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
        queryRoomNightsDto.setHotelId(hotelId);
        queryRoomNightsDto.setDistributorCodes(distributorCodes);
        Date startDate = new Date();
        queryRoomNightsDto.setStartTime(DateUtil.dateToString(DateUtil.getDate(startDate, -30, 0)));
        queryRoomNightsDto.setEndTime(DateUtil.dateToString(startDate));
        Integer roomNights = disHotelDailyOrderDao.selectRoomNights(queryRoomNightsDto);
        if (roomNights == null) {
            roomNights = 0;
        }

        // 关注房型 按照房型ID + # + 早餐分组
        List<HotelPriceMonitorRoom> hotelPriceMonitorRoomList = hotelPriceMonitorRoomDao.selectMonitorRoomList(projectId, hotelId);
        List<String> hotelPriceMonitorRoomKeyList = hotelPriceMonitorRoomList.stream().map(o -> o.getRoomTypeId() + "#" + o.getBreakfastNum()).collect(Collectors.toList());

        // 根据关注房型过来价格监控数据
        hotelPriceMonitors = hotelPriceMonitors.stream().filter(hotelPriceMonitor -> hotelPriceMonitorRoomKeyList.contains(hotelPriceMonitor.getRoomTypeId() + "#" + hotelPriceMonitor.getBreakfastNum())).collect(Collectors.toList());

        Boolean isFirstViolation = null;
        Map<String, com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> tmcProductDetailMap = new HashMap<>();
        Map<String, List<HotelPriceMonitor>> hotelPriceMonitorRoomMap = hotelPriceMonitors.stream().collect(Collectors.groupingBy(o -> o.getRoomTypeId() + "#" + o.getBreakfastNum()));
        for(String monitorRoomKey : hotelPriceMonitorRoomMap.keySet()) {
            // 查询项目意向酒店
            ProjectIntentHotel projectIntentHotel = queryProjectIntentHotel(projectId, hotelId);

            String[] monitorRoomKeyArray = monitorRoomKey.split("#");
            Long roomTypeId = Long.valueOf(monitorRoomKeyArray[0]);
            Integer breakfastNum = Integer.valueOf(monitorRoomKeyArray[1]);

            // 需要调用tmchub 集合map
            Map<String, HotelPriceMonitor> hotelPriceMonitorMap = new HashMap<>();
            Set<String> fullRoomList = new HashSet<>();
            Set<String> upPriceList = new HashSet<>();

            List<HotelPriceMonitor> roomHotelPriceMonitorList = hotelPriceMonitorRoomMap.get(monitorRoomKey);
            for (HotelPriceMonitor hotelPriceMonitor : roomHotelPriceMonitorList) {
                if (isMoreThan24H(hotelPriceMonitor.getModifyTime())) {
                    logger.info("超过24小时无效监控数据 " + JSON.toJSONString(hotelPriceMonitor));
                    continue;
                }
                if (CompareResultTypeEnum.FULL.key == hotelPriceMonitor.getCompareResult().intValue()) {
                    fullRoomList.add(DateUtil.dateToString(hotelPriceMonitor.getSaleDate()));
                } else if (CompareResultTypeEnum.UP.key == hotelPriceMonitor.getCompareResult().intValue()) {
                    upPriceList.add(DateUtil.dateToString(hotelPriceMonitor.getSaleDate()));
                }
                hotelPriceMonitorMap.put(DateUtil.dateToString(hotelPriceMonitor.getSaleDate()), hotelPriceMonitor);
            }
            Long priceCode = roomHotelPriceMonitorList.get(0).getPriceCode();
            ProjectHotelPrice projectHotelPrice = projectHotelPriceDao.selectByPrimaryKey(priceCode);
            if (projectHotelPrice == null) {
                logger.error("报价信息为空,priceCode：" + priceCode + ",projectId:" + projectId + ",hotelId:" + hotelId);
                return "报价信息为空";
            }

            // 查询报价组
            ProjectHotelPriceGroup hotelPriceGroup = projectHotelPriceGroupMap.get(projectHotelPrice.getHotelPriceGroupId());
            if(hotelPriceGroup == null){
                hotelPriceGroup = hotelPriceGroupDao.selectByPrimaryKey(projectHotelPrice.getHotelPriceGroupId());
                projectHotelPriceGroupMap.put(projectHotelPrice.getHotelPriceGroupId(), hotelPriceGroup);
            }

            // 调用tmcHub接口失败异常
            List<QueryTmcHubResult> queryTmcHubFailedResultList = new ArrayList<>();

            // 满房查询tmchub记录
            List<ViolationsFullMonitorLog> violationsFulMonitorLogList = new ArrayList<>();

            // lra承诺：1-是，0-否  取第一个监控价钱的LRA
            Integer lra = roomHotelPriceMonitorList.get(0).getLra();
            StringBuilder violationItemString = new StringBuilder();
            BigDecimal penaltyScore;
            BigDecimal fullPenaltyScore = new BigDecimal(0L);
            BigDecimal upPenaltyScore = new BigDecimal(0l);
            boolean isRoomFullViolation = false; // 是否满分违规
            boolean isPriceUpViolation = false; // 是否涨价违规
            if (lra == RfpConstant.constant_0) {
                if (noLraSoDaysNotice == RfpConstant.constant_1) {
                    //如果满房天数大于等于监控的满房天数，需要调用tmc-hub产品接口判断其它渠道是否可售
                    boolean flag = fullRoomMonitor(fullRoomList, hotelViolationsConfig.getNoLraSoDays(),
                            hotelViolationsConfig.getNoLraSoDaysOther(), hotelPriceMonitorMap,
                            hotelId, violationsFulMonitorLogList, queryTmcHubFailedResultList,
                            tmcProductDetailMap);
                    if (flag) {
                        violationItemString.append("近15天协议关房C端有房可售天数超过" + hotelViolationsConfig.getNoLraSoDays() + "天；");
                        isRoomFullViolation = true;
                        fullPenaltyScore = fullPenaltyScore.add(hotelViolationsConfig.getNoLraSoPenaltyScore());
                    }

                }
                if (noLraPiDaysNotice == RfpConstant.constant_1) {
                    //涨价天数判断
                    boolean flag = upRoomMonitor(upPriceList, hotelViolationsConfig.getNoLraPiDays(),
                            hotelViolationsConfig.getNoLraPiDaysAvg(), hotelPriceMonitorMap, projectHotelPrice.getBasePrice());
                    if (flag) {
                        violationItemString.append("近15天涨价天数超过" + hotelViolationsConfig.getNoLraPiDays() + "天；");
                        isPriceUpViolation = true;
                        upPenaltyScore = upPenaltyScore.add(hotelViolationsConfig.getNoLraPiPenaltyScore());
                    }
                }
            } else if (lra == RfpConstant.constant_1) {
                if (lraSoDaysNotice == RfpConstant.constant_1) {
                    //如果满房天数大于等于监控的满房天数，需要调用tmc-hub产品接口判断其它渠道是否可售
                    boolean flag = fullRoomMonitor(fullRoomList, hotelViolationsConfig.getLraSoDays(),
                            hotelViolationsConfig.getLraSoDaysOther(), hotelPriceMonitorMap, hotelId, violationsFulMonitorLogList, queryTmcHubFailedResultList, tmcProductDetailMap);
                    if (flag) {
                        violationItemString.append("近15天协议关房C端有房可售天数超过" + hotelViolationsConfig.getLraSoDays() + "天；");
                        isRoomFullViolation = true;
                        fullPenaltyScore = fullPenaltyScore.add(hotelViolationsConfig.getLraSoPenaltyScore());
                    }
                }
                if (lraPiDaysNotice == RfpConstant.constant_1) {
                    //涨价天数判断
                    boolean flag = upRoomMonitor(upPriceList, hotelViolationsConfig.getLraPiDays(),
                            hotelViolationsConfig.getLraPiDaysAvg(), hotelPriceMonitorMap, projectHotelPrice.getBasePrice());
                    if (flag) {
                        violationItemString.append("近15天涨价天数超过" + hotelViolationsConfig.getLraPiDays() + "天；");
                        isPriceUpViolation = true;
                        upPenaltyScore = upPenaltyScore.add(hotelViolationsConfig.getLraPiPenaltyScore());
                    }
                }
            }
            // 扣分总数
            penaltyScore = fullPenaltyScore.add(upPenaltyScore);

            //酒店违规信息为空则说明有没有违规操作 (不是复核逻辑)
            if (violationItemString.toString().length() <= 0) {
                // 异步保存调用tmchub失败异常
                HotelViolationsMonitor tmpHotelViolationsMonitor = new HotelViolationsMonitor();
                tmpHotelViolationsMonitor.setViolationsMonitorId(0L);
                tmpHotelViolationsMonitor.setHotelId(hotelId);
                asyncSaveQueryTmcHubFailedResultList(tmpHotelViolationsMonitor, queryTmcHubFailedResultList);
                continue;
            }

            // 记住第一个违规房型
            if (isFirstViolation == null && penaltyScore.compareTo(BigDecimal.ZERO) > 0) {
                isFirstViolation = true;
            } else {
                penaltyScore = BigDecimal.ZERO;
            }

            // 违规提示
            StringBuffer remindTypes = new StringBuffer();
            if (CollectionUtils.isNotEmpty(violationsRemindConfigs)) {
                //查询7天内未解决的任务
                List<HotelViolationsMonitor> hotelViolationsMonitors = hotelViolationsMonitorDao.queryHotelViolationsMonitors(hotelId, projectId);
                int needProcessing = CollectionUtils.isNotEmpty(hotelViolationsMonitors) ? hotelViolationsMonitors.size() : 0;
                for (ViolationsRemindConfig violationsRemindConfig : violationsRemindConfigs) {
                    if (violationsRemindConfig.getMaximumNights() >= roomNights
                            && violationsRemindConfig.getMinimumNights() <= roomNights) {
                        if (needProcessing >= violationsRemindConfig.getNeedSystemReminderCount()) {
                            remindTypes.append(",").append(ReminderTypeEnum.SYSTEM_REMINDER.key.toString());
                        }
                        if (needProcessing >= violationsRemindConfig.getNeedPriorityReminderCount()) {
                            remindTypes.append(",").append(ReminderTypeEnum.PRIORITY_REMINDER.key.toString());
                        }
                        if (needProcessing >= violationsRemindConfig.getNeedJialiFollowUpCount()) {
                            remindTypes.append(",").append(ReminderTypeEnum.JIALI_FOLLOW_UP.key.toString());
                        }
                        if (needProcessing >= violationsRemindConfig.getNeedBusinessFollowUpCount()) {
                            remindTypes.append(",").append(ReminderTypeEnum.BUSINESS_FOLLOW_UP.key.toString());
                        }
                    }
                }
            }

            // 新增酒店违规监控记录
            HotelViolationsMonitor hotelViolationsMonitor = new HotelViolationsMonitor();
            hotelViolationsMonitor.setHotelId(hotelId);
            hotelViolationsMonitor.setProjectId(projectId);
            hotelViolationsMonitor.setViolationItem(violationItemString.toString());
            hotelViolationsMonitor.setViolationType(ViolationTypeEnum.PRICE_MONITORING.key);
            hotelViolationsMonitor.setPenaltyScore(penaltyScore);
            hotelViolationsMonitor.setStatus(HotelViolationMonitorEnum.QUESTIONABILITY.key);
            hotelViolationsMonitor.setCreator(RfpConstant.CREATOR);
            hotelViolationsMonitor.setModifier(RfpConstant.CREATOR);
            hotelViolationsMonitor.setProcurementVolume(roomNights);
            hotelViolationsMonitor.setReminderLevel(remindTypes.length() > 0 ? remindTypes.substring(1) : null);
            hotelViolationsMonitor.setSendStatus(RfpConstant.constant_0);
            hotelViolationsMonitor.setFullPenaltyScore(fullPenaltyScore);
            hotelViolationsMonitor.setUpPenaltyScore(upPenaltyScore);
            hotelViolationsMonitor.setRoomTypeId(roomTypeId);
            hotelViolationsMonitor.setBreakfastNum(breakfastNum);
            BigDecimal subtract = projectIntentHotel.getHotelServicePoints().subtract(penaltyScore);
            if (subtract.doubleValue() <= 0) {
                hotelViolationsMonitor.setHotelServicePoints(new BigDecimal(0));
            } else {
                hotelViolationsMonitor.setHotelServicePoints(subtract);
            }
            int insert = hotelViolationsMonitorDao.insert(hotelViolationsMonitor);
            if (insert != 1) {
                throw new RuntimeException("保存酒店违规监控信息失败,projectId:" + projectId + ",hotelId:" + hotelId);
            }

            // 新增满分查询C端接口日志
            List<String> fullHasPriceRoomDateList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(violationsFulMonitorLogList)) {
                violationsFulMonitorLogList.forEach(item -> {
                    item.setViolationsMonitorId(hotelViolationsMonitor.getViolationsMonitorId());
                    if (item.getSalePrice() != null) {
                        fullHasPriceRoomDateList.add(DateUtil.dateToString(item.getSaleDate()));
                    }
                });
                violationsFulMonitorLogDao.batchInsert(violationsFulMonitorLogList);
            }

            // 新增酒店违规价格日历
            List<HotelViolationsDailyPrice> hotelViolationsDailyPrices = prepareHotelViolationsDailyPrices(hotelViolationsMonitor.getViolationsMonitorId(),
                    roomHotelPriceMonitorList, isRoomFullViolation, isPriceUpViolation, fullHasPriceRoomDateList);
            hotelViolationsDailyPriceDao.batchInsert(hotelViolationsDailyPrices);

            // 更新酒店服务分总数
            if (penaltyScore.compareTo(BigDecimal.ZERO) > 0) {
                projectIntentHotelDao.updateHotelServicePoints(penaltyScore, projectIntentHotel.getProjectIntentHotelId());
            }
            // 如果满分或者涨价，记录复核处理队列  hotel violation monitor id
            if (isRoomFullViolation || isPriceUpViolation) {
                try {
                    // projectId_hotelId_hotelViolationsMonitorId
                    RedisService.sadd(RedisConstant.HOTEL_VIOLATIONS_PRICE_REPEAT_MONITOR, projectHotelId + RfpConstant.UNDERLINE + hotelViolationsMonitor.getViolationsMonitorId());
                } catch (Exception ex) {
                    logger.error("新增复核队列异常", ex);
                }
                // 更新违规监控统计
                projectIntentHotelService.statByHotelViolationMonitor(projectIntentHotel, hotelViolationsMonitor);
            }
            // 异步保存调用tmchub失败异常
            asyncSaveQueryTmcHubFailedResultList(hotelViolationsMonitor, queryTmcHubFailedResultList);

        }
        return null;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String consumeHotelPriceViolationsRepeatMonitor(String projectHotelId, String modifier) throws Exception {
        HotelViolationsMonitor hotelViolationsMonitor;
        // 是否为复核前预先检查
        boolean isPrepareRepeat = false;
        String[] split = projectHotelId.split(RfpConstant.UNDERLINE);
        if(split.length < 3){
            return "projectHotelId 长度不符合要求 " + projectHotelId;
        }
        Jedis jedis = RedisService.getResource();
        String lockKey = RedisLockConstant.CONSUME_REPEAT_MONITOR_LOCK_KEY + projectHotelId;
        RedisDistributedLock lock = new RedisDistributedLock(jedis);
        long currentStamp = System.currentTimeMillis();
        try {
            if (lock.lock(lockKey, currentStamp, RedisLockConstant.EXPIRE_TIME_SECOND, RedisLockConstant.NO_WAITE_TIME, RedisLockConstant.NO_TIMEOUT_TIME)) {
                // projectId_hotelId_violationsMonitorId
                // projectId_hotelId_violationsMonitorId_0_retryCount
                Long projectId = Long.valueOf(split[0]);
                Long hotelId = Long.valueOf(split[1]);
                Long violationsMonitorId = Long.valueOf(split[2]);
                hotelViolationsMonitor = hotelViolationsMonitorDao.getByViolationsMonitorId(violationsMonitorId);
                // projectId_hotelId_violationsMonitorId_0 为预先检查
                if (hotelViolationsMonitor == null) {
                    return "violationsMonitorId 不存在 " + violationsMonitorId;
                }
                if (split.length == 4L) {
                    isPrepareRepeat = true;
                } else {
                    if (hotelViolationsMonitor.getIsPaybackScore() == RfpConstant.constant_1) {
                        return "已经复核一次，不能复核";
                    }
                    if (!cn.hutool.core.date.DateUtil.isSameDay(hotelViolationsMonitor.getCreateTime(), new Date())) {
                        return "不是当日违规，不能复核";
                    }
                    if (hotelViolationsMonitor.getStatus() == RfpConstant.constant_1) {
                        return "已经解决违规，不能复核";
                    }
                }

                List<HotelViolationsDailyPrice> allHotelViolationsDailyPrices = hotelViolationsDailyPriceDao.queryHotelViolationsDailyPrice(violationsMonitorId);
                List<HotelViolationsDailyPrice> hotelViolationsDailyPrices = allHotelViolationsDailyPrices.stream().filter(o -> o.getIsViolation() == RfpConstant.constant_1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(hotelViolationsDailyPrices)) {
                    return "未找到酒店违规记录";
                }

                // 需要调用tmchub 集合map
                Map<String, HotelViolationsDailyPrice> hotelViolationsDailyPriceMap = new HashMap<>();
                List<Date> saleDateList = new ArrayList<>();
                for (HotelViolationsDailyPrice hotelViolationsDailyPrice : hotelViolationsDailyPrices) {
                    if (CompareResultTypeEnum.FULL.key == hotelViolationsDailyPrice.getCompareResult().intValue()) {
                        saleDateList.add(hotelViolationsDailyPrice.getSaleDate());
                        hotelViolationsDailyPriceMap.put(DateUtil.dateToString(hotelViolationsDailyPrice.getSaleDate()), hotelViolationsDailyPrice);
                    } else if (CompareResultTypeEnum.UP.key == hotelViolationsDailyPrice.getCompareResult().intValue()) {
                        saleDateList.add(hotelViolationsDailyPrice.getSaleDate());
                        hotelViolationsDailyPriceMap.put(DateUtil.dateToString(hotelViolationsDailyPrice.getSaleDate()), hotelViolationsDailyPrice);
                    }
                }

                //  复核逻辑 查询价格监控
                HotelPriceMonitor queryHotelPriceMonitor = new HotelPriceMonitor();
                queryHotelPriceMonitor.setProjectId(projectId);
                queryHotelPriceMonitor.setHotelId(hotelId);
                queryHotelPriceMonitor.setSaleDate(cn.hutool.core.date.DateUtil.offsetDay(new Date(), -1));
                queryHotelPriceMonitor.setRoomTypeId(hotelViolationsMonitor.getRoomTypeId());
                queryHotelPriceMonitor.setBreakfastNum(hotelViolationsMonitor.getBreakfastNum());
                List<HotelPriceMonitor> hotelPriceMonitorList = hotelPriceMonitorDao.selectHotelPriceMonitorList(queryHotelPriceMonitor);
                Map<String, HotelPriceMonitor> hotelPriceMonitorMap = new HashMap<>();
                hotelPriceMonitorList.forEach(item -> {
                    hotelPriceMonitorMap.put(DateUtil.dateToString(item.getSaleDate()), item);
                });


                // 查询机构各自的合作商信息 // 调用tmc-hub接口需要partnerCode
                Long orgId = hotelPriceMonitorList.get(0).getOrgId();
                int breakfastNum = hotelPriceMonitorList.get(0).getBreakfastNum();
                String partnerCode = RedisService.hget(RedisConstant.PRICE_MONITOR_CONFIG, String.valueOf(orgId));
                if (StringUtils.isBlank(partnerCode)) {
                    XxlJobLogger.log("{}机构，合作商编码无效，复核定时任务结束", orgId);
                    return "复核合作商编码无效，复核定时任务结束";
                }

                // 预先自动补偿检查调用tmc_hub, 刷新缓存
                if (isPrepareRepeat) {
                    for (String saleDate : hotelViolationsDailyPriceMap.keySet()) {
                        HotelViolationsDailyPrice hotelViolationsDailyPrice = hotelViolationsDailyPriceMap.get(saleDate);
                        prepareRepeatQueryTmcHubProduct(hotelId, hotelViolationsDailyPrice, saleDate, DateUtil.dateToString(DateUtil.getDate(DateUtil.stringToDate(saleDate), 1, 0)), partnerCode);
                    }
                    return "自动补偿预检查完成";
                }

                // 调用tmcHub接口失败异常
                List<QueryTmcHubResult> queryTmcHubFailedResultList = new ArrayList<>();

                List<com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> responses = summaryProductDetail(hotelId, partnerCode, saleDateList);
                // 验证response是否正确
                Boolean flag = verifyResponse(partnerCode, hotelId, responses);
                if (!flag) {
                    for (String saleDate : hotelPriceMonitorMap.keySet()) {
                        HotelPriceMonitor hotelPriceMonitor = hotelPriceMonitorMap.get(saleDate);
                        QueryTmcHubResult queryTmcHubResult = new QueryTmcHubResult();
                        queryTmcHubResult.setOrgId(orgId);
                        queryTmcHubResult.setSaleDate(saleDate);
                        queryTmcHubResult.setResult(false);
                        queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_RESPONSE_FAILED.key);
                        queryTmcHubResult.setFailedInfo("Response failed");
                        queryTmcHubResult.setHotelId(hotelPriceMonitor.getHotelId());
                        queryTmcHubResult.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
                        queryTmcHubResult.setRoomTypeName(hotelPriceMonitor.getRoomTypeName());
                        queryTmcHubFailedResultList.add(queryTmcHubResult);
                    }
                    int retryCount = 1;
                    if(split.length == 5) {
                       retryCount = Integer.parseInt(split[4]);
                       retryCount++;
                    }
                    // 最多重试两次
                    if(retryCount <= 2) {
                       // 新增自动复核重试队列
                       try {
                           // projectId_hotelId_hotelViolationsMonitorId_0_retryCount
                           String retryKey = projectId + RfpConstant.UNDERLINE + hotelId + RfpConstant.UNDERLINE + hotelViolationsMonitor.getViolationsMonitorId() +RfpConstant.UNDERLINE + "0" + RfpConstant.UNDERLINE + retryCount;
                           RedisService.sadd(RedisConstant.HOTEL_VIOLATIONS_PRICE_REPEAT_MONITOR_RETRY, retryKey);
                           logger.info("新增自动复核重试 {},{}", retryCount, retryKey);
                       } catch (Exception ex) {
                           logger.error("新增复核队列异常", ex);
                       }
                   }
                   return "复核查询价格计划异常";
                }

                // 查找对应日期的同价房
                // 查询同房型数据
                PriceMonitorConfig priceMonitorConfig = cachedPriceMonitorConfigManager.getByProjectId(projectId);
                boolean isAgreementMonitor = false;
                if(priceMonitorConfig != null && priceMonitorConfig.getIsMonitorAgreement() == RfpConstant.constant_1){
                    isAgreementMonitor = true;
                }
                Map<String, List<TmcHubProductResponse>> theSameLevelTmcHubProductMap = dealTheSameLevelTmcHubProduct(isAgreementMonitor,responses, hotelPriceMonitorMap);


                Long roomTypeId = hotelViolationsDailyPrices.get(0).getRoomTypeId();
                Map<String, PriceItem> salePriceRoomMap = new HashMap<>(); // <SaleDate,  Response>
                for (com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> productDetailResponseResponse : responses) {
                    ProductDetailResponse productDetailResponse = productDetailResponseResponse.getBussinessResponse();
                    if (productDetailResponse == null || CollectionUtils.isEmpty(productDetailResponse.getRoomItems())) {
                        continue;
                    }
                    for (RoomItem roomItem : productDetailResponse.getRoomItems()) {
                        if (roomItem.getRoomId().longValue() != roomTypeId.longValue()) {
                            continue;
                        }
                        if (CollectionUtils.isEmpty(roomItem.getProducts())) {
                            continue;
                        }

                        logger.info("productDetailResponse.getRoomItems() {}", JSON.toJSON(roomItem.getProducts()));
                        for (ProductDetail productDetail : roomItem.getProducts()) {
                            if (CollectionUtils.isEmpty(productDetail.getPriceItems())) {
                                continue;
                            }
                            if(isAgreementMonitor){
                                if(CollectionUtils.isEmpty(productDetail.getProductTags())){
                                    continue;
                                }
                                List<ProductLabel> productLabelList = productDetail.getProductTags().stream().filter(o -> ProductTagUtility.isAgreementProduct(o.getTagCode())).collect(Collectors.toList());
                                if(CollectionUtils.isEmpty(productLabelList)){
                                    continue;
                                }
                            }
                            for (PriceItem priceItem : productDetail.getPriceItems()) {
                                PriceItem mapPriceItem = salePriceRoomMap.get(priceItem.getSaleDate());
                                // 相同早餐类型， 价位最低作为房型价格
                                if (BreakfastUtil.isTheSameBreakfast(breakfastNum, priceItem.getBreakfastNum()) &&
                                        !PriceRoomUtil.isNoSaleRoom(priceItem) &&
                                        (mapPriceItem == null || PriceRoomUtil.getPriceItemMonitorPrice(mapPriceItem).compareTo(PriceRoomUtil.getPriceItemMonitorPrice(priceItem)) > 0)) {
                                    salePriceRoomMap.put(priceItem.getSaleDate(), priceItem);
                                }
                            }
                        }
                    }
                }

                // 检查是否复核结果
                List<HotelViolationsDailyCheck> dailyCheckList = new ArrayList<>();
                // 满房查询tmchub记录
                List<ViolationsFullMonitorLog> violationsFulMonitorLogList = new ArrayList<>();
                boolean isAllFullPass = true;
                boolean isAllUpPass = true;

                // 恢复原价天数
                int restorePriceDayCount = 0;
                // 同档其他房型天数
                int theSameLevelDayCount = 0;
                // 关闭C端房态天数
                int clientRoomClosedDayCount = 0;

                for (HotelViolationsDailyPrice hotelViolationsDailyPrice : hotelViolationsDailyPrices) {
                    String saleDateStr = DateUtil.dateToString(hotelViolationsDailyPrice.getSaleDate());
                    PriceItem priceItem = salePriceRoomMap.get(saleDateStr);

                    HotelPriceMonitor hotelPriceMonitor = hotelPriceMonitorMap.get(saleDateStr);
                    HotelViolationsDailyCheck dailyCheck = new HotelViolationsDailyCheck();
                    BeanUtils.copyProperties(hotelPriceMonitor, dailyCheck);
                    dailyCheck.setBeforeCompareResult(hotelViolationsDailyPrice.getCompareResult());
                    dailyCheck.setBeforeIsViolation(hotelViolationsDailyPrice.getIsViolation());
                    dailyCheck.setBeforeDifPrice(hotelViolationsDailyPrice.getDifPrice());
                    dailyCheck.setViolationsMonitorId(violationsMonitorId);
                    dailyCheck.setCompareResult(null); // 覆盖结果未知
                    dailyCheck.setSalePrice(null); // 默认空
                    dailyCheck.setIsPassCheck(RfpConstant.constant_0);
                    if (priceItem != null) {
                        // 最新报价如果是原价或者降价 直接通过
                        if (priceItem.getRoomStatus() == RoomStateEnum.HAVA_ROOM.key) {
                            double diffPrice = PriceRoomUtil.getPriceItemMonitorPrice(priceItem) - (hotelPriceMonitor.getMonitorDayPrice() + 1);
                            if (diffPrice > 0) {
                                dailyCheck.setCompareResult(CompareResultTypeEnum.UP.key);
                            } else if (diffPrice <= 0 && diffPrice >= -1) {
                                dailyCheck.setCompareResult(CompareResultTypeEnum.ORIGIN.key);
                                dailyCheck.setIsPassCheck(RfpConstant.constant_1);
                                dailyCheck.setCheckPassType(ViolationCheckPassTypeEnum.RESTORE_PRICE.key);
                                restorePriceDayCount++;
                            } else {
                                dailyCheck.setCompareResult(CompareResultTypeEnum.DROP.key);
                                dailyCheck.setIsPassCheck(RfpConstant.constant_1);
                                dailyCheck.setCheckPassType(ViolationCheckPassTypeEnum.RESTORE_PRICE.key);
                                restorePriceDayCount++;
                            }
                            dailyCheck.setDifPrice(BigDecimal.valueOf(PriceRoomUtil.getPriceItemMonitorPrice(priceItem) - hotelPriceMonitor.getMonitorDayPrice()));
                            dailyCheck.setSalePrice(BigDecimal.valueOf(PriceRoomUtil.getPriceItemMonitorPrice(priceItem)));
                        } else if (priceItem.getRoomStatus() == RoomStateEnum.FULL_ROOM.key) {
                            dailyCheck.setCompareResult(CompareResultTypeEnum.FULL.key);
                            dailyCheck.setSalePrice(null);
                        }
                        logger.info("dailyCheck json {}", JSON.toJSON(dailyCheck));
                        // 复核没有找到房型，设置为满房
                    } else {
                        dailyCheck.setCompareResult(CompareResultTypeEnum.FULL.key);
                        QueryTmcHubResult queryTmcHubResult = new QueryTmcHubResult();
                        queryTmcHubResult.setOrgId(orgId);
                        queryTmcHubResult.setSaleDate(saleDateStr);
                        queryTmcHubResult.setResult(false);
                        queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL_ROOMS.key);
                        queryTmcHubResult.setFailedInfo("复核没有找到对应房型");
                        queryTmcHubResult.setHotelId(hotelPriceMonitor.getHotelId());
                        queryTmcHubResult.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
                        queryTmcHubResult.setRoomTypeName(hotelPriceMonitor.getRoomTypeName());
                        queryTmcHubFailedResultList.add(queryTmcHubResult);
                    }

                    // 查询同档房
                    List<HotelPriceMonitorCheckInfo> hotelPriceMonitorCheckInfos = new ArrayList<>();
                    boolean hasTheSameLevelRoom = false;
                    List<TmcHubProductResponse> tmcHubProductResponses = theSameLevelTmcHubProductMap.get(saleDateStr);
                    if (CollectionUtils.isNotEmpty(tmcHubProductResponses)) {
                        for (TmcHubProductResponse tmcHubProductResponse : tmcHubProductResponses) {
                            // 排除相同房型
                            if (tmcHubProductResponse.getRoomId().longValue() == roomTypeId.longValue()) {
                                continue;
                            }
                            hasTheSameLevelRoom = true;
                            HotelPriceMonitorCheckInfo hotelPriceMonitorCheckInfo = new HotelPriceMonitorCheckInfo();
                            hotelPriceMonitorCheckInfo.setPrice(PriceRoomUtil.getPriceItemMonitorPrice(tmcHubProductResponse));
                            hotelPriceMonitorCheckInfo.setRoomName(tmcHubProductResponse.getRoomName());
                            hotelPriceMonitorCheckInfo.setBreakfastNum(BreakfastNumEnum.getValueByKey(tmcHubProductResponse.getBreakfastNum()));
                            // 只记录5个
                            if (hotelPriceMonitorCheckInfos.size() <= 5) {
                                hotelPriceMonitorCheckInfos.add(hotelPriceMonitorCheckInfo);
                            }
                        }

                        // 存在同档房 通过审核
                        if (hasTheSameLevelRoom && dailyCheck.getIsPassCheck() == RfpConstant.constant_0) {
                            dailyCheck.setIsPassCheck(RfpConstant.constant_1);
                            dailyCheck.setCheckPassType(ViolationCheckPassTypeEnum.THE_SAME_LEVEL_ROOM.getKey());
                            theSameLevelDayCount++;
                        }
                    }

                    // 如果没有同档房型，并且是满房, 判断是否违规
                    if (dailyCheck.getCompareResult() == CompareResultTypeEnum.FULL.key && dailyCheck.getIsPassCheck() == RfpConstant.constant_0) {
                        // 其他Partner 检查C端是否满房
                        boolean isClientHasPrice = false;
                        com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> tmcHubResponse = callTmcHubBackList(
                                hotelId,
                                saleDateStr,
                                DateUtil.dateToString(DateUtil.getDate(DateUtil.stringToDate(saleDateStr), 1, 0)),
                                BaseConfig.getViolationMonitorPartnerCode());
                        // 初始化违规满分查询日志
                        ViolationsFullMonitorLog violationsFulMonitorLog = new ViolationsFullMonitorLog();
                        violationsFulMonitorLog.setSaleDate(DateUtil.stringToDate(saleDateStr));
                        violationsFulMonitorLog.setViolationsMonitorId(violationsMonitorId);
                        violationsFulMonitorLog.setQueryDateTime(new Date());
                        violationsFulMonitorLog.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
                        boolean hasRoom = false;
                        CompareFullRoomClientPriceResult compareFullRoomClientPriceResult = null;

                        if ("000".equals(tmcHubResponse.getReturnCode()) &&
                                tmcHubResponse.getBussinessResponse() != null &&
                                CollectionUtils.isNotEmpty(tmcHubResponse.getBussinessResponse().getRoomItems())
                        ) {
                            compareFullRoomClientPriceResult = PriceRoomUtil.compareFullRoomClientPrice(hotelPriceMonitor.getRoomTypeId(), tmcHubResponse.getBussinessResponse(), violationsFulMonitorLog);
                            hasRoom = compareFullRoomClientPriceResult.isClientHasRoom();
                            isClientHasPrice = compareFullRoomClientPriceResult.isClientHasPrice();
                        }

                        // C端不存在房型，记录异常
                        if (!hasRoom) {
                            QueryTmcHubResult queryTmcHubResult = new QueryTmcHubResult();
                            queryTmcHubResult.setOrgId(hotelPriceMonitor.getOrgId());
                            queryTmcHubResult.setHotelId(hotelId);
                            queryTmcHubResult.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
                            queryTmcHubResult.setRoomTypeName(hotelPriceMonitor.getRoomTypeName());
                            queryTmcHubResult.setSaleDate(saleDateStr);
                            queryTmcHubResult.setResult(false);
                            queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL_ROOMS.key);
                            queryTmcHubResult.setFailedInfo(JsonUtil.objectToJson(tmcHubResponse));
                            queryTmcHubFailedResultList.add(queryTmcHubResult);
                        }
                        violationsFulMonitorLogList.add(violationsFulMonitorLog);

                        // C端有房，不通过，C端无房通过
                        dailyCheck.setIsPassCheck(isClientHasPrice ? RfpConstant.constant_0 : RfpConstant.constant_1);
                        dailyCheck.setCheckPassType(isClientHasPrice ? ViolationCheckPassTypeEnum.CLIENT_ROOM_CLOSED.key : ViolationCheckPassTypeEnum.NO_PASS.getKey());
                        if(!isClientHasPrice){
                            clientRoomClosedDayCount++;
                        }
                    }

                    dailyCheck.setPassRoomInfo(JSON.toJSONString(hotelPriceMonitorCheckInfos));
                    dailyCheckList.add(dailyCheck);

                    // 判断是否审核通过
                    if (hotelViolationsDailyPrice.getCompareResult() == CompareResultTypeEnum.UP.key && dailyCheck.getIsPassCheck() == RfpConstant.constant_0) {
                        isAllUpPass = false;
                    }
                    if (hotelViolationsDailyPrice.getCompareResult() == CompareResultTypeEnum.FULL.key && dailyCheck.getIsPassCheck() == RfpConstant.constant_0) {
                        isAllFullPass = false;
                    }
                }
                if (CollectionUtils.isNotEmpty(dailyCheckList)) {
                    hotelViolationsDailyCheckDao.batchInsert(dailyCheckList);
                }
                logger.info("复查结果 hotelViolationsMonitor {}, {}, {}", hotelViolationsMonitor.getViolationsMonitorId(), isAllUpPass, isAllFullPass);

                BigDecimal paybackScore = BigDecimal.ZERO;
                if (isAllFullPass && hotelViolationsMonitor.getFullPenaltyScore() != null && hotelViolationsMonitor.getFullPenaltyScore().compareTo(BigDecimal.ZERO) > 0) {
                    paybackScore = paybackScore.add(hotelViolationsMonitor.getFullPenaltyScore());
                }
                if (isAllUpPass && hotelViolationsMonitor.getUpPenaltyScore() != null && hotelViolationsMonitor.getUpPenaltyScore().compareTo(BigDecimal.ZERO) > 0) {
                    paybackScore = paybackScore.add(hotelViolationsMonitor.getUpPenaltyScore());
                }

                // 设置复核统计值
                hotelViolationsMonitor.setTotalViolationDayCount(hotelViolationsDailyPrices.size()); // 违规总天数
                hotelViolationsMonitor.setRestorePriceDayCount(restorePriceDayCount); //恢复原价天数
                hotelViolationsMonitor.setTheSameLevelDayCount(theSameLevelDayCount); // 同档房型天数
                hotelViolationsMonitor.setClientRoomClosedDayCount(clientRoomClosedDayCount); // 关闭C端房态天数

                // 设置基本信息
                hotelViolationsMonitor.setRoomTypeId(roomTypeId);
                hotelViolationsMonitor.setBreakfastNum(hotelViolationsMonitor.getBreakfastNum());
                hotelViolationsMonitor.setIsPaybackScore(RfpConstant.constant_1);
                hotelViolationsMonitor.setModifier(modifier);
                hotelViolationsMonitor.setModifyTime(new Date());
                hotelViolationsMonitor.setSendPaybackStatus(SendPaybackEmailStatusEnum.NO_NEED.key);
                // 根据解决
                if (paybackScore.compareTo(BigDecimal.ZERO) > 0) {
                    hotelViolationsMonitor.setStatus(ViolationProcessStatusEnum.RESOLVED.key);
                    hotelViolationsMonitor.setProcessingInfo("满足自动复核要求，自动改为已解决");
                    hotelViolationsMonitor.setProcessingDate(new Date());
                } else {
                    hotelViolationsMonitor.setStatus(ViolationProcessStatusEnum.UNRESOLVED.key);
                    hotelViolationsMonitor.setProcessingInfo("自动复核未满足补偿服务条件");
                    hotelViolationsMonitor.setProcessingDate(new Date());
                }

                int updateResult = hotelViolationsMonitorDao.updateRecheckViolationMonitorResult(hotelViolationsMonitor);
                if (updateResult > 0) {
                    // 记录处理标签记录
                    LabelProcessRecordDto recordDto = new LabelProcessRecordDto();
                    recordDto.setViolationsMonitorId(hotelViolationsMonitor.getViolationsMonitorId());
                    recordDto.setOrgType(OrgTypeEnum.PLATFORM.key);
                    recordDto.setCreator(RfpConstant.CREATOR);
                    recordDto.setProcessMSG(hotelViolationsMonitor.getProcessingInfo());
                    recordDto.setMonitorProcessStatus(hotelViolationsMonitor.getStatus());
                    hotelViolationsMonitorDao.labelProcessRecord(recordDto);
                }
                BigDecimal adjustmentPaybackScore = paybackScore;
                if (hotelViolationsMonitor.getPenaltyScore().compareTo(BigDecimal.ZERO) == 0) {
                    adjustmentPaybackScore = BigDecimal.ZERO;
                }

                // 查询项目意向酒店
                ProjectIntentHotel projectIntentHotel = queryProjectIntentHotel(projectId, hotelId);
                // 自动复核补偿分数不能超过100 bug:手动复核加分之后再跑自动复核，会导致总分超过100
                BigDecimal totalServicePoint = projectIntentHotel.getHotelServicePoints().add(adjustmentPaybackScore);
                if(totalServicePoint.compareTo(new BigDecimal("100")) > 0){
                    adjustmentPaybackScore = new BigDecimal("100").subtract(projectIntentHotel.getHotelServicePoints());
                    totalServicePoint = new BigDecimal("100");
                }
                if (updateResult > 0 && paybackScore.compareTo(BigDecimal.ZERO) > 0) {
                    // 新增补偿记录
                    HotelViolationsMonitor paybackHotelViolationsMonitor = new HotelViolationsMonitor();
                    BeanUtils.copyProperties(hotelViolationsMonitor, paybackHotelViolationsMonitor);
                    paybackHotelViolationsMonitor.setViolationsId(hotelViolationsMonitor.getViolationsMonitorId());
                    paybackHotelViolationsMonitor.setViolationsMonitorId(null);
                    paybackHotelViolationsMonitor.setViolationType(ViolationTypeEnum.REWARDS_OR_COMPENSATION.key);

                    paybackHotelViolationsMonitor.setViolationItem("自动复核补偿分数：" + adjustmentPaybackScore);

                    // 初始化发送电邮短信状态
                    paybackHotelViolationsMonitor.setSendPaybackStatus(SendPaybackEmailStatusEnum.INIT.key);
                    // 更新补偿分数
                    paybackHotelViolationsMonitor.setPenaltyScore(adjustmentPaybackScore);
                    paybackHotelViolationsMonitor.setStatus(RfpConstant.constant_1);
                    paybackHotelViolationsMonitor.setProcessingInfo("满足自动复核要求，自动改为已解决");
                    paybackHotelViolationsMonitor.setProcessingDate(new Date());
                    paybackHotelViolationsMonitor.setHotelServicePoints(totalServicePoint);
                    hotelViolationsMonitorDao.insert(paybackHotelViolationsMonitor);
                }

                // 项目意向酒店加分
                if (updateResult > 0 && adjustmentPaybackScore.compareTo(BigDecimal.ZERO) > 0) {
                    projectIntentHotelDao.addHotelServicePoints(adjustmentPaybackScore, projectIntentHotel.getProjectIntentHotelId());
                }
                // 记录C端满房记录
                if (CollectionUtils.isNotEmpty(violationsFulMonitorLogList)) {
                    violationsFulMonitorLogDao.batchInsert(violationsFulMonitorLogList);
                }
                // 异步保存调用tmchub失败异常
                asyncSaveQueryTmcHubFailedResultList(hotelViolationsMonitor, queryTmcHubFailedResultList);
            } else {
                logger.info("复核获取锁失败 " + lockKey);
                return  "当前复核正在处理中，请稍后再试";
            }
        } finally {
            if (jedis != null) {
                lock.unLock(lockKey, String.valueOf(currentStamp + RedisLockConstant.EXPIRE_TIME_SECOND * 1000 + 1));
                jedis.close();
            }
        }
        return null;

    }

    /**
     * 保存查询tmc结果失败履约异常记录
     */
    private void saveTmcHubFailedResultList(HotelViolationsMonitor hotelViolationsMonitor, List<QueryTmcHubResult> queryTmcHubFailedResultList){
        // 是否需要查询房型映射
        boolean needQueryRoomMapping = false;
        for(QueryTmcHubResult queryTmcHubResult : queryTmcHubFailedResultList){
            if(isNeedCheckRoomMapping(queryTmcHubResult)){
                needQueryRoomMapping = true;
                break;
            }
        }
        // 根据酒店ID查询酒店映射
        List<RoomMappingDto> roomMappingDtoList = null;
        if(needQueryRoomMapping){
            roomMappingDtoList = queryRoomMapping(hotelViolationsMonitor.getHotelId());

        }
        // 过滤酒店房型映射
        Map<Long, RoomMappingDto> roomMappingDtoMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(roomMappingDtoList)) {
            roomMappingDtoMap = roomMappingDtoList.stream().collect(Collectors.toMap(RoomMappingDto::getRoomId, Function.identity(), (o1, o2) -> o1));
        }

        // 保存异常信息
        Map<Long, String> roomTypeNameMap = new HashMap<>();
        List<MonitorCallTmchubError> monitorCallTmchubErrorList = new ArrayList<>();
        for(QueryTmcHubResult queryTmcHubResult : queryTmcHubFailedResultList){
            MonitorCallTmchubError monitorCallTmchubError = new MonitorCallTmchubError();
            monitorCallTmchubError.setOrgId(queryTmcHubResult.getOrgId());
            monitorCallTmchubError.setHotelId(queryTmcHubResult.getHotelId());
            monitorCallTmchubError.setRoomTypeId(queryTmcHubResult.getRoomTypeId());
            monitorCallTmchubError.setRoomType(queryTmcHubResult.getRoomTypeName());
            monitorCallTmchubError.setViolationMonitorId(0L);
            monitorCallTmchubError.setMonitorDate(StringUtil.isEmpty(queryTmcHubResult.getSaleDate()) ? new Date() : DateUtil.stringToDate(queryTmcHubResult.getSaleDate()));
            if(StringUtil.isEmpty(queryTmcHubResult.getRoomTypeName())){
                if(!roomTypeNameMap.containsKey(queryTmcHubResult.getRoomTypeId())) {
                    String roomName = hotelViolationsDailyCheckDao.selectRoomName(queryTmcHubResult.getRoomTypeId());
                    roomTypeNameMap.put(queryTmcHubResult.getRoomTypeId(), roomName);
                }
                monitorCallTmchubError.setRoomType(roomTypeNameMap.get(queryTmcHubResult.getRoomTypeId()));
            }
            if(hotelViolationsMonitor != null && hotelViolationsMonitor.getViolationsMonitorId() > 0){
                monitorCallTmchubError.setViolationMonitorId(hotelViolationsMonitor.getViolationsMonitorId());
            }
            monitorCallTmchubError.setMonitorErrorType(MonitorErrorTypeEnum.INTERFACE.key);
            monitorCallTmchubError.setMonitorTime(new Date());
            monitorCallTmchubError.setErrorReason(QueryTmcHubFailedReasonEnum.getValueByKey(queryTmcHubResult.getQueryTmcHubFailedReason()));
            monitorCallTmchubError.setCreateTime(new Date());
            // 需要检查房型映射
            if(isNeedCheckRoomMapping(queryTmcHubResult)){
                if(CollectionUtils.isEmpty(roomMappingDtoList)){
                    monitorCallTmchubError.setMonitorErrorType(MonitorErrorTypeEnum.NO_MAPPED.key);
                    monitorCallTmchubError.setErrorReason(QueryTmcHubFailedReasonEnum.NO_HOTEL_MAPPED.value);
                    monitorCallTmchubErrorList.add(monitorCallTmchubError);
                } else if(!roomMappingDtoMap.containsKey(monitorCallTmchubError.getRoomTypeId())){
                    monitorCallTmchubError.setMonitorErrorType(MonitorErrorTypeEnum.NO_MAPPED.key);
                    monitorCallTmchubError.setErrorReason(QueryTmcHubFailedReasonEnum.NO_HOTEL_ROOM_MAPPED.value);
                    monitorCallTmchubErrorList.add(monitorCallTmchubError);
                }
            } else {
                monitorCallTmchubErrorList.add(monitorCallTmchubError);
            }
        }
        if(!monitorCallTmchubErrorList.isEmpty()){
            monitorCallTmchubErrorDao.batchInsert(monitorCallTmchubErrorList);
        }
    }

    @Override
    public void initSendPriceViolationsMonitor() throws Exception {
        List<HotelViolationsMonitor> hotelViolationsMonitors = hotelViolationsMonitorDao.queryViolationMonitorBySendStatus(ViolationTypeEnum.PRICE_MONITORING.key);
        if (CollectionUtils.isEmpty(hotelViolationsMonitors)) {
            return;
        }
        List<String> violationsMonitorIds = new ArrayList<>();
        for (HotelViolationsMonitor hotelViolationsMonitor : hotelViolationsMonitors) {
            violationsMonitorIds.add(hotelViolationsMonitor.getViolationsMonitorId().toString());
        }
        RedisService.sadd(RedisConstant.SEND_HOTEL_PRICE_VIOLATIONS_MONITOR, violationsMonitorIds.toArray(new String[violationsMonitorIds.size()]));
    }

    @Override
    public void consumeSendPriceViolationsMonitor(Long violationsMonitorId) throws Exception {
        Response response = queryHotelViolationsDailyPrice(violationsMonitorId);
        if (response == null || response.getData() == null) {
            return;
        }
        QueryViolationsDailyPriceResponse queryViolationsDailyPriceResponse = (QueryViolationsDailyPriceResponse) response.getData();
        ProjectIntentHotel projectIntentHotel = queryProjectIntentHotel(queryViolationsDailyPriceResponse.getProjectId(), queryViolationsDailyPriceResponse.getHotelId());
        boolean sendFlag = sendHtmlService.sendhotelPriceViolationsMonitor(queryViolationsDailyPriceResponse, projectIntentHotel, false);
        if (sendFlag) {
            hotelViolationsMonitorDao.updateViolationMonitorSendStatus(violationsMonitorId);
        }
    }

    @Override
    public void consumeSendRecheckPriceViolationsMonitor(HotelViolationsMonitor hotelViolationsMonitor) throws Exception {
        // 更新发送状态为发送中
        if(Objects.equals(hotelViolationsMonitor.getSendPaybackStatus(), SendPaybackEmailStatusEnum.INIT.key)){
            hotelViolationsMonitor.setSendPaybackStatus(SendPaybackEmailStatusEnum.SENDING.key);
        }
        hotelViolationsMonitorDao.updateRecheckViolationMonitorSendStatus(hotelViolationsMonitor);

        // 查询违规电邮数据
        long violationsMonitorId = hotelViolationsMonitor.getViolationsMonitorId();
        Response response = queryHotelViolationsDailyPrice(violationsMonitorId);
        if (response == null || response.getData() == null) {
            return;
        }
        QueryViolationsDailyPriceResponse queryViolationsDailyPriceResponse = (QueryViolationsDailyPriceResponse) response.getData();
        ProjectIntentHotel projectIntentHotel = queryProjectIntentHotel(queryViolationsDailyPriceResponse.getProjectId(), queryViolationsDailyPriceResponse.getHotelId());
        boolean sendFlag = sendHtmlService.sendhotelPriceViolationsMonitor(queryViolationsDailyPriceResponse, projectIntentHotel, true);
        // 更新发送状态
        hotelViolationsMonitor.setSendPaybackStatus(sendFlag ? SendPaybackEmailStatusEnum.FINISHED_SUCCEEDED.key : SendPaybackEmailStatusEnum.FINISHED_FAILED.key);
        hotelViolationsMonitorDao.updateRecheckViolationMonitorSendStatus(hotelViolationsMonitor);
    }


    @Override
    public void initHotelOrderViolationsMonitor() throws Exception {
        List<Long> projectIds = hotelViolationsConfigDao.queryNeedOrderViolationsMonitorProject();
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }

        //从订单监控中获取分销商编码
        List<QueryOrderMonitorConfigResponse> orderMonitorConfigs = orderMonitorConfigDao.queryOrderMonitorConfig(projectIds);
        if (CollectionUtils.isEmpty(orderMonitorConfigs)) {
            logger.warn("无分销商信息，projectIds:" + JSON.toJSONString(projectIds));
            return;
        }
        Map<String, Long> distributorCodeAndProejctIdMap = new HashMap<>();
        Set<String> distributorCodes = new HashSet<>();
        for (QueryOrderMonitorConfigResponse queryOrderMonitorConfigResponse : orderMonitorConfigs) {
            String distributorCode = queryOrderMonitorConfigResponse.getDistributorCode();
            Long newProjectId = queryOrderMonitorConfigResponse.getProjectId();
            distributorCodes.add(distributorCode);
            if(distributorCodeAndProejctIdMap.containsKey(distributorCode)) {
                Long oldProjectId = distributorCodeAndProejctIdMap.get(distributorCode);
                if(newProjectId > oldProjectId){
                    distributorCodeAndProejctIdMap.put(distributorCode, newProjectId);
                }
            }else{
                distributorCodeAndProejctIdMap.put(distributorCode, newProjectId);
            }
        }
        QueryNeedHotelOrderMonitorRequest queryNeedHotelOrderMonitorRequest = new QueryNeedHotelOrderMonitorRequest();
        queryNeedHotelOrderMonitorRequest.setDistributorCodes(distributorCodes);
        Date date = new Date();
        Date queryStartDate = DateUtil.getDate(date, 0, -1, -5);
        Date queryEndDate = date;
        queryNeedHotelOrderMonitorRequest.setQueryStartDate(DateUtil.dateToString(queryStartDate, RfpConstant.dateFormat));
        queryNeedHotelOrderMonitorRequest.setQueryEndDate(DateUtil.dateToString(queryEndDate, RfpConstant.dateFormat));
        List<NeedHotelOrderMonitorResponse> needHotelOrderMonitorResponses = hotelViolationOrderDetailDao.queryNeedHotelOrderMonitor(queryNeedHotelOrderMonitorRequest);
        if (CollectionUtils.isEmpty(needHotelOrderMonitorResponses)) {
            logger.warn("未查询到订单信息：" + JSON.toJSONString(needHotelOrderMonitorResponses));
            return;
        }

        // 过滤下单金额大于OTA金额的订单
        needHotelOrderMonitorResponses = needHotelOrderMonitorResponses.stream().filter(o -> o.getOtaAmount().multiply(new BigDecimal(o.getRoomNum())).doubleValue() < o.getOrderSum().doubleValue()).collect(Collectors.toList());

        // 分销商过滤
        List<Long> orderIdList = needHotelOrderMonitorResponses.stream().map(NeedHotelOrderMonitorResponse::getOrderId).collect(Collectors.toList());
        List<QueryOrderExcludeSupplierConfigResponse> orderExcludeSupplierConfigResponseList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderIdList)) {
            orderExcludeSupplierConfigResponseList = orderExcludeSupConfigDao.queryOrderExcludeSupplierConfig(projectIds);
        }
        if (!CollectionUtils.isEmpty(orderExcludeSupplierConfigResponseList) ) {
            Set<String> excludeSupplierCodeSet = orderExcludeSupplierConfigResponseList.stream().map(QueryOrderExcludeSupplierConfigResponse::getSupplierCode).collect(Collectors.toSet());
            List<String> excludeSupplierCodeList  = new ArrayList<>(excludeSupplierCodeSet);
            List<List<Long>> orderIdListList = Lists.partition(orderIdList, 1000);
            List<Long> excludeOrderIdList = new ArrayList<>();
            logger.info("excludeSupplierCodeList{}", excludeSupplierCodeList);
            for(List<Long> subOrderIdList : orderIdListList){
                List<Long> excludeOrderIds = hotelViolationOrderDetailDao.queryExcludeSupplierOrderId(subOrderIdList, excludeSupplierCodeList);
                if(CollectionUtils.isNotEmpty(excludeOrderIds)){
                    excludeOrderIdList.addAll(excludeOrderIds);
                }
            }
            // 删除需要过滤的订单ID
            if(CollectionUtils.isNotEmpty(excludeOrderIdList)){
                logger.info("excludeOrderIdList {}", JSON.toJSONString(excludeOrderIdList));
                orderIdList.removeAll(excludeOrderIdList);
            }
        }

        Map<String, List<NeedHotelOrderMonitorResponse>> needHotelOrderMonitorResponseMap = new HashMap<>();
        for (NeedHotelOrderMonitorResponse needHotelOrderMonitorResponse : needHotelOrderMonitorResponses) {
            // 过滤订单
            if(!orderIdList.contains(needHotelOrderMonitorResponse.getOrderId())) {
                continue;
            }

            try {
                Long projectIdInfo = distributorCodeAndProejctIdMap.get(needHotelOrderMonitorResponse.getDistributorCode());
                needHotelOrderMonitorResponse.setProjectId(projectIdInfo);
                String key = projectIdInfo + "_" + needHotelOrderMonitorResponse.getHotelId();
                if (needHotelOrderMonitorResponseMap.containsKey(key)) {
                    needHotelOrderMonitorResponseMap.get(key).add(needHotelOrderMonitorResponse);
                } else {
                    List<NeedHotelOrderMonitorResponse> needHotelOrderMonitorResponsesList = new ArrayList<>();
                    needHotelOrderMonitorResponsesList.add(needHotelOrderMonitorResponse);
                    needHotelOrderMonitorResponseMap.put(key, needHotelOrderMonitorResponsesList);
                }
            } catch (Exception e) {
                logger.error("比较订单信息异常:" + JSON.toJSONString(needHotelOrderMonitorResponse), e);
            }
        }

        if (needHotelOrderMonitorResponseMap.size() > 0) {
            Set<String> hotelOrderMonitorResponses = new HashSet<>();
            for (Map.Entry<String, List<NeedHotelOrderMonitorResponse>> stringListEntry : needHotelOrderMonitorResponseMap.entrySet()) {
                String[] split = stringListEntry.getKey().split("_");
                String projectId = split[0];
                String hotelId = split[1];
                HotelOrderMonitorResponse hotelOrderMonitorResponse = new HotelOrderMonitorResponse();
                hotelOrderMonitorResponse.setHotelId(Long.valueOf(hotelId));
                hotelOrderMonitorResponse.setProjectId(Long.valueOf(projectId));
                hotelOrderMonitorResponse.setQueryEndDate(queryEndDate);
                hotelOrderMonitorResponse.setQueryStartDate(queryStartDate);
                hotelOrderMonitorResponse.setNeedHotelOrderMonitorResponses(stringListEntry.getValue());
                hotelOrderMonitorResponses.add(JSON.toJSONString(hotelOrderMonitorResponse));
            }
            RedisService.sadd(RedisConstant.HOTEL_ORDER_VIOLATIONS_MONITOR, hotelOrderMonitorResponses.toArray(new String[hotelOrderMonitorResponses.size()]));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void consumeHotelOrderViolationsMonitor(HotelOrderMonitorResponse hotelOrderMonitorResponse) {
        if (hotelOrderMonitorResponse == null) {
            return;
        }
        Long projectId = hotelOrderMonitorResponse.getProjectId();
        Long hotelId = hotelOrderMonitorResponse.getHotelId();

        QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
        queryProjectIntentHotelDetailDto.setProjectId(projectId);
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(hotelId);
        queryProjectIntentHotelDetailDto.setHotelIds(hotelIds);

        // 项目补充履约监控名单
        ProjectAddedHotel projectAddedHotel = null;
        ProjectIntentHotel projectIntentHotel = null;
        List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
        if (CollectionUtils.isEmpty(projectIntentHotels)) {
            logger.error("邀请酒店信息为空,projectId：" + projectId + ",hotelId:" + hotelId);

            // 查询是否存在项目酒店补充履约名单
            projectAddedHotel = projectAddedHotelDao.selectByProjectIdAndHotelId(projectId, hotelId);
            if(projectAddedHotel == null) {
                return;
            }
        } else {
            projectIntentHotel = projectIntentHotels.get(0);
            if (projectIntentHotel.getBidState().intValue() != HotelBidStateEnum.BID_WINNING.bidState.intValue()) {
                return;
            }
        }
        HotelViolationsConfig hotelViolationsConfig = hotelViolationsConfigDao.queryHotelViolationConfig(projectId);
        if (hotelViolationsConfig == null) {
            return;
        }
        if (hotelViolationsConfig.getOrderPiNotice() == null || hotelViolationsConfig.getOrderPiNotice() == RfpConstant.constant_0) {
            return;
        }
        List<NeedHotelOrderMonitorResponse> needHotelOrderMonitorResponses = hotelOrderMonitorResponse.getNeedHotelOrderMonitorResponses();
        if (CollectionUtils.isEmpty(needHotelOrderMonitorResponses)) {
            return;
        }
        List<NeedHotelOrderMonitorResponse> filteredNeedHotelOrderMonitorResponses = new ArrayList<>();
        for (NeedHotelOrderMonitorResponse needHotelOrderMonitorRespons : needHotelOrderMonitorResponses) {
            BigDecimal orderSum = needHotelOrderMonitorRespons.getOrderSum();
            BigDecimal otaAmount = needHotelOrderMonitorRespons.getOtaAmount().multiply(new BigDecimal(needHotelOrderMonitorRespons.getRoomNum()));
            BigDecimal difAmount = orderSum.subtract(otaAmount).abs();
            if(difAmount.doubleValue() < 1){
                continue;
            }
            //校验是否满足发送通知
            if (difAmount.multiply(new BigDecimal(100)).doubleValue() <= (orderSum.multiply(hotelViolationsConfig.getOrderPiAvg()).doubleValue())) {
                continue;
            }
            filteredNeedHotelOrderMonitorResponses.add(needHotelOrderMonitorRespons);
        }

        if (filteredNeedHotelOrderMonitorResponses.size() <= 0) {
            return;
        }

        List<String> orderCodes = new ArrayList<>();
        Map<String, NeedHotelOrderMonitorResponse> filteredNeedHotelOrderMonitorResponMap = new HashMap<>();
        for (NeedHotelOrderMonitorResponse filteredNeedHotelOrderMonitorRespons : filteredNeedHotelOrderMonitorResponses) {
            orderCodes.add(filteredNeedHotelOrderMonitorRespons.getOrderCode());
            filteredNeedHotelOrderMonitorResponMap.put(filteredNeedHotelOrderMonitorRespons.getOrderCode(), filteredNeedHotelOrderMonitorRespons);
        }

        //校验订单是否已经监控
        List<HotelViolationOrderDetail> hotelViolationOrderDetails = hotelViolationOrderDetailDao.queryByOrderCode(orderCodes);
        if (hotelViolationOrderDetails != null && hotelViolationOrderDetails.size() > 0) {
            for (HotelViolationOrderDetail hotelViolationOrderDetail : hotelViolationOrderDetails) {
                if (orderCodes.contains(hotelViolationOrderDetail.getOrderCode())) {
                    orderCodes.remove(hotelViolationOrderDetail.getOrderCode());
                }
            }
            if (orderCodes.size() <= 0) {
                return;
            }
        }

        List<QueryHotelOrderInfoResponse> queryHotelOrderInfoResponses = hotelViolationOrderDetailDao.queryHotelOrderInfo(orderCodes);
        if (queryHotelOrderInfoResponses == null || queryHotelOrderInfoResponses.size() <= 0) {
            logger.error("查询订单详情为空：" + JSON.toJSONString(hotelOrderMonitorResponse));
            return;
        }
        Map<Long, QueryHotelOrderInfoResponse> queryHotelOrderInfoResponsMap = new HashMap<>();
        for (QueryHotelOrderInfoResponse queryHotelOrderInfoRespons : queryHotelOrderInfoResponses) {
            queryHotelOrderInfoResponsMap.put(queryHotelOrderInfoRespons.getOrderId(), queryHotelOrderInfoRespons);
        }

        List<HotelOrderRoomRateDetailsResponse> hotelOrderRoomRateDetailsResponses = hotelViolationOrderDetailDao.queryHotelOrderRoomRateDetails(queryHotelOrderInfoResponsMap.keySet());
        if (CollectionUtils.isEmpty(hotelOrderRoomRateDetailsResponses)) {
            logger.error("查询订单每日房费为空：" + JSON.toJSONString(hotelOrderMonitorResponse));
            return;
        }
        Map<Long, List<HotelOrderRoomRateDetailsResponse>> hotelOrderRoomRateDetailsResponseMap = new HashMap<>();
        for (HotelOrderRoomRateDetailsResponse hotelOrderRoomRateDetailsRespons : hotelOrderRoomRateDetailsResponses) {
            Long orderId = hotelOrderRoomRateDetailsRespons.getOrderId();
            if (hotelOrderRoomRateDetailsResponseMap.containsKey(hotelOrderRoomRateDetailsRespons.getOrderId())) {
                hotelOrderRoomRateDetailsResponseMap.get(orderId).add(hotelOrderRoomRateDetailsRespons);
            } else {
                List<HotelOrderRoomRateDetailsResponse> hotelOrderRoomRateDetailsResponseInfos = new ArrayList<>();
                hotelOrderRoomRateDetailsResponseInfos.add(hotelOrderRoomRateDetailsRespons);
                hotelOrderRoomRateDetailsResponseMap.put(orderId, hotelOrderRoomRateDetailsResponseInfos);
            }
        }

        //从订单监控中获取分销商编码
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(projectId);
        List<QueryOrderMonitorConfigResponse> orderMonitorConfigs = orderMonitorConfigDao.queryOrderMonitorConfig(projectIds);
        if (CollectionUtils.isEmpty(orderMonitorConfigs)) {
            logger.error("无分销商信息，projectId:" + projectId + ",hotelId:" + hotelId);
            return;
        }
        List<String> distributorCodes = new ArrayList();
        for (QueryOrderMonitorConfigResponse queryOrderMonitorConfigResponse : orderMonitorConfigs) {
            distributorCodes.add(queryOrderMonitorConfigResponse.getDistributorCode());
        }

        List<ViolationsRemindConfig> violationsRemindConfigs = violationsRemindConfigDao.queryViolationsRemindConfig(projectId);
        QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
        queryRoomNightsDto.setHotelId(hotelId);
        queryRoomNightsDto.setDistributorCodes(distributorCodes);
        Date startDate = new Date();
        queryRoomNightsDto.setStartTime(DateUtil.dateToString(DateUtil.getDate(startDate,-30,0)));
        queryRoomNightsDto.setEndTime(DateUtil.dateToString(startDate));
        Integer roomNights = disHotelDailyOrderDao.selectRoomNights(queryRoomNightsDto);
        if (roomNights == null) {
            roomNights = 0;
        }
        StringBuffer remindTypes = new StringBuffer();
        if (CollectionUtils.isNotEmpty(violationsRemindConfigs)) {
            //查询7天内未解决的任务
            List<HotelViolationsMonitor> hotelViolationsMonitors = hotelViolationsMonitorDao.queryHotelViolationsMonitors(hotelId, projectId);
            int needProcessing = CollectionUtils.isNotEmpty(hotelViolationsMonitors) ? hotelViolationsMonitors.size() : 0;
            for (ViolationsRemindConfig violationsRemindConfig : violationsRemindConfigs) {
                if (violationsRemindConfig.getMaximumNights().intValue() >= roomNights.intValue()
                        && violationsRemindConfig.getMinimumNights().intValue() <= roomNights.intValue()) {
                    if (needProcessing >= violationsRemindConfig.getNeedSystemReminderCount().intValue()) {
                        remindTypes.append(",").append(ReminderTypeEnum.SYSTEM_REMINDER.key.toString());
                    }
                    if (needProcessing >= violationsRemindConfig.getNeedPriorityReminderCount().intValue()) {
                        remindTypes.append(",").append(ReminderTypeEnum.PRIORITY_REMINDER.key.toString());
                    }
                    if (needProcessing >= violationsRemindConfig.getNeedJialiFollowUpCount().intValue()) {
                        remindTypes.append(",").append(ReminderTypeEnum.JIALI_FOLLOW_UP.key.toString());
                    }
                    if (needProcessing >= violationsRemindConfig.getNeedBusinessFollowUpCount().intValue()) {
                        remindTypes.append(",").append(ReminderTypeEnum.BUSINESS_FOLLOW_UP.key.toString());
                    }
                }
            }
        }

        Set<Long> longs = hotelOrderRoomRateDetailsResponseMap.keySet();
        BigDecimal penaltyScore = new BigDecimal(longs.size()).multiply(hotelViolationsConfig.getOrderPiPenaltyScore());
        String violationItemString = DateUtil.dateToString(hotelOrderMonitorResponse.getQueryStartDate(), RfpConstant.dateFormat) + "至"
                + DateUtil.dateToString(hotelOrderMonitorResponse.getQueryEndDate(), RfpConstant.dateFormat) + "价格高于散客售价订单" + longs.size() + "个";
        HotelViolationsMonitor hotelViolationsMonitor = new HotelViolationsMonitor();
        hotelViolationsMonitor.setHotelId(hotelOrderMonitorResponse.getHotelId());
        hotelViolationsMonitor.setProjectId(projectId);
        hotelViolationsMonitor.setViolationItem(violationItemString);
        hotelViolationsMonitor.setViolationType(ViolationTypeEnum.ORDER_MONITORING.key);
        hotelViolationsMonitor.setPenaltyScore(penaltyScore);
        hotelViolationsMonitor.setFullPenaltyScore(BigDecimal.ZERO);
        hotelViolationsMonitor.setUpPenaltyScore(BigDecimal.ZERO);
        hotelViolationsMonitor.setStatus(HotelViolationMonitorEnum.QUESTIONABILITY.key);
        hotelViolationsMonitor.setCreator(RfpConstant.CREATOR);
        hotelViolationsMonitor.setModifier(RfpConstant.CREATOR);
        hotelViolationsMonitor.setProcurementVolume(roomNights);
        hotelViolationsMonitor.setReminderLevel(remindTypes.length() > 0 ? remindTypes.substring(1) : null);
        hotelViolationsMonitor.setSendStatus(RfpConstant.constant_0);
        // 补充履约监控名单默认100
        BigDecimal subtract = new BigDecimal("100");
        if(projectIntentHotel != null) {
            subtract = projectIntentHotel.getHotelServicePoints().subtract(penaltyScore);
        } else {
            // 查询最大的服务分
            HotelViolationsMonitor lastHotelViolationsMonitor = null;
            Long maxHotelViolationsMonitorId = hotelViolationsMonitorDao.queryMaxHotelViolationsMonitorId(hotelId, projectId);
            if(maxHotelViolationsMonitorId != null){
                lastHotelViolationsMonitor = hotelViolationsMonitorDao.getByViolationsMonitorId(maxHotelViolationsMonitorId);
            }
            if(lastHotelViolationsMonitor != null){
                subtract = lastHotelViolationsMonitor.getHotelServicePoints();
            }
            subtract = subtract.subtract(penaltyScore);
        }
        if(subtract.doubleValue() <= 0) {
            hotelViolationsMonitor.setHotelServicePoints(new BigDecimal(0));
        }else{
            hotelViolationsMonitor.setHotelServicePoints(subtract);
        }
        String orderMonitorTime = DateUtil.dateToString(hotelOrderMonitorResponse.getQueryStartDate(), RfpConstant.dateFormat1)
                + "~" + DateUtil.dateToString(hotelOrderMonitorResponse.getQueryEndDate(), RfpConstant.dateFormat1);
        hotelViolationsMonitor.setOrderMonitorTime(orderMonitorTime);
        int insert = hotelViolationsMonitorDao.insert(hotelViolationsMonitor);
        if (insert != 1) {
            throw new RuntimeException("保存酒店违规监控信息失败,projectId:" + projectId + ",hotelId:" + hotelId);
        }
        ArrayList<HotelViolationOrderDetail> hotelViolationOrderDetailslList = new ArrayList<>();
        HotelViolationOrderDetail hotelViolationOrderDetail;
        for (QueryHotelOrderInfoResponse queryHotelOrderInfoResponse : queryHotelOrderInfoResponses) {
            try {
                Long orderId = queryHotelOrderInfoResponse.getOrderId();
                String orderCode = queryHotelOrderInfoResponse.getOrderCode();
                NeedHotelOrderMonitorResponse needHotelOrderMonitorResponse = filteredNeedHotelOrderMonitorResponMap.get(orderCode);
                hotelViolationOrderDetail = new HotelViolationOrderDetail();
                hotelViolationOrderDetail.setViolationsMonitorId(hotelViolationsMonitor.getViolationsMonitorId());
                hotelViolationOrderDetail.setOrderCode(orderCode);
                hotelViolationOrderDetail.setOrderId(orderId);
                hotelViolationOrderDetail.setHotelId(queryHotelOrderInfoResponse.getHotelId());
                hotelViolationOrderDetail.setRoomTypeId(queryHotelOrderInfoResponse.getRoomTypeId());
                hotelViolationOrderDetail.setRoomnum(queryHotelOrderInfoResponse.getRoomNum());
                Date checkInDate = queryHotelOrderInfoResponse.getCheckInDate();
                Date checkOutDate = queryHotelOrderInfoResponse.getCheckOutDate();
                long day = DateUtil.getDay(DateUtil.dateFormat(checkInDate, DateUtil.defaultFormat), DateUtil.dateFormat(checkOutDate, DateUtil.defaultFormat));
                hotelViolationOrderDetail.setNights(new Long(day).intValue());
                List<HotelOrderRoomRateDetailsResponse> hotelOrderRoomRateDetailsResponseList = hotelOrderRoomRateDetailsResponseMap.get(orderId);
                Collections.sort(hotelOrderRoomRateDetailsResponseList);
                hotelViolationOrderDetail.setBreakfastnum(hotelOrderRoomRateDetailsResponseList.get(0).getBreakfastNum());
                hotelViolationOrderDetail.setOrderAmount(needHotelOrderMonitorResponse.getOrderSum());
                hotelViolationOrderDetail.setOtaAmount(needHotelOrderMonitorResponse.getOtaAmount().multiply(new BigDecimal(needHotelOrderMonitorResponse.getRoomNum())));
                hotelViolationOrderDetail.setOtaPriceIds(needHotelOrderMonitorResponse.getOtaPriceIds());
                hotelViolationOrderDetail.setSaveAmount(needHotelOrderMonitorResponse.getOtaAmount().multiply(new BigDecimal(needHotelOrderMonitorResponse.getRoomNum())).subtract(needHotelOrderMonitorResponse.getOrderSum()));
                hotelViolationOrderDetail.setCreateOrderTime(queryHotelOrderInfoResponse.getCreateTime());
                StringBuffer orderDailyPriceString = new StringBuffer();
                Map<String, HotelOrderRoomRateDetailsResponse> hotelOrderRoomRateDetailsResponseHashMap = new HashMap<>();
                for (HotelOrderRoomRateDetailsResponse hotelOrderRoomRateDetailsResponse : hotelOrderRoomRateDetailsResponseList) {
                    hotelOrderRoomRateDetailsResponseHashMap.put(DateUtil.dateToString(hotelOrderRoomRateDetailsResponse.getRoomDate()), hotelOrderRoomRateDetailsResponse);
                }
                for (Map.Entry<String, HotelOrderRoomRateDetailsResponse> stringHotelOrderRoomRateDetailsResponseEntry : hotelOrderRoomRateDetailsResponseHashMap.entrySet()) {
                    HotelOrderRoomRateDetailsResponse hotelOrderRoomRateDetailsResponse = stringHotelOrderRoomRateDetailsResponseEntry.getValue();
                    orderDailyPriceString.append(DateUtil.dateToString(hotelOrderRoomRateDetailsResponse.getRoomDate(), RfpConstant.dateFormat2)).append("(").append(hotelOrderRoomRateDetailsResponse.getRoomPrice().toString()).append(")").append("，");
                }
                hotelViolationOrderDetail.setOrderDailyPrice(orderDailyPriceString.substring(0,orderDailyPriceString.length()-1));
                hotelViolationOrderDetailslList.add(hotelViolationOrderDetail);
            } catch (Exception e) {
                logger.error("订单监控每日报价数据组装异常：" + JSON.toJSONString(queryHotelOrderInfoResponse), e);
            }
        }
        if (CollectionUtils.isNotEmpty(hotelViolationOrderDetailslList)) {
            hotelViolationOrderDetailDao.batchInsert(hotelViolationOrderDetailslList);
            if(projectIntentHotel != null){
                projectIntentHotelDao.updateHotelServicePoints(penaltyScore, projectIntentHotel.getProjectIntentHotelId());
                // 更新违规监控统计
                projectIntentHotelService.statByHotelViolationMonitor(projectIntentHotel, hotelViolationsMonitor);
            }
        } else {
            throw new RuntimeException("订单监控每日报价数据组装为空" + JSON.toJSONString(hotelOrderMonitorResponse));
        }
    }

    @Override
    public void initSendOrderViolationsMonitor() throws Exception {
        List<HotelViolationsMonitor> hotelViolationsMonitors = hotelViolationsMonitorDao.queryViolationMonitorBySendStatus(ViolationTypeEnum.ORDER_MONITORING.key);
        if (CollectionUtils.isEmpty(hotelViolationsMonitors)) {
            return;
        }
        List<String> violationsMonitorIds = new ArrayList<>();
        for (HotelViolationsMonitor hotelViolationsMonitor : hotelViolationsMonitors) {
            violationsMonitorIds.add(hotelViolationsMonitor.getViolationsMonitorId().toString());
        }
        RedisService.sadd(RedisConstant.SEND_HOTEL_ORDER_VIOLATIONS_MONITOR, violationsMonitorIds.toArray(new String[violationsMonitorIds.size()]));
    }

    @Override
    public void consumeSendOrderViolationsMonitor(Long violationsMonitorId) throws Exception {
        Response response = queryHotelViolationsOrderDetail(violationsMonitorId);
        if (response == null || response.getData() == null) {
            return;
        }
        QueryViolationsOrderDetailResponse queryViolationsOrderDetailResponse = (QueryViolationsOrderDetailResponse) response.getData();

        QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
        queryProjectIntentHotelDetailDto.setProjectId(queryViolationsOrderDetailResponse.getProjectId());
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(queryViolationsOrderDetailResponse.getHotelId());
        queryProjectIntentHotelDetailDto.setHotelIds(hotelIds);
        List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);

        ProjectIntentHotel projectIntentHotel = null;
        // 项目附加履约酒店
        ProjectAddedHotel projectAddedHotel = null;
        if (CollectionUtils.isEmpty(projectIntentHotels)) {
            logger.error("邀请酒店信息为空,projectId：" + queryViolationsOrderDetailResponse.getProjectId() + ",hotelId:" + queryViolationsOrderDetailResponse.getHotelId() + ",violationsMonitorId:" + violationsMonitorId);

            // 是否存在项目附加履约酒店
            //若监控到“补充履约监控名单”中的酒店有倒挂情况时，根据酒店所属酒店机构的联系人信息，发送履约通知短信和履约通知邮件
            projectAddedHotel = projectAddedHotelDao.selectByProjectIdAndHotelId(queryViolationsOrderDetailResponse.getProjectId(), queryViolationsOrderDetailResponse.getHotelId());
            if(projectAddedHotel == null || Objects.equals(projectAddedHotel.getNotifyType(), ProjectMonitorNotifyUserTypeEnum.NO_NOTIFY.key)) {
                return;
            }
            projectAddedHotel = projectAddedHotelService.setContactInfoByNotifyType(projectAddedHotel);
            if(!StringUtil.isValidString(projectAddedHotel.getContactEmail()) && !StringUtil.isValidString(projectAddedHotel.getContactMobile()) && !StringUtil.isValidString(projectAddedHotel.getContactUserName())){
                logger.error("邀请酒店联系信息为空,projectId：" + queryViolationsOrderDetailResponse.getProjectId() + ",hotelId:" + queryViolationsOrderDetailResponse.getHotelId() + ",violationsMonitorId:" + violationsMonitorId);
                return;
            }
            projectIntentHotel = new ProjectIntentHotel();
            projectIntentHotel.setProjectId(projectAddedHotel.getProjectId());
            projectIntentHotel.setHotelId(projectAddedHotel.getHotelId());
            projectIntentHotel.setBidContactEmail(projectAddedHotel.getContactEmail());
            projectIntentHotel.setBidContactName(projectAddedHotel.getContactUserName());
            projectIntentHotel.setBidContactMobile(projectAddedHotel.getContactMobile());
        } else {
            projectIntentHotel = projectIntentHotels.get(0);
        }
        boolean sendFlag = sendHtmlService.sendhotelOrderViolationsMonitor(queryViolationsOrderDetailResponse, projectIntentHotel);
        if (sendFlag) {
            hotelViolationsMonitorDao.updateViolationMonitorSendStatus(violationsMonitorId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response addHotelViolationMonitor(AddHotelViolationMonitorDto addHotelViolationMonitorDto) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        Long projectId = addHotelViolationMonitorDto.getProjectId();
        Long hotelId = addHotelViolationMonitorDto.getHotelId();
        String rewardMessage = addHotelViolationMonitorDto.getRewardMessage();
        BigDecimal rewardScore = addHotelViolationMonitorDto.getRewardScore();
        Long orgId = addHotelViolationMonitorDto.getOrgId();
        if (projectId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("项目不能为空");
            return response;
        }
        if (hotelId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("酒店不能为空");
            return response;
        }
        if (!StringUtil.isValidString(rewardMessage) || rewardMessage.length()>500) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("奖励或补偿原因不能为空且长度不能超过500");
            return response;
        }
        if (rewardScore == null || rewardScore.doubleValue()<=0 || rewardScore.doubleValue()>100) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("奖励或补偿分数必须大于0小于100");
            return response;
        }
        Project project = projectDao.selectByPrimaryKey(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("无效的项目信息");
            return response;
        }
        if(orgId != null){
            if(project.getTenderOrgId() == null || project.getTenderOrgId().intValue() != orgId){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("项目不属于当前企业");
                return response;
            }
        }

        QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
        queryProjectIntentHotelDetailDto.setProjectId(projectId);
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(hotelId);
        queryProjectIntentHotelDetailDto.setBidState(HotelBidStateEnum.BID_WINNING.bidState);
        queryProjectIntentHotelDetailDto.setHotelIds(hotelIds);

        List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
        ProjectAddedHotel projectAddedHotel = projectAddedHotelDao.selectByProjectIdAndHotelId(projectId, hotelId);
        if(CollectionUtils.isEmpty(projectIntentHotels) && projectAddedHotel == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("当前项目未查询到酒店信息");
            return response;
        }
        String hotelName = projectIntentHotelDao.selectHotelNameByHotelId(hotelId);

        // 补充履约监控名单默认100
        ProjectIntentHotel projectIntentHotel = null;
        BigDecimal addHotelServicePoints = new BigDecimal("100");
        if(CollectionUtils.isNotEmpty(projectIntentHotels)) {
            projectIntentHotel = projectIntentHotels.get(0);
            addHotelServicePoints = projectIntentHotel.getHotelServicePoints().add(rewardScore);
        } else {
            // 查询最大的服务分  JEHF-2739 履约监控违规补充名单监控到订单倒挂违规后，可以进行人工补偿
            HotelViolationsMonitor lastHotelViolationsMonitor = null;
            Long maxHotelViolationsMonitorId = hotelViolationsMonitorDao.queryMaxHotelViolationsMonitorId(hotelId, projectId);
            if(maxHotelViolationsMonitorId != null){
                lastHotelViolationsMonitor = hotelViolationsMonitorDao.getByViolationsMonitorId(maxHotelViolationsMonitorId);
            }
            if(lastHotelViolationsMonitor != null){
                addHotelServicePoints = lastHotelViolationsMonitor.getHotelServicePoints();
            }
            addHotelServicePoints = addHotelServicePoints.add(rewardScore);
        }

        if(addHotelServicePoints.doubleValue()>100){
            String msg = project.getProjectName() + "项目，" + hotelName + "酒店当前服务分为" + addHotelServicePoints + "分，本次提交奖励或补偿" + rewardScore + "分，奖励或补偿分数后服务分超过100，请确认后重新填写。";
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(msg);
            return response;
        }
        HotelViolationsMonitor hotelViolationsMonitor = new HotelViolationsMonitor();
        hotelViolationsMonitor.setHotelId(hotelId);
        hotelViolationsMonitor.setProjectId(projectId);
        hotelViolationsMonitor.setViolationItem(rewardMessage);
        hotelViolationsMonitor.setViolationType(ViolationTypeEnum.REWARDS_OR_COMPENSATION.key);
        hotelViolationsMonitor.setPenaltyScore(rewardScore);
        hotelViolationsMonitor.setFullPenaltyScore(BigDecimal.ZERO);
        hotelViolationsMonitor.setUpPenaltyScore(BigDecimal.ZERO);
        hotelViolationsMonitor.setStatus(HotelViolationMonitorEnum.RESOLVED.key);
        hotelViolationsMonitor.setCreator(addHotelViolationMonitorDto.getOperator());
        hotelViolationsMonitor.setModifier(addHotelViolationMonitorDto.getOperator());
        hotelViolationsMonitor.setProcurementVolume(null);
        hotelViolationsMonitor.setReminderLevel(null);
        hotelViolationsMonitor.setSendStatus(RfpConstant.constant_1);
        hotelViolationsMonitor.setHotelServicePoints(addHotelServicePoints);
        hotelViolationsMonitor.setSendTime(new Date());
        int insert = hotelViolationsMonitorDao.insert(hotelViolationsMonitor);
        if (insert != 1) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("保存失败");
            return response;
        }
        if(projectIntentHotel != null) {
            projectIntentHotelDao.addHotelServicePoints(rewardScore, projectIntentHotel.getProjectIntentHotelId());
        }
        return response;
    }

    @Override
    public Response resolvedNotCooperationViolation() {
        // 定义返回值
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        List<Long> notCooperationProjectIntentIdList = projectIntentHotelDao.queryLastNotCooperationProjectIntentHotel();
        logger.info("7天前不处理违规报价违规 {}", notCooperationProjectIntentIdList);

        if(CollectionUtils.isEmpty(notCooperationProjectIntentIdList)){
            response.setData(0);
            return response;
        }
        Date before7Days = cn.hutool.core.date.DateUtil.offsetDay(DateUtil.getCurrentDate(), -7);
        for(Long projectIntentHotelId : notCooperationProjectIntentIdList){
            // 查询是否违规日期超过7天
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
            Date lastViolationDate = hotelViolationsMonitorDao.queryMaxHotelViolationsMonitorDate(projectIntentHotel.getHotelId(), projectIntentHotel.getProjectId());
            if(lastViolationDate != null && lastViolationDate.before(before7Days)){
                MonitorStatLabelProcessDto monitorStatLabelProcessDto = new MonitorStatLabelProcessDto();
                monitorStatLabelProcessDto.setProjectIntentHotelId(projectIntentHotelId);
                monitorStatLabelProcessDto.setStatProcessStatus(ViolationStatProcessStatusEnum.RESOLVED.key);
                monitorStatLabelProcessDto.setProcessMSG("不配合酒店最近7天无违规记录，状态改为已解决");
                monitorStatLabelProcessDto.setOrgType(OrgTypeEnum.PLATFORM.key);
                monitorStatLabelProcessDto.setOperator(RfpConstant.CREATOR);
                monitorStatLabelProcess(monitorStatLabelProcessDto);
            }
        }
        response.setData(notCooperationProjectIntentIdList.size());
        return response;
    }


    private boolean upRoomMonitor(Set<String> upPriceList, int piDays, BigDecimal piDaysAvg, Map<String, HotelPriceMonitor> hotelPriceMonitorMap, BigDecimal basePrice) {
        if (upPriceList.size() != 0 && upPriceList.size() >= piDays) {
            BigDecimal monitorDifTotalPrice = new BigDecimal(0);
            for (String saleDate : upPriceList) {
                HotelPriceMonitor hotelPriceMonitor = hotelPriceMonitorMap.get(saleDate);
                monitorDifTotalPrice = monitorDifTotalPrice.add(hotelPriceMonitor.getDifPrice());
            }

            BigDecimal totalPrice = basePrice.multiply(new BigDecimal(upPriceList.size()));
            if (monitorDifTotalPrice.divide(totalPrice, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).doubleValue() > piDaysAvg.doubleValue()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否需要检查房型
     */
    private boolean isNeedCheckRoomMapping(QueryTmcHubResult queryTmcHubResult){
        if(queryTmcHubResult == null || queryTmcHubResult.isResult()){
            return false;
        }
        return queryTmcHubResult.getQueryTmcHubFailedReason() == QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL.key ||
                queryTmcHubResult.getQueryTmcHubFailedReason() == QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL_ROOMS.key;
    }


    private boolean fullRoomMonitor(Set<String> fullRoomList, int fullDays, int otherDays,
                                    Map<String, HotelPriceMonitor> hotelPriceMonitorMap, Long hotelId,
                                    List<ViolationsFullMonitorLog> violationsFulMonitorLogList,
                                    List<QueryTmcHubResult> failedResultList,
                                    Map<String, com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> tmcProductDetailMap) {
        if (!fullRoomList.isEmpty() && fullRoomList.size() >= fullDays) {
            int haveRoomCount = 0;
            for (String saleDate : fullRoomList) {
                HotelPriceMonitor hotelPriceMonitor = hotelPriceMonitorMap.get(saleDate);

                // 初始化违规满分查询日志
                ViolationsFullMonitorLog violationsFulMonitorLog = new ViolationsFullMonitorLog();
                violationsFulMonitorLog.setSaleDate(DateUtil.stringToDate(saleDate));
                violationsFulMonitorLog.setQueryDateTime(new Date());
                violationsFulMonitorLog.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
                violationsFulMonitorLogList.add(violationsFulMonitorLog);

                //如果返回true ,代表有房
                QueryTmcHubResult queryTmcHubResult = queryTmcHubProduct(hotelId, hotelPriceMonitor, saleDate, DateUtil.dateToString(DateUtil.getDate(DateUtil.stringToDate(saleDate), 1, 0)), violationsFulMonitorLog, tmcProductDetailMap);
                logger.info("queryTmcHubResult : " + hotelPriceMonitor.getRoomTypeId() + " " + JsonUtil.objectToJson(queryTmcHubResult));
                if (queryTmcHubResult.isResult()) {
                    haveRoomCount++;
                } else {
                    failedResultList.add(queryTmcHubResult);
                }
            }
            if (haveRoomCount >= otherDays) {
                return true;
            }
        }
        return false;
    }

    // 查询房型映射
    private List<RoomMappingDto> queryRoomMapping(Long hotelId){
        Map<String, List<RoomMappingDto>> resultMap = new HashMap<>();

        // 查询API
        String requestUrl = BaseConfig.getHotelSyncBldServerUrl() + "queryRoomMapping/PABLD/" + hotelId;
        String responseJson = "";
        try {
            // 查询酒店映射结果
            responseJson = HttpClientUtil.getMethod(requestUrl, BaseConfig.getConnectTimeOut(), BaseConfig.getSocketTimeOut());
            ObjectMapper objectMapper = new ObjectMapper();
            resultMap = objectMapper.readValue(responseJson, new TypeReference<Map<String, List<RoomMappingDto>>>() {
            });
        } catch (Exception ex){
            logger.info("queryRoomMapping request: " + requestUrl);
            logger.info("queryRoomMapping response: " + responseJson);
            logger.error("queryRoomMapping异常", ex);
        }
        return resultMap.get(String.valueOf(hotelId));
    }

    /**
     * 自动补偿预检查tmc hub接口 更新缓存
     */
    private void prepareRepeatQueryTmcHubProduct(Long hotelId, HotelViolationsDailyPrice hotelViolationsDailyPrice, String checkInTime, String checkOutTime, String orgPartnerCode) {
        //调产品查询接口
        try {
            ProductDetailRequest productDetailRequest = new ProductDetailRequest();
            productDetailRequest.setHotelId(hotelId);
            productDetailRequest.setCheckInDate(checkInTime);
            productDetailRequest.setCheckOutDate(checkOutTime);
            SignatureHelpRequestDto signatureHelpRequestDto = new SignatureHelpRequestDto();
            signatureHelpRequestDto.setPartnerCode(orgPartnerCode);
            signatureHelpRequestDto.setSecurityKey("noSignature");
            tmcHubApiManager.queryProductDetailForNoSign(productDetailRequest, signatureHelpRequestDto,true);
        } catch (Exception e) {
            String errorMessage = "预先检查查询产品接口异常，酒店id：" + hotelId + ",roomId：" + hotelViolationsDailyPrice.getRoomTypeId() + ",checkInTime：" + checkInTime + ",checkOutTime：" + checkOutTime;
            logger.error(errorMessage);

        }
    }

    private QueryTmcHubResult queryTmcHubProduct(Long hotelId, HotelPriceMonitor hotelPriceMonitor, String checkInTime, String checkOutTime, ViolationsFullMonitorLog violationsFulMonitorLog, Map<String, com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> tmcProductDetailMap) {
        // 定义查询tmc hub返回结果
        QueryTmcHubResult queryTmcHubResult = new QueryTmcHubResult();
        queryTmcHubResult.setOrgId(hotelPriceMonitor.getOrgId());
        queryTmcHubResult.setHotelId(hotelId);
        queryTmcHubResult.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
        queryTmcHubResult.setRoomTypeName(hotelPriceMonitor.getRoomTypeName());
        queryTmcHubResult.setSaleDate(checkInTime);
        //调产品查询接口
        try {
            com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> productDetailResponseResponse = null;
            if(tmcProductDetailMap.containsKey(checkInTime + "#" + checkOutTime)){
                productDetailResponseResponse = tmcProductDetailMap.get(checkInTime + "#" + checkOutTime);
            } else {
                ProductDetailRequest productDetailRequest = new ProductDetailRequest();
                productDetailRequest.setHotelId(hotelId);
                productDetailRequest.setCheckInDate(checkInTime);
                productDetailRequest.setCheckOutDate(checkOutTime);
                SignatureHelpRequestDto signatureHelpRequestDto = new SignatureHelpRequestDto();
                signatureHelpRequestDto.setPartnerCode(BaseConfig.getViolationMonitorPartnerCode());
                signatureHelpRequestDto.setSecurityKey("noSignature");
                productDetailResponseResponse = tmcHubApiManager.queryProductDetailForNoSign(productDetailRequest, signatureHelpRequestDto, true);
                tmcProductDetailMap.put(checkInTime + "#" + checkOutTime, productDetailResponseResponse);
            }
            if (!"000".equals(productDetailResponseResponse.getReturnCode()) || productDetailResponseResponse.getBussinessResponse() == null) {
                queryTmcHubResult.setResult(false);
                queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_RESPONSE_FAILED.key);
                queryTmcHubResult.setFailedInfo(JsonUtil.objectToJson(productDetailResponseResponse));
                return queryTmcHubResult;
            }
            ProductDetailResponse bussinessResponse = productDetailResponseResponse.getBussinessResponse();
            if (CollectionUtils.isEmpty(bussinessResponse.getRoomItems())) {
                queryTmcHubResult.setResult(false);
                queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL_ROOMS.key);
                queryTmcHubResult.setFailedInfo("酒店房型为空");
                return queryTmcHubResult;
            }
            // 比较C端满房是否存在可售价格
            CompareFullRoomClientPriceResult compareFullRoomClientPriceResult = PriceRoomUtil.compareFullRoomClientPrice(hotelPriceMonitor.getRoomTypeId(), productDetailResponseResponse.getBussinessResponse(), violationsFulMonitorLog);
            queryTmcHubResult.setResult(compareFullRoomClientPriceResult.isClientHasPrice());

            // 未找到合适的房型
            if(!queryTmcHubResult.isResult() && queryTmcHubResult.getQueryTmcHubFailedReason() == 0){
                queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_NO_HOTEL_ROOMS.key);
            }
        } catch (Exception e) {
            String errorMessage = "查询产品接口异常，酒店id：" + hotelId + ",roomId：" + hotelPriceMonitor.getRoomTypeId() + ",checkInTime：" + checkInTime + ",checkOutTime：" + checkOutTime;
            logger.error(errorMessage);
            queryTmcHubResult.setResult(false);
            queryTmcHubResult.setQueryTmcHubFailedReason(QueryTmcHubFailedReasonEnum.INTERFACE_EXCEPTION.key);
            queryTmcHubResult.setFailedInfo(errorMessage);
        }
        return queryTmcHubResult;
    }

    private void assembleCheckPassStatExcel(List<QueryHotelViolationCheckPassStatResponse> queryHotelViolationCheckPassStatResponses, Sheet sheet) {
        for (QueryHotelViolationCheckPassStatResponse queryHotelViolationCheckPassStatResponse : queryHotelViolationCheckPassStatResponses) {
            Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            dataRow.createCell(0).setCellValue(queryHotelViolationCheckPassStatResponse.getProvinceName());
            dataRow.createCell(1).setCellValue(queryHotelViolationCheckPassStatResponse.getCityName());
            dataRow.createCell(2).setCellValue(queryHotelViolationCheckPassStatResponse.getHotelName());
            dataRow.createCell(3).setCellValue(queryHotelViolationCheckPassStatResponse.getIsFollowHotel() == RfpConstant.constant_1 ? "是" : "否");
            dataRow.createCell(4).setCellValue(queryHotelViolationCheckPassStatResponse.getProjectName());
            dataRow.createCell(5).setCellValue(queryHotelViolationCheckPassStatResponse.getHotelGroupName());
            dataRow.createCell(6).setCellValue(queryHotelViolationCheckPassStatResponse.getBrandName());
            dataRow.createCell(7).setCellValue(queryHotelViolationCheckPassStatResponse.getTotalRoomNight());
            dataRow.createCell(8).setCellValue(queryHotelViolationCheckPassStatResponse.getHotelServicePoints().toString());
            dataRow.createCell(9).setCellValue(queryHotelViolationCheckPassStatResponse.getBrandName());
            dataRow.createCell(10).setCellValue(queryHotelViolationCheckPassStatResponse.getTotalViolationCount());
            dataRow.createCell(11).setCellValue(queryHotelViolationCheckPassStatResponse.getSolvedCount());
            dataRow.createCell(12).setCellValue(queryHotelViolationCheckPassStatResponse.getSolvedRisk().toString());
            dataRow.createCell(13).setCellValue(queryHotelViolationCheckPassStatResponse.getTotalViolationDayCount());
            dataRow.createCell(14).setCellValue(queryHotelViolationCheckPassStatResponse.getRestorePriceDayCount());
            dataRow.createCell(15).setCellValue(queryHotelViolationCheckPassStatResponse.getRestorePriceDayRisk().multiply(new BigDecimal("100")) + "%");
            dataRow.createCell(16).setCellValue(queryHotelViolationCheckPassStatResponse.getTheSameLevelDayCount());
            dataRow.createCell(17).setCellValue(queryHotelViolationCheckPassStatResponse.getTheSameLevelDayRisk().multiply(new BigDecimal("100")) + "%");
            dataRow.createCell(18).setCellValue(queryHotelViolationCheckPassStatResponse.getClientRoomClosedDayCount());
            dataRow.createCell(19).setCellValue(queryHotelViolationCheckPassStatResponse.getClientRoomClosedDayRisk().multiply(new BigDecimal("100")) + "%");
            dataRow.createCell(20).setCellValue(queryHotelViolationCheckPassStatResponse.getTotalPassDailyCount());
            dataRow.createCell(21).setCellValue(queryHotelViolationCheckPassStatResponse.getTotalPassDailyRisk().multiply(new BigDecimal("100")) + "%");
        }

    }

    private void assembleExcel(List<QueryHotelViolationMonitorResponse> queryHotelViolationMonitorResponses, Sheet sheet) {
        for (QueryHotelViolationMonitorResponse queryHotelViolationMonitorResponse : queryHotelViolationMonitorResponses) {
            Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            dataRow.createCell(0).setCellValue(DateUtil.dateToString(queryHotelViolationMonitorResponse.getViolationDate(), RfpConstant.dateFormat));
            dataRow.createCell(1).setCellValue(queryHotelViolationMonitorResponse.getOrgName());
            dataRow.createCell(2).setCellValue(queryHotelViolationMonitorResponse.getProjectName());
            dataRow.createCell(3).setCellValue(queryHotelViolationMonitorResponse.getProvinceName());
            dataRow.createCell(4).setCellValue(queryHotelViolationMonitorResponse.getCityName());

            dataRow.createCell(5).setCellValue(queryHotelViolationMonitorResponse.getHotelId());
            dataRow.createCell(6).setCellValue(queryHotelViolationMonitorResponse.getHotelName());
            dataRow.createCell(7).setCellValue(queryHotelViolationMonitorResponse.getIsFollowHotel() == RfpConstant.constant_1 ? "是":"否");
            dataRow.createCell(8).setCellValue(queryHotelViolationMonitorResponse.getHotelGroupName());
            if(queryHotelViolationMonitorResponse.getProcurementVolume() == null){
                dataRow.createCell(9).setCellValue("");
            } else {
                dataRow.createCell(9).setCellValue(queryHotelViolationMonitorResponse.getProcurementVolume());
            }
            dataRow.createCell(10).setCellValue(queryHotelViolationMonitorResponse.getHotelServicePoints().toString());
            if(queryHotelViolationMonitorResponse.getViolationType().intValue() == ViolationTypeEnum.REWARDS_OR_COMPENSATION.key) {
                dataRow.createCell(11).setCellValue("+" + queryHotelViolationMonitorResponse.getPenaltyScore().toString());
            }else{
                dataRow.createCell(11).setCellValue("-" + queryHotelViolationMonitorResponse.getPenaltyScore().toString());
            }
            dataRow.createCell(12).setCellValue(queryHotelViolationMonitorResponse.getViolationItem());
            dataRow.createCell(13).setCellValue(queryHotelViolationMonitorResponse.getViolationTypeName());
            dataRow.createCell(14).setCellValue(queryHotelViolationMonitorResponse.getHotelReplyMessage());
            if (queryHotelViolationMonitorResponse.getHotelReplyDate() != null) {
                dataRow.createCell(15).setCellValue(DateUtil.dateToString(queryHotelViolationMonitorResponse.getHotelReplyDate(), RfpConstant.dateFormat));
            } else {
                dataRow.createCell(15).setCellValue("");
            }
            dataRow.createCell(16).setCellValue(queryHotelViolationMonitorResponse.getReminderLevel());
            dataRow.createCell(17).setCellValue(queryHotelViolationMonitorResponse.getDuration());
            dataRow.createCell(18).setCellValue(queryHotelViolationMonitorResponse.getProcessingInfo());
            dataRow.createCell(19).setCellValue(DateUtil.dateToString(queryHotelViolationMonitorResponse.getProcessingDate(), RfpConstant.dateFormat));
            dataRow.createCell(20).setCellValue(HotelViolationMonitorEnum.getEnumByKey(queryHotelViolationMonitorResponse.getProcessingStatus()).value);
        }
    }

    private void assembleStatExcel(List<QueryHotelViolationMonitorStatResponse> queryHotelViolationMonitorStatResponses, Sheet sheet) {
        for (QueryHotelViolationMonitorStatResponse statResponse : queryHotelViolationMonitorStatResponses) {
            Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            dataRow.createCell(0).setCellValue(dealCellValue(statResponse.getProvinceName()));
            dataRow.createCell(1).setCellValue(dealCellValue(statResponse.getCityName()));
            dataRow.createCell(2).setCellValue(dealCellValue(statResponse.getHotelName()));
            dataRow.createCell(3).setCellValue(dealCellValue(statResponse.getIsFollowHotel() == RfpConstant.constant_1 ? "是" : "否"));
            dataRow.createCell(4).setCellValue(dealCellValue(statResponse.getProjectName()));
            dataRow.createCell(5).setCellValue(dealCellValue(statResponse.getHotelGroupName()));
            dataRow.createCell(6).setCellValue(dealCellValue(statResponse.getBrandName()));
            dataRow.createCell(7).setCellValue(dealCellValue(statResponse.getTotalRoomNight()));
            dataRow.createCell(8).setCellValue(dealCellValue(statResponse.getHotelServicePoints()));
            dataRow.createCell(9).setCellValue(dealCellValue(statResponse.getTotalViolationCount()));
            dataRow.createCell(10).setCellValue(dealCellValue(statResponse.getLastReminderLevel()));
            dataRow.createCell(11).setCellValue(dealCellValue(ViolationStatProcessStatusEnum.getValueByKey(statResponse.getProcessingStatus())));
            dataRow.createCell(12).setCellValue(dealCellValue(statResponse.getProcessMSG()));
            dataRow.createCell(13).setCellValue(dealCellValue(statResponse.getMonitorFollowName()));
            dataRow.createCell(14).setCellValue(dealCellValue(statResponse.getModifier()));
            dataRow.createCell(15).setCellValue(dealCellValue(OrgTypeEnum.getValueByKey(statResponse.getModifyOrgType().intValue())));
            if (statResponse.getModifyTime() == null) {
                dataRow.createCell(16).setCellValue("");
            } else {
                dataRow.createCell(16).setCellValue(DateUtil.dateToString(statResponse.getModifyTime(), RfpConstant.dateFormat));
            }
            dataRow.createCell(17).setCellValue(statResponse.getLastViolationTime() == null ? "" : DateUtil.dateToString(statResponse.getLastViolationTime(), RfpConstant.dateFormat));

        }
    }

    private String dealCellValue(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    /** 判断离现在是否超过24小时 **/
    private boolean isMoreThan24H(Date compareDate){
        if(compareDate == null){
            return false;
        }
        Date nowTime = new Date();
        return nowTime.getTime() - compareDate.getTime() > 24*60*60*1000;
    }

    /**
     *
     * 根据价格监控组装价格监控日历数据

     */
    private List<HotelViolationsDailyPrice> prepareHotelViolationsDailyPrices(Long violationsMonitorId, List<HotelPriceMonitor> hotelPriceMonitors,
                                                                              boolean isRoomFullViolation, boolean isPriceUpViolation,
                                                                              List<String> fullHasPriceRoomDateList){
        List<HotelViolationsDailyPrice> hotelViolationsDailyPrices = new ArrayList<>();
        for (HotelPriceMonitor hotelPriceMonitor : hotelPriceMonitors) {
            HotelViolationsDailyPrice hotelViolationsDailyPrice = new HotelViolationsDailyPrice();
            hotelViolationsDailyPrice.setViolationsMonitorId(violationsMonitorId);
            hotelViolationsDailyPrice.setHotelId(hotelPriceMonitor.getHotelId());
            hotelViolationsDailyPrice.setProjectId(hotelPriceMonitor.getProjectId());
            hotelViolationsDailyPrice.setRoomTypeId(hotelPriceMonitor.getRoomTypeId());
            hotelViolationsDailyPrice.setSaleDate(hotelPriceMonitor.getSaleDate());
            hotelViolationsDailyPrice.setSalePrice(hotelPriceMonitor.getSalePrice());
            hotelViolationsDailyPrice.setBasePrice(hotelPriceMonitor.getMonitorDayPrice() != null ? BigDecimal.valueOf(hotelPriceMonitor.getMonitorDayPrice()) : null);
            hotelViolationsDailyPrice.setLra(hotelPriceMonitor.getLra());
            hotelViolationsDailyPrice.setCompareResult(hotelPriceMonitor.getCompareResult());
            hotelViolationsDailyPrice.setIsViolation(RfpConstant.constant_0);
            if(hotelPriceMonitor.getCompareResult() == CompareResultTypeEnum.FULL.key && isRoomFullViolation &&
                    fullHasPriceRoomDateList.contains(DateUtil.dateToString(hotelPriceMonitor.getSaleDate()))
            ){
                hotelViolationsDailyPrice.setIsViolation(RfpConstant.constant_1); // 满房违规
            } else if(hotelPriceMonitor.getCompareResult() == CompareResultTypeEnum.UP.key){
                hotelViolationsDailyPrice.setIsViolation(RfpConstant.constant_1); // 涨价违规
            }
            hotelViolationsDailyPrice.setPriceCode(hotelPriceMonitor.getPriceCode());
            hotelViolationsDailyPrice.setDifPrice(hotelPriceMonitor.getDifPrice());
            hotelViolationsDailyPrices.add(hotelViolationsDailyPrice);
        }
        return hotelViolationsDailyPrices;
    }

    /**
     * 查询意向酒店
     * @param projectId
     * @param hotelId
     * @return
     */
    private ProjectIntentHotel queryProjectIntentHotel(Long projectId, Long hotelId){
        QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
        queryProjectIntentHotelDetailDto.setProjectId(projectId);
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(hotelId);
        queryProjectIntentHotelDetailDto.setHotelIds(hotelIds);
        List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
        if (CollectionUtils.isEmpty(projectIntentHotels)) {
            logger.error("邀请酒店信息为空,projectId：" + projectId + ",hotelId:" + hotelId);
            return null;
        }
        return projectIntentHotels.get(0);
    }

    /**
     * 根据日历查询tmchub 房型信息
     * @return
     */
    private List<com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> summaryProductDetail(Long hotelId,String partnerCode, List<Date> saleDateList){
        ArrayList<com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> list = new ArrayList<>();
        // 查询指定销售日期数据
        if(CollectionUtils.isNotEmpty(saleDateList)){
            for(Date saleDate : saleDateList) {
                com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> productDetailResponseResponse = callTmcHubBackList(hotelId, DateUtil.dateToString(saleDate), DateUtil.dateToString(cn.hutool.core.date.DateUtil.offsetDay(saleDate, 1)), partnerCode);
                list.add(productDetailResponseResponse);
            }
        }
        return list;
    }


    private com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> callTmcHubBackList(Long hotelId,String checkInTime,String checkOutTime,String partnerCode){

        //调产品查询接口
        ProductDetailRequest productDetailRequest = new ProductDetailRequest();
        productDetailRequest.setHotelId(hotelId);
        productDetailRequest.setCheckInDate(checkInTime);
        productDetailRequest.setCheckOutDate(checkOutTime);

        com.fangcang.hotel.delivery.tmc.common.tmchub.api.request.SignatureHelpRequestDto signatureHelpRequestDto = new com.fangcang.hotel.delivery.tmc.common.tmchub.api.request.SignatureHelpRequestDto();
        signatureHelpRequestDto.setPartnerCode(partnerCode);
        signatureHelpRequestDto.setSecurityKey("noSignature");
        com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> productDetailResponseResponse = tmcHubApiManager.queryProductDetailForNoSign(productDetailRequest, signatureHelpRequestDto,true);

        return productDetailResponseResponse;
    }


    /**
     * 处理tmcHub返回商品信息 (过滤相同档房型)
     */
    private Map<String, List<TmcHubProductResponse>> dealTheSameLevelTmcHubProduct(boolean isAgreementMonitor, List<com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> list,
                                                                                   Map<String, HotelPriceMonitor> hotelPriceMonitorMap) {
        ArrayList<TmcHubProductResponse> tmcHubProductResponses = new ArrayList<>();
        // 房型早餐
        for (com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> productDetailResponseResponse : list) {
            ProductDetailResponse productDetailResponse = productDetailResponseResponse.getBussinessResponse();
            if(productDetailResponse == null){
                continue;
            }
            if(CollectionUtils.isEmpty(productDetailResponse.getRoomItems())){
                continue;
            }

            for (RoomItem roomItem : productDetailResponse.getRoomItems()) {
                for (ProductDetail product : roomItem.getProducts()) {
                    //只获取可定产品的数据
                    if (product.getBookType() != BookTypeEnum.FULL.key) {
                        continue;
                    }
                    if(isAgreementMonitor){
                        if(CollectionUtils.isEmpty(product.getProductTags())){
                            continue;
                        }
                        List<ProductLabel> productLabelList = product.getProductTags().stream().filter(o -> ProductTagUtility.isAgreementProduct(o.getTagCode())).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(productLabelList)){
                            continue;
                        }
                    }

                    // 过滤符合要求房型 （可以售卖，符合要求）
                    for (PriceItem priceItem : product.getPriceItems()) {
                        HotelPriceMonitor hotelPriceMonitor = hotelPriceMonitorMap.get(priceItem.getSaleDate());
                        // 同一早餐类型
                        if (BreakfastUtil.isTheSameBreakfast(hotelPriceMonitor.getBreakfastNum(), priceItem.getBreakfastNum())) {
                            // 有房可售
                            if (PriceRoomUtil.isNoSaleRoom(priceItem)) {
                                continue;
                            }
                            // 符合价格
                            // 同档房 监控房型 豪华大床房 单早 450 ，做报价记录时，若有其他房型报价 单早 价格在451元以下，都记录到同档房型报价记录中
                            if (PriceRoomUtil.getPriceItemMonitorPrice(priceItem) - hotelPriceMonitor.getMonitorDayPrice() <= 1) {
                                TmcHubProductResponse tmcHubProductResponse = new TmcHubProductResponse();
                                BeanUtils.copyProperties(priceItem, tmcHubProductResponse);
                                tmcHubProductResponse.setHotelId(productDetailResponse.getHotelId());
                                tmcHubProductResponse.setRoomId(roomItem.getRoomId());
                                tmcHubProductResponse.setRoomName(roomItem.getRoomName());
                                tmcHubProductResponse.setRatePlanId(product.getRatePlanId());
                                tmcHubProductResponse.setRatePlanName(product.getRatePlanName());
                                tmcHubProductResponses.add(tmcHubProductResponse);
                            }
                        }
                    }
                }
            }

        }
        // 返回日历所有满足要求房型
        Map<String, List<TmcHubProductResponse>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tmcHubProductResponses)) {
            map = tmcHubProductResponses.stream().collect(Collectors.groupingBy(PriceItem::getSaleDate));
        }
        return map;
    }

    /**
     *
     * 检查tmc hub返回response是否都正常
     */
    private Boolean verifyResponse(String partnerCode, Long hotelId, List<com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse>> responses
                                   ){
        Boolean flag = true;
        for (com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> response : responses) {
            if (!"000".equals(response.getReturnCode())){
                flag = false;
                break;
            }
        }
        // 失败，记录所有返回数据
        if (!flag){
            for (com.fangcang.tmc.hub.api.response.Response<ProductDetailResponse> respons : responses) {
                logger.error("复核合作商编码:{},酒店ID:{},查询价格计划异常:{},",partnerCode,hotelId,respons.getReturnMsg());
            }
            return  false;
        }
        return true;
    }

    /**
     * 异步保存tmcHub是否结果
     */
    private void asyncSaveQueryTmcHubFailedResultList(HotelViolationsMonitor hotelViolationsMonitor, List<QueryTmcHubResult> queryTmcHubFailedResultList){
        if(CollectionUtils.isNotEmpty(queryTmcHubFailedResultList)){
            violationMonitorExecutor.execute(() -> {
                try {
                    saveTmcHubFailedResultList(hotelViolationsMonitor, queryTmcHubFailedResultList);
                } catch (Exception ex) {
                    logger.error("保存查询tmc结果失败履约异常记录失败", ex);
                }
            });
        }
    }



}
