package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.entity.HotelPriceMonitorRoom;

/**
 * 关注房型
 */
public interface HotelPriceMonitorRoomService {

    Response monitorRoomType(HotelPriceMonitorRoom hotelPriceMonitorRoom);


    Response voidMonitorRoomType(UserDTO userDTO, HotelPriceMonitorRoom hotelPriceMonitorRoom);
}
