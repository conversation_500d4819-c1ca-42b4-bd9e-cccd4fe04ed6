package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.QueryMonitorCallTmchubErrorListRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

public interface MonitorCallTmchubErrorService {

    /**
     * 查询酒店履约监控异常
     */
    Response queryMonitorCallTmchubError(QueryMonitorCallTmchubErrorListRequest queryMonitorCallTmchubErrorListRequest);


    /**
     * 导出履约异常数据
     */
    void exportMonitorCallTmchubError(QueryMonitorCallTmchubErrorListRequest queryMonitorCallTmchubErrorListRequest, HttpServletRequest request, HttpServletResponse httpServletResponse) throws Exception;


}