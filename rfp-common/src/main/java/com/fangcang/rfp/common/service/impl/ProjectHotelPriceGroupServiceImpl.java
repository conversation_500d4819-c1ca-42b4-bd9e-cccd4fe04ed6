package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.ImportHotelGroupBidDto;
import com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse;
import com.fangcang.rfp.common.dto.response.ProjectHotelPriceGroupResponse;
import com.fangcang.rfp.common.dto.response.ProjectHotelPriceLevelResponse;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.PriceApplicableRoomService;
import com.fangcang.rfp.common.service.ProjectHotelBidStrategyService;
import com.fangcang.rfp.common.service.ProjectHotelPriceGroupService;
import com.fangcang.rfp.common.util.BidUtil;
import com.fangcang.rfp.common.util.StringUtils;
import com.fangcang.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 房价组服务
 */
@Service
public class ProjectHotelPriceGroupServiceImpl implements ProjectHotelPriceGroupService {

    private static Logger logger = LoggerFactory.getLogger(ProjectHotelPriceServiceImpl.class);

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;
    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;
    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;
    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;
    @Autowired
    private PriceApplicableRoomService priceApplicableRoomService;
    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;
    @Autowired
    private ProjectHotelTendStrategyDao projectHotelTendStrategyDao;
    @Autowired
    private BidOperateLogDao bidOperateLogDao;
    @Autowired
    private ProjectHotelBidStrategyService projectHotelBidStrategyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response insertProjectHotelPriceLevel(ProjectIntentHotel projectIntentHotel, ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse, String creator) {
        Response response = new Response();
        Integer roomLevelNo = projectHotelPriceLevelResponse.getRoomLevelNo();
        // 查询酒店邀约数据
        if (projectIntentHotel == null) {
            projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectHotelPriceLevelResponse.getProjectIntentHotelId());
            if (projectIntentHotel == null) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("酒店邀约数据为空");
                return response;
            }
        }
        boolean isNeedValidateBid = BidUtil.isNeedValidateBid(projectIntentHotel, projectHotelBidStrategyService);

        // 获取新增房档号
        if (roomLevelNo == null) {
            Integer maxRoomLevelNo = projectHotelPriceGroupDao.selectMaxRoomLevelNo(projectHotelPriceLevelResponse.getProjectIntentHotelId(), null, null);
            roomLevelNo = maxRoomLevelNo == null ? 1 : maxRoomLevelNo + 1;
        }

        // 检查level数
        if (isNeedValidateBid) { //上传不需要检查
            ProjectHotelTendStrategy projectHotelTendStrategy = projectHotelTendStrategyDao.selectByPrimaryKey(projectIntentHotel.getProjectId());
            if (projectHotelTendStrategy != null && projectHotelTendStrategy.getSupportMaxRoomTypeCount() != null && projectHotelTendStrategy.getSupportMaxRoomTypeCount() == RfpConstant.constant_1 && projectHotelTendStrategy.getMaxRoomTypeCount() != null) {
                int roomLevelCount = projectHotelPriceGroupDao.selectMaxRoomLevelCount(projectHotelPriceLevelResponse.getProjectIntentHotelId(), null, null);
                if (roomLevelCount >= projectHotelTendStrategy.getMaxRoomTypeCount()) {
                    response.setMsg("酒店报价最多房档数不能超过" + projectHotelTendStrategy.getMaxRoomTypeCount() + "个");
                    return response;
                }
            }
        }

        // 检查房型是否在其他房档中已经存在
        if (isNeedValidateBid && CollectionUtils.isNotEmpty(projectHotelPriceLevelResponse.getRoomResponseList())) {
            Response validateResponse = priceApplicableRoomService.validateRoomType(projectHotelPriceLevelResponse.getProjectIntentHotelId(), roomLevelNo, projectHotelPriceLevelResponse.getRoomResponseList());
            if (validateResponse != null) {
                return validateResponse;
            }
        }

        // 新增房型
        if (CollectionUtils.isNotEmpty(projectHotelPriceLevelResponse.getRoomResponseList())) {
            List<PriceApplicableRoom> priceApplicableRoomList = new ArrayList<>();
            List<PriceApplicableRoomInfoResponse> priceApplicableRoomResponseList = projectHotelPriceLevelResponse.getRoomResponseList();
            for (PriceApplicableRoomInfoResponse priceApplicableRoomResponse : priceApplicableRoomResponseList) {
                // Lanyon导入房型为空
                if(priceApplicableRoomResponse.getRoomTypeId() == null){
                    continue;
                }
                PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
                BeanUtils.copyProperties(priceApplicableRoomResponse, priceApplicableRoom);
                priceApplicableRoom.setRoomLevelNo(roomLevelNo);
                priceApplicableRoom.setCreator(creator);
                priceApplicableRoom.setModifier(creator);
                priceApplicableRoomList.add(priceApplicableRoom);

            }
            if(CollectionUtils.isNotEmpty(priceApplicableRoomList)) {
                priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), roomLevelNo);
                int insertResult = priceApplicableRoomDao.insertBatch(priceApplicableRoomList);
                if (insertResult == 0) {
                    logger.info("新增房型失败 {}", JSON.toJSONString(priceApplicableRoomList));
                    throw new RuntimeException("新增价格组失败");
                }
            }
        }

        // 页面新增设置房档和房型总数
        for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()) {
            if(projectHotelPriceGroupResponse.getRoomLevelNo() == null) {
                projectHotelPriceGroupResponse.setRoomLevelNo(roomLevelNo);
            }
            // 设置房型总数
            projectHotelPriceGroupResponse.setLevelTotalRoomCount(projectHotelPriceLevelResponse.getTotalRoomCount());
            projectHotelPriceGroupResponse.setLevelRoomType1Count(projectHotelPriceLevelResponse.getLevelRoomType1Count());
            projectHotelPriceGroupResponse.setLevelRoomType2Count(projectHotelPriceLevelResponse.getLevelRoomType2Count());

        }

        // 检查房档价格组报价基本数据
        for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
            if(projectHotelPriceGroupResponse.getLra() == null){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("需要选择LRA");
                return response;
            }
            if(projectHotelPriceGroupResponse.getCancelRestrictType() == null){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("需要选择退款政策");
                return response;
            }
        }
        // 新增房档价格组
        for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
            insertHotelPriceGroup(projectHotelPriceGroupResponse, creator, isNeedValidateBid);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("新增成功");
        return response;
    }

    public void insertHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, String creator, boolean isNeedValidateBid){
        // 新增房档价格组
        ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
        BeanUtils.copyProperties(projectHotelPriceGroupResponse, projectHotelPriceGroup);

        projectHotelPriceGroup.setCreator(creator);
        projectHotelPriceGroup.setModifier(creator);
        String groupKey = generateHotelGroupPriceKey(projectHotelPriceGroup);

        // 查询是否存在group
        ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
        BeanUtils.copyProperties(projectHotelPriceGroup, queryProjectHotelPriceGroup);
        queryProjectHotelPriceGroup.setHotelPriceGroupId(null);
        List<ProjectHotelPriceGroup> dbProjectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
        long dbProjectHotelPriceGroupId= 0L;
        Integer levelTotalRoomCount = projectHotelPriceGroupResponse.getLevelTotalRoomCount();
        Integer levelRoomType1Count = projectHotelPriceGroupResponse.getLevelRoomType1Count();
        Integer levelRoomType2Count = projectHotelPriceGroupResponse.getLevelRoomType2Count();
        if(isNeedValidateBid) {
            if (CollectionUtils.isNotEmpty(dbProjectHotelPriceGroupList)) {
                for (ProjectHotelPriceGroup dbProjectHotelPriceGroup : dbProjectHotelPriceGroupList) {
                    if (dbProjectHotelPriceGroup.getLevelTotalRoomCount() != null ||
                            dbProjectHotelPriceGroup.getLevelRoomType1Count() != null ||
                            dbProjectHotelPriceGroup.getLevelRoomType2Count() != null
                    ) {
                        levelTotalRoomCount = dbProjectHotelPriceGroup.getLevelTotalRoomCount();
                        levelRoomType1Count = dbProjectHotelPriceGroup.getLevelRoomType1Count();
                        levelRoomType2Count = dbProjectHotelPriceGroup.getLevelRoomType2Count();
                    }
                    String dbGroupKey = generateHotelGroupPriceKey(dbProjectHotelPriceGroup);
                    if (dbGroupKey.equals(groupKey)) {
                        dbProjectHotelPriceGroupId = dbProjectHotelPriceGroup.getHotelPriceGroupId();
                        break;
                    }
                }
            }
            if (dbProjectHotelPriceGroupId > 0L) {
                projectHotelPriceGroupDao.deleteByPrimaryKey(dbProjectHotelPriceGroupId);
                projectHotelPriceDao.deleteByGroupId(dbProjectHotelPriceGroupId);
            }
        }
        projectHotelPriceGroup.setLevelTotalRoomCount(levelTotalRoomCount);
        projectHotelPriceGroup.setLevelRoomType1Count(levelRoomType1Count);
        projectHotelPriceGroup.setLevelRoomType2Count(levelRoomType2Count);
        int insertCount = projectHotelPriceGroupDao.insert(projectHotelPriceGroup);
        if (insertCount == 0) {
            logger.info("新增价格组失败 {}", JSON.toJSONString(projectHotelPriceGroup));
            throw new RuntimeException("新增价格组失败");
        }


        // 新增房档价格
        Long hotelPriceGroupId = projectHotelPriceGroup.getHotelPriceGroupId();
        // 过滤没有价格报价
        List<ProjectHotelPrice> insertProjectPriceList = projectHotelPriceGroupResponse.getHotelPriceList().stream().filter(o -> o.getBasePrice() != null).collect(Collectors.toList());
        for(ProjectHotelPrice projectHotelPrice : insertProjectPriceList){
            projectHotelPrice.setHotelPriceGroupId(hotelPriceGroupId);
            projectHotelPrice.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
            projectHotelPrice.setProjectId(projectHotelPriceGroupResponse.getProjectId());
            projectHotelPrice.setHotelId(projectHotelPriceGroupResponse.getHotelId());
            projectHotelPrice.setRoomLevelNo(projectHotelPriceGroup.getRoomLevelNo());
            projectHotelPrice.setCreator(creator);
            projectHotelPrice.setModifier(creator);
        }
        int insertPriceCount = projectHotelPriceDao.insertBatch(insertProjectPriceList);
        if(insertPriceCount == 0){
            logger.info("新增价格失败 {}", JSON.toJSONString(insertProjectPriceList));
            throw new RuntimeException("新增价格失败");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public long getNextHotelPriceGroupId() {
        return projectHotelPriceGroupDao.selectNextSequenceKey();
    }

    /**
     * 检查退改策略
     */
    @Override
    public String validateCancelRestrict(ProjectHotelTendStrategy projectHotelTendStrategy, ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse) {
        if(projectHotelTendStrategy != null &&
                (projectHotelTendStrategy.getSupport1stCancelCount() == RfpConstant.constant_1 || projectHotelTendStrategy.getSupportFreeCancel() == RfpConstant.constant_1)){
            Date todayRequestCancelRestrictTime = DateUtil.parse(DateUtil.formatDate(new Date()) + " " +  projectHotelPriceGroupResponse.getCancelRestrictTime() + ":00", DatePattern.NORM_DATETIME_PATTERN);
            Date todaySupportCancelRestrictTime = DateUtil.parse(DateUtil.formatDate(new Date()) + " " + projectHotelTendStrategy.getSupportCancelTime()  +":00", DatePattern.NORM_DATETIME_PATTERN);
            // 天数大于  采购策略   直接不通过
            if(projectHotelPriceGroupResponse.getCancelRestrictDay() > projectHotelTendStrategy.getSupportCancelDay()){
                return "酒店报价需要支持" + projectHotelTendStrategy.getSupportCancelDay() + "天前可免费退改";
            }
            // 天数  小于  直接通过
            if(projectHotelPriceGroupResponse.getCancelRestrictDay() < projectHotelTendStrategy.getSupportCancelDay()){
                return null;
            }
            // 天数等于 =  再校验时间是否  大于采购策略  （大于等于通过）
            if(todayRequestCancelRestrictTime.compareTo(todaySupportCancelRestrictTime) < 0 ){
                return "酒店报价需要支持" + projectHotelTendStrategy.getSupportCancelTime() + "前可免费退改";
            }
        }
        return null;
    }

    private String generateHotelGroupPriceKey(ProjectHotelPriceGroup projectHotelPriceGroup){
        return projectHotelPriceGroup.getApplicableWeeks() + "#" + projectHotelPriceGroup.getLra() + "#" + projectHotelPriceGroup.getCancelRestrictType() + "#" + projectHotelPriceGroup.getCancelRestrictDay() + "#" + projectHotelPriceGroup.getCancelRestrictTime();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response insertProjectHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, String creator) {
        Response response = new Response();

        // 检查 同房档group是否重复 比较价格星期是否有交叉重叠
        Response compareResponse = compareApplicableWeeks(projectHotelPriceGroupResponse, false);
        if(compareResponse != null){
            return compareResponse;
        }

        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectHotelPriceGroupResponse.getProjectId(), projectHotelPriceGroupResponse.getHotelId());
        boolean isNeedValidate = BidUtil.isNeedValidateBid(projectIntentHotel, projectHotelBidStrategyService);
        insertHotelPriceGroup(projectHotelPriceGroupResponse, creator, isNeedValidate);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("新增成功");
        return response;
    }


    /**
     * 检查 同房档group是否重复 比较价格星期是否有交叉重叠
     */
    private Response compareApplicableWeeks(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, boolean isUpdate) {
        List<String> applicableWeeks = Arrays.asList(projectHotelPriceGroupResponse.getApplicableWeeks().split(","));
        ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
        projectHotelPriceGroup.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
        projectHotelPriceGroup.setRoomLevelNo(projectHotelPriceGroupResponse.getRoomLevelNo());
        projectHotelPriceGroup.setProjectId(projectHotelPriceGroupResponse.getProjectId());
        projectHotelPriceGroup.setHotelId(projectHotelPriceGroupResponse.getHotelId());

        // Lanyon导入数据不需要检查
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectHotelPriceGroupResponse.getProjectIntentHotelId());
        if(!BidUtil.isNeedValidateBid(projectIntentHotel, projectHotelBidStrategyService)){
            return null;
        }

        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(projectHotelPriceGroup);
        for(ProjectHotelPriceGroup dbProjectHotelPriceGroup : projectHotelPriceGroupList){
            if(isUpdate && dbProjectHotelPriceGroup.getHotelPriceGroupId().longValue() == projectHotelPriceGroupResponse.getHotelPriceGroupId().longValue()){
                continue;
            }
            if(Objects.equals(dbProjectHotelPriceGroup.getLra(), projectHotelPriceGroupResponse.getLra())){
                for(String week : applicableWeeks){
                    if(dbProjectHotelPriceGroup.getApplicableWeeks().contains(week)){
                        Response response = new Response();
                        response.setResult(ReturnResultEnum.FAILED.errorNo);
                        response.setMsg("同房档星期有交叉重叠");
                        return response;
                    }
                }
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateProjectHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, UserDTO userDTO) {
        Response response = new Response();

        // 更新价格组
        if(projectHotelPriceGroupResponse.getHotelPriceGroupId() == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("价格组ID为空");
            return response;
        }

        // 平台更新不需要检查
        if(!Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
            // 检查 同房档group是否重复 比较价格星期是否有交叉重叠
            Response compareResponse = compareApplicableWeeks(projectHotelPriceGroupResponse, true);
            if (compareResponse != null) {
                return compareResponse;
            }
        }

        // 更新价格
        List<Long> updatePriceCodeList = projectHotelPriceGroupResponse.getHotelPriceList().stream().map(ProjectHotelPrice::getPriceCode).filter(Objects::nonNull).collect(Collectors.toList());;

        // 平台修改记录操作日志
        ProjectHotelPriceGroup dbProjectHotelPriceGroup = projectHotelPriceGroupDao.selectByPrimaryKey(projectHotelPriceGroupResponse.getHotelPriceGroupId());
        // 锁定报价不能修改
        if(!Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.PLATFORM.key) && Objects.equals(dbProjectHotelPriceGroup.getIsLocked(), RfpConstant.constant_1)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("锁定价格组不能修改");
            return response;
        }

        List<ProjectHotelPrice> dbProjectHotelPriceList = null;
        if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.PLATFORM.key)) {
            dbProjectHotelPriceList = projectHotelPriceDao.selectInfoByProjectHotelGroupId(projectHotelPriceGroupResponse.getHotelPriceGroupId());

            // 记录价格组操作日志
            List<BidOperateLog> bidOperateLogList = new ArrayList<>();
            if(!Objects.equals(dbProjectHotelPriceGroup.getApplicableWeeks(), projectHotelPriceGroupResponse.getApplicableWeeks())){
                StringBuilder operateContent = new StringBuilder();
                String[] split = dbProjectHotelPriceGroup.getApplicableWeeks().split(",");
                StringBuffer buffer = new StringBuffer();
                for (String s : split) {
                    String valueByKey = ApplicableWeeksTypeEnum.getValueByKey(Integer.valueOf(s));
                    buffer.append(valueByKey + ",");
                }
                String applicableWeeksDesc = buffer.substring(0, buffer.length() - 1);
                operateContent.append("将适用星期从 ").append(applicableWeeksDesc).append("修改为").append(projectHotelPriceGroupResponse.getApplicableWeeksDesc());
                BidOperateLog bidOperateLog = new BidOperateLog();
                bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                bidOperateLog.setOperator(userDTO.getOperator());
                bidOperateLog.setOperateContent(operateContent.toString());
                bidOperateLogList.add(bidOperateLog);

            }
            if(!Objects.equals(dbProjectHotelPriceGroup.getLra(), projectHotelPriceGroupResponse.getLra())){
                String operateContent = "将LRA承若从 " + (dbProjectHotelPriceGroup.getLra() == RfpConstant.constant_1 ? "YES" : "NO") + "修改为" + (projectHotelPriceGroupResponse.getLra() == RfpConstant.constant_1 ? "YES" : "NO");
                BidOperateLog bidOperateLog = new BidOperateLog();
                bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                bidOperateLog.setOperator(userDTO.getOperator());
                bidOperateLog.setOperateContent(operateContent);
                bidOperateLogList.add(bidOperateLog);
            }
            if((StringUtil.isValidString(dbProjectHotelPriceGroup.getRemark()) || StringUtil.isValidString(projectHotelPriceGroupResponse.getRemark())) && !Objects.equals(dbProjectHotelPriceGroup.getRemark(), projectHotelPriceGroupResponse.getRemark())){
                String operateContent = "将备注从 " + (StringUtils.isNotEmpty(dbProjectHotelPriceGroup.getRemark()) ?  dbProjectHotelPriceGroup.getRemark() : "空") + "修改为" +(StringUtils.isNotEmpty(projectHotelPriceGroupResponse.getRemark()) ?  projectHotelPriceGroupResponse.getRemark() : "空");
                BidOperateLog bidOperateLog = new BidOperateLog();
                bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                bidOperateLog.setOperator(userDTO.getOperator());
                bidOperateLog.setOperateContent(operateContent);
                bidOperateLogList.add(bidOperateLog);
            }
            // 退改条例
            if(!Objects.equals(dbProjectHotelPriceGroup.getCancelRestrictType(), projectHotelPriceGroupResponse.getCancelRestrictType()) ||
                    !Objects.equals(dbProjectHotelPriceGroup.getCancelRestrictDay(), projectHotelPriceGroupResponse.getCancelRestrictDay()) ||
                    !Objects.equals(dbProjectHotelPriceGroup.getCancelRestrictTime(), projectHotelPriceGroupResponse.getCancelRestrictTime())
            ){
                StringBuilder operateContent = new StringBuilder();
                String beforeCancelString = "";
                String afterCancelString = "";
                if(dbProjectHotelPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key){
                    beforeCancelString = "提前" + dbProjectHotelPriceGroup.getCancelRestrictDay() + "天" + dbProjectHotelPriceGroup.getCancelRestrictTime() +"点之前可免费退改，之后收取首晚房费的退订费";
                } else if (dbProjectHotelPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.PAY.key){
                    beforeCancelString = "提前" + dbProjectHotelPriceGroup.getCancelRestrictDay() + "天" + dbProjectHotelPriceGroup.getCancelRestrictTime() +"点之前可免费退改，之后不可退改";
                } else if(dbProjectHotelPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.FREE.key){
                    beforeCancelString = "免费退改";
                }

                if(projectHotelPriceGroupResponse.getCancelRestrictType() == CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key){
                    afterCancelString = "提前" + projectHotelPriceGroupResponse.getCancelRestrictDay() + "天" + projectHotelPriceGroupResponse.getCancelRestrictTime() +"点之前可免费退改，之后收取首晚房费的退订费";
                } else if (projectHotelPriceGroupResponse.getCancelRestrictType() == CancelRestrictTypeEnum.PAY.key){
                    afterCancelString = "提前" + projectHotelPriceGroupResponse.getCancelRestrictDay() + "天" + projectHotelPriceGroupResponse.getCancelRestrictTime() +"点之前可免费退改，之后不可退改";
                } else if(projectHotelPriceGroupResponse.getCancelRestrictType() == CancelRestrictTypeEnum.FREE.key){
                    afterCancelString = "免费退改";
                }
                operateContent.append("将退改条例从 ").append(beforeCancelString).append("修改为 ").append(afterCancelString);
                BidOperateLog bidOperateLog = new BidOperateLog();
                bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                bidOperateLog.setOperator(userDTO.getOperator());
                bidOperateLog.setOperateContent(operateContent.toString());
                bidOperateLogList.add(bidOperateLog);
            }

            // 记录删除了的价格日志
            Map<Long, ProjectHotelPrice> dbPriceMap = dbProjectHotelPriceList.stream().collect(Collectors.toMap(ProjectHotelPrice::getPriceCode, Function.identity()));
            for(Long priceCode : dbPriceMap.keySet()){
                if(updatePriceCodeList.contains(priceCode)){
                    continue;
                }
                ProjectHotelPrice deleteProjectHotelPrice = dbPriceMap.get(priceCode);
                // 记录删除日志
                BidOperateLog bidOperateLog = new BidOperateLog();
                bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                bidOperateLog.setOperator(userDTO.getOperator());
                bidOperateLog.setOperateContent("删除" + HotelPriceTypeEnum.getEnumByKey(deleteProjectHotelPrice.getPriceType()).value + "-" + BreakfastNumEnum.getValueByKey(deleteProjectHotelPrice.getBreakfastNum()) + "-" + deleteProjectHotelPrice.getBasePrice()+"元");
                bidOperateLogList.add(bidOperateLog);
            }

            // 记录新增/更新日志
            for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()){
                if(projectHotelPrice.getPriceCode() == null){
                    // 记录新增日志
                    BidOperateLog bidOperateLog = new BidOperateLog();
                    bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                    bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                    bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                    bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                    bidOperateLog.setOperator(userDTO.getOperator());
                    bidOperateLog.setOperateContent("新增" + HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "-" + BreakfastNumEnum.getValueByKey(projectHotelPrice.getBreakfastNum()) + "-" + projectHotelPrice.getBasePrice() +"元");
                    bidOperateLogList.add(bidOperateLog);
                } else {
                    ProjectHotelPrice updateDbProjectHotelPrice = dbPriceMap.get(projectHotelPrice.getPriceCode());
                    if(!Objects.equals(updateDbProjectHotelPrice.getBreakfastNum(), projectHotelPrice.getBreakfastNum()) ||
                            !Objects.equals(updateDbProjectHotelPrice.getBasePrice(), projectHotelPrice.getBasePrice())
                    ) {
                        BidOperateLog bidOperateLog = new BidOperateLog();
                        bidOperateLog.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
                        bidOperateLog.setProjectId(dbProjectHotelPriceGroup.getProjectId());
                        bidOperateLog.setHotelId(dbProjectHotelPriceGroup.getHotelId());
                        bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                        bidOperateLog.setOperator(userDTO.getOperator());
                        bidOperateLog.setOperateContent("将" + HotelPriceTypeEnum.getEnumByKey(updateDbProjectHotelPrice.getPriceType()).value + "-" + BreakfastNumEnum.getValueByKey(updateDbProjectHotelPrice.getBreakfastNum()) + "-" + updateDbProjectHotelPrice.getBasePrice() +"元 修改为 " +
                                HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "-" + BreakfastNumEnum.getValueByKey(projectHotelPrice.getBreakfastNum()) + "-" + projectHotelPrice.getBasePrice() +"元"
                        );
                        bidOperateLogList.add(bidOperateLog);
                    }
                }
            }

            // 记录操作日期
            if(CollectionUtils.isNotEmpty(bidOperateLogList)){
                bidOperateLogDao.insertBatch(bidOperateLogList);
            }

        }

        // 更新group
        ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
        BeanUtils.copyProperties(projectHotelPriceGroupResponse, projectHotelPriceGroup);
        projectHotelPriceGroup.setModifier(userDTO.getOperator());
        int updateRecord = projectHotelPriceGroupDao.updateByPrimaryKeySelective(projectHotelPriceGroup);
        if(updateRecord == 0){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("更新价格组失败");
            return response;
        }

        // 删除不需要更新的price
        projectHotelPriceDao.deleteByGroupIdAndExcludePriceCodes(projectHotelPriceGroup.getHotelPriceGroupId(), updatePriceCodeList);

        // 更新或新增价格
        for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()){
            projectHotelPrice.setProjectIntentHotelId(projectHotelPriceGroupResponse.getProjectIntentHotelId());
            projectHotelPrice.setProjectId(projectHotelPriceGroupResponse.getProjectId());
            projectHotelPrice.setHotelId(projectHotelPriceGroupResponse.getHotelId());
            projectHotelPrice.setRoomLevelNo(projectHotelPriceGroupResponse.getRoomLevelNo());
            projectHotelPrice.setHotelPriceGroupId(projectHotelPriceGroupResponse.getHotelPriceGroupId());
            if(projectHotelPrice.getPriceCode() != null){
                projectHotelPriceDao.updateByPrimaryKeySelective(projectHotelPrice);
            } else {
                projectHotelPrice.setHotelPriceGroupId(projectHotelPriceGroupResponse.getHotelPriceGroupId());
                projectHotelPriceDao.insert(projectHotelPrice);
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("修改成功");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response deleteProjectHotelPriceGroup(Long hotelPriceGroupId, UserDTO userDTO){
        Response response = new Response();
        ProjectHotelPriceGroup projectHotelPriceGroup = projectHotelPriceGroupDao.selectByPrimaryKey(hotelPriceGroupId);
        // 锁定报价不能修改
        if(!Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.PLATFORM.key) && Objects.equals(projectHotelPriceGroup.getIsLocked(), RfpConstant.constant_1)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("锁定价格组不能修改");
            return response;
        }

        projectHotelPriceGroupDao.deleteByPrimaryKey(hotelPriceGroupId);
        projectHotelPriceDao.deleteByGroupId(hotelPriceGroupId);
        // 删除关注的房型
        HotelPriceMonitorRoom deleteHotelPriceMonitorRoom = new HotelPriceMonitorRoom();
        deleteHotelPriceMonitorRoom.setProjectId(projectHotelPriceGroup.getProjectId());
        deleteHotelPriceMonitorRoom.setHotelId(projectHotelPriceGroup.getHotelId());
        deleteHotelPriceMonitorRoom.setHotelPriceGroupId(projectHotelPriceGroup.getHotelPriceGroupId());
        deleteHotelPriceMonitorRoom.setModifier(userDTO.getOperator());
        hotelPriceMonitorRoomDao.delete(deleteHotelPriceMonitorRoom);

        // 检查是否还存在group, 不存在就情况房型
        ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
        queryProjectHotelPriceGroup.setProjectIntentHotelId(projectHotelPriceGroup.getProjectIntentHotelId());
        queryProjectHotelPriceGroup.setRoomLevelNo(projectHotelPriceGroup.getRoomLevelNo());
        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
        if(CollectionUtils.isEmpty(projectHotelPriceGroupList)){
            priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectHotelPriceGroup.getProjectId(), projectHotelPriceGroup.getHotelId(), projectHotelPriceGroup.getRoomLevelNo());

        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("删除成功");
        return response;
    }

}
