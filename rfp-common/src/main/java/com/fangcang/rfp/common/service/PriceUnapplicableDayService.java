package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.PriceUnapplicableDayDto;
import com.fangcang.rfp.common.dto.request.ProjectHotelBidStrategyRequest;
import com.fangcang.rfp.common.entity.PriceUnapplicableDay;

import java.util.List;

public interface PriceUnapplicableDayService {

    /**
     * 查询不可用日期
     * @param projectHotelBidStrategyRequest
     * @return
     */
    Response selectPriceUnapplicableDayList(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest);

    /**
     * 编辑不可用日期
     * @param priceUnapplicableDay
     * @return
     */
    Response updatePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay);

    /**
     * 删除不可用日期
     * @param priceUnapplicableDay
     * @return
     */
    Response deletePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay);

    /**
     * 新增不可用日期
     * @param priceUnapplicableDayDto
     * @return
     */
    Response insertPriceUnapplicableDay(PriceUnapplicableDayDto priceUnapplicableDayDto);

    public Response insertSinglePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay);


    /**
     * 根据价格编码查询不适用日期列表
     * @param intentHotelId
     * @return
     */
    List<PriceUnapplicableDay> selectPriceUnapplicableDayListByIntentHotelId(Long intentHotelId);


}
