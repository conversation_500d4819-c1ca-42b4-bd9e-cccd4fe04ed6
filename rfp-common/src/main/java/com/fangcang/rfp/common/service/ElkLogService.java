package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.thread.LogDataThread;
import com.fangcang.redis.dao.LogDataDao;
import com.fangcang.redis.entity.LogData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

/**
 * @ClassName ElkLogService
 * @Description ELK日志记录服务
 */
@Service
public class ElkLogService {
    private Logger logger = LoggerFactory.getLogger(ElkLogService.class);
    @Autowired
    private TaskExecutor logDataExecutor;

    @Autowired
    private LogDataDao logDataDao;

    public void saveLogData(LogData logData) {
        try {
            if (logData != null) {
                logDataExecutor.execute(new LogDataThread(logDataDao, logData));
            }
        } catch (Exception e) {
            logger.error("save logdata in elk error." + e);
        }
    }
}
