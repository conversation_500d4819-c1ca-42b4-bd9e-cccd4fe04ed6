package com.fangcang.rfp.common.service.impl;

import com.fangcang.rfp.common.dao.PriceUnapplicableDayDao;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.PriceUnapplicableDayDto;
import com.fangcang.rfp.common.dto.request.ProjectHotelBidStrategyRequest;
import com.fangcang.rfp.common.entity.PriceUnapplicableDay;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.service.PriceUnapplicableDayService;
import com.fangcang.rfp.common.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @auther cjh
 * @description 项目酒店报价不适用日期 服务类
 * @date 2022/10/19
 */
@Service
public class PriceUnapplicableDayServiceImpl implements PriceUnapplicableDayService {

    private static Logger logger = LoggerFactory.getLogger(ProjectIntentHotelServiceImpl.class);

    @Autowired
    private PriceUnapplicableDayDao priceUnapplicableDayDao;

    @Override
    public Response selectPriceUnapplicableDayList(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest) {
        Response response = new Response();

        List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(priceUnapplicableDays);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updatePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay) {
        Response response = new Response();
        try {
            priceUnapplicableDayDao.updateByPrimaryKey(priceUnapplicableDay);
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        } catch (Exception e) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("更新不可用日期失败");
            logger.error("更新不可用日期失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response deletePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay) {
        Response response = new Response();
        try {
            priceUnapplicableDayDao.deleteByPrimaryKey(priceUnapplicableDay.getUnapplicableDayId());
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        } catch (Exception e) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("删除不可用日期失败");
            logger.error("删除不可用日期失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response insertPriceUnapplicableDay(PriceUnapplicableDayDto priceUnapplicableDayDto) {
        Response response = new Response();
        try {
            PriceUnapplicableDay priceUnapplicableDay = new PriceUnapplicableDay();
            priceUnapplicableDay.setProjectIntentHotelId(priceUnapplicableDayDto.getProjectIntentHotelId());
            priceUnapplicableDay.setProjectId(priceUnapplicableDayDto.getProjectId());
            priceUnapplicableDay.setHotelId(priceUnapplicableDayDto.getHotelId());
            priceUnapplicableDayDao.deleteByPriceUnapplicableDay(priceUnapplicableDay);
            if (CollectionUtils.isNotEmpty(priceUnapplicableDayDto.getPriceUnapplicableDayList())) {
                for (PriceUnapplicableDay unapplicableDay : priceUnapplicableDayDto.getPriceUnapplicableDayList()) {
                    unapplicableDay.setProjectIntentHotelId(priceUnapplicableDayDto.getProjectIntentHotelId());
                    unapplicableDay.setProjectId(priceUnapplicableDayDto.getProjectId());
                    unapplicableDay.setHotelId(priceUnapplicableDayDto.getHotelId());
                }
                priceUnapplicableDayDao.insertBatch(priceUnapplicableDayDto.getPriceUnapplicableDayList());
            }
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        } catch (Exception e) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("新增不可用日期失败");
            logger.error("新增不可用日期失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response insertSinglePriceUnapplicableDay(PriceUnapplicableDay priceUnapplicableDay) {
        Response response = new Response();
        try {
            priceUnapplicableDayDao.insert(priceUnapplicableDay);
            response.setData(priceUnapplicableDay);
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        } catch (Exception e) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("新增不可用日期失败");
            logger.error("新增不可用日期失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
    }

    @Override
    public List<PriceUnapplicableDay> selectPriceUnapplicableDayListByIntentHotelId(Long intentHotelId) {

        ArrayList<Long> longs = new ArrayList<>();
        longs.add(intentHotelId);
        return priceUnapplicableDayDao.selectPriceUnapplicableDayListByIntentHotelId(longs);
    }
}
