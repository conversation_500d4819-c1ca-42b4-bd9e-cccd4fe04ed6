package com.fangcang.rfp.common.service.impl;

import com.fangcang.rfp.common.dao.HotelPriceMonitorRoomDao;
import com.fangcang.rfp.common.dao.ProjectHotelPriceDao;
import com.fangcang.rfp.common.dao.ProjectIntentHotelDao;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.entity.HotelPriceMonitorRoom;
import com.fangcang.rfp.common.entity.ProjectHotelPrice;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.service.HotelPriceMonitorRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class HotelPriceMonitorRoomServiceImpl implements HotelPriceMonitorRoomService {

    private static final Logger logger = LoggerFactory.getLogger(HotelGroupServiceImpl.class);

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;


    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;

    /**
     * 关注监控房型
     * @param hotelPriceMonitorRoom
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response monitorRoomType(HotelPriceMonitorRoom hotelPriceMonitorRoom) {
        Response response = new Response();
        HotelPriceMonitorRoom dbHotelPriceMonitorRoom = hotelPriceMonitorRoomDao.getMonitorRoom(hotelPriceMonitorRoom);
        int record = 0;
        if(dbHotelPriceMonitorRoom != null){
            dbHotelPriceMonitorRoom.setModifier(hotelPriceMonitorRoom.getModifier());
            record = hotelPriceMonitorRoomDao.updateNoDelete(dbHotelPriceMonitorRoom);
        } else {
            record = hotelPriceMonitorRoomDao.insert(hotelPriceMonitorRoom);
        }
        if(record == 1){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
        }
        return response;
    }

    /**
     * 取消监控房型
     * @param hotelPriceMonitorRoom
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response voidMonitorRoomType(UserDTO userDTO, HotelPriceMonitorRoom hotelPriceMonitorRoom) {
        Response response = new Response();
        HotelPriceMonitorRoom dbHotelPriceMonitorRoom = hotelPriceMonitorRoomDao.getMonitorRoom(hotelPriceMonitorRoom);
        int record = 0;
        if(dbHotelPriceMonitorRoom != null){
            dbHotelPriceMonitorRoom.setModifier(userDTO.getOperator());
            record = hotelPriceMonitorRoomDao.delete(dbHotelPriceMonitorRoom);
        }
        if(record == 1){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
        }
        return response;

    }
}
