package com.fangcang.rfp.common.enums;

import com.fangcang.rfp.common.util.BidUtil;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum WeightScoreStandardEnum {

    // -------------------------------- Score Standards

    // DISTANCE_OFFICER_POI
    DIS_OFFICER_POI_0_1_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("2.5"),"0-1km"),
    DIS_OFFICER_POI_1_2_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("2.4"),"1-2km"),
    DIS_OFFICER_POI_2_3_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("2.2"),"2-3km"),
    DIS_OFFICER_POI_3_4_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("2"),"3-4km"),
    DIS_OFFICER_POI_4_5_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("1.8"),"4-5km"),
    DIS_OFFICER_POI_5_6_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("1.5"),"5-6km"),
    DIS_OFFICER_POI_6_7_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("1.2"),"6-7km"),
    DIS_OFFICER_POI_7_8_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("0.8"),"7-8km"),
    DIS_OFFICER_POI_8_9_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("0.4"),"8-9km"),
    DIS_OFFICER_POI_9_10_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("0.2"),"9-10km"),
    DIS_OFFICER_POI_MORE_THAN_10_KM(WeightTypeEnum.DIS_OFFICER_POI.name(), new BigDecimal("0"),"> 10km"),

    // HOTEL_STAR
    FIVE_QUISAFIVE_STAR(WeightTypeEnum.HOTEL_STAR.name(), new BigDecimal("1"),"五星级/豪华型"),   // = 五星级/豪华型 ★ (19,29)
    FOUR_QUISAFOUR_STAR(WeightTypeEnum.HOTEL_STAR.name(), new BigDecimal("0.5"),"四星级/高档型"), // 四星级/高档型 (39,49)
    THREESTAR_QUISATWOSTAR(WeightTypeEnum.HOTEL_STAR.name(), new BigDecimal("0"),"三星级/舒适型"), //三星级/舒适型(59,64)
    QUISATWOSTAR(WeightTypeEnum.HOTEL_STAR.name(), new BigDecimal("0"),"经济型"), //经济型 (69)
    TWOSTAR_BELOWTWOSTAR(WeightTypeEnum.HOTEL_STAR.name(), new BigDecimal("0"),"二星及以下/客栈及公寓"), // 二星及以下/客栈及公寓 (66,79)

    // RATING 评分
    RATING_MORE_THAN_48(WeightTypeEnum.RATING.name(), new BigDecimal("1.5")," OTA分 ≥ 4.8"),
    RATING_45_TO_48(WeightTypeEnum.RATING.name(), new BigDecimal("1"),"4.5 ≤ OTA分 < 4.8"),
    RATING_42_TO_45(WeightTypeEnum.RATING.name(), new BigDecimal("0"),"4.2 ≤ OTA分 < 4.5"),
    RATING_40_TO_42(WeightTypeEnum.RATING.name(), new BigDecimal("-0.5"),"4.0 ≤ OTA分 < 4.2"),
    RATING_35_TO_40(WeightTypeEnum.RATING.name(), new BigDecimal("-1"),"3.5 ≤ OTA分 < 4.0"),
    RATING_LESS_THAN_35(WeightTypeEnum.RATING.name(), new BigDecimal("-2"),"OTA分 ≤ 3.5"),

    // WEIGHT_BRAND 酒店品
    BRAND_GROUP_1(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"酒店品牌 / 集团隶属于第 1 分组"),
    BRAND_GROUP_2(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"酒店品牌 / 集团隶属于第 2 分组"),
    BRAND_GROUP_3(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"酒店品牌 / 集团隶属于第 3 分组"),
    BRAND_GROUP_4(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"酒店品牌 / 集团隶属于第 4 分组"),
    BRAND_GROUP_5(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"酒店品牌 / 集团隶属于第 5 分组"),
    OTHER_GROUP(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"其他分组"),
    SINGLE_HOTEL(WeightTypeEnum.WEIGHT_BRAND.name(), new BigDecimal("1.5"),"单体酒店分组"),

    // FACILITY 装修年限
    FACILITY_3_YEAR(WeightTypeEnum.FACILITY.name(), new BigDecimal("1"),"装修年限 ≤ 3 年"),
    FACILITY_5_YEAR(WeightTypeEnum.FACILITY.name(), new BigDecimal("0.8"),"装修年限 ≤ 5 年"),
    FACILITY_8_YEAR(WeightTypeEnum.FACILITY.name(), new BigDecimal("0.5"),"装修年限 ≤ 8 年"),
    FACILITY_10_YEAR(WeightTypeEnum.FACILITY.name(), new BigDecimal("0.2"),"装修年限 ≤ 10 年"),
    FACILITY_BIGGER_10_YEAR(WeightTypeEnum.FACILITY.name(), new BigDecimal("0"),"装修年限 > 10 年"),

    // FACILITY 房型面积
    ROOM_MORE_THAN_35(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("1"),"房间面积 ≥ 35 ㎡"),
    ROOM_30_TO_35(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("0.9"),"30 ㎡ ≤ 房间面积 < 35 ㎡"),
    ROOM_25_TO_30(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("0.7"),"25 ㎡ ≤ 房间面积 < 30 ㎡"),
    ROOM_20_TO_25(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("0.5"),"20 ㎡ ≤ 房间面积 < 25 ㎡"),
    ROOM_15_TO_20(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("0.2"),"15 ㎡ ≤ 房间面积 < 20 ㎡"),
    ROOM_LESS_15(WeightTypeEnum.ROOM_SIZE.name(), new BigDecimal("0"),"房间面积 ＜ 15 ㎡"),

    // BOOKIE_OF_LAST_YEAR 最近一年预定量
    BOOKIE_MORE_THAN_10000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("1.5"),"预订量 ≥ 10000间夜"),
    BOOKIE_8000_TO_10000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("1.4"),"8000 ≤ 预订量 < 10000"),
    BOOKIE_5000_TO_8000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("1.3"),"5000 ≤ 预订量 < 8000"),
    BOOKIE_3000_TO_5000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("1.2"),"3000 ≤ 预订量 < 5000"),
    BOOKIE_1000_TO_3000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("1"),"1000 ≤ 预订量 < 3000"),
    BOOKIE_800_TO_1000(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0.8"),"800 ≤ 预订量 < 1000"),
    BOOKIE_500_TO_800(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0.5"),"500 ≤ 预订量 < 800"),
    BOOKIE_300_TO_500(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0.3"),"300 ≤ 预订量 < 500"),
    BOOKIE_200_TO_300(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0.2"),"200 ≤ 预订量 < 300"),
    BOOKIE_100_TO_200(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0.1"),"100 ≤ 预订量 < 200"),
    BOOKIE_LESS_100(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name(), new BigDecimal("0"),"预订量 < 100"),

    // PRICE_ADVANTAGE 价格优势
    PRICE_ADVANTAGE_MORE_THAN_25(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("2.5"),"价格优惠 ≥ 25%"),
    PRICE_ADVANTAGE_15_TO_25(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("1.5"),"15% ≤ 价格优惠 < 25%"),
    PRICE_ADVANTAGE_10_TO_15(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("1"),"10% ≤ 价格优惠 < 15%"),
    PRICE_ADVANTAGE_5_TO_10(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("0"),"5% ≤ 价格优惠 < 10%"),
    PRICE_ADVANTAGE_0_TO_5(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("-2"),"0% ≤ 价格优惠 < 5%"),
    PRICE_ADVANTAGE_LESS_THAN_0(WeightTypeEnum.PRICE_ADVANTAGE.name(), new BigDecimal("-5"),"价格优惠 < 0%"),


    // RENEW_PRICE 续约价格
    RENEW_PRICE_MORE_THAN_5(WeightTypeEnum.RENEW_PRICE.name(), new BigDecimal("2"),"续约价格 ≥ 5%"),
    RENEW_PRICE_1_TO_5(WeightTypeEnum.RENEW_PRICE.name(), new BigDecimal("1"),"1% ≤ 续约价格 < 5%"),
    RENEW_PRICE_NEGATIVE_1_TO_1(WeightTypeEnum.RENEW_PRICE.name(), new BigDecimal("0"),"-1% ≤ 续约价格 < 1%"),
    RENEW_PRICE_NEGATIVE_5_TO_NEGATIVE_1(WeightTypeEnum.RENEW_PRICE.name(), new BigDecimal("-1"),"-5% ≤ 续约价格 < -1%"),
    RENEW_PRICE_LESS_THAN_NEGATIVE_2(WeightTypeEnum.RENEW_PRICE.name(), new BigDecimal("-2"),"续约价格 < -5%"),

    // TRAVEL_STANDARD 旅游标准
    TRAVEL_STANDARD_MORE_THAN_900(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0"),"最低报价 ≥ 900"),
    TRAVEL_STANDARD_600_TO_900(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0.2"),"600 ≤ 最低报价 < 900"),
    TRAVEL_STANDARD_500_TO_600(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0.5"),"500 ≤ 最低报价 < 600"),
    TRAVEL_STANDARD_400_TO_500(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0.4"),"400 ≤ 最低报价 < 500"),
    TRAVEL_STANDARD_300_TO_400(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0.3"),"300 ≤ 最低报价 < 400"),
    TRAVEL_STANDARD_200_TO_300(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0.2"),"200 ≤ 最低报价 < 300"),
    TRAVEL_STANDARD_LESS_THAN_200(WeightTypeEnum.TRAVEL_STANDARD.name(), new BigDecimal("0"),"最低报价 < 200"),

    // RATE_INCLUDED_BREAKFAST 包含早餐
    RATE_INCLUDED_BREAKFAST_Y(WeightTypeEnum.RATE_INCLUDED_BREAKFAST.name(), new BigDecimal("1"),"包含早餐"),
    RATE_INCLUDED_BREAKFAST_N(WeightTypeEnum.RATE_INCLUDED_BREAKFAST.name(), new BigDecimal("0"),"不含早餐"),

    // LRA/NLRA
    LRA_NLRA_Y(WeightTypeEnum.LRA_NLRA.name(), new BigDecimal("1"),"支持LRA"),
    LRA_NLRA_N(WeightTypeEnum.LRA_NLRA.name(), new BigDecimal("0"),"不支持LRA"),

    // 取消政策
    CANC_POL_18(WeightTypeEnum.CANC_POL.name(), new BigDecimal("0.5"),"入住当天18:00前取消"),
    CANC_POL_12(WeightTypeEnum.CANC_POL.name(), new BigDecimal("0.3"),"入住当天12:00前取消"),
    CANC_POL_1_DAY_24(WeightTypeEnum.CANC_POL.name(), new BigDecimal("0.0"),"入住前1天24:00前取消"),

    // LAST_YEAR_SERVICE_POINT
    LAST_YEAR_SERVICE_POINT_MORE_THAN_80(WeightTypeEnum.LAST_YEAR_SERVICE_POINT.name(), new BigDecimal("2"),"履约分 ≥ 80"),
    LAST_YEAR_SERVICE_60_TO_80(WeightTypeEnum.LAST_YEAR_SERVICE_POINT.name(), new BigDecimal("1"),"60 ≤ 履约分 < 80"),
    LAST_YEAR_SERVICE_40_TO_60(WeightTypeEnum.LAST_YEAR_SERVICE_POINT.name(), new BigDecimal("0"),"40 ≤ 履约分 < 60"),
    LAST_YEAR_SERVICE_LESS_THAN_40(WeightTypeEnum.LAST_YEAR_SERVICE_POINT.name(), new BigDecimal("-1"),"履约分 ≤ 40"),

    // 是否支持公司统一支付
    CO_PAY_Y(WeightTypeEnum.CO_PAY.name(), new BigDecimal("1"),"支持"),
    CO_PAY_N(WeightTypeEnum.CO_PAY.name(), new BigDecimal("0"),"不支持"),

    //是否支持专6发票
    INVOICE_6_Y(WeightTypeEnum.INVOICE_6.name(), new BigDecimal("1"),"支付"),
    INVOICE_6_N(WeightTypeEnum.INVOICE_6.name(), new BigDecimal("0"),"不支持");

    private final String weightType;
    private final BigDecimal value;
    private final String desc;

    WeightScoreStandardEnum(String weightType, BigDecimal value, String desc){
        this.weightType = weightType;
        this.value = value;
        this.desc = desc;
    }


    public String getWeightType() {
        return weightType;
    }

    public BigDecimal getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }




    public static List<WeightScoreStandardEnum> getWeightScoreStandardByType(String weightType){
        return Arrays.stream(WeightScoreStandardEnum.values())
                .filter(weightScoreStandardEnum -> weightScoreStandardEnum.weightType.equals(weightType))
                .collect(Collectors.toList());
    }

}

