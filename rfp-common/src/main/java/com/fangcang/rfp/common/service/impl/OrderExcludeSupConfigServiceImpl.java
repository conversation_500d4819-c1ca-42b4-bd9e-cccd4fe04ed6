package com.fangcang.rfp.common.service.impl;


import com.fangcang.hotel.agent.dto.response.CompanyResponseDTO;
import com.fangcang.hotel.agent.facade.CompanyFacade;
import com.fangcang.hotel.supply.dto.queryDTO.SupplyOrgAutoQueryDTO;
import com.fangcang.hotel.supply.dto.responseDTO.SupplyOrgAutoQueryResponseDTO;
import com.fangcang.hotel.supply.dto.responseDTO.SupplyOrgResponseDTO;
import com.fangcang.hotel.supply.facade.SupplyOrgFacade;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.dao.OrderExcludeSupConfigDao;
import com.fangcang.rfp.common.dao.OrderMonitorConfigDao;
import com.fangcang.rfp.common.dao.PriceApplicableRoomDao;
import com.fangcang.rfp.common.dao.ProjectHotelPriceDao;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.ModifyPriceRoomDto;
import com.fangcang.rfp.common.dto.request.OrderExcludeSupplierConfigRequest;
import com.fangcang.rfp.common.dto.request.OrderMonitorConfigRequest;
import com.fangcang.rfp.common.dto.response.OrderExcludeSupplierConfigResponse;
import com.fangcang.rfp.common.dto.response.OrderMonitorConfigResponse;
import com.fangcang.rfp.common.entity.OrderExcludeSupConfig;
import com.fangcang.rfp.common.entity.OrderMonitorConfig;
import com.fangcang.rfp.common.entity.PriceApplicableRoom;
import com.fangcang.rfp.common.entity.ProjectHotelPrice;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.enums.StateEnum;
import com.fangcang.rfp.common.service.OrderExcludeSupConfigService;
import com.fangcang.rfp.common.service.OrderMonitorConfigService;
import com.fangcang.rfp.common.util.PageUtil;
import com.fangcang.rfp.common.util.StringUtils;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;

/**
 * 订单监控供应商设置服务实现类
 */
@Service
public class OrderExcludeSupConfigServiceImpl implements OrderExcludeSupConfigService {

    private static Logger logger = LoggerFactory.getLogger(OrderExcludeSupConfigServiceImpl.class);

    @Autowired
    private OrderExcludeSupConfigDao orderExcludeSupConfigDao;

    @Autowired
    private SupplyOrgFacade supplyOrgFacade;

    @Override
    @Transactional
    public Response insertOrderExcludeSupConfig(OrderExcludeSupConfig record) {
        Response response = new Response();

        SupplyOrgResponseDTO supplyOrgResponseDTO = supplyOrgFacade.querySupplyByMerchantCodeAndSupplyCode(record.getSupplierCode(), BaseConfig.getSupplierMerchantCode());
        if(supplyOrgResponseDTO == null){
            logger.info("merchantCode {}, supplierCode {}", BaseConfig.getSupplierMerchantCode(),record.getSupplierCode());
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("供应商不存在");
            return response;
        }
        String supplierName = supplyOrgResponseDTO.getSupplierName();
        OrderExcludeSupConfig orderExcludeSupConfig = orderExcludeSupConfigDao.selectInfoByOrgIdAndSupplierCode(record);
        try {
            record.setState(StateEnum.Effective.key);
            record.setSupplierName(supplierName);
            if (orderExcludeSupConfig != null){
                orderExcludeSupConfigDao.update(record);
            }else {
                record.setMerchantCode(BaseConfig.getSupplierMerchantCode());
                orderExcludeSupConfigDao.insert(record);
            }
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } catch (Exception e) {
            logger.error("新增订单监控排除供应商失败,机构ID：" + record.getOrgId());
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("新增供应商失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return response;
    }

    @Override
    @Transactional
    public Response deleteOrderExcludeSupConfig(OrderExcludeSupConfig record) {
        Response response = new Response();
        try {
            record.setState(StateEnum.Invalid.key);
            orderExcludeSupConfigDao.update(record);
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } catch (Exception e) {
            logger.error("删除订单监控排除供应商配置失败,机构ID：" + record.getOrgId());
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("删除订单监控排除供应商配置失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return response;
    }


    @Override
    public Response selectSupplierCodeList(OrderExcludeSupplierConfigRequest orderExcludeSupplierConfigRequest) {
        Response response = new Response();

        PageHelper.startPage(orderExcludeSupplierConfigRequest.getCurrentPage(),orderExcludeSupplierConfigRequest.getPageSize());
        List<OrderExcludeSupplierConfigResponse> orderExcludeSupplierConfigResponses = orderExcludeSupConfigDao.selectExcludeSupplierCodeList(orderExcludeSupplierConfigRequest);
        PageResult pageResult = PageUtil.makePageResult(orderExcludeSupplierConfigResponses);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);

        return response;
    }

}
