package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ContractDto;
import com.fangcang.rfp.common.dto.request.ContractQueryRequest;
import com.fangcang.rfp.common.dto.request.ContractSignatureRequest;
import com.fangcang.rfp.common.entity.Contract;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ContractService {

    /**
     * 新增合同
     * @param contract
     */
    Response insertContract(Contract contract);


    /**
     * 查询合同分页列表
     * @param contractRequest
     * @return
     */
    Response selectContractPage(ContractQueryRequest contractRequest);

    /**
     * 查看合同
     * @param contractRequest
     * @return
     */
    Response viewContract(ContractQueryRequest contractRequest);

    /**
     * 下载合同
     * @param contractRequest
     * @return
     */
    void downloadContract(HttpServletResponse httpServletResponse, ContractQueryRequest contractRequest);


    /**
     * 查询合同列表(单表查询)
     * @param contractQueryRequest
     * @return
     */
    Response querySimpleContractList(ContractQueryRequest contractQueryRequest);

    /**
     * 批量新增合同
     * @param contractList
     * @return
     */
    Response batchAddContract(List<ContractDto> contractList);

    /**
     * 查询合同列表明细(详细，含甲乙方机构信息)
     * @param contractQueryRequest
     * @return
     */
    Response queryContractList(ContractQueryRequest contractQueryRequest);

    /**
     * 根据ID修改合同
     * @param contractDto
     * @return
     */
    Response updateContractById(ContractDto contractDto);

    /**
     * 根据主键ID查看合同
     * @param contractId
     * @return
     */
    Response selectByPrimaryKey(Long contractId);

    /**
     * 根据合同编号查看合同
     * @param contractCode
     * @return
     */
    Contract selectByContractCode(String contractCode);

    /**
     * 根据合同状态查询列表
     * @param contractState
     * @return
     */
    List<Contract> selectByContractState(Integer contractState);

    /**
     * 在线签章
     * @param contractId
     * @return
     * @throws Exception
     */
    Response onlineSignature(Long contractId) throws Exception;

    /**
     * 页面在线签章，更改状态
     * @param contractSignatureRequest
     * @return
     * @throws Exception
     */
    Response onlineSignatureUpdateStatus(ContractSignatureRequest contractSignatureRequest) throws Exception;

    /**
     * 房仓合同归档
     * @param contractId
     * @return
     */
    Response fcContractFiling(String contractId,String modifier) throws Exception;

    /**
     * 查询余额、合同数据并更新缓存
     */
    void selectChargeStattisticListUpdateCache() throws Exception;

    /**
     * 根据项目ID、项目业务ID查询已完成合同
     * @return
     */
    Response selectFinishContractByProjectIdAndIntentHotelId(Long projectId,Long intentHotelId);


    /**
     * 在线签章参数校验方法
     * @param contractId
     * @return
     * @throws Exception
     */
    Response onlineSignatureCheckParamMethod(Long contractId) throws Exception;

    /**
     * 转电子签章
     * @param contractDto
     * @return
     */
    Response changeElectronicSign(ContractDto contractDto);
}
