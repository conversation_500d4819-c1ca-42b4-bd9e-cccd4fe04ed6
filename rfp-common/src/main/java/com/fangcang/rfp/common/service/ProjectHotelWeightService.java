package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.response.HotelCustomBidWeightDetailVO;
import com.fangcang.rfp.common.dto.response.QueryCustomTendStrategyResponse;
import com.fangcang.rfp.common.dto.response.QueryProjectHotelPriceDetailResponse;
import com.fangcang.rfp.common.entity.ProjectCustomBidStrategy;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProjectHotelWeightService {


    public void generateProjectHotelWeight(Long projectId, Long hotelId, String userCode);

    public List<HotelCustomBidWeightDetailVO> calculateCustomStageWeight(List<ProjectCustomBidStrategy> projectHotelCustomBidStrategies, List<QueryCustomTendStrategyResponse> projectCustomTendStrategyResponses);

    public void setBidWeightInfo(QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse, Long projectId, Long hotelId);

    public void asyncGenerateProjectHotelWeight(Long projectId);
}
