package com.fangcang.rfp.common.constants;

public class RedisLockConstant {

    /**key存活时间**/
    public static final Integer EXPIRE_TIME_SECOND = 300;

    /**获取锁的等待间隔，默认不间隔**/
    public static final Integer NO_WAITE_TIME= -1;

    /**最大等待时间，默认不等**/
    public static final Integer NO_TIMEOUT_TIME = -1;

    /** 复核违规监控锁 */
    public static final  String CONSUME_REPEAT_MONITOR_LOCK_KEY = "RFP:LOCK:consumeRepeatMonitor:";

    /** 生产酒店六边形 */
    public static final  String GENERATE_HOTEL_HEXAGON_LOCK_KEY = "RFP:LOCK:generateHotelHexagon:";
}
