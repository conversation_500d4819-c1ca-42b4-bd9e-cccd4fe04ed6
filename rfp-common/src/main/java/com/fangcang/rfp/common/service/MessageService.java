package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.request.RfpSendEmailDto;
import com.fangcang.rfp.common.dto.request.RfpSmsSendInfoDto;

/**
 * <AUTHOR>
 * @date 2022/8/26 21:10
 */
public interface MessageService {

    /**
     * 发送短信
     *
     * @param rfpSmsSendInfoDto
     * @return
     * @throws Exception
     */
    String sendMessage(RfpSmsSendInfoDto rfpSmsSendInfoDto) throws Exception;

    /**
     * 邮件推送
     *
     * @param rfpSendEmailDto
     * @return
     * @throws Exception
     */
    boolean sendEmail(RfpSendEmailDto rfpSendEmailDto) throws Exception;
}
