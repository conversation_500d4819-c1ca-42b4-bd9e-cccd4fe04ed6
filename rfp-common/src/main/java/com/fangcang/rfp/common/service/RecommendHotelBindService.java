package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.QueryRecommendHotelParam;
import com.fangcang.rfp.common.dto.request.SendRecommendHotelInvitationRequest;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:01
 */
public interface RecommendHotelBindService {

    /**
     * 绑定项目查询推荐酒店
     *
     * @param queryRecommendHotelParam
     * @return
     */
    Response queryRecommendHotelBind(QueryRecommendHotelParam queryRecommendHotelParam);

    /**
     * 发送邀请
     *
     * @param sendRecommendHotelInvitationRequest
     * @return
     */
    Response sendRecommendHotelInvitation(SendRecommendHotelInvitationRequest sendRecommendHotelInvitationRequest) throws Exception;

    /**
     * 查询绑定项目
     *
     * @param orgId
     * @return
     */
    Response queryBindProject(Long orgId) throws Exception;

    /**
     * 查询项目信息
     *
     * @param orgId
     * @return
     */
    Response queryProjectInfo(Long orgId);
}
