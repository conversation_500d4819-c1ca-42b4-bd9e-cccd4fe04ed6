package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.OperateEnterpriseOrgHotelGroupContactUserDto;
import com.fangcang.rfp.common.dto.request.OrgHotelGroupContactUserQueryRequest;
import com.fangcang.rfp.common.dto.response.OrgHotelGroupContactUserQueryResponse;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OrgHotelGroupContactUserService {

    Response add(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto);

    Response delete(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto);

    Response update(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto);

    Response queryOrgHotelGroupContactUserList(OrgHotelGroupContactUserQueryRequest request);

    Response queryOrgHotelGroupContactUserLogList(OrgHotelGroupContactUserQueryRequest request);

    OrgHotelGroupContactUserQueryResponse queryOrgHotelGroupContactUser(Long enterpriseOrgId, Long hotelGroupOrgId);


}
