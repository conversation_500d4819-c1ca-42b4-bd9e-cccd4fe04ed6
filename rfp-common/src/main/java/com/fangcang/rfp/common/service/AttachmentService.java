package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.file.FileResponse;
import com.fangcang.rfp.common.dto.request.DownloadFileToStreamRequest;
import com.fangcang.rfp.common.dto.request.QueryAttachmentInfoParam;
import com.fangcang.rfp.common.entity.Attachment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;

/**
 * <AUTHOR>
 * @date 2022/9/9 15:35
 */
public interface AttachmentService {

    /**
     * 上传附件
     *
     * @param uploadFile
     * @param operator
     * @param businessType
     * @return
     * @throws Exception
     */
    Response uploadFile(MultipartFile uploadFile, String operator, Integer businessType) throws Exception;

    /**
     * 查询附件
     *
     * @param queryAttachmentInfoParam
     * @return
     * @throws Exception
     */
    Response queryAttachmentInfo(QueryAttachmentInfoParam queryAttachmentInfoParam);

    /**
     * 更新附件外部id信息
     * @param attachment
     * @return
     */
    Response updateAttachmentExternalIdByFileId(Attachment attachment);

    /**
     * 删除附件信息
     * @param attachment
     * @return
     */
    Response deleteAttachmentByFileId(Attachment attachment);

    /**
     * 内部文件上传
     * @param uploadFile
     * @param fileName
     * @param operator
     * @param businessType
     * @return
     * @throws Exception
     */
    Response insideUploadFile(File uploadFile, String fileName,  String operator, Integer businessType) throws Exception;

    /**
     * 上传文件到服务器不记录到数据库
     * @param uploadFiles
     * @param businessType
     * @return
     */
    FileResponse uploadFileSaveServer(MultipartFile[] uploadFiles, Integer businessType);

    /**
     * 上传文件绑定外部id
     * @param uploadFile
     * @param operator
     * @param businessType
     * @param externalId
     * @return
     */
    Response uploadFileBindExternalId(MultipartFile uploadFile, String operator, Integer businessType, Long externalId) throws Exception;

    /**
     * 下载文件返回二进制流
     * @param downloadFileToStreamRequest
     * @return
     */
    byte[] downloadFileToStream(DownloadFileToStreamRequest downloadFileToStreamRequest);
}
