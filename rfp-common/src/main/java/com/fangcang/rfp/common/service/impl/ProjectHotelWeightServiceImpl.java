package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.fangcang.enums.BreakfastNumEnum;
import com.fangcang.enums.HotelStarEnum;
import com.fangcang.enums.InvoiceProviderEnum;
import com.fangcang.hotel.base.api.dto.query.RoomSummaryInfoQuery;
import com.fangcang.hotel.base.api.dto.response.RoomInfoResponseDto;
import com.fangcang.hotel.base.api.facade.RoomInfoFacade;
import com.fangcang.rfp.common.cache.CachedProjectManager;
import com.fangcang.rfp.common.constants.ProjectWeightHotelGroupType;
import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.*;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.HotelRoomUtil;
import com.fangcang.rfp.common.util.LocationUtil;
import com.fangcang.rfp.common.util.MathUtil;
import com.fangcang.rfp.common.util.StringUtils;
import com.fangcang.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ProjectHotelWeightServiceImpl implements ProjectHotelWeightService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectHotelWeightServiceImpl.class);

    @Autowired
    private ProjectHotelWeightDao projectHotelWeightDao;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;
    @Autowired
    private ProjectPoiDao projectPoiDao;
    @Autowired
    private HotelDao hotelDao;
    @Autowired
    private ProjectWeightHotelGroupDao projectWeightHotelGroupDao;
    @Autowired
    private RoomInfoFacade roomInfoFacade;
    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;
    @Autowired
    private ProjectHotelBidStrategyDao projectHotelBidStrategyDao;
    @Autowired
    private CachedProjectManager cachedProjectManager;
    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;
    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;
    @Autowired
    private OtaHotelDailyMinPriceDao otaHotelDailyMinPriceDao;
    @Autowired
    private ProjectHotelHistoryDataDao projectHotelHistoryDataDao;
    @Autowired
    private ProjectHotelBidStrategyService projectHotelBidStrategyService;
    @Autowired
    private ProjectIntentHotelService projectIntentHotelService;

    /**
     * 生成项目酒店权重
     */
    public void generateProjectHotelWeight(Long projectId, Long hotelId, String userCode) {
        // 查询项目权重
        Response queryProjectWeightResponse = projectService.queryProjectWeight(projectId);
        if(!Objects.equals(ReturnResultEnum.SUCCESS.errorNo, queryProjectWeightResponse.getResult())){
            logger.error("查询项目权重失败 {}", projectId);
        }
        ProjectWeightResponse projectWeightResponse = (ProjectWeightResponse) queryProjectWeightResponse.getData();

        // 查询酒店报价
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectId, hotelId);
        // 查询酒店信息
        HotelResponse hotel =  hotelDao.selectHotelInfo(hotelId);
        // 查询项目
        Project project = cachedProjectManager.getById(projectId);

        // 查询酒店报价策略
        ProjectHotelBidStrategy projectHotelBidStrategy = projectHotelBidStrategyDao.selectByPrimaryKey(projectIntentHotel.getProjectIntentHotelId());

        // 项目酒店权重计算
        List<ProjectHotelWeight> projectHotelWeightList = new ArrayList<>();
        List<ProjectWeightVO> projectWeightList = projectWeightResponse.getProjectWeightList();
        for(ProjectWeightVO projectWeightVO : projectWeightList){
            if(projectWeightVO.getIsEnabled() == RfpConstant.constant_0){
                continue;
            }
            // 位置--------------------------------------------------------------------------------------------------------
            // 距离OFFICE或POI的距离
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.DIS_OFFICER_POI.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateDistanceScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
            // 品质 -------------------------------------------------------------------------------------------------------
            // 星级
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.HOTEL_STAR.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateHotelStarScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
            // OTA 评分
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.RATING.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateRattingScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
            // 酒店品牌
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.WEIGHT_BRAND.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateBrandScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
            //  装修时间：根据装修或开业时间计算得分（有装修时间优先使用装修时间计算）
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.FACILITY.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateFacilityScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
            // 房型面积 酒店房间面积（报价最低房型）：根据第一档房型面积计算权重得分。（一个房档多个房型面积不同，取最小面积）
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.ROOM_SIZE.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateRoomSizeScore(hotel, projectWeightVO, projectId);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            // 预定量 --------------------------------------------------------------------------------------------------
            // 预定量：可根据具体预定量获取对应设置分值，计算根据历史交易或邀约列表中的历史交易数据进行统计。 酒店历史一年预订情况
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.BOOKIE_OF_LAST_YEAR.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateLastYearBookieScore(hotel, projectWeightVO, projectIntentHotel);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            // 结算及履约 ---------------------------------------------------------------------------------------------
            // 酒店支持公司统一支付：根据选项承诺计算分值
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.CO_PAY.name()) && projectHotelBidStrategy != null){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateSupportScore(WeightScoreStandardEnum.CO_PAY_Y.name(), WeightScoreStandardEnum.CO_PAY_N.name(), projectWeightVO, projectHotelBidStrategy.getSupportCoPay());
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            // 发票权重，能提供税率6%增值税专用发
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.INVOICE_6.name()) && projectHotelBidStrategy != null){
                int isSupport = RfpConstant.constant_0;
                if(Objects.equals(projectHotelBidStrategy.getProvideInvoiceType(), InvoiceEnum.SPECIAL.getKey()) &&
                        projectHotelBidStrategy.getProvideInvoiceTaxRate().compareTo(new BigDecimal(6)) >= 0
                ){
                    isSupport = RfpConstant.constant_1;
                }
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateSupportScore(WeightScoreStandardEnum.INVOICE_6_Y.name(),WeightScoreStandardEnum.INVOICE_6_N.name(), projectWeightVO, isSupport);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            // 履约服务分 根据去年关联项目酒店服务分进行计算分值   （先只做国内，海外目前没有履约监控）
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.LAST_YEAR_SERVICE_POINT.name())){
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateLastYearServicePointScore(hotel, projectWeightVO, project);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            // 价格政策 --------------------------------------------------------------------------------------------------------------------
            //  是否含早：所有报价是否有包含含早的报价
            ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
            projectIntentHotelRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            List<ProjectHotelPriceLevelResponse> hotelPriceLevelResponseList = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.RATE_INCLUDED_BREAKFAST.name())){
                int isSupport = 0;
                // 是否包含早餐
                for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()) {
                            if (!projectHotelPrice.getBreakfastNum().equals(BreakfastNumEnum.ZERO.key)) {
                                isSupport = 1;
                                break;
                            }
                        }
                        if(isSupport == RfpConstant.constant_1){
                            break;
                        }
                    }
                    if(isSupport == RfpConstant.constant_1){
                        break;
                    }
                }
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateSupportScore(WeightScoreStandardEnum.RATE_INCLUDED_BREAKFAST_Y.name(), WeightScoreStandardEnum.RATE_INCLUDED_BREAKFAST_N.name(),projectWeightVO, isSupport);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            //  LRA: 所有报价是否有包含LRA的报价
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.LRA_NLRA.name())) {
                int isSupport = 0;
                for(ProjectHotelPriceLevelResponse hotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : hotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        if (projectHotelPriceGroupResponse.getLra().equals(StateEnum.Effective.key)) {
                            isSupport = RfpConstant.constant_1;
                            break;
                        }
                    }
                    if(isSupport == RfpConstant.constant_1){
                        break;
                    }
                }
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateSupportScore(WeightScoreStandardEnum.LRA_NLRA_Y.name(), WeightScoreStandardEnum.LRA_NLRA_N.name(),projectWeightVO, isSupport);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            //  免费取消条款：按免费取消条款政策计算得分
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.CANC_POL.name())) {
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateCancelPol(hotel, projectWeightVO, hotelPriceLevelResponseList);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            //  价格优势：根据签约最低价格与对应C端近12个月C端每日最低价的中位数（不是平均价）比较，根据图示分组，获取对应得分
            BigDecimal minPrice = null;
            for(ProjectHotelPriceLevelResponse hotelPriceLevelResponse : hotelPriceLevelResponseList){
                for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : hotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                    for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()){
                        if(projectHotelPrice.getBasePrice() == null || projectHotelPrice.getBasePrice().compareTo(BigDecimal.ZERO) == 0){
                            continue;
                        }
                        if(minPrice == null || minPrice.compareTo(projectHotelPrice.getBasePrice()) > 0){
                            minPrice = projectHotelPrice.getBasePrice();
                        }
                    }
                }
            }
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.TRAVEL_STANDARD.name())) {
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateTravelStandard(hotel, projectWeightVO, minPrice);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            //  续约价格：根据续约最低价与去年签约最低价进行比较，降低为正数，负数表示涨价
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.RENEW_PRICE.name())) {
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculateRenewPrice(hotel, projectWeightVO, projectId, minPrice);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }

            //  价格优势：根据签约最低价格与对应C端近12个月C端每日最低价的中位数（不是平均价）比较，根据图示分组，获取对应得分。
            if(projectWeightVO.getWeightType().equals(WeightTypeEnum.PRICE_ADVANTAGE.name())) {
                List<WeightScoreStandardVO> weightScoreStandardVOList = calculatePriceAdvantage(hotel, projectWeightVO, projectId, minPrice);
                projectHotelWeightList.add(convertProjectHotelWeight(projectId, hotelId, projectWeightVO, weightScoreStandardVOList, userCode));
            }
        }
        BigDecimal totalScore = BigDecimal.ZERO;
        // 新增报价权重详情
        if(CollectionUtils.isNotEmpty(projectHotelWeightList)) {
            projectIntentHotelService.batchUpdateProjectWeight(projectId, hotelId, projectHotelWeightList);
            Optional<BigDecimal> sumOptional = projectHotelWeightList.stream().map(ProjectHotelWeight::getScore).reduce(BigDecimal::add);
            totalScore = sumOptional.orElse(BigDecimal.ZERO);
        } else {
            // 清空之前权限
            projectHotelWeightDao.deleteByProjectHotelId(projectId, hotelId);
        }

        // 计算自定义权重
        List<ProjectCustomBidStrategy> projectHotelCustomBidStrategies = projectHotelBidStrategyService.queryProjectCustomBidStrategyList(projectId, hotelId);
        List<QueryCustomTendStrategyResponse> projectCustomTendStrategyResponses = projectService.queryProjectCustomTendStrategy(projectId);
        List<HotelCustomBidWeightDetailVO> hotelCustomBidWeightDetailVOS = calculateCustomStageWeight(projectHotelCustomBidStrategies, projectCustomTendStrategyResponses);
        BigDecimal customStageWeight = hotelCustomBidWeightDetailVOS.stream().map(HotelCustomBidWeightDetailVO::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
        totalScore = totalScore.add(customStageWeight);
        logger.info("计算customStageWeight {}, {}", hotel.getHotelId(), customStageWeight);

        // 更新权重分
        projectIntentHotel.setBidWeight(totalScore);
        projectIntentHotel.setModifier(userCode);
        logger.info("计算总权重分 {}, {}", hotel.getHotelId(), totalScore);
        projectIntentHotelDao.updateBidWeight(projectIntentHotel);
    }

    /**
     * 转换项目酒店权重
     */
    private ProjectHotelWeight convertProjectHotelWeight(Long projectId, Long hotelId, ProjectWeightVO projectWeightVO, List<WeightScoreStandardVO> weightScoreStandardVOList, String userCode){
        BigDecimal score = BigDecimal.ZERO;
        ProjectHotelWeight projectHotelWeight = new ProjectHotelWeight();
        projectHotelWeight.setProjectId(projectId);
        projectHotelWeight.setHotelId(hotelId);
        projectHotelWeight.setCategoryCode(projectWeightVO.getCategoryCode());
        projectHotelWeight.setWeightType(projectWeightVO.getWeightType());

        // 记录权重评分标准
        projectHotelWeight.setScoreStandards(JsonUtil.objectToJson(weightScoreStandardVOList));
        // 计算分数
        for (WeightScoreStandardVO weightScoreStandardVO : weightScoreStandardVOList){
            if(weightScoreStandardVO.getValue() != null) {
                score = score.add(weightScoreStandardVO.getValue());
            }
        }
        // 设置分值
        projectHotelWeight.setScore(score);
        projectHotelWeight.setCreator(userCode);
        projectHotelWeight.setModifier(userCode);
        return projectHotelWeight;
    }

    private List<WeightScoreStandardVO> calculateCancelPol(HotelResponse hotel, ProjectWeightVO projectWeightVO, List<ProjectHotelPriceLevelResponse> hotelPriceLevelResponseList){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();

        // 计算最大退房时间
        Integer maxBeforeHours = null;
        for(ProjectHotelPriceLevelResponse hotelPriceLevelResponse : hotelPriceLevelResponseList){
            for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : hotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                if (projectHotelPriceGroupResponse.getCancelRestrictType() == 2 && projectHotelPriceGroupResponse.getCancelRestrictDay().intValue() == 0) {
                   int beforeHours = Integer.parseInt(projectHotelPriceGroupResponse.getCancelRestrictTime().substring(0, 1));
                   if(maxBeforeHours == null || beforeHours > maxBeforeHours){
                       maxBeforeHours = beforeHours;
                   }
                }
            }
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(maxBeforeHours == null){
            weightScoreStandardEnumName = WeightScoreStandardEnum.CANC_POL_1_DAY_24.name();
        } else if(maxBeforeHours <= 12){
            weightScoreStandardEnumName = WeightScoreStandardEnum.CANC_POL_12.name();
        } else if(maxBeforeHours <= 18){
            weightScoreStandardEnumName = WeightScoreStandardEnum.CANC_POL_18.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.CANC_POL_1_DAY_24.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}, {}", hotel.getHotelId(), maxBeforeHours, weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }

    // 酒店报价的最低价按设置价格分组进行计算得分
    private List<WeightScoreStandardVO> calculateTravelStandard(HotelResponse hotel, ProjectWeightVO projectWeightVO, BigDecimal minPrice){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        if(minPrice == null){
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(minPrice.compareTo(new BigDecimal("900")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_MORE_THAN_900.name();
        } else if(minPrice.compareTo(new BigDecimal("600")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_600_TO_900.name();
        } else if(minPrice.compareTo(new BigDecimal("500")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_500_TO_600.name();
        } else if(minPrice.compareTo(new BigDecimal("400")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_400_TO_500.name();
        } else if(minPrice.compareTo(new BigDecimal("300")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_300_TO_400.name();
        } else if(minPrice.compareTo(new BigDecimal("200")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_200_TO_300.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.TRAVEL_STANDARD_LESS_THAN_200.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}, {}", hotel.getHotelId(), minPrice, weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }


    // RENEW_PRICE
    private List<WeightScoreStandardVO> calculateRenewPrice(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId, BigDecimal minPrice){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        if(minPrice == null){
            return result;
        }

        // 查询去年报价
        Project project = cachedProjectManager.getById(projectId);
        if(project.getRelatedProjectId() == null){
            return result;
        }
        ProjectIntentHotel lastYearProjectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(project.getRelatedProjectId(), hotel.getHotelId());
        if(lastYearProjectIntentHotel == null){
            logger.info("lastYearProjectIntentHotel is null {}", hotel.getHotelId());
            return result;
        }
        // 查询去年最低价格
        BigDecimal lastYearMinPrice = projectHotelPriceDao.selectMinPrice(lastYearProjectIntentHotel.getProjectIntentHotelId());
        if(lastYearMinPrice == null){
            logger.info("lastYearMinPrice is null {}", hotel.getHotelId());
            return result;
        }
        BigDecimal saveAmount = minPrice.subtract(lastYearMinPrice);
        BigDecimal savePercentage = saveAmount.divide(lastYearMinPrice, 2, RoundingMode.HALF_UP);

        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(savePercentage.compareTo(new BigDecimal("0.05")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RENEW_PRICE_MORE_THAN_5.name();
        } else if(minPrice.compareTo(new BigDecimal("0.01")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RENEW_PRICE_1_TO_5.name();
        } else if(minPrice.compareTo(new BigDecimal("-0.01")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RENEW_PRICE_NEGATIVE_1_TO_1.name();
        } else if(minPrice.compareTo(new BigDecimal("-0.05")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RENEW_PRICE_NEGATIVE_5_TO_NEGATIVE_1.name();
        } else if(minPrice.compareTo(new BigDecimal("-0.05")) < 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RENEW_PRICE_LESS_THAN_NEGATIVE_2.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}, {}", hotel.getHotelId(), minPrice, weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }


    // 价格优势：根据签约最低价格与对应C端近12个月C端每日最低价的中位数（不是平均价）比较  PRICE_ADVANTAGE
    private List<WeightScoreStandardVO> calculatePriceAdvantage(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId, BigDecimal minPrice){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        if(minPrice == null){
            return result;
        }

        // 查询最近12个月的OTA最低价格
        Date dateFrom = DateUtil.offset(new Date(), DateField.MONTH, -12);
        Date dateTo = new Date();
        List<BigDecimal> minPriceList = otaHotelDailyMinPriceDao.queryMinPriceList(hotel.getHotelId(), DateUtil.formatDate(dateFrom), DateUtil.formatDate(dateTo));
        if(CollectionUtils.isEmpty(minPriceList)){
            return result;
        }
        // 取中位数
        int size = minPriceList.size();
        BigDecimal median = null;
        if (size % 2 == 1) {
            // 奇数个元素：返回中间的元素
            median = minPriceList.get(size / 2);
        } else {
            // 偶数个元素：返回中间两个元素的平均值
            int mid1 = size / 2 - 1;
            int mid2 = size / 2;
            median = (minPriceList.get(mid1).add(minPriceList.get(mid2))).divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
        }
        if(median == null || median.compareTo(BigDecimal.ZERO) == 0){
            return result;
        }
        BigDecimal percentage = BigDecimal.ONE.subtract(minPrice.divide(median,2, RoundingMode.HALF_UP));

        // 查询去年报价
        Project project = cachedProjectManager.getById(projectId);
        if(project.getRelatedProjectId() == null){
            return result;
        }

        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(percentage.compareTo(new BigDecimal("0.25")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_MORE_THAN_25.name();
        } else if(minPrice.compareTo(new BigDecimal("0.15")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_15_TO_25.name();
        } else if(minPrice.compareTo(new BigDecimal("0.1")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_10_TO_15.name();
        } else if(minPrice.compareTo(new BigDecimal("0.05")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_5_TO_10.name();
        } else if(minPrice.compareTo(new BigDecimal("0")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_0_TO_5.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.PRICE_ADVANTAGE_LESS_THAN_0.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, median: {}, {}, {}", hotel.getHotelId(), minPrice, median, weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }

    //  距离OFFICE或POI的距离：根据酒店与同城市最近的POI距离计算权重分值
    private List<WeightScoreStandardVO> calculateDistanceScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        // 查询同城POI
        List<ProjectPoiInfoResponse> projectPoiInfoResponseList = projectPoiDao.selectMapProjectPoiInfo(projectId, null, hotel.getCity(), null);
        Double poiDistance = null;

        if(CollectionUtils.isNotEmpty(projectPoiInfoResponseList)){
            for(ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList){
                Double distance = LocationUtil.getKmDistance(projectPoiInfoResponse.getLatBaiDu(), projectPoiInfoResponse.getLngBaiDu(),
                        hotel.getLatBaidu(), hotel.getLngBaidu(), projectPoiInfoResponse.getPoiName(), hotel.getHotelName());
                if(distance != null && (poiDistance == null || distance < poiDistance)){
                    poiDistance = distance;
                }
            }
            if(poiDistance == null || poiDistance < 0 || poiDistance >= 10){
                logger.info("距离OFFICE或POI的距离 {} {} {}", hotel.getHotelId(), hotel.getCity(),poiDistance);
                return result;
            }
            List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
            Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
            String weightScoreStandardEnumName = "";
            WeightScoreStandardVO weightScoreStandardVO = null;
            if(poiDistance < 1){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_0_1_KM.name();
            } else if(poiDistance < 2){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_1_2_KM.name();
            } else if(poiDistance < 3){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_2_3_KM.name();
            } else if(poiDistance < 4){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_3_4_KM.name();
            } else if(poiDistance < 5){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_4_5_KM.name();
            } else if(poiDistance < 6){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_5_6_KM.name();
            } else if(poiDistance < 7){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_6_7_KM.name();
            } else if(poiDistance < 8){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_7_8_KM.name();
            } else if(poiDistance < 9){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_8_9_KM.name();
            } else if(poiDistance < 10){
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_9_10_KM.name();
            } else {
                weightScoreStandardEnumName = WeightScoreStandardEnum.DIS_OFFICER_POI_MORE_THAN_10_KM.name();
            }
            if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
                weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
                result.add(weightScoreStandardVO);
            }
            logger.info(projectWeightVO.getWeightType() +": {}, {}, {}, {}", hotel.getHotelId(), poiDistance, weightScoreStandardEnumName, weightScoreStandardVO);
        }
        return result;
    }
    /**
     * 6、品质
     *       星级：5星 改为  五星级/豪华型
     *                  4星 改为  四星级/高档型
     *                  3星  改为  三星级/舒适型
     *                  增加 经济型
     *                  增加 二星及以下/客栈及公寓
     *                  根据酒店所在星级分组进行权重得分
     *      评分：根据OTA评分计算所在分组，获得权重得分
     */
    private List<WeightScoreStandardVO> calculateHotelStarScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        // 酒店星级
        String hotelStar = hotel.getHotelStar();
        if(StringUtils.isEmpty(hotelStar)){
            logger.info("酒店星级为空 {}", hotel.getHotelId());
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));

        HotelStarEnum hotelStarEnum = HotelStarEnum.getEnumByKey(Integer.parseInt(hotelStar));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(hotelStarEnum.key == HotelStarEnum.FIVESTAR.key || hotelStarEnum.key == HotelStarEnum.QUISAFIVESTAR.key){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FIVE_QUISAFIVE_STAR.name();
        } else if(hotelStarEnum.key == HotelStarEnum.FOURSTAR.key || hotelStarEnum.key == HotelStarEnum.QUISAFOURSTAR.key){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FOUR_QUISAFOUR_STAR.name();
        } else if(hotelStarEnum.key == HotelStarEnum.THREESTAR.key || hotelStarEnum.key == HotelStarEnum.QUISATHREESTAR.key){
            weightScoreStandardEnumName = WeightScoreStandardEnum.THREESTAR_QUISATWOSTAR.name();
        } else if(hotelStarEnum.key == HotelStarEnum.QUISATWOSTAR.key){
            weightScoreStandardEnumName = WeightScoreStandardEnum.QUISATWOSTAR.name();
        } else if(hotelStarEnum.key == HotelStarEnum.TWOSTAR.key || hotelStarEnum.key == HotelStarEnum.BELOWTWOSTAR.key){
            weightScoreStandardEnumName = WeightScoreStandardEnum.TWOSTAR_BELOWTWOSTAR.name();
        }

        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }

    /**
     * 评分：根据OTA评分计算所在分组，获得权重得分
     */
    private List<WeightScoreStandardVO> calculateRattingScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        // OTA评分
        String hotelRatting = hotel.getRatting();
        if(StringUtils.isEmpty(hotelRatting)){
            logger.info("OTA评分 {}", hotel.getHotelId());
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));

        BigDecimal ratting = new BigDecimal(hotelRatting);
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(ratting.compareTo(new BigDecimal("4.8")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_MORE_THAN_48.name();
        } else if(ratting.compareTo(new BigDecimal("4.5")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_45_TO_48.name();
        } else if(ratting.compareTo(new BigDecimal("4.2")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_42_TO_45.name();
        } else if(ratting.compareTo(new BigDecimal("4.0")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_40_TO_42.name();
        } else if(ratting.compareTo(new BigDecimal("3.5")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_35_TO_40.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.RATING_LESS_THAN_35.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }

    /**
     * 酒店品牌：可以根据酒店品牌设置权重分值。（需要有一个明细页面，可以添加集团或品牌，进行设置权重分，优先匹配品牌。参考附件做默认值）
     */
    private List<WeightScoreStandardVO> calculateBrandScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        List<ProjectWeightHotelGroup> groupList = null;

        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));

        // 是否有定义酒店集团分组，没有定义就是其他分组
        List<ProjectWeightHotelGroup> projectWeightHotelGroupList = projectWeightHotelGroupDao.queryByRefId(projectId, null,null);
        if(CollectionUtils.isEmpty(projectWeightHotelGroupList)){
            WeightScoreStandardVO weightScoreStandardVO = weightScoreStandardMap.get(WeightScoreStandardEnum.OTHER_GROUP.name());
            if(weightScoreStandardVO != null){
                result.add(weightScoreStandardVO);
            }
            return result;
        }

        // 单体酒店
        String hotelBrand = hotel.getHotelBrand();
        if(StringUtils.isEmpty(hotelBrand)){
           groupList = projectWeightHotelGroupList.stream().filter(projectWeightHotelGroup -> projectWeightHotelGroup.getGroupType().equals(ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_SINGLE_HOTEL)).filter(o -> Objects.equals(o.getReferenceId(), hotel.getHotelId())).collect(Collectors.toList());
        }
        // 是否有匹配的品牌
        if(StringUtils.isNotEmpty(hotel.getHotelBrand()) && CollectionUtils.isEmpty(groupList)) {
            Long hotelBrandId = Long.valueOf(hotelBrand);
            groupList = projectWeightHotelGroupList.stream().filter(projectWeightHotelGroup -> projectWeightHotelGroup.getReferenceId().equals(hotelBrandId)).collect(Collectors.toList());
        }

        // 是否有匹配的酒店集团
        if(StringUtils.isNotEmpty(hotel.getHotelGroup()) && CollectionUtils.isEmpty(groupList)){
            Long hotelGroupId = Long.valueOf(hotel.getHotelGroup());
            groupList = projectWeightHotelGroupList.stream().filter(projectWeightHotelGroup -> projectWeightHotelGroup.getReferenceId().equals(hotelGroupId)).collect(Collectors.toList());
        }

        WeightScoreStandardVO weightScoreStandardVO = null;
        if(CollectionUtils.isNotEmpty(groupList)){
            ProjectWeightHotelGroup projectWeightHotelGroup = groupList.get(0);
            String weightScoreStandardEnumName = "";

            if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_1)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.BRAND_GROUP_1.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_2)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.BRAND_GROUP_2.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_3)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.BRAND_GROUP_3.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_4)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.BRAND_GROUP_4.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_5)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.BRAND_GROUP_5.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_OTHER)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.OTHER_GROUP.name();
            } else if(Objects.equals(projectWeightHotelGroup.getGroupType(), ProjectWeightHotelGroupType.PROJECT_WEIGHT_HOTEL_GROUP_SINGLE_HOTEL)) {
                weightScoreStandardEnumName = WeightScoreStandardEnum.SINGLE_HOTEL.name();
            }
            if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
                weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
                result.add(weightScoreStandardVO);
            }
            logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

        }

        return result;
    }

    /**
     * 装修时间：根据装修或开业时间计算得分（有装修时间优先使用装修时间计算） FACILITY
     */
    private List<WeightScoreStandardVO> calculateFacilityScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        // 装修时间
        Date hotelDate = hotel.getFitmentDate(); //
        // 开业时间
        if(hotelDate == null){
            hotelDate = hotel.getPraciceDate();
        }
        if(hotelDate == null){
            logger.info("装修和开业时间为空 {}", hotel.getHotelId());
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));

        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        Date now = new Date();
        int diffYear = (int)DateUtil.betweenYear(hotelDate, now, true);
        if(diffYear <= 3){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FACILITY_3_YEAR.name();
        } else if(diffYear <= 5){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FACILITY_5_YEAR.name();
        } else if(diffYear <= 8){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FACILITY_8_YEAR.name();
        } else if(diffYear <= 10){
            weightScoreStandardEnumName = WeightScoreStandardEnum.FACILITY_10_YEAR.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.FACILITY_BIGGER_10_YEAR.name();
        }

        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

        return result;
    }

    /**
     * 酒店房间面积（报价最低房型）：根据第一档房型面积计算权重得分。（一个房档多个房型面积不同，取最小面积）
     */
    private List<WeightScoreStandardVO> calculateRoomSizeScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Long projectId){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();

        RoomSummaryInfoQuery roomSummaryInfoQuery = new RoomSummaryInfoQuery();
        roomSummaryInfoQuery.setHotelId(hotel.getHotelId());
        List<RoomInfoResponseDto> roomInfoResponseDtos = null;
        Map<Long, RoomInfoResponseDto> roomInfoResponseDtoMap = new HashMap<Long, RoomInfoResponseDto>();
        try {
            roomInfoResponseDtos = roomInfoFacade.queryRoomSummaryInfoList(roomSummaryInfoQuery);
            roomInfoResponseDtoMap = roomInfoResponseDtos.stream().collect(Collectors.toMap(RoomInfoResponseDto::getRoomId, roomInfoResponseDto -> roomInfoResponseDto));
        } catch (Exception ex){
            logger.error("根据酒店Id查酒店和房型信息失败" + hotel.getHotelId(),ex);
            return result;
        }
        BigDecimal minRoomAreaSize = null;
        if (!CollectionUtils.isEmpty(roomInfoResponseDtos)) {
           // 查询房档一的房型
            //  List<PriceApplicableRoomInfoResponse> selectPriceApplicableRoom(PriceApplicableRoom priceApplicableRoom);
            PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
            priceApplicableRoom.setProjectId(projectId);
            priceApplicableRoom.setHotelId(hotel.getHotelId());
            priceApplicableRoom.setRoomLevelNo(1);
            List<PriceApplicableRoomInfoResponse> priceApplicableRoomList = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);
            for(PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse : priceApplicableRoomList){
                RoomInfoResponseDto roomInfoResponseDto = roomInfoResponseDtoMap.get(priceApplicableRoomInfoResponse.getRoomTypeId());
                if(roomInfoResponseDto != null && StringUtils.isNotEmpty(roomInfoResponseDto.getRoomAreaSize())){
                    try {
                        BigDecimal roomSize = HotelRoomUtil.matchRoomSize(roomInfoResponseDto.getRoomAreaSize());
                            if (minRoomAreaSize == null || roomSize != null && roomSize.compareTo(BigDecimal.ZERO) >= 0) {
                                minRoomAreaSize = roomSize;
                            }
                        } catch(Exception ex){
                            logger.error("获取酒店面积失败" + hotel.getHotelId() + "_" + roomInfoResponseDto.getRoomAreaSize(), ex);
                        }
                    }
            }
            if(minRoomAreaSize != null){
                List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
                Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));

                String weightScoreStandardEnumName = "";
                WeightScoreStandardVO weightScoreStandardVO = null;
                if(minRoomAreaSize.compareTo(new BigDecimal("35")) >= 0){
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_MORE_THAN_35.name();
                } else if(minRoomAreaSize.compareTo(new BigDecimal("30")) >= 0){
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_30_TO_35.name();
                } else if(minRoomAreaSize.compareTo(new BigDecimal("25")) >= 0){
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_25_TO_30.name();
                } else if( minRoomAreaSize.compareTo(new BigDecimal("20")) >= 0){
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_20_TO_25.name();
                } else if(minRoomAreaSize.compareTo(new BigDecimal("15")) >= 0){
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_15_TO_20.name();
                } else {
                    weightScoreStandardEnumName = WeightScoreStandardEnum.ROOM_LESS_15.name();
                }
                if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
                    weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
                    result.add(weightScoreStandardVO);
                }
                logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

                return result;
            }
        }
        return result;
    }

    /**
     *  履约服务分：根据去年关联项目酒店服务分进行计算分值
     */
    private List<WeightScoreStandardVO> calculateLastYearServicePointScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, Project project){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();

        // 是否关联去年项目
        Long lastYearProjectId = project.getRelatedProjectId();
        if(lastYearProjectId == null){
           return result;
        }
        // 查询去年报价
        ProjectIntentHotel lastYearProjectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(project.getProjectId(), hotel.getHotelId());
        if(lastYearProjectIntentHotel == null || lastYearProjectIntentHotel.getHotelServicePoints() == null || !Objects.equals(lastYearProjectIntentHotel.getBidState(), HotelBidStateEnum.BID_WINNING.bidState)){
            logger.info("lastYearProjectIntentHotel {}", lastYearProjectIntentHotel);
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        if(lastYearProjectIntentHotel.getHotelServicePoints().compareTo(new BigDecimal("80")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.LAST_YEAR_SERVICE_POINT_MORE_THAN_80.name();
        } else if(lastYearProjectIntentHotel.getHotelServicePoints().compareTo(new BigDecimal("60")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.LAST_YEAR_SERVICE_60_TO_80.name();
        } else if(lastYearProjectIntentHotel.getHotelServicePoints().compareTo(new BigDecimal("40")) >= 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.LAST_YEAR_SERVICE_40_TO_60.name();
        } else if( lastYearProjectIntentHotel.getHotelServicePoints().compareTo(new BigDecimal("40")) < 0){
            weightScoreStandardEnumName = WeightScoreStandardEnum.LAST_YEAR_SERVICE_LESS_THAN_40.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}", hotel.getHotelId(), weightScoreStandardEnumName, weightScoreStandardVO);

        return result;

    }

    /**
     *  酒店历史一年预定量
     */
    private List<WeightScoreStandardVO> calculateLastYearBookieScore(HotelResponse hotel, ProjectWeightVO projectWeightVO, ProjectIntentHotel projectIntentHotel){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();

        // 是否存在去年预定量
        Integer latestYearRoomNight =  projectHotelHistoryDataDao.queryProjectHotelHistoryRoomNightCount(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        if(latestYearRoomNight == null){
            return result;
        }
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        String weightScoreStandardEnumName = "";
        WeightScoreStandardVO weightScoreStandardVO = null;
        // 预订量 ≥ 10000间夜
        if(latestYearRoomNight >= 10000){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_MORE_THAN_10000.name();
        } else if(latestYearRoomNight >= 8000){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_8000_TO_10000.name();
        } else if(latestYearRoomNight >= 5000){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_5000_TO_8000.name();
        } else if(latestYearRoomNight >= 3000){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_3000_TO_5000.name();
        } else if(latestYearRoomNight >= 1000){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_1000_TO_3000.name();
        } else if(latestYearRoomNight >= 800){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_800_TO_1000.name();
        } else if(latestYearRoomNight >= 500){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_500_TO_800.name();
        } else if(latestYearRoomNight >= 300){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_300_TO_500.name();
        } else if(latestYearRoomNight >= 200){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_200_TO_300.name();
        } else if(latestYearRoomNight >= 100){
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_100_TO_200.name();
        } else {
            weightScoreStandardEnumName = WeightScoreStandardEnum.BOOKIE_LESS_100.name();
        }
        if(StringUtils.isNotEmpty(weightScoreStandardEnumName)){
            weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
            result.add(weightScoreStandardVO);
        }
        logger.info(projectWeightVO.getWeightType() +": {}, {}, {}, {}", hotel.getHotelId(),  latestYearRoomNight, weightScoreStandardEnumName, weightScoreStandardVO);

        return result;

    }


    /**
     * 是否支持
     */
    private List<WeightScoreStandardVO> calculateSupportScore(String supportWeightScoreStandardEnumName, String unSupportWeightScoreStandardEnumName, ProjectWeightVO projectWeightVO, Integer isSupport){
        // 定义返回值
        List<WeightScoreStandardVO> result = new ArrayList<>();
        // 是否支持
        String weightScoreStandardEnumName = Objects.equals(isSupport, RfpConstant.constant_1) ? supportWeightScoreStandardEnumName : unSupportWeightScoreStandardEnumName;
        List<WeightScoreStandardVO> weightScoreStandardVOList = JsonUtil.jsonToList(projectWeightVO.getScoreStandards(), WeightScoreStandardVO.class);
        Map<String, WeightScoreStandardVO> weightScoreStandardMap = weightScoreStandardVOList.stream().collect(Collectors.toMap(WeightScoreStandardVO::getCode, weightScoreStandardVO -> weightScoreStandardVO));
        WeightScoreStandardVO weightScoreStandardVO = weightScoreStandardMap.get(weightScoreStandardEnumName);
        result.add(weightScoreStandardVO);

        return result;

    }

    /**
     * 计算自定义策略权重
     */
    public List<HotelCustomBidWeightDetailVO> calculateCustomStageWeight(List<ProjectCustomBidStrategy> projectHotelCustomBidStrategies, List<QueryCustomTendStrategyResponse> projectCustomTendStrategyResponses){
        //自定义采购策略权重
        List<HotelCustomBidWeightDetailVO> hotelCustomBidWeightDetailVOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(projectCustomTendStrategyResponses) && CollectionUtils.isNotEmpty(projectHotelCustomBidStrategies)){
            Map<Long,QueryCustomTendStrategyResponse> queryCustomTendStrategyResponseMap = new HashMap<>();
            for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : projectCustomTendStrategyResponses) {
                queryCustomTendStrategyResponseMap.put(queryCustomTendStrategyResponse.getCustomTendStrategyId(),queryCustomTendStrategyResponse);
            }
            for (ProjectCustomBidStrategy projectHotelCustomBidStrategy : projectHotelCustomBidStrategies) {
                // 文本录入不记录权重分
                if(projectHotelCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                    continue;
                }
                Long customTendStrategyId = projectHotelCustomBidStrategy.getCustomTendStrategyId();
                QueryCustomTendStrategyResponse queryCustomTendStrategyResponse = queryCustomTendStrategyResponseMap.get(customTendStrategyId);
                if(queryCustomTendStrategyResponse == null){
                    continue;
                }
                // 没有启用权重
                if(!Objects.equals(queryCustomTendStrategyResponse.getWhtStrategyNameState(),RfpConstant.constant_1)){
                    continue;
                }

                // 计算自定义权重
                HotelCustomBidWeightDetailVO hotelCustomBidWeightDetailVO = new HotelCustomBidWeightDetailVO();
                hotelCustomBidWeightDetailVO.setStrategyId(projectHotelCustomBidStrategy.getCustomTendStrategyId());
                hotelCustomBidWeightDetailVO.setStrategyName(queryCustomTendStrategyResponse.getStrategyName());
                hotelCustomBidWeightDetailVO.setStrategyType(projectHotelCustomBidStrategy.getStrategyType());
                hotelCustomBidWeightDetailVO.setOptionNameList(new LinkedList<>());

                // 是否权重
                if(projectHotelCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key){
                    if(queryCustomTendStrategyResponse.getWhtStrategyName() != null && Objects.equals(projectHotelCustomBidStrategy.getSupportStrategyName(), RfpConstant.constant_1)){
                        hotelCustomBidWeightDetailVO.setScore(queryCustomTendStrategyResponse.getWhtStrategyName());
                        hotelCustomBidWeightDetailVO.getOptionNameList().add("是");
                    }
                }
                // 单选或者多选权重
                if(projectHotelCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.CHECKBOX.key || projectHotelCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.RADIO.key){
                    BigDecimal score = null;
                    for(CustomStrategyOptionVO customStrategyOptionVO : queryCustomTendStrategyResponse.getOptions()){
                        if(customStrategyOptionVO.getWeightScore() == null){
                            continue;
                        }
                        for(ProjectCustomBidStrategyOption projectCustomBidStrategyOption : projectHotelCustomBidStrategy.getOptions()){
                            if(Objects.equals(projectCustomBidStrategyOption.getOptionId(), customStrategyOptionVO.getOptionId()) &&
                                    Objects.equals(projectCustomBidStrategyOption.getIsSupport(), RfpConstant.constant_1)
                            ){
                                hotelCustomBidWeightDetailVO.getOptionNameList().add(customStrategyOptionVO.getOptionName());
                                if (score == null){
                                    score = customStrategyOptionVO.getWeightScore();
                                } else {
                                    score = score.add(customStrategyOptionVO.getWeightScore());
                                }
                                hotelCustomBidWeightDetailVO.setScore(score);
                                logger.info(" 单选或者多选权重 ：" + customStrategyOptionVO.getOptionName() + " OptionId " + customStrategyOptionVO.getOptionId() + ":" +
                                        customStrategyOptionVO.getWeightScore() + " score " + score);
                            }
                        }
                    }
                }
                if(CollectionUtils.isNotEmpty(hotelCustomBidWeightDetailVO.getOptionNameList()) && hotelCustomBidWeightDetailVO.getScore() != null) {
                    hotelCustomBidWeightDetailVOList.add(hotelCustomBidWeightDetailVO);
                } else {
                    logger.info("无效的 hotelCustomBidWeightDetailVO: {}", JsonUtil.objectToJson(hotelCustomBidWeightDetailVO));
                }
            }
        }
        return hotelCustomBidWeightDetailVOList;
    }


    // 设置报价权重信息
    public void setBidWeightInfo(QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse, Long projectId, Long hotelId){
        // 查询项目权重雷达
        List<ProjectCategoryWeightVO> projectCategoryWeightVOList = cachedProjectManager.queryProjectCategoryWeightList(projectId);
        Map<String, ProjectCategoryWeightVO> orgProjectCategoryWeightVOMap = projectCategoryWeightVOList.stream().collect(Collectors.toMap(ProjectCategoryWeightVO::getCategoryCode, projectCategoryWeightVO -> projectCategoryWeightVO));

        // 查询酒店报价权重雷达值
        List<ProjectHotelWeight> projectHotelWeightList = projectHotelWeightDao.queryByProjectHotelId(projectId, hotelId);
        Map<String, List<ProjectHotelWeight>> projectHotelWeightMap = projectHotelWeightList.stream().collect(Collectors.groupingBy(ProjectHotelWeight::getCategoryCode));
        List<HotelBidWeightVO> hotelBidWeightList = new LinkedList<>();
        // 项目权重设置
        List<String> projectCategoryCodeList = projectCategoryWeightVOList.stream().map(ProjectCategoryWeightVO::getCategoryCode).collect(Collectors.toList());
        Map<String, ProjectCategoryWeightVO> projectCategoryWeightVOMap = new LinkedHashMap<>();
        Map<String, BigDecimal> projectCategoryTotalWeight = new LinkedHashMap<>();
        for (String categoryCode : projectCategoryCodeList) {
            if (categoryCode.equals(WeightCategoryCodeEnum.CUSTOM_STRATEGY.name())) {
                continue;
            }
            BigDecimal sumWeight = null;
            String categoryName = WeightCategoryCodeEnum.getByName(categoryCode).getDesc();
            HotelBidWeightVO hotelBidWeightVO = new HotelBidWeightVO();
            hotelBidWeightVO.setCategoryCode(categoryCode);
            hotelBidWeightVO.setCategoryName(categoryName);
            hotelBidWeightVO.setDetailList(new LinkedList<>());
            List<ProjectHotelWeight> categoryWeightList = projectHotelWeightMap.get(categoryCode);
            if (CollectionUtils.isNotEmpty(categoryWeightList)) {
                for (ProjectHotelWeight projectHotelWeight : categoryWeightList) {
                    List<WeightScoreStandardVO> standardList = JsonUtil.jsonToList(projectHotelWeight.getScoreStandards(), WeightScoreStandardVO.class);
                    if(CollectionUtils.isEmpty(standardList)){
                        continue;
                    }
                    if(sumWeight == null) {
                        sumWeight = BigDecimal.ZERO;
                    }
                    sumWeight = sumWeight.add(projectHotelWeight.getScore());
                    String weightTypeName = WeightTypeEnum.getValueByName(projectHotelWeight.getWeightType());

                    if (CollectionUtils.isNotEmpty(standardList)) {
                        for (WeightScoreStandardVO standardVO : standardList) {
                            HotelBidWeightDetailVO hotelBidWeightDetailVO = new HotelBidWeightDetailVO();
                            hotelBidWeightDetailVO.setWeightType(projectHotelWeight.getWeightType());
                            hotelBidWeightDetailVO.setWeightTypeName(weightTypeName);
                            hotelBidWeightDetailVO.setDesc(standardVO.getDesc());
                            hotelBidWeightDetailVO.setValue(standardVO.getValue());
                            hotelBidWeightDetailVO.setCode(standardVO.getCode());
                            hotelBidWeightVO.getDetailList().add(hotelBidWeightDetailVO);
                        }
                    }
                }
            }
            hotelBidWeightVO.setWeight(sumWeight);
            if(sumWeight != null){
                projectCategoryTotalWeight.put(categoryCode, sumWeight);
                projectCategoryWeightVOMap.put(categoryCode, orgProjectCategoryWeightVOMap.get(categoryCode));
                hotelBidWeightList.add(hotelBidWeightVO);
            }
        }
        // 自定义权重值
        List<ProjectCustomBidStrategy> projectHotelCustomBidStrategies = projectHotelBidStrategyService.queryProjectCustomBidStrategyList(projectId, hotelId);
        if (CollectionUtils.isNotEmpty(projectHotelCustomBidStrategies)) {
            List<QueryCustomTendStrategyResponse> projectCustomTendStrategyResponses = projectService.queryProjectCustomTendStrategy(projectId);
            List<HotelCustomBidWeightDetailVO> customBidWeightDetailVOS = this.calculateCustomStageWeight(projectHotelCustomBidStrategies, projectCustomTendStrategyResponses);
            BigDecimal customStageWeight = customBidWeightDetailVOS.stream().map(HotelCustomBidWeightDetailVO::getScore).reduce(BigDecimal.ZERO, BigDecimal::add);
            queryProjectHotelPriceDetailResponse.setCustomBidWeightDetailList(customBidWeightDetailVOS);
            projectCategoryTotalWeight.put(WeightCategoryCodeEnum.CUSTOM_STRATEGY.name(), customStageWeight);
            projectCategoryWeightVOMap.put(WeightCategoryCodeEnum.CUSTOM_STRATEGY.name(), orgProjectCategoryWeightVOMap.get(WeightCategoryCodeEnum.CUSTOM_STRATEGY.name()));
        }

        // 设置分类和分值
        queryProjectHotelPriceDetailResponse.setProjectCategoryWeightList(new LinkedList<>(projectCategoryWeightVOMap.values()));
        queryProjectHotelPriceDetailResponse.setWeightList(new LinkedList<>(projectCategoryTotalWeight.values()));

        // 设置报价项目权重明显
        queryProjectHotelPriceDetailResponse.setHotelBidWeightList(hotelBidWeightList);
    }

    @Async
    public void asyncGenerateProjectHotelWeight(Long projectId) {
        // 重新计算已经生成的权重分
            try{
                Thread.sleep(6000L);
            } catch (Exception ex){
                logger.error("项目权重生成失败：" + ex.getMessage());
            }
            List<ProjectIntentHotel> projectIntentHotelList = projectIntentHotelDao.selectByProjectId(projectId);
            for (ProjectIntentHotel projectIntentHotel : projectIntentHotelList){
                try {
                    String key = RedisConstant.PROJECT_HOTEL_WEIGHT_GENERATING;
                    RedisService.lpush(RedisConstant.PROJECT_HOTEL_WEIGHT_GENERATING,projectId + "_" + projectIntentHotel.getHotelId());
                } catch (Exception ex){
                    logger.error("项目权重生成失败：" + ex.getMessage());
                }
            }

    }

}
