package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.MonitorCallTmchubErrorDao;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.QueryHotelViolationMonitorRequest;
import com.fangcang.rfp.common.dto.request.QueryMonitorCallTmchubErrorListRequest;
import com.fangcang.rfp.common.dto.response.ExportMonitorCallTmchubErrorResponse;
import com.fangcang.rfp.common.dto.response.MonitorCallTmchubErrorDto;
import com.fangcang.rfp.common.dto.response.QueryHotelViolationMonitorResponse;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.MonitorCallTmchubErrorService;
import com.fangcang.rfp.common.util.ExcelHelper;
import com.fangcang.rfp.common.util.PageUtil;
import com.fangcang.util.StringUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 履约监控异常服务
 */
@Service
public class MonitorCallTmchubErrorServiceImpl implements MonitorCallTmchubErrorService {

    private static final Logger logger = LoggerFactory.getLogger(MonitorCallTmchubErrorService.class);

    @Autowired
    private MonitorCallTmchubErrorDao monitorCallTmchubErrorDao;

    // 查询履约异常数据
    @Override
    public Response queryMonitorCallTmchubError(QueryMonitorCallTmchubErrorListRequest queryMonitorCallTmchubErrorListRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        // 默认查询最近7天
        if (StringUtil.isEmpty(queryMonitorCallTmchubErrorListRequest.getMonitorDateFrom())) {
            queryMonitorCallTmchubErrorListRequest.setMonitorDateFrom(DateUtil.formatDate(DateUtil.offsetDay(new Date(), -7)));
        }

        // 分页查询
        if (queryMonitorCallTmchubErrorListRequest.isPage()) {
            PageHelper.startPage(queryMonitorCallTmchubErrorListRequest.getCurrentPage(), queryMonitorCallTmchubErrorListRequest.getPageSize());
        }

        // 分页查询数据
        List<MonitorCallTmchubErrorDto>  monitorCallTmchubErrorDtoList = monitorCallTmchubErrorDao.queryList(queryMonitorCallTmchubErrorListRequest);
        response.setData(PageUtil.makePageResult(monitorCallTmchubErrorDtoList));

        return response;
    }


    // 导出履约异常数据
    @Override
    public void exportMonitorCallTmchubError(QueryMonitorCallTmchubErrorListRequest queryMonitorCallTmchubErrorListRequest, HttpServletRequest request, HttpServletResponse httpServletResponse) throws Exception {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("序号");
            headerRow.createCell(1).setCellValue("机构名称");
            headerRow.createCell(2).setCellValue("酒店名称");
            headerRow.createCell(3).setCellValue("酒店ID");
            headerRow.createCell(4).setCellValue("房型名称");
            headerRow.createCell(5).setCellValue("房型ID");
            headerRow.createCell(6).setCellValue("监控日期");
            headerRow.createCell(7).setCellValue("异常类型");
            headerRow.createCell(8).setCellValue("异常原因");
            headerRow.createCell(9).setCellValue("监控时间");

            // 默认查询最近7天
            if (StringUtil.isEmpty(queryMonitorCallTmchubErrorListRequest.getMonitorDateFrom())) {
                queryMonitorCallTmchubErrorListRequest.setMonitorDateFrom(DateUtil.formatDate(DateUtil.offsetDay(new Date(), -7)));
            }
            List<MonitorCallTmchubErrorDto> monitorCallTmchubErrorDtoList = monitorCallTmchubErrorDao.queryList(queryMonitorCallTmchubErrorListRequest);
            List<ExportMonitorCallTmchubErrorResponse> responsesDataList = monitorCallTmchubErrorDtoList.stream().map(this::convertToExportResponse).collect(Collectors.toList());
            int i=0;
            for (ExportMonitorCallTmchubErrorResponse monitorCallTmchubErrorResponse : responsesDataList) {
                i++;
                Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
                dataRow.createCell(0).setCellValue(i);
                dataRow.createCell(1).setCellValue(monitorCallTmchubErrorResponse.getOrgName());
                dataRow.createCell(2).setCellValue(monitorCallTmchubErrorResponse.getHotelName());
                dataRow.createCell(3).setCellValue(monitorCallTmchubErrorResponse.getHotelId());
                dataRow.createCell(4).setCellValue(monitorCallTmchubErrorResponse.getRoomType());
                dataRow.createCell(5).setCellValue(monitorCallTmchubErrorResponse.getRoomTypeId());
                dataRow.createCell(6).setCellValue(monitorCallTmchubErrorResponse.getMonitorDate());
                dataRow.createCell(7).setCellValue(monitorCallTmchubErrorResponse.getMonitorErrorType());
                dataRow.createCell(8).setCellValue(monitorCallTmchubErrorResponse.getErrorReason());
                dataRow.createCell(9).setCellValue(monitorCallTmchubErrorResponse.getMonitorTime());
               }
            String fileName = "履约违规监控异常信息" + com.fangcang.util.DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("导出履约违规监控异常：" + JSON.toJSONString(queryMonitorCallTmchubErrorListRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }

    }

    /**
     *
     * @return
     */
    private ExportMonitorCallTmchubErrorResponse convertToExportResponse(MonitorCallTmchubErrorDto dto){
        ExportMonitorCallTmchubErrorResponse response = new ExportMonitorCallTmchubErrorResponse();
        response.setOrgName(dto.getOrgName());
        response.setHotelName(dto.getHotelName());
        response.setHotelId(dto.getHotelId());
        response.setRoomType(dto.getRoomType());
        response.setRoomTypeId(dto.getRoomTypeId());
        response.setMonitorDate(DateUtil.formatDate(dto.getMonitorDate()));
        response.setMonitorErrorType(MonitorErrorTypeEnum.getValueByKey(dto.getMonitorErrorType()));
        response.setErrorReason(dto.getErrorReason());
        response.setMonitorTime(DateUtil.formatDateTime(dto.getMonitorTime()));
        return response;
    }


}
