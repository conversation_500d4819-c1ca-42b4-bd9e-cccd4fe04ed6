package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.ImportBidTaskDto;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

public interface ImportBidTaskService {

    Response addImportBidTaskRequest(UserDTO userDTO, AddImportBidTaskRequest addImportBidTaskRequest) throws Exception;

    Response queryList(ImportBidTaskRequest importBidTaskRequest);

    Response delete(UserDTO userDTO, DeleteImportBidTask deleteImportBidTask);

    Response getWithDetail(Long importBidTaskId);

    Response getImportBidTaskBidInfo(Long importBidTaskId);

    Response update(UserDTO userDTO, ImportBidTaskDto importBidTaskDto, Boolean isGeneratedBid);

    Response generateBid(UserDTO userDTO, GenerateBidByTask generateBidByTask);

    InputStream exportImportBidTask(UserDTO userDTO, ImportBidTaskRequest importBidTaskRequest, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 下载报价任务模板
     */
    void downloadBidTaskTemplate(HttpServletResponse httpServletResponse);

    /**
     * 批量新增报价任务报价
     * @param projectImportHotelDataMap
     * @param userDTO
     * @return
     */
    Response batchInsertBidData(Map<Long, ImportBidTaskDto> importBidTaskDtoMap, Map<Long, Map<Long, ImportLanyonBidDto>> projectImportHotelDataMap, UserDTO userDTO);

    /**
     * 检查报价任务
     * @param importBidTaskList
     * @param projectImportHotelDataMap
     * @param userDTO
     * @return
     */
    List<String> validateBidTask(boolean isGroupUpload, List<ImportBidTaskDto> importBidTaskList, Map<Long, Map<Long, ImportLanyonBidDto>> projectImportHotelDataMap, UserDTO userDTO);


    /**
     * 校验标准报价
     */
    Response validateUploadStandBidData(MultipartFile uploadFile, Long projectId, UserDTO userDTO) throws Exception;

    public Response batchValidateImportBidTask(UserDTO userDTO, List<Long> bidImportTaskIdList);
}
