package com.fangcang.rfp.common.dto.request;

import com.fangcang.rfp.common.dto.ProjectWeightVO;
import com.fangcang.rfp.common.dto.common.PageQuery;
import com.fangcang.rfp.common.entity.ProjectCustomTendStrategy;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/30 18:27
 */
public class QueryProjectWeightGroupRequest extends PageQuery {

    // 项目ID
    private Long projectId;

    //分组类型 1,2,3,4,5,6,其他,7 酒店
    private Integer groupType;

    private Long groupId;

    private Long brandId;

    public Long getProjectId() {
        return projectId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }
}
