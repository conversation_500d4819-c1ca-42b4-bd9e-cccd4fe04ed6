package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ContractPreviewRequest;
import com.fangcang.rfp.common.dto.request.GenerateContractRequest;

/**
 * <AUTHOR>
 * @ClassName ContractGenerateService
 * @Description 合同生成服务Service
 * @createTime 2022-10-31 21:15:00
 */
public interface ContractGenerateService {
    /**
     * 添加项目酒店(标书)到待生成合同队列
     * @param generateContractRequest
     * @return
     */
    Response addContractForGenerate(GenerateContractRequest generateContractRequest) throws Exception;

    /**
     * 确认中标后，生成合同预览
     * @param contractPreviewRequest
     * @return
     * @throws Exception
     */
    Response generateContractPreview(ContractPreviewRequest contractPreviewRequest) throws Exception;

    /**
     * 确认生成合同
     * @param contractPreviewRequest
     * @return
     */
    Response confirmGenerateContract(ContractPreviewRequest contractPreviewRequest) throws Exception;

    /**
     * 生成酒店报价合同
     * @param generateContractRequest
     * @return
     */
    Response generateHotelContract(GenerateContractRequest generateContractRequest) throws Exception;

    /**
     * 生成酒店公付合同
     * @param generateContractRequest
     * @return
     */
    Response generateHotelCoPayContract(GenerateContractRequest generateContractRequest) throws Exception;
}
