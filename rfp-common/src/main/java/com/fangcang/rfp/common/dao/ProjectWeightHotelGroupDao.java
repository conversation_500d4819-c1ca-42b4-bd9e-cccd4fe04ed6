package com.fangcang.rfp.common.dao;

import com.fangcang.rfp.common.entity.ProjectWeightHotelGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProjectWeightHotelGroupDao {

    List<ProjectWeightHotelGroup> query(@Param("projectId") Long projectId, @Param("groupType") Integer groupType, @Param("groupId") Long groupId, @Param("brandId") Long brandId);

    List<ProjectWeightHotelGroup> queryByRefId(@Param("projectId") Long projectId, @Param("groupType") Integer groupType, @Param("referenceId") Long referenceId);

    int delete(@Param("projectWeightHotelGroupId") Long projectWeightHotelGroupId);

    int insert(@Param("projectWeightHotelGroup")ProjectWeightHotelGroup projectWeightHotelGroup);

}
