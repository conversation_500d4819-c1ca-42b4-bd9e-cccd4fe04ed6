package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.enums.HotelStarEnum;
import com.fangcang.hotel.base.api.dto.query.HotelSummaryInfoQueryDto;
import com.fangcang.hotel.base.api.dto.query.RoomSummaryInfoQuery;
import com.fangcang.hotel.base.api.dto.response.HotelSummaryInfoResponseDto;
import com.fangcang.hotel.base.api.dto.response.RoomInfoResponseDto;
import com.fangcang.hotel.base.api.facade.HotelInfoFacade;
import com.fangcang.hotel.base.api.facade.RoomInfoFacade;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.HotelCertVO;
import com.fangcang.rfp.common.dto.common.OrgDTO;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.BidUtil;
import com.fangcang.rfp.common.util.LocationUtil;
import com.fangcang.rfp.common.util.PageUtil;
import com.fangcang.rfp.common.util.StringUtils;
import com.fangcang.util.DateUtil;
import com.fangcang.util.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @auther cjh
 * @description
 * @date 2022/10/8
 */
@Service
public class ProjectIntentHotelServiceImpl implements ProjectIntentHotelService {

    private static Logger logger = LoggerFactory.getLogger(ProjectIntentHotelServiceImpl.class);

    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private HotelInfoFacade hotelInfoFacade;

    @Autowired
    private RoomInfoFacade roomInfoFacade;

    @Autowired
    private ProjectHotelBidStrategyDao projectHotelBidStrategyDao;

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;
    
    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;

    @Autowired
    private PriceUnapplicableDayDao priceUnapplicableDayDao;

    @Autowired
    private OrgService orgService;

    @Autowired
    private OrgSubjectService orgSubjectService;

    @Autowired
    private ProjectCustomBidStrategyDao projectCustomBidStrategyDao;

    @Autowired
    private DisHotelDailyOrderDao disHotelDailyOrderDao;

    @Autowired
    private HotelViolationsMonitorDao hotelViolationsMonitorDao;

    @Autowired
    private PriceApplicableDayDao priceApplicableDayDao;

    @Autowired
    private HotelDao hotelDao;

    @Autowired
    private OrgSubjectDao orgSubjectDao;

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;

    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;

    @Autowired
    private ProjectIntentHotelGroupDao projectIntentHotelGroupDao;

    @Autowired
    private OrgHotelGroupBrandDao orgHotelGroupBrandDao;

    @Autowired
    private ProjectHotelBidStrategyService projectHotelBidStrategyService;

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;

    @Autowired
    private HotelRelatedCertService hotelRelatedCertService;
    @Autowired
    private ProjectHotelWeightDao projectHotelWeightDao;

    @Override
    public Response selectProjectHotelTentList(ProjectIntentHotelRequest projectIntentHotelRequest) {

        Response response = new Response();

        dealLastYear(projectIntentHotelRequest);

        List<ProjectHotelTentResponse> projectHotelTentRespons;
        int totalCount = 0;
        int totalPage = 0;
        if(projectIntentHotelRequest.getOrgType() != null && projectIntentHotelRequest.getOrgType().intValue() == OrgTypeEnum.HOTEL.key.intValue()){
            // 根据酒店机构id查询机构下所有酒店项目信息
            // 酒店机构查询报价项目数据量少，逻辑关联复杂所以使用内存分页
            projectHotelTentRespons = this.selectHotelOrgTentList(projectIntentHotelRequest);
            if(projectIntentHotelRequest.getProjectState() != null && projectIntentHotelRequest.getBidState() == HotelBidStateEnum.NO_BID.bidState){
                projectHotelTentRespons = projectHotelTentRespons.stream().filter(o -> o.getProjectState() != ProjectStateEnum.BIDDING_COMPLETED.key && o.getProjectState() != ProjectStateEnum.BID_ABANDONED.key).collect(Collectors.toList());
            }
            totalCount = projectHotelTentRespons.size();
            totalPage = cn.hutool.core.util.PageUtil.totalPage(totalCount, projectIntentHotelRequest.getPageSize());
            projectHotelTentRespons = ListUtil.page(projectIntentHotelRequest.getCurrentPage()-1, projectIntentHotelRequest.getPageSize(), projectHotelTentRespons);
        } else {
            PageHelper.startPage(projectIntentHotelRequest.getCurrentPage(), projectIntentHotelRequest.getPageSize());
            projectHotelTentRespons = projectIntentHotelDao.selectHotelTentList(projectIntentHotelRequest);
        }

        // 查询项目意向酒店ID
        for (ProjectHotelTentResponse projectHotelTentRespon : projectHotelTentRespons) {
            projectIntentHotelRequest.setProjectId(projectHotelTentRespon.getProjectId());
            if(projectIntentHotelRequest.getOrgType() != null && projectIntentHotelRequest.getOrgType().intValue() == OrgTypeEnum.HOTEL.key.intValue()) {
                projectIntentHotelRequest.setLoopFilterHotelId(projectHotelTentRespon.getHotelId());
            }
            ConfirmAgreeProtocolResponse confirmAgreeProtocolResponse = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
            if (confirmAgreeProtocolResponse != null && confirmAgreeProtocolResponse.getProjectIntentHotelId() != null) {
                projectHotelTentRespon.setProjectIntentHotelId(confirmAgreeProtocolResponse.getProjectIntentHotelId());
            }
        }

        // 查询招标信息
        projectIntentHotelRequest.setLoopFilterHotelId(null);
        for (ProjectHotelTentResponse projectIntentHotelRespons : projectHotelTentRespons) {
            projectIntentHotelRequest.setProjectId(projectIntentHotelRespons.getProjectId());
            if(projectIntentHotelRequest.getOrgType() != null && projectIntentHotelRequest.getOrgType().intValue() == OrgTypeEnum.HOTEL.key.intValue()) {
                projectIntentHotelRequest.setLoopFilterHotelId(projectIntentHotelRespons.getHotelId());
            }
            ProjectHotelTentResponse projectHotelTentResponse = projectIntentHotelDao.selectHotelTentOtherInfo(projectIntentHotelRequest);
            if (projectHotelTentResponse != null) {
                projectIntentHotelRespons.setHotelContactName(projectHotelTentResponse.getHotelContactName());
                projectIntentHotelRespons.setBasePrice(projectHotelTentResponse.getBasePrice());
                projectIntentHotelRespons.setSupportIncludeCommission(projectHotelTentResponse.getSupportIncludeCommission());
                projectIntentHotelRespons.setTendCommission(projectHotelTentResponse.getTendCommission());
                projectIntentHotelRespons.setProvideInvoiceType(projectHotelTentResponse.getProvideInvoiceType());
                projectIntentHotelRespons.setProvideInvoiceTaxRate(projectHotelTentResponse.getProvideInvoiceTaxRate());
            }
        }

        dealTenderTimeAndPublishTime(projectHotelTentRespons);

        PageResult pageResult;
        if (projectHotelTentRespons instanceof Page) {
            pageResult = PageUtil.makePageResult(projectHotelTentRespons);
        } else {
            pageResult = new PageResult();
            pageResult.setList(projectHotelTentRespons);
            pageResult.setCurrentPage(projectIntentHotelRequest.getCurrentPage());
            pageResult.setTotalPage(totalPage);
            pageResult.setPageSize(projectIntentHotelRequest.getPageSize());
            pageResult.setTotalCount(totalCount);
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response isHotelSatisfyProjectBaseInfo(IntentHotelRequest intentHotelRequest) {
        /**
         1.  酒店集团/酒店报价都会调用 isHotelSatisfyProjectBaseInfo 接口
         2.   如果酒店是邀约酒店或者项目是邀约项目，检查通过，否则检查项目基础信息是否符合项目要求。
         3.   isHotelSatisfyProjectBaseInfo 返回成功
         如果项目是在线合同的获取合同确认页面，否则进入确认报价。
         4.   isHotelSatisfyProjectBaseInfo 返回失败，提示失败信息。
         */
        Response response = new Response();
        Map<Long, List<Long>> projectInviteHotelMap = new HashMap<>();
        for (String projectHotelIdKey : intentHotelRequest.getProjectIntentHotelParamList()) {
            // ProjectID
            Long aLong = Long.valueOf(projectHotelIdKey.substring(0, projectHotelIdKey.indexOf("_")));
            Long hotelId = Long.valueOf(projectHotelIdKey.substring(projectHotelIdKey.indexOf("_")+1));
            Response projectResponse = projectService.queryProjectHotelTendStrategy(aLong);
            ProjectHotelTendStrategy projectHotelTendStrategy = null;
            if (projectResponse.getData() != null) {
                 projectHotelTendStrategy = (ProjectHotelTendStrategy) projectResponse.getData();
            }

            // 判断时间是否在项目时间内（仅判断公共项目）
            Response basicInformationResponse = projectService.queryProjectBasicInformation(aLong);
            Project projectBasicInformation = (Project) basicInformationResponse.getData();
            if (projectBasicInformation.getTenderType().intValue() == 1) {
                Date enrollEndTime = projectBasicInformation.getEnrollEndTime();
                Date enrollEndDate = DateUtil.dateFormat(enrollEndTime, "yyyy-MM-dd");

                Date date = new Date();
                Date nowDate = DateUtil.dateFormat(date, "yyyy-MM-dd");
                if (nowDate.compareTo(enrollEndDate) > 0) {
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg("报名时间已过，不可报价");
                    return response;
                }
            }

            //校验是否需要在线合同，需要在线合同时需要校验机构销售人信息（姓名、手机、邮箱）、财务结算人信息（姓名、手机、邮箱、地址）、
            // 签约主体校验默认签约主体（签约主体名称、统一社会信用代码、收款银行名称、收款账户名、收款账户号、营业执照）必须齐全
            if(projectBasicInformation.getNeedOnlineContracts() != null
                    && projectBasicInformation.getNeedOnlineContracts() == NeedOnlineContractsEnum.NEED_ONLINE_CONTRACTS.key){
                //查询机构信息
                Org orgByHotelId = orgService.selectByPrimaryKey(intentHotelRequest.getSupplyOrgId());
                if(orgByHotelId == null){
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg("机构信息不存在，不可报价");
                    return response;
                }else{
                    List<String> resultList = new ArrayList<>();
                    if(!StringUtil.isValidString(orgByHotelId.getContactName())){
                        resultList.add("销售人姓名为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getContactMobile())){
                        resultList.add("销售人手机号为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getContactEmail())){
                        resultList.add("销售人邮箱为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getBalanceContactName())){
                        resultList.add("结算人姓名为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getBalanceContactMobile())){
                        resultList.add("结算人手机号为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getBalanceContactEmail())){
                        resultList.add("结算人邮箱为空");
                    }
                    if(!StringUtil.isValidString(orgByHotelId.getAddressDetail())){
                        resultList.add("结算人地址为空");
                    }
                    List<Long> orgIds = new ArrayList<>();
                    orgIds.add(orgByHotelId.getOrgId());
                    //查询默认签约主体
                    Response queryOrgSubjectResponse = orgSubjectService.selectDefaultSubjectByOrgId(orgIds);
                    if(queryOrgSubjectResponse == null || ReturnResultEnum.SUCCESS.errorNo.intValue() != queryOrgSubjectResponse.getResult().intValue()){
                        response.setResult(ReturnResultEnum.FAILED.errorNo);
                        response.setMsg("查询机构签约信息异常");
                        return response;
                    }
                    if(queryOrgSubjectResponse.getData() == null){
                        resultList.add("签约主体信息为空");
                    }else{
                        List<OrgSubject> orgSubjectList = (List<OrgSubject>) (queryOrgSubjectResponse.getData());
                        if(orgSubjectList == null || orgSubjectList.size()<=0){
                            resultList.add("签约主体信息为空");
                        }else{
                            //默认签约主体只有一个
                            OrgSubject orgSubject = orgSubjectList.get(0);
                            if(!StringUtil.isValidString(orgSubject.getSubjectName())){
                                resultList.add("签约主体名称为空");
                            }
                            if(!StringUtil.isValidString(orgSubject.getCertCode())){
                                resultList.add("统一社会信用代码为空");
                            }
                            if(!StringUtil.isValidString(orgSubject.getBank())){
                                resultList.add("收款银行名称为空");
                            }
                            if(!StringUtil.isValidString(orgSubject.getAccountName())){
                                resultList.add("收款账户名为空");
                            }
                            if(!StringUtil.isValidString(orgSubject.getAccountNumber())){
                                resultList.add("收款账户号为空");
                            }
                            if(!StringUtil.isValidString(orgSubject.getCertUrl())){
                                resultList.add("营业执照为空");
                            }
                        }
                    }
                    if(resultList.size()>0){
                        HotelSatisfyProjectResponse hotelSatisfyProjectResponse = new HotelSatisfyProjectResponse();
                        hotelSatisfyProjectResponse.setStringList(resultList);
                        hotelSatisfyProjectResponse.setProjectName(projectBasicInformation.getProjectName());
                        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                        response.setData(hotelSatisfyProjectResponse);
                        return response;
                    }
                }
            }

            // 判断酒店是否符合项目采购策略的基本信息
            if (projectHotelTendStrategy != null &&
                    projectHotelTendStrategy.getOnlyPoiCity().equals(StateEnum.Invalid.key) &&
                    projectHotelTendStrategy.getOnlyPoiDistance().equals(StateEnum.Invalid.key) &&
                    projectHotelTendStrategy.getOnlyHotelStar().equals(StateEnum.Invalid.key)) {
               continue;
            }

            // 查询酒店是否被邀约
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectBasicInformation.getProjectId(), hotelId);
            if(projectIntentHotel != null && projectIntentHotel.getInviteStatus() == RfpConstant.constant_1){
                logger.info("酒店为邀约酒店，不需要检查基础信息限制 {}", projectIntentHotel.getProjectIntentHotelId());
                continue;
            }

            // 酒单白名单不需要校验
            boolean isHotelWhite = projectHotelBidStrategyService.isBidWhiteHotel(projectBasicInformation.getProjectId(), hotelId);

            // 酒店是否为邀约酒店，如果是邀约酒店不需要检查酒店基础信息限制
            if(isHotelWhite){
                logger.info("酒店为白名单酒店，不需要检查基础信息限制 {}", hotelId);
            } else {
                Boolean cityHotelFlag = true;
                Boolean poiDistanceFlag = true;
                Boolean hotelStarFlag = true;
                ArrayList<String> resultList = new ArrayList<>();

                // 酒店基础信息
                HotelSummaryInfoQueryDto hotelSummaryInfoQueryDto = new HotelSummaryInfoQueryDto();
                ArrayList<Long> hotelIds = new ArrayList<>();
                hotelIds.add(hotelId);
                hotelSummaryInfoQueryDto.setHotelIds(hotelIds);
                List<HotelSummaryInfoResponseDto> hotelSummaryInfoResponseDtos = null;
                try {
                    hotelSummaryInfoResponseDtos = hotelInfoFacade.queryHotelSummaryInfo(hotelSummaryInfoQueryDto);
                } catch (Exception e) {
                    logger.error("查询酒店基础信息失败", e);
                }

                // 企业项目POI信息
                Response projectPoiInfoData = projectService.queryProjectPoiInfo(aLong);
                ArrayList<ProjectPoiInfoResponse> projectPoiInfoResponseList = new ArrayList<>();
                if (projectPoiInfoData != null && projectPoiInfoData.getData() != null) {
                    List<Object> objects = objToList(projectPoiInfoData.getData());
                    if (!CollectionUtils.isEmpty(objects)) {
                        for (Object object : objects) {
                            projectPoiInfoResponseList.add((ProjectPoiInfoResponse) object);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(projectPoiInfoResponseList) && !CollectionUtils.isEmpty(hotelSummaryInfoResponseDtos)) {
                    if (projectHotelTendStrategy != null && projectHotelTendStrategy.getOnlyPoiCity().equals(StateEnum.Effective.key)) {
                        cityHotelFlag = false;
                        for (ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList) {
                            // 判断poi的城市编码和酒店的城市编码是否一致，一致则true，否则false
                            if (!StringUtils.isEmpty(hotelSummaryInfoResponseDtos.get(0).getCityCode()) &&
                                    hotelSummaryInfoResponseDtos.get(0).getCityCode().equals(projectPoiInfoResponse.getCityCode())) {
                                cityHotelFlag = true;
                                break;
                            }
                        }
                        if(!cityHotelFlag){
                            resultList.add("仅招POI所在城市的酒店");
                        }
                    }

                    if (projectHotelTendStrategy != null && projectHotelTendStrategy.getOnlyPoiDistance().equals(StateEnum.Effective.key)) {
                        poiDistanceFlag = false;

                        // 需要关联城市，在相同城市才计算距离
                        for (ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList) {
                            if (!StringUtils.isEmpty(hotelSummaryInfoResponseDtos.get(0).getCityCode()) &&
                                    hotelSummaryInfoResponseDtos.get(0).getCityCode().equals(projectPoiInfoResponse.getCityCode())) {
                                if (projectPoiInfoResponse.getLatBaiDu() != null &&
                                        projectPoiInfoResponse.getLngBaiDu() != null &&
                                        hotelSummaryInfoResponseDtos.get(0).getLatBaidu() != null &&
                                        hotelSummaryInfoResponseDtos.get(0).getLngBaidu() != null) {
                                    double distance = LocationUtil.getDistance(projectPoiInfoResponse.getLatBaiDu(), projectPoiInfoResponse.getLngBaiDu(), hotelSummaryInfoResponseDtos.get(0).getLatBaidu(), hotelSummaryInfoResponseDtos.get(0).getLngBaidu());

                                    BigDecimal bigDecimal = new BigDecimal(distance);
                                    BigDecimal multiply = projectHotelTendStrategy.getPoiDistance().multiply(new BigDecimal(1000));
                                    if (multiply.compareTo(bigDecimal) > 0) {
                                        poiDistanceFlag = true;
                                        break;
                                    }
                                }
                            }
                        }
                        if(!poiDistanceFlag){
                            resultList.add("仅签约距离项目POI" + projectHotelTendStrategy.getPoiDistance() + "公里以内的酒店");
                        }
                    }
                }

                if (projectHotelTendStrategy != null && projectHotelTendStrategy.getOnlyHotelStar().equals(StateEnum.Effective.key) && !CollectionUtils.isEmpty(hotelSummaryInfoResponseDtos)) {
                    hotelStarFlag = false;

                    String[] split = projectHotelTendStrategy.getHotelStar().split(",");
                    StringBuffer buffer = new StringBuffer();
                    for (String s : split) {
                        RfpHotelStarEnum enumByKey = RfpHotelStarEnum.getEnumByKey(Integer.valueOf(s));
                        buffer.append(enumByKey.message + "、");
                    }

                    // 判断是否满足星级要求
                    ArrayList<String> strings = new ArrayList<>();
                    for (String s : split) {
                        RfpHotelStarEnum enumByKey = RfpHotelStarEnum.getEnumByKey(Integer.valueOf(s));
                        String[] split1 = enumByKey.value.split(",");
                        for (String s1 : split1) {
                            strings.add(s1);
                        }
                    }
                    if (strings.contains(hotelSummaryInfoResponseDtos.get(0).getHotelStar())) {
                        hotelStarFlag = true;
                    }
                    if(!hotelStarFlag){
                        String substring = buffer.substring(0, buffer.length() - 1);
                        resultList.add("仅签约以下酒店类型:" + substring);
                    }
                }

                // 存在不符合规则检查，返回response
                if (!resultList.isEmpty()) {
                    HotelSatisfyProjectResponse hotelSatisfyProjectResponse = new HotelSatisfyProjectResponse();
                    hotelSatisfyProjectResponse.setStringList(resultList);
                    hotelSatisfyProjectResponse.setProjectName(projectBasicInformation.getProjectName());
                    response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                    response.setData(hotelSatisfyProjectResponse);
                    return response;
                }
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;

    }

    public Boolean isDataRepetition(ProjectIntentHotelRequest projectIntentHotelRequest) {
        ConfirmAgreeProtocolResponse confirmAgreeProtocolResponse = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
        if (confirmAgreeProtocolResponse != null) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response confirmAgreeProtocol(ProjectIntentHotelRequest projectIntentHotelRequest,  UserDTO userDTO) {
        Response response = new Response();
        Long supplyOrgId = userDTO.getOrgDTO().getOrgId();
        try {
            ConfirmAgreeProtocolResponse confirmAgreeProtocolResponse = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
            if (confirmAgreeProtocolResponse == null) {
                // 新增一条项目意向酒店记录
                ProjectIntentHotel intentHotel = new ProjectIntentHotel();
                intentHotel.setProjectId(projectIntentHotelRequest.getProjectId());
                intentHotel.setHotelId(projectIntentHotelRequest.getHotelId());
                intentHotel.setInviteStatus(StateEnum.Invalid.key);
                intentHotel.setSendMailStatus(StateEnum.Invalid.key);
                intentHotel.setBidState(StateEnum.Invalid.key);
                intentHotel.setIsUpload(RfpConstant.constant_0);
                intentHotel.setBidUploadSource(RfpConstant.constant_0);
                intentHotel.setCreator(projectIntentHotelRequest.getCreator());
                intentHotel.setModifier(projectIntentHotelRequest.getCreator());
                Boolean dataRepetition = isDataRepetition(projectIntentHotelRequest);
                if (dataRepetition) {
                    response.setResult(ReturnResultEnum.INSERT_DATA_EXCEPTION.errorNo);
                    response.setMsg("项目意向酒店数据重复");
                    return response;
                }
                this.setHotelGroupBidContentInfo(intentHotel, userDTO);
                intentHotel.setBidOrgType(userDTO.getOrgDTO().getOrgType());
                projectIntentHotelDao.insertProjectIntentHotel(intentHotel);
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                response.setData(intentHotel);
                return response;
            } else{
                Long hotelOrgId = confirmAgreeProtocolResponse.getBidOrgId();
                if((userDTO.getOrgDTO().getOrgType().intValue() == OrgTypeEnum.HOTEL.key ||
                        userDTO.getOrgDTO().getOrgType().intValue() == OrgTypeEnum.HOTELGROUP.key)
                ){
                    if(hotelOrgId != null &&  hotelOrgId.intValue() != supplyOrgId.intValue()){
                        response.setResult(ReturnResultEnum.INSERT_DATA_EXCEPTION.errorNo);
                        response.setMsg("所选酒店已经在项目中进行报价，不可重复新增");
                        return response;
                    }
                }else{
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg(ReturnResultEnum.FAILED.message);
                    return response;
                }
                if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTEL.key)){
                    confirmAgreeProtocolResponse.setBidOrgType(OrgTypeEnum.HOTEL.key);
                } else if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
                    confirmAgreeProtocolResponse.setBidOrgType(OrgTypeEnum.HOTELGROUP.key);
                }
                this.setHotelGroupBidContentInfo(confirmAgreeProtocolResponse, userDTO);
            }
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setData(confirmAgreeProtocolResponse);
        } catch (Exception e) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("同意协议失败");
            logger.error("同意协议失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        return response;
    }

    public Response getHotelBidCertInfo(Long projectIntentHotelId, Long hotelId){
        Response response = new Response();
        HotelCertVO hotelCertVO = new HotelCertVO();
        if(projectIntentHotelId != null) {
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
            if (projectIntentHotel != null) {
                hotelRelatedCertService.setBidHotelCertInfo(projectIntentHotel);
                hotelCertVO.setSpecialTradesCertUrl(projectIntentHotel.getSpecialTradesCertUrl());
                hotelCertVO.setSpecialTradesCertDate(projectIntentHotel.getSpecialTradesCertDate());
                hotelCertVO.setHygieneLicenseCertUrl(projectIntentHotel.getHygieneLicenseCertUrl());
                hotelCertVO.setHygieneLicenseCertDate(projectIntentHotel.getHygieneLicenseCertDate());
                hotelCertVO.setFireSafetyCertUrl(projectIntentHotel.getFireSafetyCertUrl());
                hotelCertVO.setFireSafetyCertDate(projectIntentHotel.getFireSafetyCertDate());
                response.setData(hotelCertVO);
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                response.setMsg(ReturnResultEnum.SUCCESS.message);
            } else {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("酒店报价不存在");
            }
        } else if(hotelId != null){
            List<HotelRelatedCert> hotelRelatedCertList = hotelRelatedCertService.queryListByHotelId(hotelId);
            if(CollectionUtils.isNotEmpty(hotelRelatedCertList)){
                for(HotelRelatedCert hotelRelatedCert: hotelRelatedCertList){
                    if(Objects.equals(hotelRelatedCert.getCertType(), HotelCertTypeEnum.SPECIAL_TRADES.key)){
                        hotelCertVO.setSpecialTradesCertUrl(hotelRelatedCert.getCertUrl());
                        hotelCertVO.setSpecialTradesCertDate(hotelRelatedCert.getCreateTime());
                    } else if(Objects.equals(hotelRelatedCert.getCertType(), HotelCertTypeEnum.HYGIENE_LICENSE.key)){
                        hotelCertVO.setHygieneLicenseCertUrl(hotelRelatedCert.getCertUrl());
                        hotelCertVO.setHygieneLicenseCertDate(hotelRelatedCert.getCreateTime());
                    } else if(Objects.equals(hotelRelatedCert.getCertType(), HotelCertTypeEnum.FIRE_SAFETY.key)){
                        hotelCertVO.setFireSafetyCertUrl(hotelRelatedCert.getCertUrl());
                        hotelCertVO.setFireSafetyCertDate(hotelRelatedCert.getCreateTime());
                    }
                }
            }
        }
        response.setData(hotelCertVO);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateProjectWeight(Long projectId, Long hotelId, List<ProjectHotelWeight> projectHotelWeightList) {
        projectHotelWeightDao.deleteByProjectHotelId(projectId, hotelId);
        if(CollectionUtils.isNotEmpty(projectHotelWeightList)) {
            projectHotelWeightDao.batchInsertOrUpdate(projectHotelWeightList);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response appointHotelConcatInfo(HotelConcatAddRequest hotelConcatAddRequest) {
        Response response = new Response();

        ArrayList<ProjectIntentHotel> projectIntentHotels = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(hotelConcatAddRequest.getProjectIntentHotelList())) {
            for (String projectHotelIdKey : hotelConcatAddRequest.getProjectIntentHotelList()) {
                // ProjectID
                Long aLong = Long.valueOf(projectHotelIdKey.substring(0, projectHotelIdKey.indexOf("_")));
                Long hotelId = Long.valueOf(projectHotelIdKey.substring(projectHotelIdKey.indexOf("_")+1));
                ProjectIntentHotel projectIntentHotel = new ProjectIntentHotel();
                projectIntentHotel.setProjectId(aLong);
                projectIntentHotel.setHotelId(hotelId);
                projectIntentHotel.setHotelContactUid(hotelConcatAddRequest.getUserId());
                projectIntentHotel.setHotelContactName(hotelConcatAddRequest.getUserName());
                projectIntentHotels.add(projectIntentHotel);
            }
        }

        for (ProjectIntentHotel projectIntentHotel : projectIntentHotels) {
            try {
                // 指派时，在酒店意向表不存在记录
                ProjectIntentHotelRequest param = new ProjectIntentHotelRequest();
                param.setProjectId(projectIntentHotel.getProjectId());
                param.setHotelId(projectIntentHotel.getHotelId());
                param.setSupplyOrgId(hotelConcatAddRequest.getSupplyOrgId());
                ProjectIntentHotel selectInfoByProjectIdAndHotelId = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(param);
                if (selectInfoByProjectIdAndHotelId == null) {
                    // 新增一条项目意向酒店记录
                    ProjectIntentHotel intentHotel = new ProjectIntentHotel();
                    intentHotel.setProjectId(projectIntentHotel.getProjectId());
                    intentHotel.setHotelId(projectIntentHotel.getHotelId());
                    intentHotel.setInviteStatus(StateEnum.Invalid.key);
                    intentHotel.setSendMailStatus(StateEnum.Invalid.key);
                    intentHotel.setBidState(StateEnum.Invalid.key);
                    intentHotel.setIsUpload(RfpConstant.constant_0);
                    intentHotel.setBidUploadSource(RfpConstant.constant_0);
                    intentHotel.setCreator(hotelConcatAddRequest.getCreator());
                    intentHotel.setModifier(hotelConcatAddRequest.getCreator());
                    //intentHotel.setHotelOrgId(hotelConcatAddRequest.getSupplyOrgId());
                    Boolean dataRepetition = isDataRepetition(param);
                    if (dataRepetition) {
                        response.setResult(ReturnResultEnum.INSERT_DATA_EXCEPTION.errorNo);
                        response.setMsg("项目意向酒店数据重复");
                    }
                    projectIntentHotelDao.insertProjectIntentHotel(intentHotel);
                }
                AppointHotelContactDto appointHotelContactDto = new AppointHotelContactDto();
                BeanUtils.copyProperties(projectIntentHotel,appointHotelContactDto);
                appointHotelContactDto.setSupplyOrgId(hotelConcatAddRequest.getSupplyOrgId());
                projectIntentHotelDao.appointHotelConcatInfo(appointHotelContactDto);
                response.setMsg(ReturnResultEnum.SUCCESS.message);
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            } catch (Exception e) {
                logger.error("更新销售人失败"+ projectIntentHotel.getProjectId() + projectIntentHotel.getHotelId(),e);
                response.setMsg(ReturnResultEnum.FAILED.message);
                response.setResult(ReturnResultEnum.FAILED.errorNo);
            }
        }

        return response;
    }

    @Override
    public Response selectHotelAndRoomListByHotelId(ProjectIntentHotelRequest projectIntentHotelRequest) {
        Response response = new Response();

        HotelInfoResponse hotelInfoResponse = new HotelInfoResponse();

        HotelSummaryInfoQueryDto hotelSummaryInfoQueryDto = new HotelSummaryInfoQueryDto();
        ArrayList<Long> longs = new ArrayList<>();
        longs.add(projectIntentHotelRequest.getHotelId());
        hotelSummaryInfoQueryDto.setHotelIds(longs);
        List<HotelSummaryInfoResponseDto> hotelSummaryInfoResponseDtos = null;
        try {
            hotelSummaryInfoResponseDtos = hotelInfoFacade.queryHotelSummaryInfo(hotelSummaryInfoQueryDto);

            if (!CollectionUtils.isEmpty(hotelSummaryInfoResponseDtos)) {
                hotelSummaryInfoResponseDtos.forEach(hotelSummaryInfoResponseDto -> {
                    hotelInfoResponse.setCityCode(hotelSummaryInfoResponseDto.getCityCode());
                    hotelInfoResponse.setCityName(hotelSummaryInfoResponseDto.getCityName());
                    hotelInfoResponse.setHotelStar(hotelSummaryInfoResponseDto.getHotelStar());
                    hotelInfoResponse.setLngBaidu(hotelSummaryInfoResponseDto.getLngBaidu());
                    hotelInfoResponse.setLatBaidu(hotelSummaryInfoResponseDto.getLatBaidu());
                    hotelInfoResponse.setHotelName(hotelSummaryInfoResponseDto.getHotelName());
                });
            }

            RoomSummaryInfoQuery roomSummaryInfoQuery = new RoomSummaryInfoQuery();
            ArrayList<RoomInfoResponse> roomInfoResponses = new ArrayList<RoomInfoResponse>();
            roomSummaryInfoQuery.setHotelId(projectIntentHotelRequest.getHotelId());
            List<RoomInfoResponseDto> roomInfoResponseDtos = null;
             roomInfoResponseDtos = roomInfoFacade.queryRoomSummaryInfoList(roomSummaryInfoQuery);

            if (!CollectionUtils.isEmpty(roomInfoResponseDtos)) {
                roomInfoResponseDtos.forEach(roomInfoResponseDto -> {
                    RoomInfoResponse roomInfoResponse = new RoomInfoResponse();
                    roomInfoResponse.setRoomId(roomInfoResponseDto.getRoomId());
                    roomInfoResponse.setRoomName(roomInfoResponseDto.getRoomName());
                    roomInfoResponses.add(roomInfoResponse);
                });
            }
            hotelInfoResponse.setRoomInfos(roomInfoResponses);
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setData(hotelInfoResponse);
        } catch (Exception e) {
            logger.error("根据酒店Id查酒店和房型信息失败",e);
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("根据酒店Id查酒店和房型信息失败");
            return response;
        }
        return response;
    }

    @Override
    public Response queryByProjectIdAndHotelIds(QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto) {
        Response response = new Response();
        try {
            List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setData(projectIntentHotels);
        } catch (Exception e) {
            logger.error("查询项目意向酒店列表信息失败", e);
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("查询项目意向酒店列表信息失败");
        }
        return response;
    }

    @Override
    public Response queryInfoByProjectIdAndHotelId(ProjectIntentHotelRequest projectIntentHotelRequest) {
        Response response = new Response();
        ConfirmAgreeProtocolResponse confirmAgreeProtocolResponse = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(confirmAgreeProtocolResponse);
        return response;
    }

    // 处理前一年时间
    @Override
    public void dealLastYear(ProjectIntentHotelRequest projectIntentHotelRequest) {
//        LocalDate of = LocalDate.of(LocalDate.now().minusYears(1).getYear(), LocalDate.now().get(ChronoField.MONTH_OF_YEAR), LocalDate.now().getDayOfMonth());
//        String s = of.toString();
//        Date date = DateUtil.stringToDate(s);
        projectIntentHotelRequest.setLastYearTime(DateUtil.dateFormat(DateUtil.getDate(new Date(),-730,0),DateUtil.defaultFormat));
    }

    // 处理object转List方法
    public List<Object> objToList(Object obj) {
        List<Object> list = new ArrayList<Object>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                list.add(o);
            }
            return list;
        }
        return null;
    }

    // 处理投标起止时间、公布评标结果日
    public void dealTenderTimeAndPublishTime(List<ProjectHotelTentResponse> projectHotelTentRespons) {
        for (ProjectHotelTentResponse projectIntentHotelRespons : projectHotelTentRespons) {
            projectIntentHotelRespons.setTenderStartTime(projectIntentHotelRespons.getEnrollStartTime());


            projectIntentHotelRespons.setTenderEndTime(projectIntentHotelRespons.getThirdBidEndTime() != null?
                    projectIntentHotelRespons.getThirdBidEndTime():projectIntentHotelRespons.getSecondBidEndTime() != null?
                    projectIntentHotelRespons.getSecondBidEndTime():projectIntentHotelRespons.getFirstBidEndTime());
            Calendar c=Calendar.getInstance();
            c.setTime(projectIntentHotelRespons.getTenderEndTime());
            c.add(Calendar.DAY_OF_MONTH,1);
            Date tomorrow=c.getTime();//这是明天
            projectIntentHotelRespons.setPublishTenderResultTime(tomorrow);
        }
    }

    @Override
    public Response queryProjectIntentHotelDetailByCondition(QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto) {
        Response response = new Response();
        ProjectIntentHotelDetailResponse projectIntentHotelDetailResponse = new ProjectIntentHotelDetailResponse();

        // 投标信息、状态
        List<ProjectIntentHotel> projectIntentHotelList  = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
        if (CollectionUtils.isEmpty(projectIntentHotelList)) {
            logger.error("无项目意向酒店信息, queryProjectIntentHotelDetailDto=" + JSON.toJSONString(queryProjectIntentHotelDetailDto));
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("无项目意向酒店信息");
            return response;
        }
        ProjectIntentHotel projectIntentHotel = projectIntentHotelList.get(0);
        projectIntentHotelDetailResponse.setProjectIntentHotel(projectIntentHotel);

        // 设置酒店名称
        HotelResponse hotelResponse = hotelDao.selectHotelInfo(projectIntentHotelDetailResponse.getProjectIntentHotel().getHotelId());
        projectIntentHotelDetailResponse.setHotelName(hotelResponse.getHotelName());

        if (queryProjectIntentHotelDetailDto.getIsQueryHotelPrice()) {
            // 投标策略
            ProjectHotelBidStrategy projectHotelBidStrategy = projectHotelBidStrategyDao.selectByPrimaryKey(projectIntentHotel.getProjectIntentHotelId());

            if (null == projectHotelBidStrategy) {
                logger.error("无项目投标策略信息, projectIntentHotelId=" + projectIntentHotel.getProjectIntentHotelId());
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("无项目报价策略信息");
                return response;
            }

            projectIntentHotelDetailResponse.setProjectHotelBidStrategy(projectHotelBidStrategy);

            // 报价明细
            ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
            projectIntentHotelRequest.setProjectId(queryProjectIntentHotelDetailDto.getProjectId());
            projectIntentHotelRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            projectIntentHotelRequest.setHotelId(projectIntentHotel.getHotelId());
            List<ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponses = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
            if (CollectionUtils.isEmpty(projectHotelPriceLevelResponses)) {
                logger.error("无项目报价信息, projectIntentHotelRequest=" + JSON.toJSONString(projectIntentHotelRequest));
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("无项目报价信息");
                return response;
            }
            projectIntentHotelDetailResponse.setHotelPriceLevelResponseList(projectHotelPriceLevelResponses);

            // 日期设置
            ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest = new ProjectHotelBidStrategyRequest();
            projectHotelBidStrategyRequest.setProjectId(queryProjectIntentHotelDetailDto.getProjectId());
            projectHotelBidStrategyRequest.setHotelId(projectIntentHotel.getHotelId());
            projectHotelBidStrategyRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());

            // 适用日期
            List<PriceApplicableDay> priceApplicableDays = priceApplicableDayDao.selectPriceApplicableDayListByProject(projectHotelBidStrategyRequest);
            if (CollectionUtils.isNotEmpty(priceApplicableDays)) {
                projectIntentHotelDetailResponse.setApplicableDayList(priceApplicableDays);
            }

            // 不适用日期
            List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);
            if (CollectionUtils.isNotEmpty(priceUnapplicableDays)) {
                projectIntentHotelDetailResponse.setUnapplicableDayList(priceUnapplicableDays);
            }

            //自定义投标策略
            QueryProjectCustomBidStrategyDto queryProjectCustomBidStrategyDto = new QueryProjectCustomBidStrategyDto();
            queryProjectCustomBidStrategyDto.setProjectId(queryProjectIntentHotelDetailDto.getProjectId());
            queryProjectCustomBidStrategyDto.setProjectIntentHotelId(queryProjectIntentHotelDetailDto.getProjectIntentHotelIds().get(0));
            List<ProjectCustomBidStrategy> projectCustomBidStrategies = projectCustomBidStrategyDao.queryProjectCustomBidStrategyByDisplayOrderAsc(queryProjectCustomBidStrategyDto);
            if(CollectionUtils.isNotEmpty(projectCustomBidStrategies)){
                projectIntentHotelDetailResponse.setProjectCustomBidStrategies(projectCustomBidStrategies);
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(projectIntentHotelDetailResponse);
        return response;
    }

    @Override
    public void updateBidContactInfo(ProjectIntentHotel projectIntentHotel) {
        projectIntentHotelDao.updateBidContactInfo(projectIntentHotel);
    }


    @Override
    public void updateBidContactInfoOnly(ProjectIntentHotel projectIntentHotel) {
        projectIntentHotelDao.updateBidContactInfoOnly(projectIntentHotel);
    }

    @Override
    public void updateHotelGroupBidContactInfoOnly(ProjectIntentHotel projectIntentHotel) {
        projectIntentHotelDao.updateBidContactInfoOnly(projectIntentHotel);
    }

    @Override
    public void updateContactInfoOnly(ProjectIntentHotel projectIntentHotel) {
        projectIntentHotelDao.updateContactInfoOnly(projectIntentHotel);
    }

    @Override
    public Response selectBidHotelListByIdAndTime(Long projectId) {
        Response response = new Response();

        List<ProjectIntentHotel> longs = projectIntentHotelDao.selectBidHotelListByIdAndTime(projectId);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(longs);
        return response;
    }

    public List<ProjectHotelTentResponse> selectHotelOrgTentList(ProjectIntentHotelRequest projectIntentHotelRequest) {
        List<ProjectHotelTentResponse> joinProjectList = projectIntentHotelDao.selectHotelOrgTentList(projectIntentHotelRequest);
        List<ProjectIntentHotel> projectIntentHotelList = projectIntentHotelDao.selectProjectIntentHotelList(projectIntentHotelRequest);
        Map<String, ProjectIntentHotel> projectIntentHoteMap = new HashMap<>();
        if(projectIntentHotelRequest.getUserId() != null){
            for(ProjectIntentHotel projectIntentHotel : projectIntentHotelList){
                projectIntentHoteMap.put(projectIntentHotel.getProjectId() + "_" + projectIntentHotel.getHotelId(), projectIntentHotel);
            }
        }
        if (Objects.equals(HotelBidStateEnum.NO_BID.bidState, projectIntentHotelRequest.getBidState())) {
            // 未报价 不包括等待酒店集团审核/拒绝状态报价
            List<ProjectIntentHotel> hotelGroupApprovedProjectIntentHotelList = projectIntentHotelList.stream().filter(o -> o.getHotelGroupApproveStatus() != null &&
                    (Objects.equals(o.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.REJECT_APPROVED.key) ||
                            Objects.equals(o.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key))).collect(Collectors.toList());
            List<String> hotelGroupApprovedProjectIdHotelIdKeyList = hotelGroupApprovedProjectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());

            projectIntentHotelList = projectIntentHotelList.stream().filter(o -> !Objects.equals(o.getBidState(), HotelBidStateEnum.NO_BID.bidState)).collect(Collectors.toList());
            // 已经报价的酒店项目
            List<String> projectIdHotelIdKeyList = projectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());
            for (int i = joinProjectList.size() - 1; i >= 0; i--) {
                ProjectHotelTentResponse projectHotelTentResponse = joinProjectList.get(i);
                String projectHotelIdKey = projectHotelTentResponse.getProjectId() + "_" + projectHotelTentResponse.getHotelId();
                projectHotelTentResponse.setProjectIdHotelIdKey(projectHotelIdKey);
                if (projectHotelTentResponse.getTenderType() == 1 && projectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 公开招标 过滤已经报价的酒店项目
                    joinProjectList.remove(i);
                    continue;
                }
                if (hotelGroupApprovedProjectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 过滤申请酒店集团审核
                    joinProjectList.remove(i);
                    continue;
                }
                // 根据员工ID过滤
                if(projectIntentHotelRequest.getUserId() != null) {
                    if(!projectIntentHoteMap.containsKey(projectHotelIdKey)) {
                        joinProjectList.remove(i);
                        continue;
                    }
                    ProjectIntentHotel projectIntentHotel = projectIntentHoteMap.get(projectHotelIdKey);
                    if(!Objects.equals(projectIntentHotel.getHotelContactUid(), projectIntentHotelRequest.getUserId())){
                        joinProjectList.remove(i);
                    }
                }
            }
            // 排序
            joinProjectList = joinProjectList.stream().sorted((o1, o2) -> {
                if(o1.getDisplayOrder() == null && o2.getDisplayOrder() == null){
                    if(o2.getCreateTime().getTime() - o1.getCreateTime().getTime() > 0L){
                        return 1;
                    }
                    return -1;
                } else if(o1.getDisplayOrder() != null && o2.getDisplayOrder() != null){
                    return o2.getDisplayOrder() - o1.getDisplayOrder();
                } else if(o1.getDisplayOrder() == null){
                    return 1;
                } else  {
                    return -1;
                }
            }).collect(Collectors.toList());
        } else {
            Map<String, ProjectIntentHotel> hotelGroupApproveProjectIntentHotelMap = new HashMap<>();
            if(projectIntentHotelRequest.getHotelGroupApproveStatus() != null){
                projectIntentHotelList = projectIntentHotelList.stream().filter(o -> Objects.equals(o.getHotelGroupApproveStatus(), projectIntentHotelRequest.getHotelGroupApproveStatus())).collect(Collectors.toList());
                hotelGroupApproveProjectIntentHotelMap = projectIntentHotelList.stream().collect(Collectors.toMap(o -> o.getProjectId() + "_" + o.getHotelId(), Function.identity()));
            } else {
                projectIntentHotelList = projectIntentHotelList.stream().filter(o -> Objects.equals(o.getBidState(), projectIntentHotelRequest.getBidState())).collect(Collectors.toList());
            }
            List<String> projectIdHotelIdKeyList = projectIntentHotelList.stream().map(o -> o.getProjectId() + "_" + o.getHotelId()).collect(Collectors.toList());
            for (int i = joinProjectList.size() - 1; i >= 0; i--) {
                ProjectHotelTentResponse projectHotelTentResponse = joinProjectList.get(i);
                String projectHotelIdKey = projectHotelTentResponse.getProjectId() + "_" + projectHotelTentResponse.getHotelId();
                projectHotelTentResponse.setProjectIdHotelIdKey(projectHotelIdKey);
                if (!projectIdHotelIdKeyList.contains(projectHotelIdKey)) { // 过滤报价状态不相等的酒店项目
                    joinProjectList.remove(i);
                    continue;
                }
                // 设置酒店集团审批信息
                ProjectIntentHotel hotelGroupApproveProjectIntentHotel = hotelGroupApproveProjectIntentHotelMap.get(projectHotelIdKey);
                if(hotelGroupApproveProjectIntentHotel != null){
                    projectHotelTentResponse.setHotelGroupApproveStatus(hotelGroupApproveProjectIntentHotel.getHotelGroupApproveStatus());
                    projectHotelTentResponse.setRejectApproveRemark(hotelGroupApproveProjectIntentHotel.getRejectApproveRemark());
                }
                // 根据员工ID过滤
                if(projectIntentHotelRequest.getUserId() != null) {
                    if(!projectIntentHoteMap.containsKey(projectHotelIdKey)) {
                        joinProjectList.remove(i);
                        continue;
                    }
                    ProjectIntentHotel projectIntentHotel = projectIntentHoteMap.get(projectHotelIdKey);
                    if(!Objects.equals(projectIntentHotel.getHotelContactUid(), projectIntentHotelRequest.getUserId())){
                        joinProjectList.remove(i);
                    }
                }
            }
        }
        return joinProjectList;
    }

    @Override
    public int generateAndUpdateTotalRoomNight(YesterdayProjectHotelResponse yesterdayProjectHotelResponse) {
        // 查询酒店间夜数
        QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
        queryRoomNightsDto.setHotelId(yesterdayProjectHotelResponse.getHotelId());
        queryRoomNightsDto.setDistributorCodes(Lists.newArrayList(yesterdayProjectHotelResponse.getDistributorCode()));
        queryRoomNightsDto.setStartTime(DateUtil.dateToString(yesterdayProjectHotelResponse.getPriceMonitorStartDate()));
        queryRoomNightsDto.setEndTime(DateUtil.dateToString(cn.hutool.core.date.DateUtil.yesterday()));
        Integer totalRoomNight = disHotelDailyOrderDao.selectRoomNights(queryRoomNightsDto);
        return projectIntentHotelDao.updateTotalRoomNight(totalRoomNight == null ? 0 : totalRoomNight, yesterdayProjectHotelResponse.getProjectIntentHotelId());

    }

    @Override
    public void statByHotelViolationMonitor(ProjectIntentHotel projectIntentHotel, HotelViolationsMonitor hotelViolationsMonitor) {
        if(Objects.equals(hotelViolationsMonitor.getViolationType(), ViolationTypeEnum.PRICE_MONITORING.key) ||
                Objects.equals(hotelViolationsMonitor.getViolationType(), ViolationTypeEnum.ORDER_MONITORING.key)) {
            int updateCount = projectIntentHotelDao.addViolationsCount(projectIntentHotel.getProjectIntentHotelId(), 1);
            if (updateCount != 1) {
                logger.error("统计违规监控数量失败 {}", projectIntentHotel.getProjectIntentHotelId());
            }

            updateCount = projectIntentHotelDao.updateLastReminderLevel(projectIntentHotel.getProjectIntentHotelId(), hotelViolationsMonitor.getReminderLevel());
            if (updateCount != 1) {
                logger.error("统计违规监控提醒等级失败 {}", projectIntentHotel.getProjectIntentHotelId());
            }

            // 当酒店最新一次违规达到加力人工跟进提醒状态，且当前酒店的跟进标签为空或已解决，将酒店设置处理标签：待跟进，备注内容：最新违规需要加力人工跟进
            if(hotelViolationsMonitor.getReminderLevel() != null && hotelViolationsMonitor.getReminderLevel().contains(String.valueOf(ReminderTypeEnum.JIALI_FOLLOW_UP.key)) &&
                    (projectIntentHotel.getViolationStatProcessStatus() == null || projectIntentHotel.getViolationStatProcessStatus() == ViolationStatProcessStatusEnum.RESOLVED.key || projectIntentHotel.getViolationStatProcessStatus() == ViolationStatProcessStatusEnum.NO_TAG.key)
            ){
                String processMsg = "最新违规需要加力人工跟进";
                updateCount = projectIntentHotelDao.updateViolationStatProcessStatusAndMsg(projectIntentHotel.getProjectIntentHotelId(), ViolationStatProcessStatusEnum.WAITFOLLOW.key, processMsg,OrgTypeEnum.PLATFORM.key);
                if (updateCount != 1) {
                    logger.error("统计违规监控跟进状态失败 {}", projectIntentHotel.getProjectIntentHotelId());
                } else {
                    // 新增待跟进记录
                    MonitorStatLabelProcessDto monitorStatLabelProcessDto = new MonitorStatLabelProcessDto();
                    monitorStatLabelProcessDto.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    monitorStatLabelProcessDto.setStatProcessStatus(ViolationStatProcessStatusEnum.WAITFOLLOW.key);
                    monitorStatLabelProcessDto.setProcessMSG(processMsg);
                    monitorStatLabelProcessDto.setOrgType(OrgTypeEnum.PLATFORM.key);
                    monitorStatLabelProcessDto.setOperator(RfpConstant.CREATOR);
                    hotelViolationsMonitorDao.labelViolationStatProcessRecord(monitorStatLabelProcessDto);
                }
            }
        }
    }

    @Override
    public Response selectHotelResponse(Long hotelId) {
        Response response = new Response();
        HotelResponse hotelResponse = hotelDao.selectHotelInfo(hotelId);
        if(!StringUtil.isEmpty(hotelResponse.getHotelStar())){
            hotelResponse.setHotelStarString(HotelStarEnum.getValueByKey(Integer.parseInt(hotelResponse.getHotelStar())));
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(hotelResponse);
        return response;
    }

    @Override
    public ProjectIntentHotel getByProjectIntentHotelId(Long projectIntentHotelId) {
        return projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
    }

    @Override
    public Response updateHotelSubject(Long projectIntentHotelId, Long subjectId, String modifier) {
        Response response = new Response();
        OrgSubject orgSubject = orgSubjectDao.selectByPrimaryKey(subjectId);
        if(orgSubject == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("签约主体不存在");
        }
        ProjectIntentHotel projectIntentHotel = new ProjectIntentHotel();
        projectIntentHotel.setProjectIntentHotelId(projectIntentHotelId);
        projectIntentHotel.setHotelOrgId(orgSubject.getOrgId());
        projectIntentHotel.setHotelSubjectId(orgSubject.getSubjectId());
        projectIntentHotel.setHotelBank(orgSubject.getBank());
        projectIntentHotel.setHotelAccountName(orgSubject.getAccountName());
        projectIntentHotel.setHotelAccountNumber(orgSubject.getAccountNumber());
        projectIntentHotel.setModifier(modifier);
        int updateResult = projectIntentHotelDao.updateBidHotelSubjectInfo(projectIntentHotel);
        if(updateResult != 1) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("更新主体失败");
            return response;
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryBidProjectInfo(QueryBidProjectInfoRequest queryBidProjectInfoRequest) {
        Response response = new Response();
        Project project = projectDao.selectByPrimaryKey(queryBidProjectInfoRequest.getProjectId());
        if(project == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目不存在");
        }

        // 查询项目报价相关数据
        QueryBidProjectInfoResponse queryBidProjectInfoResponse = new QueryBidProjectInfoResponse();
        BeanUtils.copyProperties(project, queryBidProjectInfoResponse);

        // 查询酒店去年是否报价
        queryBidProjectInfoResponse.setIsBidLastYear(0);
        if(queryBidProjectInfoRequest.getHotelId() != null && project.getRelatedProjectId() != null){
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(project.getRelatedProjectId(), queryBidProjectInfoRequest.getHotelId());
            if(projectIntentHotel != null){
                queryBidProjectInfoResponse.setIsBidLastYear(1);
                queryBidProjectInfoResponse.setLastYearProjectId(project.getRelatedProjectId());
            }
        }

        response.setData(queryBidProjectInfoResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    /**
     * 酒店集团审核通过
     * */
    @Override
    public Response hotelGroupApproveBid(Long projectIntentHotelId, UserDTO userDTO) {
        Response response = new Response();
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
        // 检查报价状态
        if(!Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key) &&
                !Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.REJECT_APPROVED.key)
        ){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("酒店集团审核状态为" + HotelGroupApproveStatusEnum.getValueByKey(projectIntentHotel.getHotelGroupApproveStatus()) + "，不能审核");
            return response;
        }
        // 当新报价 审核通过为 已报价状态
        projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.APPROVED.key);
        // 议价中审核通过为修改报价
        if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)){
            projectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);
        }

        projectIntentHotel.setModifier(userDTO.getOperator());
        projectIntentHotelDao.updateHotelGroupApprove(projectIntentHotel);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    /**
     * 酒店集团审核驳回
     */
    @Override
    public Response hotelGroupRejectBid(Long projectIntentHotelId, String rejectRemark, UserDTO userDTO) {
        Response response = new Response();
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
        // 检查报价状态
        if(!Objects.equals(projectIntentHotel.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("酒店集团审核状态为" + HotelGroupApproveStatusEnum.getValueByKey(projectIntentHotel.getHotelGroupApproveStatus()) + "，不能审核");
            return response;
        }
        // 审核不通过为审核驳回（审核不通过填写审核不通过原因）
        projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.REJECT_APPROVED.key);
        projectIntentHotel.setRejectApproveRemark(rejectRemark);
        projectIntentHotel.setModifier(userDTO.getOperator());
        projectIntentHotelDao.updateHotelGroupApprove(projectIntentHotel);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    /**
     * 酒店集团查询报价统计数据
     */
    @Override
    public Response queryBidStatCount(UserDTO userDTO) {
        Response response = new Response();

        QueryBidStatCountResponse queryBidStatCountResponse = new QueryBidStatCountResponse();
        // 查询邀请酒店数量
        QueryTendProjectInviteHotelInfoRequest queryTendProjectInviteHotelInfoRequest = new QueryTendProjectInviteHotelInfoRequest();
        if (userDTO.getRoleCodeType().equals(RoleCodeEnum.EMPLOYEE.key)) {
            queryTendProjectInviteHotelInfoRequest.setUserId(userDTO.getUserId());
        }

        queryTendProjectInviteHotelInfoRequest.setHotelGroupId(String.valueOf(userDTO.getOrgDTO().getGroupId()));
        queryTendProjectInviteHotelInfoRequest.setOrgId(userDTO.getOrgId());
        Integer inviteHotelCount = projectIntentHotelGroupDao.queryTendProjectInviteHotelInfoCount(queryTendProjectInviteHotelInfoRequest);
        queryBidStatCountResponse.setInviteHotelCount(inviteHotelCount == null ? 0 : inviteHotelCount);

        // 查询报价状态统计数据
        QueryProjectOverviewRequest queryProjectOverviewRequest = new QueryProjectOverviewRequest();
        queryProjectOverviewRequest.setOrgId(userDTO.getOrgId());
        if (userDTO.getRoleCodeType().equals(RoleCodeEnum.EMPLOYEE.key)) {
            queryProjectOverviewRequest.setUserId(userDTO.getUserId());
        }
        if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            queryProjectOverviewRequest.setHotelGroupBrandIdList(userDTO.getHotelGroupBrandIdList());
        }
        List<BidStateCountDto> bidStateCountDtoList = projectIntentHotelGroupDao.queryTendProjectInfoGroupByBidStat(queryProjectOverviewRequest);
        for(BidStateCountDto bidStateCountDto : bidStateCountDtoList){
            // 新标
            if(bidStateCountDto.getBidState() == HotelBidStateEnum.NEW_BID.bidState){
                queryBidStatCountResponse.setNewBidCount(bidStateCountDto.getBidStateCount());
            }
            // 议价中
            if(bidStateCountDto.getBidState() == HotelBidStateEnum.UNDER_NEGOTIATION.bidState){
                queryBidStatCountResponse.setUnderNegotiationCount(bidStateCountDto.getBidStateCount());
            }
            // 修订报价
            if(bidStateCountDto.getBidState() == HotelBidStateEnum.UPDATED_BID.bidState){
                queryBidStatCountResponse.setUpdatedBidCount(bidStateCountDto.getBidStateCount());
            }
            // 拒绝议价
            if(bidStateCountDto.getBidState() == HotelBidStateEnum.REJECT_NEGOTIATION.bidState){
                queryBidStatCountResponse.setRejectNegotiationCount(bidStateCountDto.getBidStateCount());
            }
        }

        // 未报价数量
        int noBidCount = projectIntentHotelGroupDao.queryTendProjectNoBidCount(queryProjectOverviewRequest);
        queryBidStatCountResponse.setNoBidCount(noBidCount);

        response.setData(queryBidStatCountResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public boolean isNeedHotelGroupApprove(Long projectId, HotelResponse hotelResponse, UserDTO userDTO) {
        // 查询酒店机构的品牌ID
        if(hotelResponse == null || !StringUtil.isValidString(hotelResponse.getHotelBrand())){
            return false;
        }

        // 查询项目开始审核的酒店集团机构
        List<Long> projectHotelGroupOrgIdList = projectIntentHotelGroupDao.queryNeedApproveHotelGroupOrgId(projectId);
        if(CollectionUtils.isEmpty(projectHotelGroupOrgIdList)){
            return false;
        }

        // 查询需要审核的所有品牌
        List<Long> brandIds = orgHotelGroupBrandDao.queryHotelGroupBrandIds(projectHotelGroupOrgIdList);
        if(CollectionUtils.isEmpty(brandIds)){
            return false;
        }

        // 集团机构没有针对 这个项目开启审核
        if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            if(!projectHotelGroupOrgIdList.contains(userDTO.getOrgDTO().getOrgId())){
                return false;
            }
        }

        // 品牌是否包括酒店品牌
        Long hotelBrandId = Long.valueOf(hotelResponse.getHotelBrand());
        return brandIds.contains(hotelBrandId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearProjectHotelBidData(ProjectIntentHotel projectIntentHotel) {
        projectIntentHotelDao.deleteByPrimaryKey(projectIntentHotel.getProjectIntentHotelId());
        projectHotelPriceDao.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        projectHotelPriceGroupDao.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceApplicableRoomDao.deleteByProjectHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceApplicableDayDao.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        priceUnapplicableDayDao.deleteByProjectIdAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        ProjectHotelBidStrategy projectHotelBidStrategy = new ProjectHotelBidStrategy();
        projectHotelBidStrategy.setProjectId(projectIntentHotel.getProjectId());
        projectHotelBidStrategy.setHotelId(projectIntentHotel.getHotelId());
        projectHotelBidStrategyDao.deleteByProjectAndHotelId(projectHotelBidStrategy);
        HotelPriceMonitorRoom hotelPriceMonitorRoom = new HotelPriceMonitorRoom();
        hotelPriceMonitorRoom.setProjectId(projectIntentHotel.getProjectId());
        hotelPriceMonitorRoom.setHotelId(projectIntentHotel.getHotelId());
        hotelPriceMonitorRoomDao.deleteData(hotelPriceMonitorRoom);
    }

    @Override
    public void setHotelGroupBidContentInfo(ProjectIntentHotel projectIntentHotel, UserDTO userDTO) {
        if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            // 查询酒店集团是否存报价模板联系方式
            ProjectIntentHotelGroup projectIntentHotelGroup = projectIntentHotelGroupDao.selectByProjectAndOrgId(projectIntentHotel.getProjectId(), userDTO.getOrgDTO().getOrgId());
            projectIntentHotel.setHotelGroupBidContactName(userDTO.getUserName());
            projectIntentHotel.setHotelGroupBidContactEmail(userDTO.getEmail());
            projectIntentHotel.setHotelGroupBidContactMobile(userDTO.getMobile());

            // 查询酒店集团是否存报价模板联系方式
            if(projectIntentHotelGroup != null && StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactName()) &&
                    StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactMobile()) &&
                    StringUtils.isNotEmpty(projectIntentHotelGroup.getHotelGroupBidContactEmail())
            ){
                projectIntentHotel.setHotelGroupBidContactName(projectIntentHotelGroup.getHotelGroupBidContactName());
                projectIntentHotel.setHotelGroupBidContactMobile(projectIntentHotelGroup.getHotelGroupBidContactMobile());
                projectIntentHotel.setHotelGroupBidContactEmail(projectIntentHotelGroup.getHotelGroupBidContactEmail());
            }
        }
    }


}


