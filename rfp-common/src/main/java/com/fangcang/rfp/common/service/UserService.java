package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.RoleDTO;
import com.fangcang.rfp.common.dto.RoleDTOBo;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.EmployeeRequest;
import com.fangcang.rfp.common.dto.request.UserParams;
import com.fangcang.rfp.common.entity.Org;
import com.fangcang.rfp.common.entity.UUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UserService {

	int deleteByPrimaryKey(Long id);

	/**
	 * 新增员工信息
	 * @param user
	 * @return
	 */
	Response insertUser(UserDTO user);

	UserDTO insert(UserDTO record);

	public UserDTO getUserByClientOrgCodeAndMobile(String clientOrgCode, String mobile);


	UUser selectByPrimaryKey(Long id);

	/**
	 * 更新员工信息
	 * @param user
	 * @return
	 */
	Response updateEmployeeInfoByParams(UserDTO user);

	/**
	 * 员工是否启用
	 * @param user
	 * @return
	 */
	Response isEnable(UserDTO user);

	/**
	 * 审核员工
	 * @param user
	 * @return
	 */
	Response auditEmployeeInfo(UserDTO user);

    int updateByPrimaryKeySelective(UUser record);

    int updateByPrimaryKey(UserDTO record);

	UserDTO login(String email ,String pswd);

	UserDTO findUserByLoginName(String loginName);

	UserDTO findUserByMobile(String mobile);

	UserDTO findUserByEmail(String email, String userId);

	Integer findUserCountByEmail(String email);


	Map<String, Object> deleteUserById(String ids);

	Map<String, Object> updateForbidUserById(Long id, Integer status);

	List<RoleDTOBo> selectRoleByUserId(Long id);

	Map<String, Object> deleteRoleByUserIds(String userIds);
	
	/**
	 * @[简要描述]根据条件查询用户
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年12月17日上午11:19:26
	 * @param user
	 * @return    
	 * @return: List<UUser>
	 */
	List<UUser> findUserByCondition(UUser user);


	/**
	 * 分页查询员工列表
	 * @param employeeRequest
	 * @return
	 */
	Response findEmployeeByPage(EmployeeRequest employeeRequest);

	/**
	 * 查询员工申请列表
	 * @param employeeRequest
	 * @return
	 * @throws Exception
	 */
	Response findEmployeeApply(EmployeeRequest employeeRequest);

	/**
	 * 联想查询员工列表
	 * @param employeeRequest
	 * @return
	 * @throws Exception
	 */
	Response findEmployeeByName(EmployeeRequest employeeRequest);

	/**
	 * 联想查询平台员工列表
	 * @param employeeRequest
	 * @return
	 * @throws Exception
	 */
	public Response findPlatformEmployeeByName(EmployeeRequest employeeRequest);
	/**
	 * 查询系统角色列表
	 * @return
	 * @throws Exception
	 */
	Response querySystemRoleList();

	/**
	 * @[简要描述]根据openId获取用户
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2017年5月12日下午2:19:02
	 * @param openId
	 * @return    
	 * @return: User
	 */
	UserDTO getUserByOpenId(String openId);

	/**
	 * @[简要描述] 重新设置密码
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年11月30日上午10:40:53
	 * @param user
	 * @return
	 * @return: Response
	 */
	Response resetPassWord(UUser user);

	/**
	 * @[简要描述] 根据外部账户获得用户信息
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年11月9日下午4:41:56
	 * @param merchantCode
	 * @return
	 * @return: User
	 */
	UserDTO getUserByOutAccount(String outAccount,String merchantCode) throws Exception;

	/**
	 * @[简要描述] 根据机构id获取机构信息
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年11月9日下午4:43:03
	 * @param orgId
	 * @return
	 * @return: Org
	 */
	Org getOrgById(Long orgId);


	/**
	 * @[简要描述] 根据条件获得用户信息
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年11月9日下午4:41:56
	 * @param user
	 * @return
	 * @return: UUser
	 */
	UserDTO getUserByCondition(UUser user);

	/**
	 * @[简要描述]根据用户Id查询角色
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2017年3月16日下午8:56:07
	 * @param userId
	 * @return
	 * @return: Role
	 */
	RoleDTO getRoleByUserId(Long userId);


	/**
	 * 根据用户ID重置密码
	 * @param uUser
	 */
	Response reSetPswd(UUser uUser);

	/**
	 * 根据电话号码找回密码或修改密码
	 */
	Response updateOrFindPswd(UserParams userParams);

    Response openRfpForIreve(UserDTO user);

	/**
	 *  根据机构类型统计用户数
	 * @param orgType
	 * @return
	 */
	Response queryUserCountByOrgType(Integer orgType);

	/**
	 * 根据部门查询所有有效用户信息
	 * @param departIds
	 * @return
	 */
	List<UserDTO> queryUserByDepartId(List<Long> departIds);

	/**
	 * 删除用户
	 * @param uUser
	 * @return
	 */
    Response deleteUser(UUser uUser);

	/**
	 * 根据名称查询机构下所有用户
	 * @param employeeRequest
	 * @return
	 */
	Response queryAllEmployeeByName(EmployeeRequest employeeRequest);

	/**
	 * 机构内根据用户名称查询用户
	 */
	List<UserDTO> getUserByUserName(Long orgId, String userName);

	/**
	 * 查询用户管理渠道机构
	 * @param userDTO
	 * @return
	 */
	List<Long> getUserRelatedChannelOrgIdList(UserDTO userDTO);

	/**
	 * 更新用户状态
	 */
	int updateUserState(String mobile, Integer state);
}
