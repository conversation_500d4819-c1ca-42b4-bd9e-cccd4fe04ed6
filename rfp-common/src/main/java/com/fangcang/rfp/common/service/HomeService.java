package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest;
import com.fangcang.rfp.common.dto.request.ProjectManagementQueryRequest;

/**
 * 首页服务
 */
public interface HomeService {

    /**
     * 酒店待办事项之可参与招标项目
     * @param projectIntentHotelRequest
     * @return
     */
    Response getHotelBackLogJoinProjectCount(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 酒店待办事项之已投标数量
     * @param projectIntentHotelRequest
     * @return
     */
    Response getHotelBackLogBidCount(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 酒店待办事项之投标议价中
     * @param projectIntentHotelRequest
     * @return
     */
    Response getHotelBackLogBargainCount(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 酒店待办事项之待处理合同数量
     * @param projectIntentHotelRequest
     * @return
     */
    Response getHotelBackLogPendingContractCount(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 企业待办事项之已投标酒店数
     * @param projectManagementQueryRequest
     * @return
     */
    Response disBackLogBidHotelCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 企业待办事项之已签约待生成合同数
     * @param projectManagementQueryRequest
     * @return
     */
    Response disBackLogSignNoCreatContractCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 企业待办事项之待处理电子签章
     * @param projectManagementQueryRequest
     * @return
     */
    Response disBackLogSignElectronicContractCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 平台待办事项之待启动招标项目
     * @param projectManagementQueryRequest
     * @return
     */
    Response platBackLogUnStartTendProjectCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 平台待办事项之实名认证/未授权
     * @param projectManagementQueryRequest
     * @return
     */
    Response platBackLogUnAuthCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 平台待办事项之已邀请未投标酒店数量
     * @param projectManagementQueryRequest
     * @return
     */
    Response platBackLogInviteHotelUnBidCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 平台待办事项之待签约合同数
     * @param projectManagementQueryRequest
     * @return
     */
    Response platBackLogUnSignContractCount(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 平台电子签约情况之累计签约合同数
     * @return
     */
    Response platElectronicSignCount();

    /**
     * 平台电子签约情况之企业开通电子签章数
     * @return
     */
    Response platElectronicSignDisCount();

    /**
     * 平台电子签约情况之酒店开通电子签章数
     * @return
     */
    Response platElectronicSignHotelCount();

}
