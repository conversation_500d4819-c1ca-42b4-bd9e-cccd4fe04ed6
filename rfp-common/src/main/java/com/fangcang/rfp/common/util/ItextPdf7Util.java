package com.fangcang.rfp.common.util;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.pdf.OverAreaDTO;
import com.fangcang.rfp.common.dto.pdf.OverTextDTO;
import com.fangcang.rfp.common.enums.HotelCoPayContracTemplatetFieldEnum;
import com.fangcang.rfp.common.enums.HotelContracTemplatetFieldEnum;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.enums.TemplateBizTypeEnum;
import com.fangcang.rfp.common.exception.ServiceException;
import com.fangcang.util.StringUtil;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.PdfCanvasProcessor;
import com.itextpdf.kernel.pdf.canvas.parser.listener.IPdfTextLocation;
import com.itextpdf.kernel.pdf.canvas.parser.listener.RegexBasedLocationExtractionStrategy;
import com.itextpdf.kernel.utils.PdfMerger;
import com.itextpdf.text.pdf.BaseFont;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName ReplaceUtils
 * @Description pdf转换工具类, 基于ItextPdf 7
 * @createTime 2022-10-17 19:15:59
 * @Param
 * @return
 */
public class ItextPdf7Util {

    private static Logger logger = LoggerFactory.getLogger(ItextPdf5Util.class);

    public static Response doOverText(OverTextDTO overTextDTO) throws IOException {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        // 得到转移后的真实占位符
        Map<String, String> keyMap = getKeyWordsMap(overTextDTO.getReplaceMap().keySet());

        List<List<OverAreaDTO>> lists =
                getTextPosition(overTextDTO.getSourceFilePath(), overTextDTO.getFinishFilePath(), keyMap, overTextDTO.getReplaceMap());

        overText(overTextDTO, lists);
        return response;
    }

    /**
     * 为key自动加上占位符并转义, 得到真正的占位符
     *
     * @param keySet
     * @return
     */
    private static Map<String, String> getKeyWordsMap(Set<String> keySet) {
        Map<String, String> keyMap = new HashMap<String, String>();

        //为key自动加上占位符并转义
        for (String key : keySet) {
            keyMap.put(key, "\\$\\<" + key + "\\>");
        }
        return keyMap;
    }

    /**
     * 获取关键字的坐标
     *
     * @param keyMap
     * @return
     */
    public static List<List<OverAreaDTO>> getTextPosition(String sourcePath, String finishPath, Map<String, String> keyMap, Map<String, String> replaceMap) throws IOException {
        List<List<OverAreaDTO>> list = new ArrayList<List<OverAreaDTO>>();
        PdfReader reader = null;
        PdfDocument pdfDocument = null;
        try {
            reader = new PdfReader(sourcePath);
            pdfDocument = new PdfDocument(reader, new PdfWriter(finishPath));

            for (int i = 1; i <= pdfDocument.getNumberOfPages(); i++) {
                List<OverAreaDTO> overAreaDTOS = new ArrayList<OverAreaDTO>();
                for (String key : keyMap.keySet()) {
                    PdfPage page = pdfDocument.getPage(i);
                    RegexBasedLocationExtractionStrategy strategy = new RegexBasedLocationExtractionStrategy(keyMap.get(key));
                    PdfCanvasProcessor canvasProcessor = new PdfCanvasProcessor(strategy);
                    canvasProcessor.processPageContent(page);
                    Collection<IPdfTextLocation> resultantLocations = strategy.getResultantLocations();
                    PdfCanvas pdfCanvas = new PdfCanvas(page);
                    pdfCanvas.setLineWidth(0.5f);

                    for (IPdfTextLocation location : resultantLocations) {
                        Rectangle rectangle = location.getRectangle();
                        pdfCanvas.rectangle(rectangle);
                        pdfCanvas.setStrokeColor(ColorConstants.RED);
                        pdfCanvas.stroke();

                        OverAreaDTO overAreaDTO = new OverAreaDTO();
                        overAreaDTO.setPageNum(location.getPageNumber());
                        overAreaDTO.setX(rectangle.getX());
                        overAreaDTO.setY(rectangle.getY());
                        overAreaDTO.setWidth(rectangle.getWidth());
                        overAreaDTO.setHeight(rectangle.getHeight());
                        overAreaDTO.setKey(key);
                        overAreaDTO.setValue(replaceMap.get(key));
                        overAreaDTOS.add(overAreaDTO);
                    }
                }

                list.add(overAreaDTOS);
            }
        } finally {
            if (null != pdfDocument) {
                pdfDocument.close();
            }
            if (null != reader) {
                reader.close();
            }
        }

        return list;
    }

    /**
     * 覆盖原有的内容 并填充新内容
     *
     * @param overTextDTO 原始参数
     * @param list
     */
    public static void overText(OverTextDTO overTextDTO, List<List<OverAreaDTO>> list) throws IOException {
        PdfDocument pdfDoc = null;
        try {
            pdfDoc = new PdfDocument(new PdfReader(overTextDTO.getSourceFilePath()), new PdfWriter(overTextDTO.getFinishFilePath()));
            if (StringUtil.isValidString(overTextDTO.getImgLogo())) {
                pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE,
                        new PdfHeaderFooterHandler(overTextDTO.getImgLogo(), overTextDTO.getFont()));
            }
            //pdfDoc.getFirstPage().newContentStreamAfter() 会覆盖掉字体
            //pdfDoc.getFirstPage().newContentStreamBefore() 只会在字体的下层添加一个背景色
            for (int i = 1; i <= pdfDoc.getNumberOfPages(); i++) {
                PdfCanvas canvas = new PdfCanvas(pdfDoc.getPage(i).newContentStreamAfter(),
                        pdfDoc.getPage(i).getResources(), pdfDoc);

                canvas.saveState();
                List<OverAreaDTO> overAreaDTOS = list.get(i - 1);
                //用白色背景覆盖原本的字体
                for (OverAreaDTO overArea : overAreaDTOS) {
                    canvas.setFillColor(ColorConstants.WHITE);
                    //覆盖的时候y + 0.35   填充字体的时候 + 1.5 主要就是避免覆盖占位符下面的线
                    canvas.rectangle(overArea.getX(), overArea.getY() + 0.35, overArea.getWidth(), overArea.getHeight());
                    //                canvas.rectangle(overArea.getX(), overArea.getY(), overArea.getWidth(), overArea.getHeight());
                }
                canvas.fill();
                canvas.restoreState();

                if (overTextDTO.isFillWithText()) {
                    //填充新内容
                    canvas.beginText();
                    for (OverAreaDTO overArea : overAreaDTOS) {
                        canvas.setFontAndSize(overTextDTO.getFont(), 11f);
                        canvas.setTextMatrix(overArea.getX(), overArea.getY() + 3f);
                        //                canvas.setTextMatrix(overArea.getX(),overArea.getY());
                        canvas.newlineShowText(overArea.getValue());
                    }
                    canvas.endText();
                }
                if (overTextDTO.isRemoveLastPage() && i == pdfDoc.getNumberOfPages()) {
                    pdfDoc.removePage(i);
                }
            }
        } finally {
            if (null != pdfDoc) {
                pdfDoc.close();
            }
        }
    }


    public static Response checkContractTemplateValid(String sourceFilePath, Integer templateBizType) throws Exception {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        if (StringUtils.isBlank(sourceFilePath)) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("合同模板校验失败," + ReturnResultEnum.PARAMETER_EXCEPTION.message + ":模板路径为空");
            return response;
        }

        if (null == templateBizType) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("合同模板校验失败," + ReturnResultEnum.PARAMETER_EXCEPTION.message + ":模板业务类型为空");
            return response;
        }

        // 合同模板中应有所有模板字段
        Map<String, String> allKeyMap = null;
        if (templateBizType.intValue() == TemplateBizTypeEnum.HOTEL_TRAVEL.key.intValue()) {
            // 合同模板中应有所有模板字段
            allKeyMap = getKeyWordsMap(HotelContracTemplatetFieldEnum.getHotelContracTemplatetFieldMap().keySet());
        } else if (templateBizType.intValue() == TemplateBizTypeEnum.CO_PAY.key.intValue()) {
            allKeyMap = getKeyWordsMap(HotelCoPayContracTemplatetFieldEnum.getHotelCoPayContracTemplatetFieldMap().keySet());
        } else {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("模板校验失败,未找到相应模板业务类型，templateBizType=" + templateBizType);
            return response;
        }

        // 酒店差旅合同模板中实际拥有模板字段
        Set<String> includeKeySet = new HashSet<String>();
        PdfReader reader = new PdfReader(sourceFilePath);
        PdfDocument pdfDocument = new PdfDocument(reader);
        //logger.info("topMargin=" + doc.getTopMargin() + ",rightMargin=" + doc.getRightMargin() + ",bottomMargin=" + doc.getBottomMargin() + ",leftMargin=" + doc.getLeftMargin());
        try {
            for (int i = 1; i <= pdfDocument.getNumberOfPages(); i++) {
                PdfPage page = pdfDocument.getPage(i);
                //logger.info("height=" + (page.getPageSize().getHeight() - doc.getTopMargin() - doc.getBottomMargin()));
                //logger.info("width=" + (page.getPageSize().getWidth() - doc.getRightMargin() - doc.getLeftMargin()));
                for (String key : allKeyMap.keySet()) {
                    RegexBasedLocationExtractionStrategy strategy = new RegexBasedLocationExtractionStrategy(allKeyMap.get(key));
                    PdfCanvasProcessor canvasProcessor = new PdfCanvasProcessor(strategy);
                    canvasProcessor.processPageContent(page);
                    Collection<IPdfTextLocation> resultantLocations = strategy.getResultantLocations();
                    if (null != resultantLocations) {
                        for (IPdfTextLocation location : resultantLocations) {
                            includeKeySet.add(key);
                        }
                    }
                }
            }
            // 如果实际拥有的字段不包含应有字段，则校验失败
            for (String key : allKeyMap.keySet()) {
                if (!includeKeySet.contains(key)) {
                    response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                    String postion = "";
                    if (templateBizType.intValue() == TemplateBizTypeEnum.HOTEL_TRAVEL.key.intValue()) {
                        // 合同模板中应有所有模板字段
                        postion = HotelContracTemplatetFieldEnum.getValueByKey(key);
                    } else if (templateBizType.intValue() == TemplateBizTypeEnum.CO_PAY.key.intValue()) {
                        postion = HotelCoPayContracTemplatetFieldEnum.getValueByKey(key);
                    }
                    response.setMsg(TemplateBizTypeEnum.HOTEL_TRAVEL.value + "模板校验失败,缺失占位符：" + allKeyMap.get(key).replaceAll("\\\\", "") + "，位置：" + postion);
                    return response;
                }
            }
        } finally {
            if (null != pdfDocument) {
                pdfDocument.close();
            }
            if (null != reader) {
                reader.close();
            }
        }
        return response;
    }

    /**
     * @param files        源PDF路径
     * @param destFilePath 合并后输出的PDF文件名
     *                     将多个PDF合并成一个PDF
     */
    public static void mergePDF(String[] files, String destFilePath, String logoPath, PdfFont font) throws Exception {
        PdfDocument pdf = null;
        PdfMerger merger = null;
        PdfReader reader = null;
        PdfDocument currPdfDocument = null;
        try {
            pdf = new PdfDocument(new PdfWriter(destFilePath));
            // 添加页眉
            if (StringUtil.isValidString(logoPath)) {
                pdf.addEventHandler(PdfDocumentEvent.END_PAGE,
                        new PdfHeaderFooterHandler(logoPath, font));
            }
            merger = new PdfMerger(pdf);

            for (int i = 0; i < files.length; i++) {
                reader = new PdfReader(files[i]);
                currPdfDocument = new PdfDocument(reader);
                merger.merge(currPdfDocument, 1, currPdfDocument.getNumberOfPages());
            }
        } finally {
            if (null != currPdfDocument) {
                currPdfDocument.close();
            }
            if (null != reader) {
                reader.close();
            }
            if (null != merger) {
                merger.close();
            }
            if (null != pdf) {
                pdf.close();
            }
        }
    }

    /**
     * 获取合所有同模板空白替换字段Map,用于生成不含关键字定位符的模板
     *
     * @param templateBizType
     * @return
     */
    public static Map<String, String> genContracTemplatetBlankFieldsMap(Integer templateBizType) {
        Map<String, String> replaceMap = new HashMap<String, String>();
        // 酒店差旅合同模板中应有所有模板字段
        Map<String, String> allKeyMap = null;
        if (null == templateBizType) {
            logger.error("获取模板空白替换字段失败，templateBizType=" + templateBizType);
            return replaceMap;
        }
        if (templateBizType.intValue() == TemplateBizTypeEnum.HOTEL_TRAVEL.key.intValue()) {
            allKeyMap = HotelContracTemplatetFieldEnum.getHotelContracTemplatetFieldMap();
            for (String key : allKeyMap.keySet()) {
                replaceMap.put(key, "");
            }
        } else if (templateBizType.intValue() == TemplateBizTypeEnum.CO_PAY.key.intValue()) {
            allKeyMap = HotelCoPayContracTemplatetFieldEnum.getHotelCoPayContracTemplatetFieldMap();
            for (String key : allKeyMap.keySet()) {
                replaceMap.put(key, "");
            }
        }
        return replaceMap;
    }

    /**
     * 生成空白模板(预览时用)
     *
     * @param sourceFilePath  源数据文件(本地临时文件路径)
     * @param finishFilePath  模板数据文件(本地临时文件路径)
     * @param templateBizType 模板业务类型
     * @return
     * @throws IOException
     */
    public static Response genBlankContracTemplatet(String sourceFilePath, String finishFilePath, Integer templateBizType) throws IOException {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        Map<String, String> blankReplaceMap = ItextPdf7Util.genContracTemplatetBlankFieldsMap(templateBizType);
        if (null == blankReplaceMap || blankReplaceMap.keySet().isEmpty()) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("未找到该模板业务类型定义，templateBizType=" + templateBizType + ",sourceFilePath=" + sourceFilePath);
            return response;
        }

        // 生成空白模板(抹掉定位符关键字)
        OverTextDTO overTextDTO = new OverTextDTO();
        overTextDTO.setSourceFilePath(sourceFilePath);
        overTextDTO.setFinishFilePath(finishFilePath);
        overTextDTO.setReplaceMap(blankReplaceMap);
        overTextDTO.setRemoveLastPage(false);
        overTextDTO.setFillWithText(false);
        response = ItextPdf7Util.doOverText(overTextDTO);
        if (ReturnResultEnum.SUCCESS.errorNo.intValue() != response.getResult()) {
            logger.error("生成空白模板文件失败,sourceFilePath=" + sourceFilePath + ",失败原因：" + response.getMsg());
            return response;
        }

        return response;
    }

    public static PdfFont getPdfFont() {
        ClassPathResource cp = new ClassPathResource("font/SIMSUN.TTF");
        File fontPathTempFile = null;
        PdfFont pdfFont = null;
        try {
            fontPathTempFile = File.createTempFile("font", ".TTF");
            FileUtils.copyInputStreamToFile(cp.getInputStream(), fontPathTempFile);
            logger.warn("*******************getPdfFont Path: ************************" + fontPathTempFile.getPath());
            pdfFont = PdfFontFactory.createFont(fontPathTempFile.getPath(), PdfEncodings.IDENTITY_H);
        } catch (Exception e) {
            logger.error("getPdfFont error", e);
            throw new ServiceException("getPdfFont error");
        } finally {
            if (null != fontPathTempFile && fontPathTempFile.exists()) {
                fontPathTempFile.delete();
            }
        }
        return pdfFont;
    }

    public static BaseFont getBaseFont() throws  Exception {
        ClassPathResource cp = new ClassPathResource("font/SIMSUN.TTF");
        File fontPathTempFile = null;
        BaseFont basefont = null;
        try {
            fontPathTempFile = File.createTempFile("font", ".TTF");
            FileUtils.copyInputStreamToFile(cp.getInputStream(), fontPathTempFile);
            logger.warn("*******************getPdfFont Path: ************************" + fontPathTempFile.getPath());
            basefont = BaseFont.createFont(fontPathTempFile.getPath(), BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            logger.error("getBaseFont error", e);
            throw new ServiceException("getBaseFont error");
        } finally {
            if (null != fontPathTempFile && fontPathTempFile.exists()) {
                fontPathTempFile.delete();
            }
        }
        return basefont;
    }

    public static PdfFont getPdfFontFromPath(String filePath) {
        PdfFont pdfFont = null;
        try {
            pdfFont = PdfFontFactory.createFont(filePath, PdfEncodings.IDENTITY_H);
        } catch (Exception e) {
            logger.error("getPdfFontFromPath error", e);
        }
        return pdfFont;
    }

    public static BaseFont getBaseFontFromPath(String filePath) throws  Exception {
        BaseFont basefont = null;
        try {
            basefont = BaseFont.createFont(filePath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            logger.error("getBaseFontFromPath error", e);
        }
        return basefont;
    }
}