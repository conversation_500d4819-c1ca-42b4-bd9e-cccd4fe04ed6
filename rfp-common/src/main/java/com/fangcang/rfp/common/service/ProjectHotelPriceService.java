package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.ProjectHotelPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ProjectHotelPriceService {

    /**
     * 查询价格组
     * @param projectIntentHotelRequest
     * @return
     */
    public ProjectHotelPriceGroupResponse selectProjectHotelPriceGroup(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 查询房价明细
     * @param projectIntentHotelRequest
     * @return
     */
    public List<ProjectHotelPriceResponse> selectProjectHotelPriceList(ProjectIntentHotelRequest projectIntentHotelRequest);


    List<ProjectHotelPrice> selectProjectHotelPriceList(@Param("projectIntentHotelId") Long projectIntentHotelId);

    /**
     *  查询房档明细
     * @param projectIntentHotelRequest
     * @return
     */
    public List<ProjectHotelPriceLevelResponse> selectProjectHotelPriceLevelList(ProjectIntentHotelRequest projectIntentHotelRequest);


    /**
     *  查询项目酒店监控房型列表
     */
    List<MonitorRoomResponse> selectAllMonitorRoomResponseList(Long projectId, Long hotelId);


    /**
     *  查询最低价格项目酒店监控房型
     */
    MonitorRoomResponse selectLowestMonitorRoomResponse(Long projectId, Long hotelId);

    /**
     *  查找默认监控房型
     */
    MonitorRoomResponse selectDefaultMonitorRoomResponse(Long projectId, Long hotelId);


    /**
     * 判断房型是否符合招标项目
     * @param hotelPriceLevelResponseList
     * @param response
     */
    public void isSatisfyProjectTender(Long projectId, List<ProjectHotelPriceLevelResponse> hotelPriceLevelResponseList, Response response);
    /**
     * 查询酒店下最低价房型
     * @param projectIntentHotelId
     * @return
     */
    ProjectHotelLowPriceResponse selectHotelLowestPrice(Long projectIntentHotelId);


    /**
     * 查询是否已存在报价
     * @param hotelId
     * @param projectId
     * @return
     */
    Response existHotelPrice(Long hotelId, Long projectId);

    Response convertToLevelPrice(Long id);


    List<HotelMinPriceResponse> selectProjectIntentLowestHotelPrice(List<Long> projectIntentHotelIdList);

    public Response updateHotelPriceByLastYearBid(Long lastYearProjectId, Long hotelId, Long projectIntentHotelId, UserDTO userDTO);


}
