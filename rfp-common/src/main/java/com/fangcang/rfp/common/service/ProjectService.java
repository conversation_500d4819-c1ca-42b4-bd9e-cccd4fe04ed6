package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.ProjectHotelRemarkResponse;
import com.fangcang.rfp.common.dto.response.QueryCustomTendStrategyResponse;
import com.fangcang.rfp.common.entity.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/15 18:01
 */
public interface ProjectService {

    /**
     * 新增或修改招标基本信息
     *
     * @param project
     * @param isDistributor 是否是企业
     * @return
     */
    Response addOrUpdateProjectBasicInformation(Project project ,boolean isDistributor);

    /**
     * 根据项目id查询招标基本信息
     *
     * @param projectId
     * @return
     */
    Response queryProjectBasicInformation(Long projectId);


    /**
     * 保存项目介绍
     *
     * @param project
     * @return
     */
    Response saveIntroduction(Project project);

    /**
     * 查询项目介绍
     *
     * @param projectId
     * @return
     */
    Response queryIntroduction(Long projectId);

    /**
     * 批量添加项目poi
     *
     * @param batchAddProjectPoiDto
     * @param operator
     * @return
     */
    Response batchAddProjectPoi(BatchAddProjectPoiDto batchAddProjectPoiDto, String operator);


    /**
     * 删除项目poi
     *
     * @param projectId
     * @param poiId
     * @return
     */
    Response deleteProjectPoi(Long projectId, Long poiId);


    /**
     * 查询项目poi信息
     *

     * @return
     */
    Response queryProjectPoiInfo(Long projectId);

    /**
     * 查询项目地图poi信息
     * @return
     */
    Response queryProjectMapPoiInfo(QueryProjectMapPoiInfoRequest queryProjectMapPoiInfoRequest);

    /**
     * 新增邀约酒店
     *
     * @return
     */
    Response addProjectIntentHotel(AddProjectIntentHotelDto addProjectIntentHotelDto);

    /**
     * 修改邀约酒店信息
     *
     * @param updateProjectIntentHotelDto
     * @return
     */
    Response updateProjectIntentHotel(UpdateProjectIntentHotelDto updateProjectIntentHotelDto, UserDTO userDTO);

    /**
     * 查询邀约酒店信息
     *
     * @param queryProjectIntentHotelParam
     * @return
     */
    Response queryProjectIntentHotelInfo(QueryProjectIntentHotelParam queryProjectIntentHotelParam);

    /**
     * 新增或修改酒店项目招标采购策略
     *
     * @param projectHotelTendStrategy
     * @return
     */
    Response addOrUpdateProjectHotelTendStrategy(AddProjectHotelTendStrategyDto projectHotelTendStrategy);

    /**
     * 查询酒店项目招标采购策略
     *
     * @param projectId
     * @return
     */
    Response queryProjectHotelTendStrategy(Long projectId);

    /**
     * 查询酒店项目是否需要校验
     *
     * @return
     */
    Response queryProjectHotelNeedValidate(Long projectIntentId);


    /**
     * 新增或修改酒店项目招标权重配置
     *
     * @param projectHotelTendWeight
     * @return
     */
    Response addOrUpdateProjectHotelTendWeight(AddProjectHotelTendWeightDto projectHotelTendWeight);

    /**
     * 查询酒店项目招标权重配置
     *
     * @param projectId
     * @return
     */
    Response queryProjectHotelTendWeight(Long projectId);

    /**
     * 查询酒店项目招标权重配置 (权重2.0)
     *
     * @param projectId
     * @return
     */
    Response queryProjectWeight(Long projectId);

    /**
     * 查询酒店项目招标权重群组配置 (权重2.0)
     *
     * @return
     */
    Response queryProjectWeightGroup(QueryProjectWeightGroupRequest queryProjectWeightGroupRequest);


    /**
     * 新增酒店项目招标权重群组配置 (权重2.0)
     *
     * @return
     */
    Response addProjectWeightGroup(AddProjectWeightGroupRequest addProjectWeightGroupRequest);


    /**
     * 移除意向酒店
     *
     * @param projectIntentHotelId
     * @return
     */
    Response deleteProjectIntentHotel(Long projectIntentHotelId);

    /**
     * 以邮件形式通知投标人,数据提供
     * @param projectIntentHotelRequestList
     * @return
     */
    Response notifyBidderProvider(List<ProjectIntentHotelRequest> projectIntentHotelRequestList, UserDTO userDTO);

    /**
     * 以邮件形式通知投标人,数据消费
     * type = 1 :消费通知酒店队列
     * type = 2 ：消费邀请酒店列表
     * @param projectIntentHotelRequest
     * @param type
     * @return
     */
    Response notifyBidderConsumer(ProjectIntentHotelRequest projectIntentHotelRequest,Integer type);


    /**
     * 邀请酒店队列消费
     * @param projectIntentHotelRequest
     * @param type
     * @return
     */
    Response inviteHotelConsumer(ProjectIntentHotelRequest projectIntentHotelRequest,Integer type);

    /**
     * 以邮件形式邀请酒店,数据提供
     * @param projectIntentHotelRequestList
     * @return
     */
    Response batchInviteHotelProvider(List<ProjectIntentHotelRequest> projectIntentHotelRequestList);

    /**
     * 发送邮件邀请
     * @param projectIntentHotelRequestList
     * @return
     */
    Response sendEmailInvite(List<ProjectIntentHotelRequest> projectIntentHotelRequestList);


    /**
     * 通过招标机构ID和监控时间查询项目信息
     * @param tenderOrgId
     * @return
     */
    Response selectProjectInfoByOrgIdAndMonitorTime(Long tenderOrgId);

    /**
     * 批量导入邀请酒店
     * @param uploadFile
     * @param operator
     * @param projectId
     * @return
     */
    Response batchAddProjectIntentHotel(MultipartFile uploadFile, String operator, Long projectId) throws Exception;

    /**
     * 邀请酒店模板下载
     * @param httpServletResponse
     */
    void downloadInviteHotelTemplate(HttpServletResponse httpServletResponse);

    /**
     * 新增项目酒店白名单
     * @param projectHotelWhite
     * @return
     */
    Response addProjectHotelWhite(ProjectHotelWhite projectHotelWhite);

    /**
     * 查询项目酒店白名单详情
     * @param queryProjectHotelWhiteRequest
     * @return
     */
    Response queryProjectHotelWhiteDetail(QueryProjectHotelWhiteRequest queryProjectHotelWhiteRequest);

    /**
     * 删除项目酒店白名单
     * @param projectId
     * @param hotelId
     * @return
     */
    Response deleteProjectHotelWhite(Long projectId, Long hotelId,Integer hotelWhiteType);

    /**
     * 新增自定义采购策略
     * @param projectCustomTendStrategy
     * @return
     */
    Response addProjectCustomTendStrategy(ProjectCustomTendStrategy projectCustomTendStrategy);

    /**
     * 删除自定义采购策略
     * @param projectId
     * @param customTendStrategyId
     * @return
     */
    Response deleteProjectCustomTendStrategy(Long projectId, Long customTendStrategyId);

    /**
     * 查询自定义采购策略
     * @param queryCustomTendStrategyRequest
     * @return
     */
    Response queryProjectCustomTendStrategy(QueryCustomTendStrategyRequest queryCustomTendStrategyRequest);

    /**
     * 查询邀请酒店集团
     * @param queryInviteHotelGroupRequest
     * @return
     */
    Response queryInviteHotelGroup(QueryInviteHotelGroupRequest queryInviteHotelGroupRequest);

    /**
     * 新增邀请酒店集团
     * @param addProjectIntentHotelGroupDto
     * @return
     */
    Response addProjectIntentHotelGroup(AddProjectIntentHotelGroupDto addProjectIntentHotelGroupDto);

    /**
     * 修改项目邀请酒店集团品牌限制
     * @param updateHotelGroupIsBrandLimitRequest
     * @return
     */
    Response updateHotelGroupIsBrandLimit(UpdateHotelGroupIsBrandLimitRequest updateHotelGroupIsBrandLimitRequest);


    /**
     * 删除邀请酒店集团
     * @param deleteProjectIntentHotelGroupDto
     * @return
     */
    Response removeProjectIntentHotelGroup(DeleteProjectIntentHotelGroupDto deleteProjectIntentHotelGroupDto);

    /**
     * 发送酒店集团邀请,校验邮箱或手机号信息
     * @param sendHotelGroupInviteDto
     * @return
     */
    Response checkSendHotelGroupInvite(SendHotelGroupInviteDto sendHotelGroupInviteDto);

    /**
     * 发送酒店报价邀请,校验邮箱或手机号信息
     * @return
     */
    Response checkSendHotelBidInvite(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 发送酒店集团邀请
     * @param sendHotelGroupInviteDto
     * @return
     */
    Response batchSendHotelGroupInvite(SendHotelGroupInviteDto sendHotelGroupInviteDto);

    /**
     *
     * @param projectId
     * @param orgId
     * @return
     */
    Response inviteHotelGroupConsumer(Long projectId, Long orgId);

    /**
     * 查询项目信息
     * @param queryProjectNameRequest
     * @return
     */
    Response queryProjectName(QueryProjectNameRequest queryProjectNameRequest);

    /**
     * 查询机构历史项目信息 （去年，10条）
     * @param projectId
     * @return
     */
    Response queryHistoryProjectInfo(Long projectId);

    /**
     * 查询机构历史项目信息列表
     * @param queryHistoryProjectInfoRequest
     * @return
     */
    Response queryHistoryProjectInfoList(QueryHistoryProjectInfoRequest queryHistoryProjectInfoRequest);

    Response queryRelateProjectInfo(QueryRelatedProjectInfoRequest queryRelatedProjectInfoRequest);


    /**
     * 绑定历史项目
     * @param bindHistoryProjectRequest
     * @return
     */
    Response bindHistoryProject(BindHistoryProjectRequest bindHistoryProjectRequest);

    /**
     * 修改历史项目 间夜 成交金额
     * @param editHistoryProjectRequest
     * @return
     */
    Response editHistoryProject(EditHistoryProjectRequest editHistoryProjectRequest);


    /**
     * 删除历史项目
     * @param deleteHistoryProjectRequest
     * @return
     */
    Response deleteHistoryProject(DeleteHistoryProjectRequest deleteHistoryProjectRequest);


    /**
     * 下载历史项目导入模板
     *
     */
    public void downloadHistoryProjectTemplate(HttpServletResponse httpServletResponse);

    /**
     * 下载项目意向酒店模板
     *
     */
    public void downloadProjectInviteHotelTemplate(HttpServletResponse httpServletResponse);

    /**

    /**
     * 批量导入历史项目
     * @param uploadFile
     * @param operator
     * @param projectId
     * @return
     */
    Response batchAddHistoryProject(MultipartFile uploadFile, String operator, Long projectId) throws Exception;

    /**
     * 批量导入项目邀请酒店集团酒店ID集合
     */
    Response importProjectInviteHotelGroupHotelIds(MultipartFile uploadFile, String operator, Long projectId) throws Exception;

    /**
     * 生产历史项目统计
     */
    Response generateHistoryProjectStat(GenerateHistoryProjectStatRequest generateHistoryProjectStatRequest) throws Exception;

    /**
     * 更新酒店推荐等级
     */
    Response generateHotelRecommendLevel(UserDTO userDTO, GenerateHistoryProjectStatRequest generateHistoryProjectStatRequest) throws Exception;


    /**
     * 快速邀约接口
     * @param quickInviteProjectIntentHotelDto
     * @return
     */
    public Response quickInviteProjectIntentHotel(QuickInviteProjectIntentHotelDto quickInviteProjectIntentHotelDto);

    /**
     * 生成导出报价备注信息
     * @param projectHotelRemarkResponseList
     * @return
     */
    public String generateProjectExportBidRemark(List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList);


    /**
     * 调整自定义采购策略排序
     */
    public Response switchProjectCustomTendStrategyDisplayOrder(UserDTO userDTO, Long fromTendStrategyId, Long toTendStrategyId);



    public Response deleteProjectIntentHotels(List<Long> projectIntentHotelIdList);


    public List<QueryCustomTendStrategyResponse> queryProjectCustomTendStrategy(Long projectId);

    /**
     * 新增或者修改项目权重 权重2.0
     */

    public Response addOrUpdateProjectWeight(AddProjectWeightDto addProjectWeightDto);

    // 删除项目权重群组配置

    public Response deleteProjectWeightGroup(DeleteProjectWeightHotelGroupRequest deleteProjectWeightHotelGroupRequest);

    int updateProjectWeight(Project project);
}
