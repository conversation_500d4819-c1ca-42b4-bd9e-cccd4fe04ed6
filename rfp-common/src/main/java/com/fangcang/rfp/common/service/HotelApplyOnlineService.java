package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.QueryHotelApplyOnlineRequest;
import com.fangcang.rfp.common.dto.request.UpdateHotelApplyOnlineDto;
import com.fangcang.rfp.common.dto.response.HotelImageInfoResponse;

/**
 * <AUTHOR>
 * @date 2023/7/6 16:15
 */
public interface HotelApplyOnlineService {

    /**
     * 查询协议酒店申请上线列表
     *
     * @param queryHotelApplyOnlineRequest
     * @return
     */
    Response queryHotelApplyOnlineList(QueryHotelApplyOnlineRequest queryHotelApplyOnlineRequest, UserDTO userDTO);

    /**
     * 查询协议酒店申请上线详情
     *
     * @param hotelOnlineId
     * @return
     */
    Response queryHotelApplyOnlineDetail(Long hotelOnlineId);

    /**
     * 修改协议酒店上线
     *
     * @param updateHotelApplyOnlineDto
     * @return
     */
    Response updateHotelApplyOnline(UpdateHotelApplyOnlineDto updateHotelApplyOnlineDto);

    /**
     * 查询协议酒店上线申请日志
     *
     * @param hotelOnlineId
     * @return
     */
    Response queryHotelApplyOnlineLog(Long hotelOnlineId);

    /**
     * 统计酒店报送数据
     *
     * @param userDTO
     * @return
     */
    Response statisticalHotelApplyOnlineCount(UserDTO userDTO);

    /**
     * 查询所有图片
     * @param hotelImageInfoResponse
     * @throws Exception
     */
    void queryAllImageUrl(HotelImageInfoResponse hotelImageInfoResponse) throws Exception;
}
