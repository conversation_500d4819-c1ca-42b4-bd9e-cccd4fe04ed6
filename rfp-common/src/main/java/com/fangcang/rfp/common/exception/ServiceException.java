package com.fangcang.rfp.common.exception;

import com.fangcang.rfp.common.enums.ReturnResultEnum;

/**
 * <AUTHOR>
 * @ClassName ParameterException
 * @Description 业务异常类
 * @createTime 2022-08-30 16:57:16
 * @Param
 * @return
 */
@SuppressWarnings("serial")
public class ServiceException extends RuntimeException {

    /**
     * 异常编码
     */
    private ReturnResultEnum errorCode;

    public ReturnResultEnum getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(ReturnResultEnum errorCode) {
        this.errorCode = errorCode;
    }

    public ServiceException() {
        super();
    }

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public ServiceException(Throwable cause) {
        super(cause);
    }

    public ServiceException(ReturnResultEnum errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public ServiceException(ReturnResultEnum errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

}
