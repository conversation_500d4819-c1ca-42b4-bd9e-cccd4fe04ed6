package com.fangcang.rfp.common.service.impl;

import com.fangcang.rfp.common.dao.OrgDao;
import com.fangcang.rfp.common.dao.OrgHotelGroupContactUserDao;
import com.fangcang.rfp.common.dao.OrgHotelGroupUserLogDao;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.OperateEnterpriseOrgHotelGroupContactUserDto;
import com.fangcang.rfp.common.dto.request.OrgHotelGroupContactUserQueryRequest;
import com.fangcang.rfp.common.dto.response.OrgHotelGroupContactUserQueryResponse;
import com.fangcang.rfp.common.dto.response.OrgHotelGroupUserLogResponse;
import com.fangcang.rfp.common.dto.response.QueryOrgFollowHotelResponse;
import com.fangcang.rfp.common.entity.Org;
import com.fangcang.rfp.common.entity.OrgFollowHotel;
import com.fangcang.rfp.common.entity.OrgHotelGroupContactUser;
import com.fangcang.rfp.common.entity.OrgHotelGroupUserLog;
import com.fangcang.rfp.common.enums.OrgTypeEnum;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.service.OrgHotelGroupContactUserService;
import com.fangcang.rfp.common.service.UserService;
import com.fangcang.rfp.common.util.PageUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class OrgHotelGroupContactUserServiceImpl implements OrgHotelGroupContactUserService {

    @Autowired
    private OrgHotelGroupContactUserDao orgHotelGroupContactUserDao;
    @Autowired
    private OrgHotelGroupUserLogDao orgHotelGroupUserLogDao;
    @Autowired
    private OrgDao orgDao;
    @Autowired
    private UserService userService;

    @Override
    public Response add(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto) {
        // 定义返回值
        Response response = new Response();

        // 查询用户是否属于渠道商员工
        OrgHotelGroupContactUserQueryRequest orgHotelGroupContactUserQueryRequest = new OrgHotelGroupContactUserQueryRequest();
        orgHotelGroupContactUserQueryRequest.setEnterpriseOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        orgHotelGroupContactUserQueryRequest.setHotelGroupOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        List<OrgHotelGroupContactUserQueryResponse> orgHotelGroupContactUserList = orgHotelGroupContactUserDao.queryOrgHotelGroupContactUserList(orgHotelGroupContactUserQueryRequest);
        if(CollectionUtils.isNotEmpty(orgHotelGroupContactUserList)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("企业已经新增该酒店集团大客户经理");
            return response;
        }

        // 检查是否为企业机构
        Org org = orgDao.selectByPrimaryKey(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        if(org == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("当前企业机构不存在");
            return response;
        }
        if(!Objects.equals(org.getOrgType(), OrgTypeEnum.DISTRIBUTOR.key)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("当前企业机构不是企业机构类型");
            return response;
        }

        Org hotelGroupOrg = orgDao.selectByPrimaryKey(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        if(hotelGroupOrg == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("当前酒店集团机构不存在");
            return response;
        }
        if(!Objects.equals(hotelGroupOrg.getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("当前酒店集团机构不是酒店集团机构");
            return response;
        }

        int count = orgHotelGroupContactUserDao.selectCount(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId(),
                operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId(),
                operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupUserId());
        if(count > 0){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("已经存在企业对应酒店集团大客户经理");
            return response;
        }
        OrgHotelGroupContactUser orgHotelGroupContactUser = new OrgHotelGroupContactUser();
        orgHotelGroupContactUser.setEnterpriseOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        orgHotelGroupContactUser.setHotelGroupOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        orgHotelGroupContactUser.setHotelGroupUserId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupUserId());
        orgHotelGroupContactUser.setCreator(operateEnterpriseOrgHotelGroupContactUserDto.getOperator());
        orgHotelGroupContactUser.setCreateTime(new Date());
        orgHotelGroupContactUser.setModifier(operateEnterpriseOrgHotelGroupContactUserDto.getOperator());
        int insertResult = orgHotelGroupContactUserDao.insertSelective(orgHotelGroupContactUser);
        if(insertResult == 1){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);

            // Record log
            recordOrgHotelGroupUserLog(orgHotelGroupContactUser, "ADD", operateEnterpriseOrgHotelGroupContactUserDto.getOperator());

        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("新增失败");
        }
        return response;
    }

    private void recordOrgHotelGroupUserLog(OrgHotelGroupContactUser orgHotelGroupContactUser, String operate, String creator){
        OrgHotelGroupUserLog orgHotelGroupUserLog = new OrgHotelGroupUserLog();
        BeanUtils.copyProperties(orgHotelGroupContactUser, orgHotelGroupUserLog);
        orgHotelGroupUserLog.setOperate(operate);
        orgHotelGroupUserLog.setCreator(creator);
        orgHotelGroupUserLogDao.insert(orgHotelGroupUserLog);
    }
    @Override
    public Response delete(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto) {
        // 定义返回值
        Response response = new Response();
        OrgHotelGroupContactUserQueryRequest orgHotelGroupContactUserQueryRequest = new OrgHotelGroupContactUserQueryRequest();
        orgHotelGroupContactUserQueryRequest.setEnterpriseOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        orgHotelGroupContactUserQueryRequest.setHotelGroupOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        List<OrgHotelGroupContactUserQueryResponse> orgHotelGroupContactUserQueryResponseList = orgHotelGroupContactUserDao.queryOrgHotelGroupContactUserList(orgHotelGroupContactUserQueryRequest);
        if(CollectionUtils.isEmpty(orgHotelGroupContactUserQueryResponseList)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        }
        orgHotelGroupContactUserDao.deleteByOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId(), operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        // Record log
        OrgHotelGroupContactUserQueryResponse orgHotelGroupContactUser = orgHotelGroupContactUserQueryResponseList.get(0);
        recordOrgHotelGroupUserLog(orgHotelGroupContactUser, "DELETE", operateEnterpriseOrgHotelGroupContactUserDto.getOperator());

        // 定义返回值
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;

    }

    @Override
    public Response update(OperateEnterpriseOrgHotelGroupContactUserDto operateEnterpriseOrgHotelGroupContactUserDto) {
        // 定义返回值
        Response response = new Response();
        OrgHotelGroupContactUserQueryRequest orgHotelGroupContactUserQueryRequest = new OrgHotelGroupContactUserQueryRequest();
        orgHotelGroupContactUserQueryRequest.setEnterpriseOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        orgHotelGroupContactUserQueryRequest.setHotelGroupOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        List<OrgHotelGroupContactUserQueryResponse> orgHotelGroupContactUserQueryResponseList = orgHotelGroupContactUserDao.queryOrgHotelGroupContactUserList(orgHotelGroupContactUserQueryRequest);
        if(CollectionUtils.isEmpty(orgHotelGroupContactUserQueryResponseList)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("没有找到企业对应大客户经理");
            return response;
        }
        OrgHotelGroupContactUser orgHotelGroupContactUser = new OrgHotelGroupContactUser();
        orgHotelGroupContactUser.setEnterpriseOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getEnterpriseOrgId());
        orgHotelGroupContactUser.setHotelGroupOrgId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupOrgId());
        orgHotelGroupContactUser.setHotelGroupUserId(operateEnterpriseOrgHotelGroupContactUserDto.getHotelGroupUserId());
        orgHotelGroupContactUser.setModifier(operateEnterpriseOrgHotelGroupContactUserDto.getOperator());

        int updateResult = orgHotelGroupContactUserDao.updateHotelGroupUserId(orgHotelGroupContactUser);
        if(updateResult == 1) {
            // Record log
            recordOrgHotelGroupUserLog(orgHotelGroupContactUser, "UPDATE", operateEnterpriseOrgHotelGroupContactUserDto.getOperator());
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
            return response;
        }
    }

    @Override
    public Response queryOrgHotelGroupContactUserList(OrgHotelGroupContactUserQueryRequest request) {
        // 定义返回值
        Response response = new Response();
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<OrgHotelGroupContactUserQueryResponse> responseList = orgHotelGroupContactUserDao.queryOrgHotelGroupContactUserList(request);
        PageResult<OrgHotelGroupContactUserQueryResponse> pageResult = PageUtil.makePageResult(responseList);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response queryOrgHotelGroupContactUserLogList(OrgHotelGroupContactUserQueryRequest request) {
        // 定义返回值
        Response response = new Response();
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<OrgHotelGroupUserLogResponse> responseList = orgHotelGroupUserLogDao.queryLogList(request.getEnterpriseOrgId(), request.getHotelGroupOrgId());
        PageResult<OrgHotelGroupUserLogResponse> pageResult = PageUtil.makePageResult(responseList);

        Map<Long, Org> orgMap = new HashMap<>();
        Map<Long, String> userMap = new HashMap<>();
        pageResult.getList().forEach(item -> {
            if(!orgMap.containsKey(item.getEnterpriseOrgId())){
                Org org = orgDao.selectByPrimaryKey(item.getEnterpriseOrgId());
                orgMap.put(org.getOrgId(), org);
            }
            if(!orgMap.containsKey(item.getHotelGroupOrgId())){
                Org org = orgDao.selectByPrimaryKey(item.getHotelGroupOrgId());
                orgMap.put(org.getOrgId(), org);
            }
            if(!userMap.containsKey(item.getHotelGroupUserId())){
                String userName = userService.selectByPrimaryKey(item.getHotelGroupUserId()).getUserName();
                userMap.put(item.getHotelGroupUserId(), userName);
            }
            item.setEnterpriseOrgName(orgMap.get(item.getEnterpriseOrgId()).getOrgName());
            item.setHotelGroupOrgName(orgMap.get(item.getHotelGroupOrgId()).getOrgName());
            item.setHotelGroupUserName(userMap.get(item.getHotelGroupUserId()));
        });

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    public OrgHotelGroupContactUserQueryResponse queryOrgHotelGroupContactUser(Long enterpriseOrgId, Long hotelGroupOrgId) {
        OrgHotelGroupContactUserQueryRequest queryRequest = new OrgHotelGroupContactUserQueryRequest();
        queryRequest.setEnterpriseOrgId(enterpriseOrgId);
        queryRequest.setHotelGroupOrgId(hotelGroupOrgId);
        List<OrgHotelGroupContactUserQueryResponse> queryResponsesList = orgHotelGroupContactUserDao.queryOrgHotelGroupContactUserList(queryRequest);
        return CollectionUtils.isNotEmpty(queryResponsesList) ? queryResponsesList.get(0) : null;
    }
}
