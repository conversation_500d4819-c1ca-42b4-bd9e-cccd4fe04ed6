package com.fangcang.rfp.common.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ProjectCategoryWeightVO implements java.io.Serializable{

    private static final long serialVersionUID = -4519446338175855582L;

    // 权重分类编号
    private String categoryCode;

    // 权重分类名称
    private String categoryName;

    // 最小权重
    private BigDecimal minWeight;

    // 最大权重
    private BigDecimal maxWeight;

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public BigDecimal getMinWeight() {
        return minWeight;
    }

    public void setMinWeight(BigDecimal minWeight) {
        this.minWeight = minWeight;
    }

    public BigDecimal getMaxWeight() {
        return maxWeight;
    }

    public void setMaxWeight(BigDecimal maxWeight) {
        this.maxWeight = maxWeight;
    }
}
