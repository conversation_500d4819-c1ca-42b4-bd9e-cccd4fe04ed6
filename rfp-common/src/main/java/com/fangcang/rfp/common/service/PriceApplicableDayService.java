package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.PriceApplicableDayDto;
import com.fangcang.rfp.common.dto.request.PriceUnapplicableDayDto;
import com.fangcang.rfp.common.dto.request.ProjectHotelBidStrategyRequest;
import com.fangcang.rfp.common.entity.PriceApplicableDay;
import com.fangcang.rfp.common.entity.PriceUnapplicableDay;

import java.util.List;

public interface PriceApplicableDayService {

    /**
     *
     * @param priceApplicableDayList
     * @return
     */
    Integer insertOrUpdateProjectHotelPriceApplicableDay(Boolean isInsert, Long projectId, Long hotelId, List<PriceApplicableDay> priceApplicableDayList, String operator);

    /**
     * 查询可用日期
     * @param priceApplicableDay
     * @return
     */
    Response selectPriceApplicableDayList(PriceApplicableDay priceApplicableDay);


    /**
     *
     * @param priceApplicableDay
     * @return
     */
    Response insertHotelPriceApplicableDay(PriceApplicableDay priceApplicableDay, String operator, boolean isUploadBid);

    /**
     * 查询项目可用日期
     */
    Response selectPriceApplicableDayListByProject(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest, boolean isNeedInit);

    /**
     * 编辑可用日期
     * @return
     */
    Response updatePriceApplicableDay(PriceApplicableDay priceApplicableDay);

    /**
     * 删除可用日期
     * @return
     */
    Response deletePriceApplicableDay(PriceApplicableDay priceApplicableDay);


    /**
     * 新增可用日期列表
     */
    Response batchInsertPriceApplicableDayDto(PriceApplicableDayDto priceApplicableDayDto,  boolean isUploadBid);

    /**
     * 根据价格码查询适用日期列表
     * @param priceCode
     * @return
     */
    List<PriceApplicableDay> selectPriceApplicableDayListByPriceCode(Long priceCode);

    public Response comparePriceApplicableDay(List<PriceApplicableDay> priceApplicableDayList, boolean isUploadBid);

    public Long selectNextApplicableDayId();




}
