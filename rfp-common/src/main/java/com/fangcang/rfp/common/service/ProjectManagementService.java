package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.QueryProjectHotelPriceDetailResponse;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/10/20 18:33
 */
public interface ProjectManagementService {

    /**
     * 企业和运营平台查询项目信息
     *
     * @param projectManagementQueryRequest
     * @return
     */
    Response enterpriseAndPlatformQueryProjectInfo(ProjectManagementQueryRequest projectManagementQueryRequest);

    /**
     * 修改项目状态
     *
     * @param updateProjectStateDto
     * @return
     */
    Response updateProjectState(UpdateProjectStateDto updateProjectStateDto);

    /**
     * 查询评标详情
     * @param projectId
     * @return
     */
    Response queryBidHeaderDetails(Long projectId);

    /**
     * 查询项目投标信息
     * @param bidHotelInfoQueryRequest
     * @return
     */
    Response queryBidHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception;
    /**
     * 查询地图项目投标信息
     * @param bidHotelInfoQueryRequest
     * @return
     */
    public Response queryMapBidHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception;

    /**
     * 查询地图项目投标信息
     * @param queryMapBidHotelInfoStatRequest
     * @return
     */
    public Response queryMapBidHotelInfoStat(QueryMapBidHotelInfoStatRequest queryMapBidHotelInfoStatRequest) throws Exception;


    /**
     * 查询地图推荐酒店列表
     * @param bidHotelInfoQueryRequest
     * @return
     */
    public Response queryMapRecommendHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception;


    /**
     * 查询酒店和POI坐标信息
     */
    Response queryMapHotelPoiBidHotelInfo(QueryMapPoiBidHotelInfoRequest queryMapHotelPoiBidHotelInfoRequest) throws Exception;
    /**
     * 查询项目状态统计
     */
    public Response queryGroupByHotelBidState(BidHotelInfoQueryRequest bidHotelInfoQueryRequest);

    /**
     * 修改项目主体信息
     * @param updateProjectIntentHotelDto
     * @return
     */
    Response updateProjectSubjectInfo(UpdateProjectIntentHotelDto updateProjectIntentHotelDto);

    /**
     * 修改报价信息
     * @param updateBidOrgInfoRequest
     * @return
     */
    Response updateBidOrgInfo(UpdateBidOrgInfoRequest updateBidOrgInfoRequest, UserDTO userDTO);


    /**
     * 项目详情
     * @param projectId
     * @return
     */
    Response queryProjectDetails(Long projectId);

    /**
     * 导出报价
     * @param projectId
     * @param request
     * @param response
     */
    InputStream exportTenderPrice(Long projectId , HttpServletRequest request, HttpServletResponse response) throws Exception;


    /**
     * 导出酒店集团报价
     * @param projectId
     * @param request
     * @param response
     */
    InputStream exportHotelGroupTenderPrice(Long projectId , UserDTO userDTO, HttpServletRequest request, HttpServletResponse response) throws Exception;

    /**
     * 导出报价迁移
     * @param projectId
     * @param request
     * @param response
     */
    InputStream exportTenderPriceForTransfer(Long projectId , HttpServletRequest request, HttpServletResponse response) throws Exception;


    /**
     * 查询报价详情
     * @param projectHotelBidStrategyRequest
     * @param queryProjectHotelPriceDetailResponse
     */
    Response queryPriceDetail(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest, QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse);


    /**
     * 修改报价联系人信息
     */
    Response updateBidContactInfo(UpdateBidContactInfoRequest updateBidContactInfoRequest, UserDTO userDTO);

    /**
     * 修改酒店集团报价联系人信息
     */
    Response updateHotelGroupBidContactInfo(UpdateHotelGroupBidContactInfoRequest updateHotelGroupBidContactInfoRequest, UserDTO userDTO);

    /**
     * 修改报价签约主体信息
     */
    Response updateBidHotelSubjectInfo(UpdateBidHotelSubjectInfoRequest updateBidHotelSubjectInfoRequest, UserDTO userDTO);
    /**
     * 锁定价格组
     */
    Response updatePriceGroupLocked(UpdatePriceGroupLockedRequest updatePriceGroupLockedRequest, UserDTO userDTO);

    /**
     * 修改报价日期
     */
    Response updateBidDay(UpdateBidDayRequest updateBidDayRequest, UserDTO userDTO);


    /**
     * 修改报价房型
     */
    Response updateBidLevelRoom(UpdateBidLevelRoomRequest updateBidLevelRoomRequest, UserDTO userDTO);

    /**
     * 上传凭证
     */
    Response updateCertsUrl(UpdateCertsUrlRequest urlRequest, UserDTO userDTO);
    /**
     * 查询地图酒店详情

     */
    Response queryMapHotelPriceDetail(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest);

    /**
     * 查询地图推荐酒店详情

     */
    Response queryMapRecommendHotelPriceDetail(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest);

    /**
     * 查询地图房档价格
     */
    Response queryMapHotelBidLevelPrice(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest);
    /**
     * 查询地图酒店报价统计

     */
    Response queryMapHotelPriceBidStat(ProjectMapHotelPriceStatRequest projectMapHotelPriceDetailRequest);

    /**
     * 查询地图POI报价统计

     */
    Response queryMapPoiBidStat(ProjectMapPoiStatRequest projectMapPoiStatRequest);



    /**
     * 酒店未报价管理
     *
     * @param queryUnpricedManagementRequest
     * @return
     */
    Response queryUnpricedHotelManagement(QueryUnpricedManagementRequest queryUnpricedManagementRequest);

    /**
     * 报价状态处理
     *
     * @param processingBidStateRequest
     * @return
     */
    Response processingBidState(ProcessingBidStateRequest processingBidStateRequest);

    /**
     * 批量注册酒店机构
     *
     * @param batchHotelRegisterRequest
     * @return
     */
    Response batchHotelRegister(BatchHotelRegisterRequest batchHotelRegisterRequest);

    /**
     * 未报价指派平台跟进人
     *
     * @param assignPlatformUserRequest
     * @return
     */
    Response assignPlatformUser(AssignPlatformUserRequest assignPlatformUserRequest);


    /**
     * 查询酒店履约详情
     */
    Response queryPerformanceInfo(QueryPerformanceInfoRequest queryPerformanceInfoRequest);

    /**
     * 导出酒店履约情况
     */
    void exportPerformanceInfo(QueryPerformanceInfoRequest queryPerformanceInfoRequest, HttpServletResponse response) throws IOException;

    /**
     * 查询热力图报价信息
     * @param queryMapHotelPoiBidHotelInfoRequest
     * @return
     */
    public Response queryHeatMapBidInfo(QueryMapPoiBidHotelInfoRequest queryMapHotelPoiBidHotelInfoRequest)throws Exception;

    /**
     * 下载签约状态导入模版
     */
    public void downloadProjectBidStateTemplate(HttpServletResponse httpServletResponse);

    /**
     * 导入签约状态
     */
    public Response importHotelBidState(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws Exception;

    /**
     * 下载签约房型模版
     */
    public void downloadProjectBidRoomTypeTemplate(HttpServletResponse httpServletResponse);

    /**
     * 导入批量更新签约房型
     */
    public Response importHotelBidRoomType(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws Exception;

    /**
     * 下载批量更新联系信息及承诺信息模板
     */
    void downloadProjectBidContactInfoAndPromiseTemplate(HttpServletResponse httpServletResponse);

    /**
     * 导入批量更新联系信息及承诺信息
     */
    Response importProjectBidContactInfoAndCommitment(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws IOException, InvalidFormatException;
}
