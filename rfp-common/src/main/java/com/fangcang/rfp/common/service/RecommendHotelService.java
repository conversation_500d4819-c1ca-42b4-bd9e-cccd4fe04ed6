package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 14:20
 */
public interface RecommendHotelService {

    /**
     * 添加推荐酒店
     *
     * @param recommendHotelParam
     * @return
     */
    Response addRecommendHotel(RecommendHotelParam recommendHotelParam);

    /**
     * 修改推荐酒店
     *
     * @param recommendHotelParam
     * @return
     */
    Response updateRecommendHotel(RecommendHotelParam recommendHotelParam);

    /**
     * 删除推荐酒店
     *
     * @param hotelId
     * @param operator
     * @return
     */
    Response removeRecommendHotel(Long hotelId, String operator);

    /**
     * 修改推荐酒店
     *
     * @param queryRecommendHotelParam
     * @return
     */
    Response queryRecommendHotelInfo(QueryRecommendHotelParam queryRecommendHotelParam);

    /**
     * 查询酒店设施
     *
     * @param hotelId
     * @return
     */
    Response queryHotelFacilities(Long hotelId) throws Exception;

    /**
     * 查询邀请酒店
     *
     * @param queryInvitationHotelParam
     * @return
     */
    Response queryInvitationHotelInfo(QueryInvitationHotelParam queryInvitationHotelParam);

    /**
     * 查询邀请酒店集团酒店
     *
     * @param queryInvitationHotelGroupHotelRequest
     * @return
     */
    Response queryInvitationHotelGroupHotel(QueryInvitationHotelGroupHotelRequest queryInvitationHotelGroupHotelRequest);


    /**
     * 删除邀请酒店集团酒店
     *
     * @param deleteInvitationHotelGroupHotelRequest
     * @return
     */
    Response deleteInvitationHotelGroupHotel(DeleteInvitationHotelGroupHotelRequest deleteInvitationHotelGroupHotelRequest);


    /**

    /**
     * 查询品牌或集团
     *
     * @param queryGroupOrBrandInfoParam
     * @return
     */
    Response queryGroupOrBrandInfo(QueryGroupOrBrandInfoParam queryGroupOrBrandInfoParam);

    /**
     * 查询酒店集团品牌
     * @param queryHotelGroupBrandRequest
     * @return
     */
    Response queryHotelGroupBrand(QueryHotelGroupBrandRequest queryHotelGroupBrandRequest);

    /**
     * 添加意向酒店
     *
     * @param addIntentHotelParam
     * @return
     */
    Response addIntentHotelCache(AddIntentHotelParam addIntentHotelParam);

    /**
     * 查询意向酒店缓存
     * @param orgId
     * @return
     */
    Response queryIntentHotelCache(Long orgId);

    /**
     * 删除意向酒店缓存
     * @param orgId
     * @param hotelId
     * @return
     */
    Response deleteIntentHotelCache(Long orgId ,Long hotelId);

    /**
     * 查询起价
     * @param hotelIds
     * @return
     */
    Response queryHotelLowestPrice(List<Long> hotelIds);

    /**
     * 查询酒店图片
     * @param hotelIds
     * @return
     */
    Response queryHotelImage(List<Long> hotelIds) throws Exception;

    /**
     * 根据酒店id查询推荐酒店信息
     * @param hotelId
     * @return
     */
    Response queryRecommendHotel(Long hotelId);
}
