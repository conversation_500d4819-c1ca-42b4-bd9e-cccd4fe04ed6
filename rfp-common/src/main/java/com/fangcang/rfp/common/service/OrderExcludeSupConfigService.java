package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.ModifyPriceRoomDto;
import com.fangcang.rfp.common.dto.request.OrderExcludeSupplierConfigRequest;
import com.fangcang.rfp.common.dto.request.OrderMonitorConfigRequest;
import com.fangcang.rfp.common.entity.OrderExcludeSupConfig;
import com.fangcang.rfp.common.entity.OrderMonitorConfig;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单监控供应商设置服务
 */
public interface OrderExcludeSupConfigService {

    Response insertOrderExcludeSupConfig(OrderExcludeSupConfig record);

    Response deleteOrderExcludeSupConfig(OrderExcludeSupConfig record);

    /**
     * 查询分销商列表
     * @param orderExcludeSupplierConfigRequest
     * @return
     */
    Response selectSupplierCodeList(OrderExcludeSupplierConfigRequest orderExcludeSupplierConfigRequest);


}
