package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.response.ProjectHotelPriceGroupResponse;
import com.fangcang.rfp.common.dto.response.ProjectHotelPriceLevelResponse;
import com.fangcang.rfp.common.entity.ProjectHotelTendStrategy;
import com.fangcang.rfp.common.entity.ProjectIntentHotel;

public interface ProjectHotelPriceGroupService {


    /**
     * 新增房档
     * @param projectHotelPriceLevelResponse
     * @return
     */
    public Response insertProjectHotelPriceLevel(ProjectIntentHotel projectIntentHotel, ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse, String creator);

    /**
     * 新增房价群组
     * @return
     */
    public Response insertProjectHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, String creator);

    /**
     * 修改房价群组
     * @return
     */
    public Response updateProjectHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse,
                                                 UserDTO userDTO);

    /**
     * 删除房价群组
     * @return
     */
    public Response deleteProjectHotelPriceGroup(Long hotelPriceGroupId, UserDTO userDTO);

    public void insertHotelPriceGroup(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse, String creator, boolean isValidateGroupKey);

    public long getNextHotelPriceGroupId();

    public String validateCancelRestrict(ProjectHotelTendStrategy projectHotelTendStrategy, ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse);


}
