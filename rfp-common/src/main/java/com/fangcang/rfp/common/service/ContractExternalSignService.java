package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.request.ContractExternalDto;
import com.fangcang.rfp.common.entity.ContractExternal;
import com.fangcang.tmc.delivery.api.remote.request.ContractSignRequest;
import com.fangcang.tmc.delivery.api.remote.response.ContractExternalResponse;

/**
 * 合同签署服务
 *
 * <AUTHOR>
 * @since 2024/3/2
 */
public interface ContractExternalSignService {


    /**
     * 查询合同
     * @param contractCode
     * @return
     */
    ContractExternalResponse queryContract(String contractCode);

    /**
     * 提交合同在线签章
     * @param request
     * @return
     */
    boolean submitSignTask(ContractSignRequest request);

    /**
     * 在线签章
     * @param contract
     * @throws Exception
     */
    void onlineSignature(ContractExternal contract) throws Exception;

    /**
     * 合同归档
     * @param contractCode
     * @param modifier
     * @return
     */
    boolean contractFiling(String contractCode,String modifier);

    /**
     * 更新合同
     * @param contractDto
     */
    void updateContractByCode(ContractExternalDto contractDto);
}
