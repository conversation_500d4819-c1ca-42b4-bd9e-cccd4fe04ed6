package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ModifyPriceRoomDto;
import com.fangcang.rfp.common.dto.request.OrderMonitorConfigRequest;
import com.fangcang.rfp.common.entity.OrderMonitorConfig;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单监控设置服务
 */
public interface OrderMonitorConfigService {

    Response insertOrderMonitorConfig(OrderMonitorConfig record);

    Response deleteOrderMonitorConfig(OrderMonitorConfig record);

    /**
     * 查询订单监控设置列表
     * @param orderMonitorConfigRequest
     * @return
     */
    Response selectOrderMonitorConfigList(OrderMonitorConfigRequest orderMonitorConfigRequest);

    /**
     * 查询分销商列表
     * @param orderMonitorConfigRequest
     * @return
     */
    Response selectDistributorCodeList(OrderMonitorConfigRequest orderMonitorConfigRequest);

    /**
     * 报价监控页面修改报价房型
     *
     * @param modifyPriceRoomDto
     * @return
     */
    Response modifyPriceRoom(@RequestBody ModifyPriceRoomDto modifyPriceRoomDto);

    /**
     * 修改早餐
     * @param modifyPriceRoomDto
     * @return
     */
    Response modifyPriceBreakfast(ModifyPriceRoomDto modifyPriceRoomDto);
}
