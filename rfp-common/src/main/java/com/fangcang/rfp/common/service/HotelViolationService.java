package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.SaveOrModifyViolationConfigDto;
import com.fangcang.rfp.common.entity.ViolationsRemindConfig;

/**
 * <AUTHOR>
 * @date 2024/1/22 10:35
 */
public interface HotelViolationService {

    /**
     * 保存或修改酒店违规配置和跟进提醒配置
     *
     * @param saveOrModifyViolationConfigDto
     * @return
     */
    Response saveOrModifyViolationConfig(SaveOrModifyViolationConfigDto saveOrModifyViolationConfigDto);

    /**
     * 查询酒店违规配置
     *
     * @param projectId
     * @return
     */
    Response queryViolationConfig(Long projectId);

    /**
     * 保存或修改跟进提醒配置
     *
     * @param violationsRemindConfig
     * @return
     */
    Response saveOrUpdateViolationsRemindConfig(ViolationsRemindConfig violationsRemindConfig);

    /**
     * 查询跟进提醒配置
     *
     * @param remindConfigId
     * @return
     */
    Response queryViolationsRemindConfig(Long remindConfigId);

    /**
     * 删除跟进提醒配置
     *
     * @param remindConfigId
     * @return
     */
    Response deleteViolationsRemindConfig(Long remindConfigId);
}
