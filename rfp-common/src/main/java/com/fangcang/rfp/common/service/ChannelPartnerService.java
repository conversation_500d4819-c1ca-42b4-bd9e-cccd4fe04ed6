package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.QueryChannelPartnerResponse;
import com.fangcang.rfp.common.entity.Department;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/21 11:00
 */
public interface ChannelPartnerService {

    /**
     * 新增渠道商
     *
     * @param addChannelPartnerDto
     * @return
     */
    Response addChannelPartner(AddChannelPartnerDto addChannelPartnerDto);

    /**
     * 获取渠道商
     *
     * @param channelPartnerRequest
     * @return
     */
    Response queryChannelPartnerById(ChannelPartnerRequest channelPartnerRequest);

    /**
     * 根据渠道商名称查询渠道商

     */
    Response queryChannelPartnerByName(QueryChannelPartnerByNameRequest queryChannelPartnerByNameRequest);


    /**
     * 查询渠道商
     *
     * @param channelPartnerRequest
     * @return
     */
    Response queryChannelPartnerList(ChannelPartnerRequest channelPartnerRequest);

    /**
     * 修改渠道商
     *
     * @param updateChannelPartnerDto
     * @return
     */
    Response update(UpdateChannelPartnerDto updateChannelPartnerDto);

    /**
     * 根据域名查询合作商信息
     * @param channelPartnerRequest
     * @return
     */
    Response queryChannelPartnerInfoList(ChannelPartnerRequest channelPartnerRequest);

}
