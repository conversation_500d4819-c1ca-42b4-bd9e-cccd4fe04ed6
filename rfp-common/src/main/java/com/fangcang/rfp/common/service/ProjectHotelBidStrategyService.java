package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.BidStrategyResponse;
import com.fangcang.rfp.common.entity.ProjectCustomBidStrategy;
import com.itextpdf.text.DocumentException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

public interface ProjectHotelBidStrategyService {

    /**
     * 临时保存提交投标
     */
    public Response saveBidTempleTender(BidStrategyResponse bidStrategyResponse, String creator);


    /**
     * 获取临时提交投标
     */
    public Response getBidTempleTender(Long projectIntentHotelId, Integer isInvited,  UserDTO userDTO);


    /**
     * 提交投标
     * @param bidStrategyResponse
     * @return
     */
    public Response submitTender(BidStrategyResponse bidStrategyResponse, UserDTO userDTO);

    /**
     * 判断是否符合企业采购策略
     * @param bidStrategyResponse
     * @return
     */
    public Response isProjectHotelBidStrategy(BidStrategyResponse bidStrategyResponse);


    /**
     * 查询投标策略详情
     * @param projectHotelBidStrategyRequest
     * @return
     */
    public Response selectProjectHotelBidStrategy(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest);


    /**
     * 通过项目ID查项目合同模板
     * @param projectHotelBidStrategyRequest
     * @return
     */
    public Response selectProjectConcatTemplateList(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest);

    /**
     * 下载酒店集团报价模板
     *
     */
    public void downloadHotelGroupBidTemplate(HttpServletResponse httpServletResponse);

    /**
     * 下载酒店集团标准报价模板
     *
     */
    public void downloadHotelGroupStandBidTemplate(HttpServletResponse httpServletResponse);

    public Response validateUploadHotelGroupBidData(MultipartFile uploadFile, String operator,
                                                    Long projectId,
                                                    Map<Long, List<ImportHotelGroupBidDto>> importHotelDataMap,
                                                    UserDTO userDTO) throws Exception;

    public Response batchInsertHotelGroupBidData(String operator, Long projectId,
                                                 Map<Long, List<ImportHotelGroupBidDto>> importHotelDataMap,
                                                 UserDTO userDTO);

    public Response batchInsertLanyonBidData(String operator, Long projectId,
                                             Map<Long, ImportLanyonBidDto> importHotelDataMap,
                                             Map<Long, List<Map<String, String>>> lanyonHotelDataMap,
                                             UserDTO userDTO);

    /**
     * 下载Lanyon报价模板
     *
     */
    public void downloadLanyonBidTemplate(HttpServletResponse httpServletResponse);


    public Response validateUploadLanyonBidData(MultipartFile uploadFile, String operator,
                                                    Long projectId,
                                                    Map<Long, ImportLanyonBidDto> importHotelDataMap,
                                                    Map<Long, List<Map<String, String>>> lanyonHotelDataMap,
                                                    UserDTO userDTO) throws Exception;

    public Response queryBidMapHotelInfo(QueryBidMapHotelInfo queryBidMapHotelInfo);


    public Response queryHotelGroupHotelBidStatus(QueryBidMapHotelInfo queryBidMapHotelInfo, UserDTO userDTO);

    public Response notifyHotelBid(QueryBidMapHotelInfo queryBidMapHotelInfo);

    /**
     *  查询酒店集团报价推荐列表
     */
    public Response queryHotelGroupBidMapRecommendHotelList(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest);


    /**
     * 拒绝议价
     */
    public Response rejectNegotiation(RejectNegotiationRequest rejectNegotiationRequest, UserDTO userDTO);

    /**
     *  查询酒店集团报价意向酒店列表
     */
    public Response queryHotelGroupBidMapInvitedHotelList(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest);

    /**
     *  查询酒店集团报价意向酒店列表城市统计
     */
    public Response queryHotelGroupBidMapInvitedCityStat(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest);

    /**
     * 检查项目是否有白名单限制
     * @param projectId
     * @param hotelId
     * @return
     */
    public boolean isBidWhiteHotel(Long projectId, Long hotelId);

    public List<ProjectCustomBidStrategy> queryProjectCustomBidStrategyList(Long projectId, Long hotelId);

    /**
     * 生成报价 pdf
     */
    void generateBidPdf(Long projectIntentHotelId, HttpServletResponse response) throws DocumentException, IOException;
}
