package com.fangcang.rfp.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.request.count.CityPriceMonitorRequest;
import com.fangcang.rfp.common.dto.request.count.HotelPriceMonitorSumRequest;
import com.fangcang.rfp.common.dto.request.count.PoiBindHotelRequest;
import com.fangcang.rfp.common.dto.request.count.PoiPriceMonitorRequest;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.dto.response.count.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.PageUtil;
import com.fangcang.util.DateUtil;
import com.fangcang.util.StringUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @auther cjh
 * @description
 * @date 2023/2/17 16:56
 */
@Service
public class HotelPriceMonitorSumServiceImpl implements HotelPriceMonitorSumService {

    private static final Logger logger = LoggerFactory.getLogger(HotelPriceMonitorSumServiceImpl.class);

    @Autowired
    private HotelPriceMonitorSumDao hotelPriceMonitorSumDao;

    @Autowired
    private OrgPoiDao orgPoiDao;

    @Autowired
    private PoiHotelDao poiHotelDao;

    @Autowired
    private HotelPriceMonitorService hotelPriceMonitorService;

    @Autowired
    private ProjectHotelTendStrategyDao projectHotelTendStrategyDao;

    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;

    @Autowired
    private PriceUnapplicableDayDao priceUnapplicableDayDao;

    @Autowired
    private ProjectCustomTendStrategyDao projectCustomTendStrategyDao;

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;

    @Autowired
    private PriceApplicableDayService priceApplicableDayService;

    @Autowired
    private UserService userService;

    @Autowired
    private HotelDao hotelDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(HotelPriceMonitorSum record) {
        try {
            HotelPriceMonitorSum hotelPriceMonitorSum = hotelPriceMonitorSumDao.selectByUnique(record);
            if (hotelPriceMonitorSum == null){
                hotelPriceMonitorSumDao.insert(record);
            }else {
                hotelPriceMonitorSumDao.update(record);
            }
        }catch (Exception e){
            logger.error("新增项目酒店报价监控汇总表记录失败",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    @Override
    public Response selectHotelPriceMonitorSumOneThreeList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest, UserDTO userDTO) {
        Response response = new Response();
        hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.ONE_THREE.key);
        hotelPriceMonitorSumRequest.setMonitorYear(LocalDate.now().getYear());
        hotelPriceMonitorSumRequest.setUserRelatedOrgIdList(userService.getUserRelatedChannelOrgIdList(userDTO));
        PageHelper.startPage(hotelPriceMonitorSumRequest.getCurrentPage(),hotelPriceMonitorSumRequest.getPageSize());
        List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectHotelPriceMonitorSumList(hotelPriceMonitorSumRequest);
        PageResult pageResult = PageUtil.makePageResult(hotelPriceMonitorSumResponses);
        List<HotelPriceMonitorSumResponse> pageList = pageResult.getList();

        if(CollectionUtils.isNotEmpty(pageList)) {
            Set<Long> projectIdSet = new HashSet<>();
            Set<Long> hotelIdSet = new HashSet<>();
            hotelPriceMonitorSumResponses.forEach(item -> {
                projectIdSet.add(item.getProjectId());
                hotelIdSet.add(item.getHotelId());

            });
            List<Long> hotelIdList = new ArrayList<>(hotelIdSet);
            List<Long> projectIdList = new ArrayList<>(projectIdSet);
            // 设置酒店品牌,集团, 省份，城市信息
            setHotelRelatedName(pageList, hotelIdList);
            // 设置关注房型数量
            setMonitorRoomCount(pageList, projectIdList, hotelIdList);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(pageResult);

        return response;
    }


    private void setMonitorRoomCount(List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses, List<Long> projectIdList, List<Long> hotelIdList){
        Map<String, Integer> projectHotelMonitorRoomMap = new HashMap<>();
        for(List<Long> subProjectIdList : Lists.partition(projectIdList, 1000)) {
            for (List<Long> subHotelIdList : Lists.partition(hotelIdList, 1000)) {
                // 查询关注房型数量
                List<HotelPriceMonitorRoom> subHotelPriceMonitorRoomList = hotelPriceMonitorRoomDao.selectMonitorRoomListByProjectIds(subProjectIdList, subHotelIdList);
                subHotelPriceMonitorRoomList.forEach(item -> {
                    String key = item.getProjectId() + "_" + item.getHotelId();
                    projectHotelMonitorRoomMap.putIfAbsent(key, 0);
                    projectHotelMonitorRoomMap.put(key, projectHotelMonitorRoomMap.get(key) + 1);
                });
            }
        }
        hotelPriceMonitorSumResponses.forEach(item -> {
            String key = item.getProjectId() + "_" + item.getHotelId();
            item.setMonitorRoomCount(projectHotelMonitorRoomMap.getOrDefault(key, 0));
        });
    }
    private void setHotelRelatedName(List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses, List<Long> hotelIdList ){
        // 查询酒店集团和省份信息
        List<HotelWithGroupBrandInfoResponse> hotelResponseList = new ArrayList<>();
        for(List<Long> subHotelIdList : Lists.partition(hotelIdList, 1000)){
            List<HotelWithGroupBrandInfoResponse> subHotelResponseList = hotelDao.selectHotelWithGroupBrandInfoList(subHotelIdList);
            hotelResponseList.addAll(subHotelResponseList);
        }
        Map<Long, HotelWithGroupBrandInfoResponse> hotelResponseMap = hotelResponseList.stream().collect(Collectors.toMap(HotelWithGroupBrandInfoResponse::getHotelId, Function.identity()));
        hotelPriceMonitorSumResponses.forEach(item -> {
            HotelWithGroupBrandInfoResponse hotelResponse = hotelResponseMap.get(item.getHotelId());
            item.setBrandName(hotelResponse.getBrandName());
            item.setGroupName(hotelResponse.getHotelGroupName());
            item.setCityName(hotelResponse.getCityName());
            item.setProvinceName(hotelResponse.getProvinceName());
        });
    }
    @Override
    public void exportHotelPriceMonitorSumList(UserDTO userDTO, HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest, HttpServletResponse httpServletResponse) throws Exception {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("序号");
            headerRow.createCell(1).setCellValue("签约项目");
            headerRow.createCell(2).setCellValue("机构名称");
            headerRow.createCell(3).setCellValue("省份");
            headerRow.createCell(4).setCellValue("城市");
            headerRow.createCell(5).setCellValue("酒店名称");
            headerRow.createCell(6).setCellValue("关注酒店");
            headerRow.createCell(7).setCellValue("集团");
            headerRow.createCell(8).setCellValue("品牌");
            headerRow.createCell(9).setCellValue("关注房型数");
            headerRow.createCell(10).setCellValue("1-3原价有房率");
            headerRow.createCell(11).setCellValue("1-3降价有房率");
            headerRow.createCell(12).setCellValue("1-3涨价有房率");
            headerRow.createCell(13).setCellValue("1-3满房率");
            headerRow.createCell(14).setCellValue("4-15原价有房率");
            headerRow.createCell(15).setCellValue("4-15降价有房率");
            headerRow.createCell(16).setCellValue("4-15涨价有房率");
            headerRow.createCell(17).setCellValue("4-15满房率");
            headerRow.createCell(18).setCellValue("更新时间");

            hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.ONE_THREE.key);
            hotelPriceMonitorSumRequest.setMonitorYear(LocalDate.now().getYear());
            hotelPriceMonitorSumRequest.setUserRelatedOrgIdList(userService.getUserRelatedChannelOrgIdList(userDTO));
            List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectHotelPriceMonitorSumList(hotelPriceMonitorSumRequest);

            if(CollectionUtils.isNotEmpty(hotelPriceMonitorSumResponses)) {
                Set<Long> projectIdSet = new HashSet<>();
                Set<Long> hotelIdSet = new HashSet<>();
                hotelPriceMonitorSumResponses.forEach(item -> {
                    projectIdSet.add(item.getProjectId());
                    hotelIdSet.add(item.getHotelId());

                });
                List<Long> hotelIdList = new ArrayList<>(hotelIdSet);
                List<Long> projectIdList = new ArrayList<>(projectIdSet);
                // 设置酒店品牌,集团, 省份，城市信息
                setHotelRelatedName(hotelPriceMonitorSumResponses, hotelIdList);
                // 设置关注房型数量
                setMonitorRoomCount(hotelPriceMonitorSumResponses, projectIdList, hotelIdList);

                // 查询4-15天违规统计数据
                hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.FOUR_FIFTEEN.key);
                List<HotelPriceMonitorSumResponse> fourFifteenHotelPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectHotelPriceMonitorSumList(hotelPriceMonitorSumRequest);
                Map<String, HotelPriceMonitorSumResponse> fourFifteenHotelPriceMonitorSumResponsesMap = fourFifteenHotelPriceMonitorSumResponses.stream().collect(Collectors.toMap(
                        o-> o.getProjectId() + "_" + o.getHotelId(), Function.identity()
                ));

                // 设置导出值
                int i=0;
                for(HotelPriceMonitorSumResponse hotelPriceMonitorSumResponse : hotelPriceMonitorSumResponses){
                    i++;
                    String key = hotelPriceMonitorSumResponse.getProjectId() + "_" + hotelPriceMonitorSumResponse.getHotelId();
                    HotelPriceMonitorSumResponse fourFifteenHotelPriceMonitorSumResponse = fourFifteenHotelPriceMonitorSumResponsesMap.get(key);
                    if(fourFifteenHotelPriceMonitorSumResponse == null){
                        hotelPriceMonitorSumResponse.setDpHaveRoomPct4To15(0.0);
                        hotelPriceMonitorSumResponse.setFullRoomPct4To15(0.0);
                        hotelPriceMonitorSumResponse.setOpHaveRoomPct4To15(0.0);
                        hotelPriceMonitorSumResponse.setUpHaveRoomPct4To15(0.0);
                    } else {
                        hotelPriceMonitorSumResponse.setDpHaveRoomPct4To15(fourFifteenHotelPriceMonitorSumResponse.getDpHaveRoomPct());
                        hotelPriceMonitorSumResponse.setFullRoomPct4To15(fourFifteenHotelPriceMonitorSumResponse.getFullRoomPct());
                        hotelPriceMonitorSumResponse.setOpHaveRoomPct4To15(fourFifteenHotelPriceMonitorSumResponse.getOpHaveRoomPct());
                        hotelPriceMonitorSumResponse.setUpHaveRoomPct4To15(fourFifteenHotelPriceMonitorSumResponse.getUpHaveRoomPct());
                    }

                    Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
                    dataRow.createCell(0).setCellValue(dealCellValue(i));
                    dataRow.createCell(1).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getProjectName()));
                    dataRow.createCell(2).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getOrgName()));
                    dataRow.createCell(3).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getProvinceName()));
                    dataRow.createCell(4).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getCityName()));
                    dataRow.createCell(5).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getHotelName()));
                    dataRow.createCell(6).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getIsFollowHotel() == RfpConstant.constant_1 ? "是" : "否"));
                    dataRow.createCell(7).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getGroupName()));
                    dataRow.createCell(8).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getBrandName()));
                    dataRow.createCell(9).setCellValue(dealCellValue(hotelPriceMonitorSumResponse.getMonitorRoomCount()));
                    dataRow.createCell(10).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getOpHaveRoomPct()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(11).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getDpHaveRoomPct()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(12).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getUpHaveRoomPct()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(13).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getFullRoomPct()).setScale(2, RoundingMode.HALF_UP) + "%"));

                    dataRow.createCell(14).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getOpHaveRoomPct4To15()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(15).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getDpHaveRoomPct4To15()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(16).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getUpHaveRoomPct4To15()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(17).setCellValue(dealCellValue(new BigDecimal(hotelPriceMonitorSumResponse.getFullRoomPct4To15()).setScale(2, RoundingMode.HALF_UP) + "%"));
                    dataRow.createCell(18).setCellValue(dealCellValue(cn.hutool.core.date.DateUtil.formatDateTime(hotelPriceMonitorSumResponse.getModifyTime())));
                }
            }

            String fileName = "酒店报价监控" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("酒店报价监控导出异常：" + JSON.toJSONString(hotelPriceMonitorSumRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }


    private String dealCellValue(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    @Override
    public Response selectHotelPriceMonitorSumFourFifList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest) {
        Response response = new Response();
        hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.FOUR_FIFTEEN.key);
        List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectHotelPriceMonitorSumList(hotelPriceMonitorSumRequest);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(hotelPriceMonitorSumResponses);

        return response;
    }

    @Override
    public Response selectMonitorDetail(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest) {
        Response response = new Response();
        MonitorDetailResponse monitorDetailResponse = new MonitorDetailResponse();
        try {
            hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.ONE_FIFTEEN.key);
            List<HotelPriceMonitorSumResponse> hotelPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectHotelPriceMonitorSumList(hotelPriceMonitorSumRequest);

            //项目采购策略
            ProjectHotelTendStrategy projectHotelTendStrategy = projectHotelTendStrategyDao.selectByPrimaryKey(hotelPriceMonitorSumRequest.getProjectId());
            monitorDetailResponse.setProjectHotelTendStrategy(projectHotelTendStrategy);

            //自定义采购策略
            QueryCustomTendStrategyRequest queryCustomTendStrategyRequest = new QueryCustomTendStrategyRequest();
            queryCustomTendStrategyRequest.setProjectId(hotelPriceMonitorSumRequest.getProjectId());
            List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses = projectCustomTendStrategyDao.queryProjectCustomTendStrategy(queryCustomTendStrategyRequest);
            monitorDetailResponse.setProjectCustomTendStrategies(queryCustomTendStrategyResponses);

            List<MonitorRoomResponse> monitorRoomResponseList = new ArrayList<>();
            // 报价明细
            ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
            projectIntentHotelRequest.setProjectId(hotelPriceMonitorSumRequest.getProjectId());
            projectIntentHotelRequest.setHotelId(hotelPriceMonitorSumRequest.getHotelId());
            List<ProjectHotelPriceLevelResponse> projectHotelPriceResponses = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
            if (CollectionUtils.isNotEmpty(projectHotelPriceResponses)) {
                monitorDetailResponse.setProjectHotelPriceLevelResponses(projectHotelPriceResponses);
                monitorRoomResponseList = projectHotelPriceService.selectAllMonitorRoomResponseList(projectIntentHotelRequest.getProjectId(), projectIntentHotelRequest.getHotelId());

            }
            // 所有监控房型列表
            monitorDetailResponse.setMonitorRoomResponseList(monitorRoomResponseList);

            // 不适用日期
            ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest = new ProjectHotelBidStrategyRequest();
            projectHotelBidStrategyRequest.setProjectId(hotelPriceMonitorSumRequest.getProjectId());
            projectHotelBidStrategyRequest.setHotelId(hotelPriceMonitorSumRequest.getHotelId());
            List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);
            if (CollectionUtils.isNotEmpty(priceUnapplicableDays)) {
                monitorDetailResponse.setPriceUnapplicableDays(priceUnapplicableDays);
            }
            if (CollectionUtils.isNotEmpty(hotelPriceMonitorSumResponses)){
                monitorDetailResponse.setHotelPriceMonitorSumResponse(hotelPriceMonitorSumResponses.get(0));
            }

            // 查询报价日期
            PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
            priceApplicableDay.setProjectId(hotelPriceMonitorSumRequest.getProjectId());
            priceApplicableDay.setHotelId(hotelPriceMonitorSumRequest.getHotelId());
            Response priceApplicableDayResponse = priceApplicableDayService.selectPriceApplicableDayList(priceApplicableDay);
            monitorDetailResponse.setPriceApplicableDays((List<PriceApplicableDay>)priceApplicableDayResponse.getData());

        }catch (Exception e){
            logger.error("查询监控详情异常",e);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(monitorDetailResponse);
        return response;
    }

    /**
     * 根据项目ID, 酒店ID, 房型，早餐查找监控
     * @param hotelPriceMonitor
     * @return
     */
    @Override
    public Response selectHotelPriceMonitorDetailResponse(HotelPriceMonitor hotelPriceMonitor) {
        Response response = new Response();
        MonitorDetailResponse monitorDetailResponse = new MonitorDetailResponse();
        hotelPriceMonitor.setSaleDate(DateUtil.stringToDate(LocalDate.now().toString(), "yyyy-MM-dd"));
        List<HotelPriceMonitor> hotelPriceMonitors = hotelPriceMonitorService.selectHotelPriceMonitorList(hotelPriceMonitor);
        ArrayList<HotelPriceMonitorDetailResponse> hotelPriceMonitorDetailResponses = new ArrayList<>();
        int roomOpHaveRoomCount = 0;
        int roomDpHaveRoomCount = 0;
        int roomUpHaveRoomCount = 0;
        int roomFullRoomPctCount = 0;
        for (HotelPriceMonitor priceMonitor : hotelPriceMonitors) {
            HotelPriceMonitorDetailResponse hotelPriceMonitorDetailResponse = new HotelPriceMonitorDetailResponse();
            hotelPriceMonitorDetailResponse.setSaleDate(priceMonitor.getSaleDate());
            hotelPriceMonitorDetailResponse.setCompareResult(priceMonitor.getCompareResult());
            hotelPriceMonitorDetailResponse.setMonitorDayPrice(priceMonitor.getMonitorDayPrice());
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.UNAPPLY_DAY.key) {
                hotelPriceMonitorDetailResponse.setResult(CompareResultTypeEnum.UNAPPLY_DAY.value);
            }
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.FULL.key) {
                hotelPriceMonitorDetailResponse.setResult(CompareResultTypeEnum.FULL.value);
                roomFullRoomPctCount++;
            }
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.ORIGIN.key) {
                hotelPriceMonitorDetailResponse.setResult(CompareResultTypeEnum.ORIGIN.value);
                roomOpHaveRoomCount++;
            }
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.UP.key ||
                    priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.DROP.key) {
                hotelPriceMonitorDetailResponse.setResult(priceMonitor.getDifPrice().toString());
            }
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.UP.key) {
                roomUpHaveRoomCount++;
            }
            if (priceMonitor.getCompareResult().intValue() == CompareResultTypeEnum.DROP.key) {
                roomDpHaveRoomCount++;
            }
            hotelPriceMonitorDetailResponses.add(hotelPriceMonitorDetailResponse);
        }
        // 近15天数据统计 排查不适用日期
        double totalCount = roomOpHaveRoomCount + roomDpHaveRoomCount + roomUpHaveRoomCount + roomFullRoomPctCount;
        if (!hotelPriceMonitors.isEmpty() && totalCount > 0) {
            monitorDetailResponse.setRoomDpHaveRoomPct(new BigDecimal(roomDpHaveRoomCount / totalCount).setScale(4, RoundingMode.HALF_UP));
            monitorDetailResponse.setRoomUpHaveRoomPct(new BigDecimal(roomUpHaveRoomCount / totalCount).setScale(4, RoundingMode.HALF_UP));
            monitorDetailResponse.setRoomFullRoomPct(new BigDecimal(roomFullRoomPctCount / totalCount).setScale(4, RoundingMode.HALF_UP));
            monitorDetailResponse.setRoomOpHaveRoomPct(BigDecimal.ONE.subtract(monitorDetailResponse.getRoomDpHaveRoomPct()).subtract(monitorDetailResponse.getRoomUpHaveRoomPct()).subtract(monitorDetailResponse.getRoomFullRoomPct()));
        }
        monitorDetailResponse.setHotelPriceMonitors(hotelPriceMonitorDetailResponses);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(monitorDetailResponse);
        return response;
    }

    @Override
    public Response selectDpUpFullPctList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest) {
        Response response = new Response();

        hotelPriceMonitorSumRequest.setMonitorDayType(MonitorDayTypeEnum.ONE_THREE.key);
        List<TransactionRankingMonitoringProportionResponse> list = null;
        if (hotelPriceMonitorSumRequest.getSortColumn().intValue() == CompareResultTypeEnum.DROP.key){
            list = hotelPriceMonitorSumDao.selectDpUpFullPctList(hotelPriceMonitorSumRequest);
        }else if (hotelPriceMonitorSumRequest.getSortColumn().intValue() == CompareResultTypeEnum.UP.key){
            list = hotelPriceMonitorSumDao.selectDpUpFullPctList(hotelPriceMonitorSumRequest);
        }else{
            list = hotelPriceMonitorSumDao.selectDpUpFullPctList(hotelPriceMonitorSumRequest);
        }

        for (TransactionRankingMonitoringProportionResponse trank : list) {
            HotelPriceMonitorSum hotelPriceMonitorSum = new HotelPriceMonitorSum();
            BeanUtils.copyProperties(trank,hotelPriceMonitorSum);
            hotelPriceMonitorSum.setMonitorDayType(MonitorDayTypeEnum.FOUR_FIFTEEN.key);
            HotelPriceMonitorSum fourFifteenPriceMonitorSum = hotelPriceMonitorSumDao.selectByUnique(hotelPriceMonitorSum);
            if (hotelPriceMonitorSumRequest.getSortColumn().intValue() == CompareResultTypeEnum.DROP.key){
                if (fourFifteenPriceMonitorSum != null && fourFifteenPriceMonitorSum.getDpHaveRoomPct() != null){
                    trank.setFourFifteenPct(fourFifteenPriceMonitorSum.getDpHaveRoomPct());
                }else {
                    trank.setFourFifteenPct(Double.valueOf(0));
                }
            }else if (hotelPriceMonitorSumRequest.getSortColumn().intValue() == CompareResultTypeEnum.UP.key){
                if (fourFifteenPriceMonitorSum != null && fourFifteenPriceMonitorSum.getUpHaveRoomPct() != null){
                    trank.setFourFifteenPct(fourFifteenPriceMonitorSum.getUpHaveRoomPct());
                }else {
                    trank.setFourFifteenPct(Double.valueOf(0));
                }
            }else{
                if (fourFifteenPriceMonitorSum != null && fourFifteenPriceMonitorSum.getFullRoomPct() != null){
                    trank.setFourFifteenPct(fourFifteenPriceMonitorSum.getFullRoomPct());
                }else {
                    trank.setFourFifteenPct(Double.valueOf(0));
                }
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(list);

        return response;
    }


    @Override
    public Response selectCityPriceMonitorList(CityPriceMonitorRequest cityPriceMonitorRequest) {
        Response response = new Response();

        cityPriceMonitorRequest.setMonitorYear(LocalDate.now().getYear());

        PageHelper.startPage(cityPriceMonitorRequest.getCurrentPage(),cityPriceMonitorRequest.getPageSize());
        List<CityPriceMonitorSumResponse> cityPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectCityPriceMonitorList(cityPriceMonitorRequest);

        PageResult pageResult = PageUtil.makePageResult(cityPriceMonitorSumResponses);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);

        return response;
    }

    @Override
    public Response selectPoiPriceMonitorList(PoiPriceMonitorRequest poiPriceMonitorRequest) {
        Response response = new Response();
        poiPriceMonitorRequest.setMonitorYear(LocalDate.now().getYear());

        PageHelper.startPage(poiPriceMonitorRequest.getCurrentPage(),poiPriceMonitorRequest.getPageSize());
        List<PoiPriceMonitorSumResponse> poiPriceMonitorSumResponses = hotelPriceMonitorSumDao.selectPoiPriceMonitorList(poiPriceMonitorRequest);

        PageResult pageResult = PageUtil.makePageResult(poiPriceMonitorSumResponses);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response selectPoiManageMonitorHotelList(PoiPriceMonitorRequest poiPriceMonitorRequest) {
        Response response = new Response();

        PageHelper.startPage(poiPriceMonitorRequest.getCurrentPage(),poiPriceMonitorRequest.getPageSize());
        List<PoiManageMonitorHotelResponse> poiManageMonitorHotelResponses = hotelPriceMonitorSumDao.selectPoiManageMonitorHotelList(poiPriceMonitorRequest);

        PageResult pageResult = PageUtil.makePageResult(poiManageMonitorHotelResponses);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response batchBind(PoiBindHotelRequest poiPriceMonitorRequest) {
        Response response = new Response();

        for (Long hotelId : poiPriceMonitorRequest.getHotelIds()) {
            PoiHotel poiHotel = new PoiHotel();
            poiHotel.setHotelId(hotelId);
            poiHotel.setPoiId(poiPriceMonitorRequest.getPoiId());
            PoiHotel queryPoiHotel = poiHotelDao.selectByPoiIdAndHotelId(poiHotel);
            if (queryPoiHotel != null){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("数据已存在");
                return response;
            }
        }

        ArrayList<PoiHotel> poiHotels = new ArrayList<>();
        for (Long hotelId : poiPriceMonitorRequest.getHotelIds()) {
            PoiHotel poiHotel = new PoiHotel();
            poiHotel.setOrgId(poiPriceMonitorRequest.getOrgId());
            poiHotel.setHotelId(hotelId);
            poiHotel.setPoiId(poiPriceMonitorRequest.getPoiId());
            poiHotel.setCreator(poiPriceMonitorRequest.getCreator());
            poiHotel.setModifier(poiPriceMonitorRequest.getModifier());

            poiHotels.add(poiHotel);
        }

        try {
            poiHotelDao.batchInsert(poiHotels);
        } catch (Exception e) {
            logger.error("批量绑定失败",e);
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response unBind(PoiBindHotelRequest poiPriceMonitorRequest) {
        Response response = new Response();

        PoiHotel poiHotel = new PoiHotel();
        poiHotel.setPoiId(poiPriceMonitorRequest.getPoiId());
        poiHotel.setHotelId(poiPriceMonitorRequest.getHotelId());
        try {
            poiHotelDao.deletePoiHotel(poiHotel);
        } catch (Exception e) {
            logger.error("解绑失败",e);
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("解绑失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response selectBingHotelList(PoiBindHotelRequest poiPriceMonitorRequest) {
        Response response = new Response();

        OrgPoi orgPoi = orgPoiDao.selectByPrimaryKey(poiPriceMonitorRequest.getPoiId());

        poiPriceMonitorRequest.setCityCode(orgPoi.getCityCode());
        poiPriceMonitorRequest.setLngBaiDu(orgPoi.getLngBaiDu());
        poiPriceMonitorRequest.setLatBaiDu(orgPoi.getLatBaiDu());
        poiPriceMonitorRequest.setNow(LocalDate.now().toString());

        PageHelper.startPage(poiPriceMonitorRequest.getCurrentPage(),poiPriceMonitorRequest.getPageSize());
        List<PoiBindHotelResponse> poiBindHotelResponses = poiHotelDao.selectBingHotelList(poiPriceMonitorRequest);

        for (PoiBindHotelResponse poiBindHotelRespons : poiBindHotelResponses) {
            if (poiBindHotelRespons.getPoiId() == null){
                poiBindHotelRespons.setType(BindTypeEnum.FALSE.key);
            }else {
                poiBindHotelRespons.setType(BindTypeEnum.TRUE.key);
            }
        }

        PageResult pageResult = PageUtil.makePageResult(poiBindHotelResponses);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response selectBingHotelDetail(PoiBindHotelRequest poiPriceMonitorRequest) {
        Response response = new Response();
        PoiBindHotelDetailResponse poiBindHotelDetailResponse = new PoiBindHotelDetailResponse();

        OrgPoi orgPoi = orgPoiDao.selectByPrimaryKey(poiPriceMonitorRequest.getPoiId());

        poiPriceMonitorRequest.setCityCode(orgPoi.getCityCode());
        poiPriceMonitorRequest.setLngBaiDu(orgPoi.getLngBaiDu());
        poiPriceMonitorRequest.setLatBaiDu(orgPoi.getLatBaiDu());
        poiPriceMonitorRequest.setNow(LocalDate.now().toString());

        List<PoiBindHotelResponse> poiBindHotelResponses = poiHotelDao.selectBingHotelList(poiPriceMonitorRequest);

        long count = poiBindHotelResponses.stream().filter(poiBindHotelResponse -> poiBindHotelResponse.getPoiId() != null).collect(Collectors.toList()).stream().count();

        poiBindHotelDetailResponse.setPoiId(poiPriceMonitorRequest.getPoiId());
        poiBindHotelDetailResponse.setSearchHotelIds(poiBindHotelResponses.size());
        poiBindHotelDetailResponse.setBindHotelIds(Long.valueOf(count).intValue());

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(poiBindHotelDetailResponse);
        return response;
    }

    @Override
    public HotelPriceMonitorSum selectByUnique(HotelPriceMonitorSum hotelPriceMonitorSum) {
        return hotelPriceMonitorSumDao.selectByUnique(hotelPriceMonitorSum);
    }

    @Override
    public List<MonitorRoomResponse> selectMonitorRoomList(Long projectId, Long hotelId) {

        return null;
    }
}
