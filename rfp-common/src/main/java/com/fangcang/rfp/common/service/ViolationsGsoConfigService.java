package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.entity.ViolationsGSOConfig;

/**
 * 违规发消息配置
 */
public interface ViolationsGsoConfigService {


    /**
     * 查询自增ID
     */
    long queryNextId();


    /**
     * 新增回归配置
     *
     * @param addViolationsGSOConfigDto
     * @return
     */
    void addViolationsGsoConfig(AddViolationsGSOConfigDto addViolationsGSOConfigDto);

    /**
     * 根据Id查询酒店集团违规配置信息
     * @return
     */
    ViolationsGSOConfig queryHotelGroupViolationGsoConfig(Long violationsGSOConfigId);


    /**
     * 查询酒店集团违规配置信息
     * @param queryHotelGroupViolationGsoConfigRequest
     * @return
     */
    Response queryHotelGroupViolationGsoConfig(QueryHotelGroupViolationGsoConfigRequest queryHotelGroupViolationGsoConfigRequest);

    /**
     * 删除违规配置
     *
     */
    Response deleteViolationsGsoConfig(Long violationsGsoConfigId, String userName);

    /**
     * 修改违规配置
     *
     * @param updateViolationsGSOConfigDto
     * @return
     */
    Response updateViolationsGsoConfig(UpdateViolationsGSOConfigDto updateViolationsGSOConfigDto);


}
