package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.entity.EmployeeOrg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @类    名:OrgService
 * @简单描述: 机构信息业务
 * @详细描述：
 * @作    者:yangjunjun
 * @日    期：2016年12月16日下午2:57:36
 *
 */
public interface EmployeeOrgService
{

	/**
	 * 新增员工机构
	 * @return
	 * @throws Exception
	 */
	Response addEmployeeOrg(AddEmployeeOrgDto addEmployeeOrgDto);

	/**
	 * 删除更新机构
	 * @return
	 * @throws Exception
	 */
	Response deleteEmployeeOrg(DeleteEmployeeOrgDto deleteEmployeeDto);

	/**
	 * 查询员工机构
	 */
	Response queryEmployeeOrgList(QueryEmployeeOrgRequest queryEmployeeOrgRequest);

	/**
	 * 查询员工机构ID
	 * @param userId
	 * @return
	 */
	List<EmployeeOrg> queryUserEmployOrgList(Long userId);





}
