package com.fangcang.rfp.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.HotelPriceMonitorRoomDao;
import com.fangcang.rfp.common.dao.PriceApplicableRoomDao;
import com.fangcang.rfp.common.dao.ProjectHotelPriceGroupDao;
import com.fangcang.rfp.common.dao.ProjectIntentHotelDao;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.response.MonitorRoomResponse;
import com.fangcang.rfp.common.dto.response.PriceApplicableRoomInfoResponse;
import com.fangcang.rfp.common.dto.response.RoomInfoResponse;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.enums.RoomLevelEnum;
import com.fangcang.rfp.common.service.BidOperateLogService;
import com.fangcang.rfp.common.service.PriceApplicableRoomService;
import com.fangcang.rfp.common.service.ProjectHotelPriceService;
import com.fangcang.util.StringUtil;
import com.itextpdf.forms.PdfSigFieldLock;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @auther cjh
 * @description
 * @date 2023/2/15 16:47
 */
@Service
public class PriceApplicableRoomServiceImpl implements PriceApplicableRoomService {
    private static Logger logger = LoggerFactory.getLogger(ProjectIntentHotelServiceImpl.class);

    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;
    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;
    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;
    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;
    @Autowired
    private BidOperateLogService bidOperateLogService;

    @Override
    public List<PriceApplicableRoom> selectPriceApplicableRoomByPriceCode(Long priceCode) {
        return  priceApplicableRoomDao.selectPriceApplicableRoomByPriceCode(priceCode);
    }

    @Override
    public List<PriceApplicableRoomInfoResponse> selectPriceApplicableRoom(Long projectIntentHotelId, Integer roomLevelNo) {
        return priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(null, projectIntentHotelId, roomLevelNo);
    }

    @Override
    public Response validateRoomType(Long projectIntentHotelId, Integer roomLevelNo, List<PriceApplicableRoomInfoResponse> priceApplicableRoomResponseList) {
        Response response = new Response();
        List<PriceApplicableRoomInfoResponse> dbPriceApplicableRoomResponseList = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(null, projectIntentHotelId, null);
        Map<Long, PriceApplicableRoomInfoResponse> dbRoomTypeIdMap = dbPriceApplicableRoomResponseList.stream().filter(o -> o.getRoomLevelNo() != null && !Objects.equals(o.getRoomLevelNo(), roomLevelNo)).collect(Collectors.toMap(PriceApplicableRoomInfoResponse::getRoomTypeId, Function.identity()));
        List<Long> addRoomTypeIdList = new ArrayList<>();
        for(PriceApplicableRoomInfoResponse priceApplicableRoomResponse : priceApplicableRoomResponseList){
            // Lanynon 导入房型ID为空
            if(priceApplicableRoomResponse.getRoomTypeId() == null){
                continue;
            }
            if(dbRoomTypeIdMap.containsKey(priceApplicableRoomResponse.getRoomTypeId())){
                int existRoomLevelNo = 0;
                for(PriceApplicableRoomInfoResponse dbPriceApplicableRoomInfoResponse : dbPriceApplicableRoomResponseList){
                    if(dbPriceApplicableRoomInfoResponse.getRoomTypeId().longValue() == priceApplicableRoomResponse.getRoomTypeId().longValue()){
                        existRoomLevelNo = dbPriceApplicableRoomInfoResponse.getRoomLevelNo();
                        break;
                    }
                }
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg(dbRoomTypeIdMap.get(priceApplicableRoomResponse.getRoomTypeId()).getRoomTypeName() + " ID: " + priceApplicableRoomResponse.getRoomTypeId() + "已经存在" + RoomLevelEnum.getValueByKey(existRoomLevelNo));
                return response;
            }
            if(addRoomTypeIdList.contains(priceApplicableRoomResponse.getRoomTypeId())){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("存在相同房型" + priceApplicableRoomResponse.getRoomTypeName() + " ID: "+ priceApplicableRoomResponse.getRoomTypeId());
                return response;
            }
            addRoomTypeIdList.add(priceApplicableRoomResponse.getRoomTypeId());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updatePriceLevelRoom(Long projectIntentHotelId, Integer roomLevelNo, List<PriceApplicableRoomInfoResponse> priceApplicableRoomResponseList, String creator,
                                         Integer totalRoomCount, Integer levelRoomType1Count, Integer levelRoomType2Count) {
        // 检查房档对应的房型
        Response validateResponse = this.validateRoomType(projectIntentHotelId, roomLevelNo, priceApplicableRoomResponseList);
        if(validateResponse != null){
            return validateResponse;
        }

        // 删除房档所有房型
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
        priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), roomLevelNo);

        // 新增房档房型
        List<PriceApplicableRoom> priceApplicableRoomList = new ArrayList<>();
        List<String> lanyonRoomNameList = new ArrayList<>();
        for(PriceApplicableRoomInfoResponse priceApplicableRoomResponse : priceApplicableRoomResponseList){
            // Lanyon更新房型为空
            if(priceApplicableRoomResponse.getRoomTypeId() == null) {
                if (StringUtil.isValidString(priceApplicableRoomResponse.getRoomTypeName())){
                    lanyonRoomNameList.add(priceApplicableRoomResponse.getRoomTypeName());
                 }
                continue;
            }
            PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
            BeanUtils.copyProperties(priceApplicableRoomResponse, priceApplicableRoom);
            priceApplicableRoom.setRoomLevelNo(roomLevelNo);
            priceApplicableRoom.setProjectIntentHotelId(projectIntentHotelId);
            priceApplicableRoom.setCreator(creator);
            priceApplicableRoom.setModifier(creator);
            priceApplicableRoomList.add(priceApplicableRoom);

        }
        if(CollectionUtils.isNotEmpty(priceApplicableRoomList)){
            int insertResult = priceApplicableRoomDao.insertBatch(priceApplicableRoomList);
            if(insertResult == 0){
                logger.info("修改房型失败 {}", JSON.toJSONString(priceApplicableRoomList));
                throw new RuntimeException("修改房型失败");
            }
        }

        // 更新房档房型总数
        ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
        projectHotelPriceGroup.setProjectIntentHotelId(projectIntentHotelId);
        projectHotelPriceGroup.setRoomLevelNo(roomLevelNo);
        projectHotelPriceGroup.setLevelTotalRoomCount(totalRoomCount);
        projectHotelPriceGroup.setLevelRoomType1Count(levelRoomType1Count);
        projectHotelPriceGroup.setLevelRoomType2Count(levelRoomType2Count);
        projectHotelPriceGroupDao.updateLevelRoomCount(projectHotelPriceGroup);

        // 更新lanyon room desc
        if(!lanyonRoomNameList.isEmpty()){
            String lanyonRoomDesc = "";
            for(String roomName : lanyonRoomNameList){
                lanyonRoomDesc = lanyonRoomDesc + roomName + ",";
            }
            lanyonRoomDesc = lanyonRoomDesc.substring(0, lanyonRoomDesc.length() -1);
            projectHotelPriceGroupDao.updateLevelLanyonRoomDesc(projectIntentHotelId, roomLevelNo, lanyonRoomDesc);
        }

        Response response = new Response();
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("修改成功");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadPriceLevelRoom(UserDTO userDTO, ProjectIntentHotel projectIntentHotel, Integer roomLevelNo,
                                     List<PriceApplicableRoom> priceApplicableRoomList, Map<Long, RoomInfoResponse> roomInfoMap) {
        // 检查房型
        if(CollectionUtils.isEmpty(priceApplicableRoomList)){
            return;
        }

        List<PriceApplicableRoomInfoResponse> oldPriceApplicableRoomList = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(null, projectIntentHotel.getProjectIntentHotelId(), roomLevelNo);

        // 删除目前存在房型
        priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), roomLevelNo);

        // 新增房型
        priceApplicableRoomDao.insertBatch(priceApplicableRoomList);

        // 记录操作日志
        List<String> newRoomLevelApplicableRoomIdList = priceApplicableRoomList.stream().map(o ->roomInfoMap.get(o.getRoomTypeId()).getRoomName() + "(" + o.getRoomTypeId()+")").collect(Collectors.toList());
        String newRoomInfo = JSON.toJSONString(newRoomLevelApplicableRoomIdList);
        if(CollectionUtils.isEmpty(oldPriceApplicableRoomList)){
            String content = "批量更新房型 新增房档" +roomLevelNo + "房型" + JSON.toJSONString(newRoomLevelApplicableRoomIdList) ;
            bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, content);
        } else {
            List<String> oldRoomLevelApplicableRoomIdList = oldPriceApplicableRoomList.stream().map(o -> o.getRoomTypeName() + "(" + o.getRoomTypeId()+")").collect(Collectors.toList());
            String oldRoomInfo = JSON.toJSONString(oldRoomLevelApplicableRoomIdList);
            if(!oldRoomInfo.equals(newRoomInfo)) {
                String content = "批量更新房型 将房档" + roomLevelNo + "房型从" + oldRoomInfo + "改为 " + newRoomInfo;
                bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, content);
            }
        }

        // 新增默认关注房型
        MonitorRoomResponse monitorRoomResponse = projectHotelPriceService.selectDefaultMonitorRoomResponse(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        if(monitorRoomResponse != null) {
            HotelPriceMonitorRoom hotelPriceMonitorRoom = new HotelPriceMonitorRoom();
            BeanUtils.copyProperties(monitorRoomResponse, hotelPriceMonitorRoom);
            hotelPriceMonitorRoom.setCreator(RfpConstant.CREATOR);
            HotelPriceMonitorRoom deleteHotelPriceMonitorRoom = new HotelPriceMonitorRoom();
            deleteHotelPriceMonitorRoom.setProjectId(projectIntentHotel.getProjectId());
            deleteHotelPriceMonitorRoom.setHotelId(projectIntentHotel.getHotelId());
            List<HotelPriceMonitorRoom> oldHotelPriceMonitorRoomList = hotelPriceMonitorRoomDao.selectMonitorRoomList(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
            if(CollectionUtils.isNotEmpty(oldHotelPriceMonitorRoomList) && !Objects.equals(oldHotelPriceMonitorRoomList.get(0).getRoomTypeId(), hotelPriceMonitorRoom.getRoomTypeId())) {
                hotelPriceMonitorRoomDao.deleteData(deleteHotelPriceMonitorRoom);
                int insertResult = hotelPriceMonitorRoomDao.insert(hotelPriceMonitorRoom);
                if (insertResult == 0) {
                    logger.error("批量更新报价房型关注房型失败 {}", JSON.toJSONString(hotelPriceMonitorRoom));
                    throw new RuntimeException("上传房型, 关注房型失败");
                } else {
                    String content = "批量更新房型 关注房型: " + roomInfoMap.get(hotelPriceMonitorRoom.getRoomTypeId()).getRoomName() + "(" + hotelPriceMonitorRoom.getRoomTypeId() + ")";
                    bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, content);
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long selectNextApplicableRoomId() {
        return priceApplicableRoomDao.selectNextApplicableRoomId();
    }
}
