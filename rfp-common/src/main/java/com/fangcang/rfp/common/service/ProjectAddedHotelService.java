package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.OrgPoiRequest;
import com.fangcang.rfp.common.dto.request.ProjectAddedHotelRequest;
import com.fangcang.rfp.common.entity.OrgPoi;
import com.fangcang.rfp.common.entity.ProjectAddedHotel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 项目附加酒店履约
*/
public interface ProjectAddedHotelService {

    /**
     * 分页查询项目附加履约酒店
     * @return
     */
    Response queryProjectAddedHotelPage(ProjectAddedHotelRequest projectAddedHotelRequest);

    /**
     * 新增项目附加履约酒店
     */
    Response addProjectAddedHotel(ProjectAddedHotel projectAddedHotel);

    /**
     * 编辑项目附加履约酒店
     */
    Response updateProjectAddedHotel(ProjectAddedHotel projectAddedHotel);


    /**
     * 删除
     */
    Response deleteProjectAddedHotel(ProjectAddedHotel projectAddedHotel);

    /**
     * 下载项目附加履约酒店导入模板
     */
    void downloadProjectAddedHotelTemplate(HttpServletResponse httpServletResponse);

    /**
     * 项目附加履约酒店导入
     */
    public Response importProjectAddedHotel(MultipartFile uploadFile, Long projectId, UserDTO userDTO) throws Exception;

    ProjectAddedHotel setContactInfoByNotifyType(ProjectAddedHotel projectAddedHotel);
}

