package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.ContractTemplateDto;
import com.fangcang.rfp.common.dto.request.ContractTemplateRequest;
import com.fangcang.rfp.common.entity.ContractTemplate;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
public interface ContractTemplateService {

    /**
     * 新增合同模板
     * @param contractTemplateDTO
     * @return
     */
    Response addContractTemplate(ContractTemplateDto contractTemplateDTO);

    /**
     * 更新合同模板
     * @param contractTemplateDTO
     * @return
     */
    Response updateContractTemplate(ContractTemplateDto contractTemplateDTO);

    /**
     * 分页查询合同模板列表
     * @param contractTemplateRequest
     * @return
     */
    Response queryContractTemplatePage(ContractTemplateRequest contractTemplateRequest);


    /**
     * 下载合同模板
     * @param contractTemplateRequest
     * @return
     */
    void downloadContractTemplate(HttpServletResponse httpServletResponse, ContractTemplateRequest contractTemplateRequest);

    /**
     * 查询模板列表
     * @param contractTemplateRequest
     * @return
     */
    List<ContractTemplateDto> queryContractTemplateList(ContractTemplateRequest contractTemplateRequest);

    /**
     * 根据主键ID查询合同模板
     * @param templateId
     * @return
     */
    ContractTemplate selectByPrimaryKey(Long templateId);



}
