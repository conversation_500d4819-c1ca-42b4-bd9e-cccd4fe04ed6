package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.AddDepartmentDto;
import com.fangcang.rfp.common.dto.request.QueryDepartmentRequest;
import com.fangcang.rfp.common.dto.request.UpdateDepartmentDto;
import com.fangcang.rfp.common.entity.Department;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/21 11:00
 */
public interface DepartmentService {

    /**
     * 新增部门
     *
     * @param addDepartmentDto
     * @return
     */
    Response addDepartment(AddDepartmentDto addDepartmentDto);

    /**
     * 查询部门
     *
     * @param queryDepartmentRequest
     * @return
     */
    Response queryDepartment(QueryDepartmentRequest queryDepartmentRequest);

    /**
     * 删除部门
     *
     * @param updateDepartmentDto
     * @return
     */
    Response deleteDepartment(UpdateDepartmentDto updateDepartmentDto);

    /**
     * 修改部门
     *
     * @param updateDepartmentDto
     * @return
     */
    Response editDepartment(UpdateDepartmentDto updateDepartmentDto);

    /**
     * 查询部门名称
     * @param departId
     * @return
     */
    String queryDepartmentName(Long departId);

    /**
     * 查询子部门
     */
    void querySonDepartments(Long departId, List<Department> departmentList);


    List<Long> queryUserByDepart(UserDTO userDTO);

    /**
     * 根据部门名称模糊查询
     * @param queryDepartmentRequest
     * @return
     */
    Response queryDepartmentByName(QueryDepartmentRequest queryDepartmentRequest);

    /**
     * 根据部门id查询用户id
     * @param departId
     * @return
     */
    List<Long> queryUserIdByDepartId(Long departId);
}
