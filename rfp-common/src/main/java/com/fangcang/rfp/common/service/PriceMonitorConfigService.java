package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.PriceMonitorAgreementConfigDto;
import com.fangcang.rfp.common.dto.request.PriceMonitorConfigDto;
import com.fangcang.rfp.common.dto.request.PriceMonitorConfigRequest;
import com.fangcang.rfp.common.entity.PriceMonitorConfig;

/**
 * 报价监控设置服务
 */
public interface PriceMonitorConfigService {

    Response insertPriceMonitorConfig(PriceMonitorConfig priceMonitorConfig);

    Response deletePriceMonitorConfig(Long orgId);

    Response updatePriceMonitorConfigState(PriceMonitorConfigDto priceMonitorConfigDto);

    Response updatePriceMonitorAgreement(PriceMonitorAgreementConfigDto priceMonitorAgreementConfigDto);

    Response selectPriceMonitorConfigList(PriceMonitorConfigRequest priceMonitorConfigRequest);

}
