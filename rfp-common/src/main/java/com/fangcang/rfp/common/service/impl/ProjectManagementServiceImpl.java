package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.hotel.base.api.dto.query.HotelDetailInfoQueryDto;
import com.fangcang.hotel.base.api.dto.response.HotelDetailInfoResponseDto;
import com.fangcang.hotel.base.api.dto.response.HotelFacilityNewResponseDto;
import com.fangcang.hotel.base.api.facade.HotelInfoFacade;
import com.fangcang.rfp.common.cache.CachedHotelManager;
import com.fangcang.rfp.common.cache.CachedProjectIntentHotelManager;
import com.fangcang.rfp.common.cache.CachedProjectManager;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.*;
import com.fangcang.rfp.common.dto.common.OrgDTO;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.thread.*;
import com.fangcang.rfp.common.util.*;
import com.fangcang.util.DateUtil;
import com.fangcang.util.JsonUtil;
import com.fangcang.util.StringUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/20 18:33
 */
@Service
public class ProjectManagementServiceImpl implements ProjectManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectManagementServiceImpl.class);

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private ProjectIntentHotelGroupDao projectIntentHotelGroupDao;

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;

    @Autowired
    private ProjectPoiDao projectPoiDao;

    @Autowired
    private TaskExecutor queryBidHotelInfoExecutor;

    @Autowired
    private OrgService orgService;

    @Autowired
    private ProjectHotelTendStrategyDao projectHotelTendStrategyDao;

    @Autowired
    private OrgSubjectDao orgSubjectDao;

    @Autowired
    private ProjectHotelTendWeightDao projectHotelTendWeightDao;

    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;

    @Autowired
    private PriceApplicableDayDao priceApplicableDayDao;

    @Autowired
    private PriceUnapplicableDayDao priceUnapplicableDayDao;

    @Autowired
    private RecommendHotelService recommendHotelService;

    @Autowired
    private TaskExecutor rfpCommonExecutor;

    @Autowired
    private HotelApplyOnlineService hotelApplyOnlineService;

    @Autowired
    private UserService userService;

    @Autowired
    private HotelViolationsMonitorDao hotelViolationsMonitorDao;

    @Autowired
    private OrderMonitorConfigDao orderMonitorConfigDao;

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;

    @Autowired
    private ProjectHotelBidStrategyDao projectHotelBidStrategyDao;

    @Autowired
    private ProjectCustomBidStrategyDao projectCustomBidStrategyDao;

    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;

    @Autowired
    private PriceApplicableDayService priceApplicableDayService;

    @Autowired
    private ProjectHotelHistoryDataDao projectHotelHistoryDataDao;

    @Autowired
    private HotelDao hotelDao;

    @Autowired
    private ProjectCustomTendStrategyDao projectCustomTendStrategyDao;

    @Autowired
    private HotelHexagonLngLatService hotelHexagonLngLatService;

    @Autowired
    private CachedHotelManager cachedHotelManager;

    @Autowired
    private CachedProjectIntentHotelManager cachedProjectIntentHotelManager;

    @Autowired
    private BidOperateLogDao bidOperateLogDao;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private RecommendHotelDao recommendHotelDao;

    @Autowired
    private HotelInfoFacade hotelInfoFacade;

    @Autowired
    private ProjectHotelRemarkDao projectHotelRemarkDao;

    @Autowired
    private ContractDao contractDao;

    @Autowired
    private BidOperateLogService bidOperateLogService;

    @Autowired
    private PriceApplicableRoomService priceApplicableRoomService;
    @Autowired
    private LanyonImportDataDao lanyonImportDataDao;
    @Autowired
    private AreadataDao areadataDao;
    @Autowired
    private CachedProjectManager cachedProjectManager;
    @Autowired
    private ProjectHotelWeightDao projectHotelWeightDao;
    @Autowired
    private ProjectHotelBidStrategyService projectHotelBidStrategyService;
    @Autowired
    private ProjectHotelWeightService projectHotelWeightService;
    @Autowired
    private ProjectCustomBidStrategyOptionDao projectCustomBidStrategyOptionDao;

    @Override
    public Response enterpriseAndPlatformQueryProjectInfo(ProjectManagementQueryRequest projectManagementQueryRequest) {
        Response response = new Response();
        Integer projectState = projectManagementQueryRequest.getProjectState();
        if (projectState == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目状态不能为空");
            return response;
        }
        Long userId = projectManagementQueryRequest.getUserId();
        Long userOrgId = projectManagementQueryRequest.getUserOrgId();
        Integer orgType = projectManagementQueryRequest.getOrgType();
        String roleCodeType = projectManagementQueryRequest.getRoleCodeType();
        if (userId == null || userOrgId == null || orgType == null || StringUtil.isEmpty(roleCodeType)) {
            //用户信息不能为空
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        }
        PageHelper.startPage(projectManagementQueryRequest.getCurrentPage(), projectManagementQueryRequest.getPageSize());
        List<ProjectManagementQueryResponse> projectManagementQueryResponses = projectDao.enterpriseAndPlatformQueryProjectInfo(projectManagementQueryRequest);
        if (CollectionUtils.isNotEmpty(projectManagementQueryResponses)) {
            List<Long> projectIds = new ArrayList<>();
            for (ProjectManagementQueryResponse projectManagementQueryRespons : projectManagementQueryResponses) {
                projectIds.add(projectManagementQueryRespons.getProjectId());
            }
            //未启动的项目不用查询已投标酒店和邀请酒店
            Map<Long, Integer> bidHotelCountMap = null;
            Map<Long, Integer> inviteHotelCountMap = null;
            if (projectState != 0) {
                //查询已投标酒店
                List<ProjectHotelCountResponse> bidHotelCountList = projectIntentHotelDao.queryBidHotelCount(projectIds);
                if (CollectionUtils.isNotEmpty(bidHotelCountList)) {
                    bidHotelCountMap = new HashMap<>();
                    for (ProjectHotelCountResponse projectHotelCountResponse : bidHotelCountList) {
                        bidHotelCountMap.put(projectHotelCountResponse.getProjectId(), projectHotelCountResponse.getHotelCount());
                    }
                }
                //已邀约酒店数
                List<ProjectHotelCountResponse> inviteHotelCountList = projectIntentHotelDao.queryInviteHotelCount(projectIds);
                if (CollectionUtils.isNotEmpty(inviteHotelCountList)) {
                    inviteHotelCountMap = new HashMap<>();
                    for (ProjectHotelCountResponse projectHotelCountResponse : inviteHotelCountList) {
                        inviteHotelCountMap.put(projectHotelCountResponse.getProjectId(), projectHotelCountResponse.getHotelCount());
                    }
                }
            }
            for (ProjectManagementQueryResponse projectManagementQueryRespons : projectManagementQueryResponses) {
                //招标开始时间、招标结束时间、评标结果时间计算
                projectManagementQueryRespons.setBidStartTime(DateUtil.dateToString(projectManagementQueryRespons.getFirstBidStartTime()));
                Date thirdBidEndTime = projectManagementQueryRespons.getThirdBidEndTime();
                if (thirdBidEndTime != null) {
                    projectManagementQueryRespons.setBidEndTime(DateUtil.dateToString(thirdBidEndTime));
                    projectManagementQueryRespons.setBidResultTime(DateUtil.dateToString(DateUtil.getDate(thirdBidEndTime, 1, 0)));
                } else {
                    Date secondBidEndTime = projectManagementQueryRespons.getSecondBidEndTime();
                    if (secondBidEndTime != null) {
                        projectManagementQueryRespons.setBidEndTime(DateUtil.dateToString(secondBidEndTime));
                        projectManagementQueryRespons.setBidResultTime(DateUtil.dateToString(DateUtil.getDate(secondBidEndTime, 1, 0)));
                    }
                    Date firstBidEndTime = projectManagementQueryRespons.getFirstBidEndTime();
                    if (firstBidEndTime != null) {
                        projectManagementQueryRespons.setBidEndTime(DateUtil.dateToString(firstBidEndTime));
                        projectManagementQueryRespons.setBidResultTime(DateUtil.dateToString(DateUtil.getDate(firstBidEndTime, 1, 0)));
                    }
                }
                if (projectState != 0) {
                    if (bidHotelCountMap != null && bidHotelCountMap.size() > 0) {
                        Long projectId = projectManagementQueryRespons.getProjectId();
                        if (bidHotelCountMap.containsKey(projectId)) {
                            projectManagementQueryRespons.setBidHotelCount(bidHotelCountMap.get(projectId));
                        }
                    }
                    if (inviteHotelCountMap != null && inviteHotelCountMap.size() > 0) {
                        Long projectId = projectManagementQueryRespons.getProjectId();
                        if (inviteHotelCountMap.containsKey(projectId)) {
                            projectManagementQueryRespons.setInviteHotelCount(inviteHotelCountMap.get(projectId));
                        }
                    }
                }
            }

        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        PageResult pageResult = PageUtil.makePageResult(projectManagementQueryResponses);
        response.setData(pageResult);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateProjectState(UpdateProjectStateDto updateProjectStateDto) {
        Response response = new Response();
        Long projectId = updateProjectStateDto.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        Integer updateProjectState = updateProjectStateDto.getProjectState();
        if (updateProjectState == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目状态不能为空");
            return response;
        }
        Project project = projectDao.selectByPrimaryKey(projectId);
        if (project == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("未查询到项目");
            return response;
        }
        Integer projectState = project.getProjectState();
        if (projectState.intValue() == ProjectStateEnum.NOT_STARTED.key) {
            //项目未启动时，只能启动和废除项目
            if (updateProjectState.intValue() != ProjectStateEnum.STARTED.key && updateProjectState.intValue() != ProjectStateEnum.BID_ABANDONED.key) {
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg("参数异常,项目状态传参有误");
                return response;
            }
            //更新项目状态
        } else if (projectState.intValue() == ProjectStateEnum.STARTED.key) {
            //项目已启动，只能终止项目
            if (updateProjectState.intValue() != ProjectStateEnum.BIDDING_COMPLETED.key) {
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg("参数异常,项目状态传参有误");
                return response;
            }
            //终止项目，所有投标状态为已中标或已否决或未投标状态，可以终止招标。
            // 若还有新建，或议价状态的投标信息，提示：请先处理完新建及议价状态投标才能终止招标。
            List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.selectInBiddingHotel(projectId);
            if (CollectionUtils.isNotEmpty(projectIntentHotels)) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("请先处理完新标及议价状态的报价才能终止签约");
                return response;
            }
            //所有已中标的投标必须要生成合同才可以终止招标，若还有已中标投标没有生成合同，则提示：请先为已中标的酒店生成合同才能终止招标。
            if(project.getNeedOnlineContracts() != null && project.getNeedOnlineContracts().intValue() == 1) {
                int count = projectIntentHotelDao.selectContractNotCompletedCount(projectId);
                if (count > 0) {
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg("请先为已中签的酒店生成合同才能终止签约");
                    return response;
                }
            }
            //若项目为在线签章项目，已中标的投标必须完成在线签章才能终止招标，若在线签章的项目还有已中标为完成在线签章的，
            // 提示：请先为已中标的酒店完成在线签章才能终止招标。(一期不做)

            //如果是终止项目，同时将该项目内所有未报价的酒店数据【状态变更为——放弃报价】
            QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
            queryProjectIntentHotelDetailDto.setProjectId(projectId);
            queryProjectIntentHotelDetailDto.setBidState(HotelBidStateEnum.NO_BID.bidState);
            List<ProjectIntentHotel> noBidProjectIntentHotels = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
            if(CollectionUtils.isNotEmpty(noBidProjectIntentHotels)){
                Set<Long> projectIntentHotelIdSet = new HashSet<>();
                for (ProjectIntentHotel noBidProjectIntentHotel : noBidProjectIntentHotels) {
                    projectIntentHotelIdSet.add(noBidProjectIntentHotel.getProjectIntentHotelId());
                }
                List<Set<Long>> splitSets = CommonUtil.splitSet(projectIntentHotelIdSet, 1000);
                ProcessingBidStateRequest processingBidStateRequest = null;
                for (Set<Long> splitSet : splitSets) {
                    processingBidStateRequest = new ProcessingBidStateRequest();
                    processingBidStateRequest.setProjectIntentHotelIds(splitSet);
                    processingBidStateRequest.setBidState(HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState);
                    processingBidStateRequest.setOperator(updateProjectStateDto.getOperator());
                    processingBidStateRequest.setUnPriceRemark("项目已结束");
                    projectIntentHotelDao.processingBidState(processingBidStateRequest);
                }
            }

        } else {
            //已完成和已废除状态不能修改
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目已完成或已废除，不能修改");
            return response;
        }

        int i = projectDao.updateProjectState(updateProjectStateDto);
        if (i == 1) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("操作项目失败");
        }
        return response;
    }

    @Override
    public Response queryBidHeaderDetails(Long projectId) {
        Response response = new Response();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        Project project = projectDao.selectByPrimaryKey(projectId);
        if (project == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("未查询到项目信息");
            return response;
        }
        ProjectHotelTendWeight projectHotelTendWeight = projectHotelTendWeightDao.selectByPrimaryKey(projectId);

        BidHeaderDetailsQueryResponse bidHeaderDetailsQueryResponse = new BidHeaderDetailsQueryResponse();
        //判断当前处于第几轮投标
        Date date = new Date();
        if (DateUtil.getDay(project.getFirstBidEndTime(), date) <= 0) {
            bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.FIRST_BID.key);
            bidHeaderDetailsQueryResponse.setFirstBidStartTime(project.getFirstBidStartTime());
            bidHeaderDetailsQueryResponse.setFirstBidEndTime(project.getFirstBidEndTime());
        } else if (project.getSecondBidEndTime() != null && DateUtil.getDay(project.getSecondBidStartTime(), date) >= 0 && DateUtil.getDay(project.getSecondBidEndTime(), date) <= 0) {
            bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.SECOND_BID.key);
            bidHeaderDetailsQueryResponse.setSecondBidStartTime(project.getSecondBidStartTime());
            bidHeaderDetailsQueryResponse.setSecondBidEndTime(project.getSecondBidEndTime());
        } else if (project.getThirdBidEndTime() != null && DateUtil.getDay(project.getThirdBidStartTime(), date) >= 0 && DateUtil.getDay(project.getThirdBidEndTime(), date) <= 0) {
            bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.THIRD_BID.key);
            bidHeaderDetailsQueryResponse.setThirdBidStartTime(project.getThirdBidStartTime());
            bidHeaderDetailsQueryResponse.setThirdBidEndTime(project.getThirdBidEndTime());
        } else {
            if (project.getThirdBidEndTime() != null) {
                bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.THIRD_BID.key);
                bidHeaderDetailsQueryResponse.setThirdBidStartTime(project.getThirdBidStartTime());
                bidHeaderDetailsQueryResponse.setThirdBidEndTime(project.getThirdBidEndTime());
            } else if (project.getSecondBidEndTime() != null) {
                bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.SECOND_BID.key);
                bidHeaderDetailsQueryResponse.setSecondBidStartTime(project.getSecondBidStartTime());
                bidHeaderDetailsQueryResponse.setSecondBidEndTime(project.getSecondBidEndTime());
            } else {
                bidHeaderDetailsQueryResponse.setStage(BiddingStageEnum.FIRST_BID.key);
                bidHeaderDetailsQueryResponse.setFirstBidStartTime(project.getFirstBidStartTime());
                bidHeaderDetailsQueryResponse.setFirstBidEndTime(project.getFirstBidEndTime());
            }
        }
        bidHeaderDetailsQueryResponse.setProjectId(projectId);
        bidHeaderDetailsQueryResponse.setProjectName(project.getProjectName());
        bidHeaderDetailsQueryResponse.setProjectType(project.getProjectType());
        bidHeaderDetailsQueryResponse.setTenderType(project.getTenderType());
        bidHeaderDetailsQueryResponse.setProjectState(project.getProjectState());
        bidHeaderDetailsQueryResponse.setTenderCount(project.getTenderCount());
        bidHeaderDetailsQueryResponse.setBudgetTotalAmount(project.getBudgetTotalAmount());
        bidHeaderDetailsQueryResponse.setTenderOrgId(project.getTenderOrgId());
        bidHeaderDetailsQueryResponse.setNeedOnlineContracts(project.getNeedOnlineContracts());
        if (projectHotelTendWeight != null) {
            bidHeaderDetailsQueryResponse.setWhtTotalWeight(projectHotelTendWeight.getWhtTotalWeight());
        } else {
            bidHeaderDetailsQueryResponse.setWhtTotalWeight(new BigDecimal(0));
        }
        //采购总价
        BigDecimal totalPurchasePrice = new BigDecimal(0);
        //已投标酒店数
        int tenderedHotelCount = 0;
        //已中标酒店数
        int bidWinningHotelCount = 0;
        //已中标、议价中和新标投标总金额
        BigDecimal tenderPurchaseTotalAmount = new BigDecimal(0);
        //已投标酒店(不包含已否决的)
        //key 酒店id value :间夜数
        Map<Long, Long> tenderedHotelMap = new HashMap<>();
        List<ProjectIntentHotel> projectIntentHotels = projectIntentHotelDao.selectByProjectId(projectId);
        if (CollectionUtils.isNotEmpty(projectIntentHotels)) {
            for (ProjectIntentHotel projectIntentHotel : projectIntentHotels) {
                Long latestYearRoomNight = projectIntentHotel.getLatestYearRoomNight();
                BigDecimal tenderAvgPrice = projectIntentHotel.getTenderAvgPrice();
                //采购总价
                if (latestYearRoomNight != null && tenderAvgPrice != null) {
                    totalPurchasePrice = totalPurchasePrice.add(new BigDecimal(latestYearRoomNight).multiply(tenderAvgPrice));
                }
                Integer bidState = projectIntentHotel.getBidState();
                //已投标的酒店数，包含已否决
                if (bidState != null && bidState != HotelBidStateEnum.NO_BID.bidState) {
                    tenderedHotelCount++;
                }
                if(bidState != null && bidState == HotelBidStateEnum.BID_WINNING.bidState){
                    bidWinningHotelCount++;
                }
                //已投标总金额，不包含已否决
                if (bidState != null && (bidState == HotelBidStateEnum.NEW_BID.bidState
                        || bidState == HotelBidStateEnum.UNDER_NEGOTIATION.bidState
                        || bidState == HotelBidStateEnum.BID_WINNING.bidState)) {
                    if (latestYearRoomNight != null && tenderAvgPrice != null) {
                        tenderPurchaseTotalAmount = tenderPurchaseTotalAmount.add(new BigDecimal(latestYearRoomNight).multiply(tenderAvgPrice));
                        tenderedHotelMap.put(projectIntentHotel.getHotelId(), latestYearRoomNight);
                    }
                }
            }
        }
        //采购金额比率
        String purchaseAmountRatio = "0.00%";
        if (project.getBudgetTotalAmount() != null && project.getBudgetTotalAmount().doubleValue() > 0) {
            purchaseAmountRatio = totalPurchasePrice.divide(project.getBudgetTotalAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%";
        }
        //已投标完成率
        String tenderedHotelCountRatio = "0.00%";
        if (project.getTenderCount() != null && project.getTenderCount() > 0) {
            tenderedHotelCountRatio = new BigDecimal(tenderedHotelCount).divide(new BigDecimal(project.getTenderCount()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%";
        }

        //已确认签约酒店数
       //#159 企业去签约页面，已确认签约酒店数只要酒店已中签即可
//        int signingConfirmedHotelCount = contractDao.selectConfirmedHotelCount(projectId);

        //已确认签约酒店完成率
        String signingConfirmedHotelCountRatio = "0.00%";
        if (project.getTenderCount() != null && project.getTenderCount() > 0) {
            signingConfirmedHotelCountRatio = new BigDecimal(bidWinningHotelCount).divide(new BigDecimal(project.getTenderCount()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%";
        }
        //投标总金额
        BigDecimal tenderTotalAmount = new BigDecimal(0);
        if (tenderedHotelMap != null && tenderedHotelMap.size() > 0) {
            List<Long> tenderedHotelIds = new ArrayList<>();
            tenderedHotelIds.addAll(tenderedHotelMap.keySet());
            List<HotelMinPriceResponse> hotelMinPriceResponses = projectHotelPriceDao.selectMinPriceByHotelIdsAndProjectId(projectId, tenderedHotelIds);
            if (CollectionUtils.isNotEmpty(hotelMinPriceResponses)) {
                for (HotelMinPriceResponse hotelMinPriceRespons : hotelMinPriceResponses) {
                    Long hotelId = hotelMinPriceRespons.getHotelId();
                    if (tenderedHotelMap.containsKey(hotelId)) {
                        tenderTotalAmount = tenderTotalAmount.add(hotelMinPriceRespons.getMinPrice().multiply(new BigDecimal(tenderedHotelMap.get(hotelId))));
                    }
                }
            }
        }
        //投标总金额同比增长率
        String tenderTotalGrowthRate = "0.00%";
        if (tenderPurchaseTotalAmount.doubleValue() > 0) {
            tenderTotalGrowthRate = (tenderTotalAmount.subtract(tenderPurchaseTotalAmount)).divide(tenderPurchaseTotalAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%";
        }

        bidHeaderDetailsQueryResponse.setTotalPurchasePrice(totalPurchasePrice);
        bidHeaderDetailsQueryResponse.setPurchaseAmountRatio(purchaseAmountRatio);
        bidHeaderDetailsQueryResponse.setTenderedHotelCount(tenderedHotelCount);
        bidHeaderDetailsQueryResponse.setTenderedHotelCountRatio(tenderedHotelCountRatio);
        bidHeaderDetailsQueryResponse.setSigningConfirmedHotelCount(bidWinningHotelCount);
        bidHeaderDetailsQueryResponse.setSigningConfirmedHotelCountRatio(signingConfirmedHotelCountRatio);
        bidHeaderDetailsQueryResponse.setTenderTotalAmount(tenderTotalAmount);
        bidHeaderDetailsQueryResponse.setTenderTotalGrowthRate(tenderTotalGrowthRate);

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(bidHeaderDetailsQueryResponse);
        return response;
    }

    @Override
    public Response queryGroupByHotelBidState(BidHotelInfoQueryRequest bidHotelInfoQueryRequest){
        Response response = new Response();
        List<HotelBidStateResponse> hotelBidStateResponseList = projectIntentHotelDao.queryGroupByHotelBidState(bidHotelInfoQueryRequest);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(hotelBidStateResponseList);
        return response;
    }

    @Override
    public Response queryBidHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        Long projectId = bidHotelInfoQueryRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        List<Integer> rfpHotelStars = bidHotelInfoQueryRequest.getRfpHotelStars();
        if (CollectionUtils.isNotEmpty(rfpHotelStars)) {
            List<Integer> fcHotelStars = new ArrayList<>();
            for (Integer rfpHotelStar : rfpHotelStars) {
                RfpHotelStarEnum enumByKey = RfpHotelStarEnum.getEnumByKey(rfpHotelStar);
                if (enumByKey != null) {
                    String[] split = enumByKey.value.split(",");
                    for (int i = 0; i < split.length; i++) {
                        fcHotelStars.add(Integer.valueOf(split[i]));
                    }
                }
            }
            if (fcHotelStars.size() > 0) {
                bidHotelInfoQueryRequest.setHotelStars(fcHotelStars);
            }
        }
        PageHelper.startPage(bidHotelInfoQueryRequest.getCurrentPage(), bidHotelInfoQueryRequest.getPageSize());
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponses = projectIntentHotelDao.queryBidHotelInfo(bidHotelInfoQueryRequest);
        if (CollectionUtils.isNotEmpty(bidHotelInfoQueryResponses)) {
            // 查询酒店集团名称
            Set<Long> hotelGroupSet = bidHotelInfoQueryResponses.stream().filter(o -> StringUtils.isNotEmpty(o.getHotelGroup())).map(o -> Long.valueOf(o.getHotelGroup())).collect(Collectors.toSet());
            Map<Long, GroupOrBrandInfoResponse> groupOrBrandInfoResponseMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(hotelGroupSet)){
                List<GroupOrBrandInfoResponse> groupOrBrandInfoResponses = recommendHotelDao.selectGroupByIdList(new ArrayList<>(hotelGroupSet));
                groupOrBrandInfoResponseMap = groupOrBrandInfoResponses.stream().collect(Collectors.toMap(GroupOrBrandInfoResponse::getBrandId, Function.identity()));
            }

            //key 酒店id ,value 酒店投标信息
            Map<Long, BidHotelInfoQueryResponse> bidHotelInfoQueryResponseMap = new HashMap<>();
            //酒店id 签约主体用到
            List<Long> hotelIds = new ArrayList<>();
            //项目意向酒店id
            List<Long> projectIntentHotelIds = new ArrayList<>();

            //已投标项目意向酒店id 合同需要用到
            List<Long> bidProjectIntentHotelIds = new ArrayList<>();
            //key 项目意向酒店id ,value 已投标酒店信息
            Map<Long, BidHotelInfoQueryResponse> bidHotelResponseMap = new HashMap<>();

            for (BidHotelInfoQueryResponse bidHotelInfoQueryRespons : bidHotelInfoQueryResponses) {
                Long hotelId = bidHotelInfoQueryRespons.getHotelId();
                Integer bidState = bidHotelInfoQueryRespons.getBidState();
                Long projectIntentHotelId = bidHotelInfoQueryRespons.getProjectIntentHotelId();
                bidHotelInfoQueryResponseMap.put(hotelId, bidHotelInfoQueryRespons);
                hotelIds.add(hotelId);
                projectIntentHotelIds.add(projectIntentHotelId);
                if (bidState != null && bidState == HotelBidStateEnum.BID_WINNING.bidState) {
                    bidProjectIntentHotelIds.add(projectIntentHotelId);
                    bidHotelResponseMap.put(projectIntentHotelId, bidHotelInfoQueryRespons);
                    //已中标的酒店，
                    if (bidHotelInfoQueryRespons.getContractState() != null && bidHotelInfoQueryRespons.getContractState() == 1) {
                        //一期合同都是非电子签章，非电子签章生成合同默认已完成
                        bidHotelInfoQueryRespons.setSigningStatus(1);
                    } else {
                        bidHotelInfoQueryRespons.setSigningStatus(0);
                    }
                }

                // 设置酒店集团名称
                if(StringUtils.isNotEmpty(bidHotelInfoQueryRespons.getHotelGroup()) && groupOrBrandInfoResponseMap.containsKey(Long.valueOf(bidHotelInfoQueryRespons.getHotelGroup()))){
                    GroupOrBrandInfoResponse groupOrBrandInfoResponse = groupOrBrandInfoResponseMap.get(Long.valueOf(bidHotelInfoQueryRespons.getHotelGroup()));
                    bidHotelInfoQueryRespons.setHotelGroupName(groupOrBrandInfoResponse.getBrandName());
                }
            }

            CountDownLatch countDownLatch = new CountDownLatch(5);
            //公付签约主体
            FutureTask<List<ProjectSubjectInfoQueryResponse>> coPayerSubjectInfoQueryResponsesTask = new FutureTask<>(new ProjectSubjectInfoQueryThread(countDownLatch, projectId, hotelIds, 1, projectIntentHotelDao));
            queryBidHotelInfoExecutor.execute(coPayerSubjectInfoQueryResponsesTask);

            //企业签约主体
            FutureTask<List<ProjectSubjectInfoQueryResponse>> distributorSubjectInfoQueryResponsesTask = new FutureTask<>(new ProjectSubjectInfoQueryThread(countDownLatch, projectId, hotelIds, 2, projectIntentHotelDao));
            queryBidHotelInfoExecutor.execute(distributorSubjectInfoQueryResponsesTask);

            //查询项目poi信息用于计算每个酒店距离poi的距离
            FutureTask<List<ProjectPoiInfoResponse>> projectPoiInfoResponsesTask = new FutureTask<>(new ProjectPoiInfoQueryThread(countDownLatch, projectId, projectPoiDao));
            queryBidHotelInfoExecutor.execute(projectPoiInfoResponsesTask);

//            //调起价接口
//            FutureTask<List<HotelLowestPrice>> hotelLowestPriceListTask = new FutureTask<>(new HotelLowestPriceQueryThread(countDownLatch, hotelIds, tmcHubApiManager));
//            queryBidHotelInfoExecutor.execute(hotelLowestPriceListTask);

            //查询图片
            FutureTask<List<ImageResponse>> imageResponsesTask = new FutureTask<>(new QueryHotelImageThread(countDownLatch, hotelIds, recommendHotelService));
            queryBidHotelInfoExecutor.execute(imageResponsesTask);

            //最低价和早餐
            FutureTask<List<HotelMinPriceResponse>> hotelMinPriceResponseListTask = new FutureTask<>(new ProjectHotelPriceQueryThread(countDownLatch, projectIntentHotelIds, projectHotelPriceDao));
            queryBidHotelInfoExecutor.execute(hotelMinPriceResponseListTask);

            // 查询酒店推荐等级
            Map<Long, HotelRecommendLevelResponse> hotelRecommendLevelResponseMap = projectHotelHistoryDataDao.queryHotelRecommendLevel(projectId, hotelIds).stream().collect(Collectors.toMap(HotelRecommendLevelResponse::getHotelId, Function.identity()));

            boolean noTimeout = true;
            try {
                //超时返回false
                noTimeout = countDownLatch.await(60000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                logger.error("去签约信息查询失败，请求参数：" + JSON.toJSONString(bidHotelInfoQueryRequest), e);
            }
            if (!noTimeout) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg(ReturnResultEnum.FAILED.message);
                return response;
            }
            //公付签约主体
            List<ProjectSubjectInfoQueryResponse> coPayerSubjectInfoQueryResponses = coPayerSubjectInfoQueryResponsesTask == null ? null : coPayerSubjectInfoQueryResponsesTask.get();

            //企业签约主体
            List<ProjectSubjectInfoQueryResponse> distributorSubjectInfoQueryResponses = distributorSubjectInfoQueryResponsesTask == null ? null : distributorSubjectInfoQueryResponsesTask.get();

            //计算每个酒店距离poi的距离
            List<ProjectPoiInfoResponse> projectPoiInfoResponses = projectPoiInfoResponsesTask == null ? null : projectPoiInfoResponsesTask.get();
//
//            //起价
//            List<HotelLowestPrice> hotelLowestPrices = hotelLowestPriceListTask == null ? null : hotelLowestPriceListTask.get();
            //图片
            List<ImageResponse> imageResponseList = imageResponsesTask == null ? null : imageResponsesTask.get();

            //报价最低价查询
            List<HotelMinPriceResponse> hotelMinPriceResponses = hotelMinPriceResponseListTask == null ? null : hotelMinPriceResponseListTask.get();

            //公付签约主体
            if (CollectionUtils.isNotEmpty(coPayerSubjectInfoQueryResponses)) {
                for (ProjectSubjectInfoQueryResponse coPayerSubjectInfoQueryRespons : coPayerSubjectInfoQueryResponses) {
                    Long hotelId = coPayerSubjectInfoQueryRespons.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    bidHotelInfoQueryResponse.setCoPayerSubjectId(coPayerSubjectInfoQueryRespons.getSubjectId());
                    bidHotelInfoQueryResponse.setCoPayerSubjectName(coPayerSubjectInfoQueryRespons.getSubjectName());
                    bidHotelInfoQueryResponse.setCoPayerOrgName(coPayerSubjectInfoQueryRespons.getOrgName());
                }
            }

            //企业签约主体
            if (CollectionUtils.isNotEmpty(distributorSubjectInfoQueryResponses)) {
                for (ProjectSubjectInfoQueryResponse distributorSubjectInfoQueryRespons : distributorSubjectInfoQueryResponses) {
                    Long hotelId = distributorSubjectInfoQueryRespons.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    bidHotelInfoQueryResponse.setDistributorSubjectId(distributorSubjectInfoQueryRespons.getSubjectId());
                    bidHotelInfoQueryResponse.setDistributorSubjectName(distributorSubjectInfoQueryRespons.getSubjectName());
                    bidHotelInfoQueryResponse.setDistributorOrgName(distributorSubjectInfoQueryRespons.getOrgName());
                }
            }
            //计算每个酒店距离poi的距离
            if (CollectionUtils.isNotEmpty(projectPoiInfoResponses)) {
                for (BidHotelInfoQueryResponse bidHotelInfoQueryResponse : bidHotelInfoQueryResponseMap.values()) {
                    if (bidHotelInfoQueryResponse.getLatBaiDu() == null || bidHotelInfoQueryResponse.getLngBaiDu() == null) {
                        continue;
                    }
                    //只需要距离最小的值
                    double minDistance = 0d;
                    String poiName = "";
                    for (ProjectPoiInfoResponse projectPoiInfoRespons : projectPoiInfoResponses) {
                        //只有相同城市的才计算距离。
                        if (!bidHotelInfoQueryResponse.getCityCode().equals(projectPoiInfoRespons.getCityCode())) {
                            continue;
                        }
                        if (projectPoiInfoRespons.getLngBaiDu() == null || projectPoiInfoRespons.getLatBaiDu() == null) {
                            continue;
                        }
                        try {
                            double distance = LocationUtil.getDistance(projectPoiInfoRespons.getLatBaiDu(), projectPoiInfoRespons.getLngBaiDu(), bidHotelInfoQueryResponse.getLatBaiDu(), bidHotelInfoQueryResponse.getLngBaiDu());
                            if (minDistance == 0 || distance <= minDistance) {
                                minDistance = distance;
                                poiName = projectPoiInfoRespons.getPoiName();
                            }
                        } catch (Exception e) {
                            logger.error("计算poi距离异常，poiId" + projectPoiInfoRespons.getPoiId());
                        }
                    }
                    if (minDistance > 0 && StringUtil.isValidString(poiName)) {
                        bidHotelInfoQueryResponse.setPoiNameDistance(new BigDecimal(String.valueOf(minDistance)).divide(new BigDecimal(1000),2, RoundingMode.HALF_UP) + "km");
                        bidHotelInfoQueryResponse.setPoiName(poiName);
                    }
                }
            }
//            //起价
//            if (CollectionUtils.isNotEmpty(hotelLowestPrices)) {
//                for (HotelLowestPrice hotelLowestPrice : hotelLowestPrices) {
//                    Long hotelId = hotelLowestPrice.getHotelId();
//                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
//                    bidHotelInfoQueryResponse.setPriceItems(hotelLowestPrice.getPriceItems());
//                }
//            }

            //图片
            if(CollectionUtils.isNotEmpty(imageResponseList)){
                for (ImageResponse imageResponse : imageResponseList) {
                    Long hotelId = imageResponse.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    bidHotelInfoQueryResponse.setHotelImageUrl(imageResponse.getImageUrl());
                }
            }

            //酒店最低价和早餐
            if (CollectionUtils.isNotEmpty(hotelMinPriceResponses)) {
                for (HotelMinPriceResponse hotelMinPriceRespons : hotelMinPriceResponses) {
                    Long hotelId = hotelMinPriceRespons.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    if(bidHotelInfoQueryResponse == null){
                        continue;
                    }
                    bidHotelInfoQueryResponse.setMinPrice(hotelMinPriceRespons.getMinPrice());
                    bidHotelInfoQueryResponse.setBreakfastNum(hotelMinPriceRespons.getBreakfastNum());
                }
            }

            // 设置推荐等级
            if(!hotelRecommendLevelResponseMap.isEmpty()){
                for(Long hotelId : hotelRecommendLevelResponseMap.keySet()){
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    String recommendLevel = hotelRecommendLevelResponseMap.get(hotelId).getRecommendLevel();
                    if(bidHotelInfoQueryResponse == null || recommendLevel == null){
                        continue;
                    }
                    bidHotelInfoQueryResponse.setRecommendLevel(Integer.valueOf(recommendLevel));
                }
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        PageResult pageResult = PageUtil.makePageResult(bidHotelInfoQueryResponses);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response queryMapRecommendHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) {
        Response response = new Response();
        Long projectId = bidHotelInfoQueryRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        List<Integer> rfpHotelStars = bidHotelInfoQueryRequest.getRfpHotelStars();
        if (CollectionUtils.isNotEmpty(rfpHotelStars)) {
            List<Integer> fcHotelStars = new ArrayList<>();
            for (Integer rfpHotelStar : rfpHotelStars) {
                RfpHotelStarEnum enumByKey = RfpHotelStarEnum.getEnumByKey(rfpHotelStar);
                if (enumByKey != null) {
                    String[] split = enumByKey.value.split(",");
                    for (int i = 0; i < split.length; i++) {
                        fcHotelStars.add(Integer.valueOf(split[i]));
                    }
                }
            }
            if (fcHotelStars.size() > 0) {
                bidHotelInfoQueryRequest.setHotelStars(fcHotelStars);
            }
        }

        // 分页查询推荐酒店
        PageHelper.startPage(bidHotelInfoQueryRequest.getCurrentPage(), bidHotelInfoQueryRequest.getPageSize());
        List<BidRecommendHotelInfoQueryResponse> bidRecommendHotelInfoQueryResponses = projectIntentHotelDao.queryRecommendHotelInfo(bidHotelInfoQueryRequest);

        // 返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        PageResult pageResult = PageUtil.makePageResult(bidRecommendHotelInfoQueryResponses);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response queryMapBidHotelInfo(BidHotelInfoQueryRequest bidHotelInfoQueryRequest) throws Exception {
        Response response = new Response();
        Long projectId = bidHotelInfoQueryRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        List<Integer> rfpHotelStars = bidHotelInfoQueryRequest.getRfpHotelStars();
        if (CollectionUtils.isNotEmpty(rfpHotelStars)) {
            List<Integer> fcHotelStars = new ArrayList<>();
            for (Integer rfpHotelStar : rfpHotelStars) {
                RfpHotelStarEnum enumByKey = RfpHotelStarEnum.getEnumByKey(rfpHotelStar);
                if (enumByKey != null) {
                    String[] split = enumByKey.value.split(",");
                    for (int i = 0; i < split.length; i++) {
                        fcHotelStars.add(Integer.valueOf(split[i]));
                    }
                }
            }
            if (fcHotelStars.size() > 0) {
                bidHotelInfoQueryRequest.setHotelStars(fcHotelStars);
            }
        }
        PageHelper.startPage(bidHotelInfoQueryRequest.getCurrentPage(), bidHotelInfoQueryRequest.getPageSize());
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponses = projectIntentHotelDao.queryBidHotelInfo(bidHotelInfoQueryRequest);
        if (CollectionUtils.isNotEmpty(bidHotelInfoQueryResponses)) {
            //key 酒店id ,value 酒店投标信息
            Map<Long, BidHotelInfoQueryResponse> bidHotelInfoQueryResponseMap = new HashMap<>();
            //酒店id 签约主体用到
            List<Long> hotelIds = new ArrayList<>();
            //项目意向酒店id
            List<Long> projectIntentHotelIds = new ArrayList<>();

            for (BidHotelInfoQueryResponse bidHotelInfoQueryRespons : bidHotelInfoQueryResponses) {
                Long hotelId = bidHotelInfoQueryRespons.getHotelId();
                Integer bidState = bidHotelInfoQueryRespons.getBidState();
                Long projectIntentHotelId = bidHotelInfoQueryRespons.getProjectIntentHotelId();
                bidHotelInfoQueryResponseMap.put(hotelId, bidHotelInfoQueryRespons);
                hotelIds.add(hotelId);
                projectIntentHotelIds.add(projectIntentHotelId);

            }

            //查询酒店去年服务分
            Project project = projectDao.selectByPrimaryKey(projectId);
            if(project.getRelatedProjectId() != null && project.getRelatedProjectId() > 0) {
                QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto = new QueryProjectIntentHotelDetailDto();
                queryProjectIntentHotelDetailDto.setProjectId(project.getRelatedProjectId());
                queryProjectIntentHotelDetailDto.setHotelIds(hotelIds);
                List<ProjectIntentHotel> yesterYearProjectInetentHotelList = projectIntentHotelDao.queryByProjectIdAndHotelIds(queryProjectIntentHotelDetailDto);
                for(ProjectIntentHotel projectIntentHotel : yesterYearProjectInetentHotelList){
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(projectIntentHotel.getHotelId());
                    if(bidHotelInfoQueryResponse == null){
                        continue;
                    }
                    bidHotelInfoQueryResponse.setLastYearServicePoints(projectIntentHotel.getHotelServicePoints());
                }
            }

            CountDownLatch countDownLatch = new CountDownLatch(2);
            //查询图片
            FutureTask<List<ImageResponse>> imageResponsesTask = new FutureTask<>(new QueryHotelImageThread(countDownLatch, hotelIds, recommendHotelService));
            queryBidHotelInfoExecutor.execute(imageResponsesTask);

            //最低价和早餐，房型
            FutureTask<List<HotelMinPriceResponse>> hotelMinPriceResponseListTask = new FutureTask<>(new ProjectHotelPriceRoomTypeQueryThread(countDownLatch, projectIntentHotelIds, projectHotelPriceService));
            queryBidHotelInfoExecutor.execute(hotelMinPriceResponseListTask);

            boolean noTimeout = true;
            try {
                //超时返回false
                noTimeout = countDownLatch.await(60000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                logger.error("去签约信息查询失败，请求参数：" + JSON.toJSONString(bidHotelInfoQueryRequest), e);
            }
            if (!noTimeout) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg(ReturnResultEnum.FAILED.message);
                return response;
            }

            //图片
            List<ImageResponse> imageResponseList = imageResponsesTask == null ? null : imageResponsesTask.get();

            //报价最低价查询
            List<HotelMinPriceResponse> hotelMinPriceResponses = hotelMinPriceResponseListTask == null ? null : hotelMinPriceResponseListTask.get();

            //图片
            if(CollectionUtils.isNotEmpty(imageResponseList)){
                for (ImageResponse imageResponse : imageResponseList) {
                    Long hotelId = imageResponse.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    bidHotelInfoQueryResponse.setHotelImageUrl(imageResponse.getImageUrl());
                }
            }

            //酒店最低价和早餐
            if (CollectionUtils.isNotEmpty(hotelMinPriceResponses)) {
                for (HotelMinPriceResponse hotelMinPriceRespons : hotelMinPriceResponses) {
                    Long hotelId = hotelMinPriceRespons.getHotelId();
                    BidHotelInfoQueryResponse bidHotelInfoQueryResponse = bidHotelInfoQueryResponseMap.get(hotelId);
                    if(bidHotelInfoQueryResponse == null){
                        continue;
                    }
                    bidHotelInfoQueryResponse.setMinPrice(hotelMinPriceRespons.getMinPrice());
                    bidHotelInfoQueryResponse.setBreakfastNum(hotelMinPriceRespons.getBreakfastNum());
                    bidHotelInfoQueryResponse.setRoomResponseList(hotelMinPriceRespons.getPriceApplicableRoomInfoResponseList());
                    bidHotelInfoQueryResponse.setRoomTypeDesc(hotelMinPriceRespons.getRoomTypeDesc());
                }
            }
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        PageResult pageResult = PageUtil.makePageResult(bidHotelInfoQueryResponses);
        response.setData(pageResult);
        return response;
    }

    public Response queryMapBidHotelInfoStat(QueryMapBidHotelInfoStatRequest queryMapBidHotelInfoStatRequest) {
        Response response = new Response();
        Long projectId = queryMapBidHotelInfoStatRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        // 查询统计信息
        ProjectBidStatCountVO result = new ProjectBidStatCountVO();
        result.setProjectId(queryMapBidHotelInfoStatRequest.getProjectId());
        List<ProjectBidBrandStatInfoVO> projectBidBrandStatInfoVOList = projectIntentHotelDao.queryBidHotelInfoStat(queryMapBidHotelInfoStatRequest);
        if(CollectionUtils.isEmpty(projectBidBrandStatInfoVOList)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            response.setData(result);
            return response;
        }

        // 城市统计
        Map<String, ProjectBidStatCountItemVO> cityCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> cityListMap = projectBidBrandStatInfoVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCityCode));
        List<String> cityCodeList = new ArrayList<>(cityListMap.keySet());
        Map<String, AreadataDto> areadataDtoMap = areadataDao.queryByCodes(cityCodeList, 3).stream().collect(Collectors.toMap(AreadataDto::getDataCode, Function.identity())) ;
        for(String cityCode : cityListMap.keySet()){
            if(!cityCountMap.containsKey(cityCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                AreadataDto areadataDto = areadataDtoMap.get(cityCode);
                itemVO.setCode(cityCode);
                itemVO.setName(areadataDto == null? cityCode : areadataDto.getDataName());
                itemVO.setCount(0);
                cityCountMap.put(cityCode, itemVO);
            }
            ProjectBidStatCountItemVO cityCountVO = cityCountMap.get(cityCode);
            for(ProjectBidBrandStatInfoVO record : cityListMap.get(cityCode)){
                cityCountVO.setCount(cityCountVO.getCount() + record.getCityBidCount());
            }
        }
        result.setCityList(new ArrayList<>(cityCountMap.values()));

        // 酒店集团统计
        projectBidBrandStatInfoVOList = projectBidBrandStatInfoVOList.stream().filter(item -> StringUtils.isNotEmpty(item.getHotelGroup())).collect(Collectors.toList());
        Map<String, List<ProjectBidBrandStatInfoVO>> hotelGroupListMap = projectBidBrandStatInfoVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getHotelGroup));
        List<Long> hotelGroupList = new ArrayList<>(hotelGroupListMap.keySet()).stream().map(Long::valueOf).collect(Collectors.toList());
        Map<Long, ProjectBidStatCountItemVO> hotelGroupCountMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(hotelGroupList)) {
            Map<Long, GroupOrBrandInfoResponse> hotelGroupNameMap = recommendHotelDao.selectGroupByIdList(hotelGroupList).stream().collect(Collectors.toMap(GroupOrBrandInfoResponse::getBrandId, Function.identity()));
            for (String hotelGroup : hotelGroupListMap.keySet()) {
                Long hotelGroupId = Long.valueOf(hotelGroup);
                if (!hotelGroupCountMap.containsKey(hotelGroupId)) {
                    ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                    GroupOrBrandInfoResponse groupOrBrandInfoResponse = hotelGroupNameMap.get(hotelGroupId);
                    itemVO.setId(hotelGroupId);
                    itemVO.setName(groupOrBrandInfoResponse == null ? hotelGroup : groupOrBrandInfoResponse.getBrandName());
                    itemVO.setCount(0);
                    hotelGroupCountMap.put(hotelGroupId, itemVO);
                }
                ProjectBidStatCountItemVO hotelGroupCountVO = hotelGroupCountMap.get(hotelGroupId);
                for (ProjectBidBrandStatInfoVO record : hotelGroupListMap.get(hotelGroup)) {
                    hotelGroupCountVO.setCount(hotelGroupCountVO.getCount() + record.getHotelGroupBidCount());
                }
            }
        }
        result.setHotelGroupList(new ArrayList<>(hotelGroupCountMap.values()));

        // 酒店品牌统计
        projectBidBrandStatInfoVOList = projectBidBrandStatInfoVOList.stream().filter(item -> StringUtils.isNotEmpty(item.getHotelBrand())).collect(Collectors.toList());
        Map<String, List<ProjectBidBrandStatInfoVO>> hotelBrandListMap = projectBidBrandStatInfoVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getHotelBrand));
        List<Long> hotelBrandList = new ArrayList<>(hotelBrandListMap.keySet()).stream().map(Long::valueOf).collect(Collectors.toList());
        Map<Long, ProjectBidStatCountItemVO> hotelBrandCountMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(hotelBrandList)) {
            Map<Long, GroupOrBrandInfoResponse> hotelBrandNameMap = recommendHotelDao.selectBrandByIdList(hotelBrandList).stream().collect(Collectors.toMap(GroupOrBrandInfoResponse::getBrandId, Function.identity()));
            for (String hotelBrand : hotelBrandListMap.keySet()) {
                Long hotelBrandId = Long.valueOf(hotelBrand);
                if (!hotelBrandCountMap.containsKey(hotelBrandId)) {
                    ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                    itemVO.setId(hotelBrandId);
                    GroupOrBrandInfoResponse groupOrBrandInfoResponse = hotelBrandNameMap.get(hotelBrandId);
                    itemVO.setName(groupOrBrandInfoResponse == null ? hotelBrand : groupOrBrandInfoResponse.getBrandName());
                    itemVO.setCount(0);
                    hotelBrandCountMap.put(hotelBrandId, itemVO);
                }
                ProjectBidStatCountItemVO hotelBrandCountVO = hotelBrandCountMap.get(hotelBrandId);
                for (ProjectBidBrandStatInfoVO record : hotelBrandListMap.get(hotelBrand)) {
                    hotelBrandCountVO.setCount(hotelBrandCountVO.getCount() + record.getHotelBrandBidCount());
                }
            }
        }
        result.setHotelBrandList(new ArrayList<>(hotelBrandCountMap.values()));
        // 排序
        result.getCityList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getHotelGroupList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());
        result.getHotelBrandList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());

        logger.info("查询项目投标统计信息成功 {}", result);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(result);
        return response;

    }

    @Override
    public Response queryMapHotelPoiBidHotelInfo(QueryMapPoiBidHotelInfoRequest queryMapHotelPoiBidHotelInfoRequest) throws Exception{
        Response response = new Response();
        Long projectId = queryMapHotelPoiBidHotelInfoRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }

        String cityCode = "";
        ProjectPoiInfoResponse basePoiResponse = null;
        HotelResponse hotelResponse = null;
        // 查询poi坐标信息
        if(queryMapHotelPoiBidHotelInfoRequest.getPoiId() != null){
            basePoiResponse = projectPoiDao.selectByProjectAndPoiId(projectId, queryMapHotelPoiBidHotelInfoRequest.getPoiId(), queryMapHotelPoiBidHotelInfoRequest.getDistance());
            if(basePoiResponse == null){
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg("参数异常,POI不存在");
                return response;
            }
            cityCode = basePoiResponse.getCityCode();
            // 查询地图酒店信息
        } else if(queryMapHotelPoiBidHotelInfoRequest.getHotelId() != null){
            hotelResponse = cachedHotelManager.selectHotelInfo(queryMapHotelPoiBidHotelInfoRequest.getHotelId());
            cityCode = hotelResponse.getCity();
            // 查询推荐酒店信息
        } else if(queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId() != null){
            hotelResponse = cachedHotelManager.selectHotelInfo(queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId());
            cityCode = hotelResponse.getCity();
        }

        // 查询项目所有酒店报价信息
        List<BidHotelInfoQueryResponse> allBidHotelInfoQueryResponses = cachedProjectIntentHotelManager.queryProjectBidHotelInfo(queryMapHotelPoiBidHotelInfoRequest.getProjectId(), cityCode);

        // 查询距离范围的酒店或者poi信息
        // 根据经纬度过滤符合条件的酒店报价
        List<BidHotelInfoQueryResponse> hotelBidHotelInfoQueryResponses = allBidHotelInfoQueryResponses.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(queryMapHotelPoiBidHotelInfoRequest.getDistance(), o.getLatBaiDu(), o.getLngBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLatBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLngBaiDu())
        ).collect(Collectors.toList());

        // 查询项目范围内POI
        List<ProjectPoiInfoResponse> allProjectPoiInfoResponseList = projectPoiDao.selectMapProjectPoiInfo(projectId, null, cityCode, queryMapHotelPoiBidHotelInfoRequest.getDistance());
        List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList = allProjectPoiInfoResponseList.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(queryMapHotelPoiBidHotelInfoRequest.getDistance(), o.getLatBaiDu(), o.getLngBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLatBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLngBaiDu())
        ).collect(Collectors.toList());

        Map<Long, BidHotelInfoQueryResponse> hotelIdBidHotelInfoQueryResponseMap = hotelBidHotelInfoQueryResponses.stream().collect(Collectors.toMap(BidHotelInfoQueryResponse::getHotelId, Function.identity()));
        Map<Long, BidHotelInfoQueryResponse> lastYearBidHotelInfoQueryResponseMap = null;
        Map<Long, BidHotelInfoQueryResponse> lastYearCommendBidHotelInfoQueryResponseMap = null;


        // 查询公里范围推荐酒店列表
        FutureTask<Map<Long, BidHotelInfoQueryResponse>> bidCommentHotelInfoQueryResponseMapTask = null;
        if(queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId() != null){
            bidCommentHotelInfoQueryResponseMapTask = new FutureTask<>(
                    new ProjectRecommendHotelQueryThread(projectId, queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId(),
                            queryMapHotelPoiBidHotelInfoRequest.getDistance(),
                            queryMapHotelPoiBidHotelInfoRequest.getLngBaiDu(),
                            queryMapHotelPoiBidHotelInfoRequest.getLatBaiDu(),
                            cityCode,
                            projectHotelHistoryDataDao, projectIntentHotelDao));
            queryBidHotelInfoExecutor.execute(bidCommentHotelInfoQueryResponseMapTask);
        }

        // 查询早餐和最低价格
        if (CollectionUtils.isNotEmpty(hotelBidHotelInfoQueryResponses)) {
            int countDownLatchCount = 2;
            CountDownLatch countDownLatch = new CountDownLatch(countDownLatchCount);
            //项目意向酒店id
            List<Long> projectIntentHotelIds = hotelBidHotelInfoQueryResponses.stream().map(BidHotelInfoQueryResponse::getProjectIntentHotelId).collect(Collectors.toList());

            //最低价和早餐，房型
            FutureTask<List<HotelMinPriceResponse>> hotelMinPriceResponseListTask = new FutureTask<>(new ProjectHotelPriceRoomTypeQueryThread(countDownLatch, projectIntentHotelIds, projectHotelPriceService));
            queryBidHotelInfoExecutor.execute(hotelMinPriceResponseListTask);

            // 查询去年报价信息
            Long baseCenterHotelId = queryMapHotelPoiBidHotelInfoRequest.getHotelId();
            if(queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId() != null){
                baseCenterHotelId = queryMapHotelPoiBidHotelInfoRequest.getRecommendHotelId();
            }
            List<Long> queryLastYearBidInfoHotelIdList = new ArrayList<>(hotelIdBidHotelInfoQueryResponseMap.keySet());
            FutureTask<Map<Long, BidHotelInfoQueryResponse>> bidHotelInfoQueryResponseMapTask = new FutureTask<>(
                    new ProjectHotelLastYearBidInfoQueryThread(countDownLatch, projectId, baseCenterHotelId,
                            queryLastYearBidInfoHotelIdList, projectHotelHistoryDataDao));
            queryBidHotelInfoExecutor.execute(bidHotelInfoQueryResponseMapTask);

            boolean noTimeout = true;
            try {
                //超时返回false
                noTimeout = countDownLatch.await(60000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                logger.error("酒店报价POI坐标信息查询失败，请求参数：" + JSON.toJSONString(queryMapHotelPoiBidHotelInfoRequest), e);
            }
            if (!noTimeout) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg(ReturnResultEnum.FAILED.message);
                return response;
            }

            //报价最低价查询
            List<HotelMinPriceResponse> hotelMinPriceResponseList = hotelMinPriceResponseListTask == null ? null : hotelMinPriceResponseListTask.get();

            //hotel去年报价信息
            lastYearBidHotelInfoQueryResponseMap = bidHotelInfoQueryResponseMapTask == null ? null : bidHotelInfoQueryResponseMapTask.get();

            //最低价和早餐
            Map<Long, HotelMinPriceResponse> hotelMinPriceResponseMap = hotelMinPriceResponseList.stream().collect(Collectors.toMap(HotelMinPriceResponse::getHotelId, Function.identity()));
            for(BidHotelInfoQueryResponse bidHotelInfoQueryResponse : hotelBidHotelInfoQueryResponses){
                HotelMinPriceResponse hotelMinPriceResponse = hotelMinPriceResponseMap.get(bidHotelInfoQueryResponse.getHotelId());
                if(hotelMinPriceResponse == null){
                    logger.info("酒店没有找到最低报价 {}", JSON.toJSONString(bidHotelInfoQueryResponse));
                    continue;
                }
                bidHotelInfoQueryResponse.setMinPrice(hotelMinPriceResponse.getMinPrice());
                bidHotelInfoQueryResponse.setBreakfastNum(hotelMinPriceResponse.getBreakfastNum());
                bidHotelInfoQueryResponse.setRoomResponseList(hotelMinPriceResponse.getPriceApplicableRoomInfoResponseList());
                bidHotelInfoQueryResponse.setRoomTypeDesc(hotelMinPriceResponse.getRoomTypeDesc());

                BidHotelInfoQueryResponse lastYearBidHotelInfoQueryResponse = lastYearBidHotelInfoQueryResponseMap.get(bidHotelInfoQueryResponse.getHotelId());
                if(lastYearBidHotelInfoQueryResponse != null) {
                    bidHotelInfoQueryResponse.setRecommendLevel(lastYearBidHotelInfoQueryResponse.getRecommendLevel());
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(lastYearBidHotelInfoQueryResponse.getLastYearAvgPrice());
                    bidHotelInfoQueryResponse.setLastYearCityOrder(lastYearBidHotelInfoQueryResponse.getLastYearCityOrder());
                    bidHotelInfoQueryResponse.setLatestYearRoomNight(lastYearBidHotelInfoQueryResponse.getLatestYearRoomNight());
                    bidHotelInfoQueryResponse.setTheSameLevelPrice(lastYearBidHotelInfoQueryResponse.isTheSameLevelPrice());
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(lastYearBidHotelInfoQueryResponse.getLastYearAvgPrice());
                } else{
                    bidHotelInfoQueryResponse.setLastYearServicePoints(BigDecimal.ZERO);
                    bidHotelInfoQueryResponse.setLastYearCityOrder(0);
                    bidHotelInfoQueryResponse.setLatestYearRoomNight(0);
                    bidHotelInfoQueryResponse.setTheSameLevelPrice(false);
                    bidHotelInfoQueryResponse.setLastYearAvgPrice(BigDecimal.ZERO);
                }
            }
        }


        //推荐酒店hotel去年报价信息
        lastYearCommendBidHotelInfoQueryResponseMap = bidCommentHotelInfoQueryResponseMapTask == null ? null : bidCommentHotelInfoQueryResponseMapTask.get();

        // 准备返回值
        // 报价酒店坐标信息
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList = new LinkedList<>();
        if(queryMapHotelPoiBidHotelInfoRequest.getHotelId() != null){
            BidHotelInfoQueryResponse baseBidHotelInfoQueryResponse = hotelIdBidHotelInfoQueryResponseMap.get(queryMapHotelPoiBidHotelInfoRequest.getHotelId());
            bidHotelInfoQueryResponseList.add(baseBidHotelInfoQueryResponse);
        }
        for(Long hotelId : hotelIdBidHotelInfoQueryResponseMap.keySet()){
            if(queryMapHotelPoiBidHotelInfoRequest.getHotelId() != null && Objects.equals(hotelId, queryMapHotelPoiBidHotelInfoRequest.getHotelId())){
                continue;
            }
            bidHotelInfoQueryResponseList.add(hotelIdBidHotelInfoQueryResponseMap.get(hotelId));
        }
        // POI排序
        List<ProjectPoiInfoResponse> poiInfoResponseList = new LinkedList<>();
        if(basePoiResponse != null){
            poiInfoResponseList.add(basePoiResponse);
            for(ProjectPoiInfoResponse poiInfoResponse : hotelProjectPoiInfoResponseList){
                if(Objects.equals(basePoiResponse.getPoiId(), poiInfoResponse.getPoiId())){
                    continue;
                }
                poiInfoResponseList.add(poiInfoResponse);
            }
        } else {
            poiInfoResponseList = hotelProjectPoiInfoResponseList;
        }

        //推荐酒店坐标信息
        List<BidHotelInfoQueryResponse> commendHotelInfoQueryResponseList = new LinkedList<>();
        if(lastYearCommendBidHotelInfoQueryResponseMap != null && !lastYearCommendBidHotelInfoQueryResponseMap.isEmpty()){
            commendHotelInfoQueryResponseList = new LinkedList<>(new ArrayList<>(lastYearCommendBidHotelInfoQueryResponseMap.values()));
            // 设置推荐价格
            List<Long> recommendHotelIdList =  commendHotelInfoQueryResponseList.stream().map(o -> o.getHotelId()).collect(Collectors.toList());
            Map<Long, RecommendHotel> recommendHotelMap = recommendHotelDao.selectRecommendHotelByHotelIds(recommendHotelIdList).stream().collect(Collectors.toMap(RecommendHotel::getHotelId, Function.identity()));
            commendHotelInfoQueryResponseList.forEach(item -> {
                if(recommendHotelMap.containsKey(item.getHotelId())) {
                    item.setReferencePrice(recommendHotelMap.get(item.getHotelId()).getReferencePrice());
                }
            });
        }


       // 返回值
        MapPoiBidHotelInfoResponse mapPoiBidHotelInfoResponse = new MapPoiBidHotelInfoResponse();
        mapPoiBidHotelInfoResponse.setProjectId(projectId);
        mapPoiBidHotelInfoResponse.setCommendHotelInfoQueryResponseList(commendHotelInfoQueryResponseList);
        mapPoiBidHotelInfoResponse.setBidHotelInfoQueryResponseList(bidHotelInfoQueryResponseList);
        mapPoiBidHotelInfoResponse.setHotelProjectPoiInfoResponseList(poiInfoResponseList);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(mapPoiBidHotelInfoResponse);

        return response;
    }


    @Override
    public Response queryHeatMapBidInfo(QueryMapPoiBidHotelInfoRequest queryMapHotelPoiBidHotelInfoRequest) throws Exception{
        Response response = new Response();
        Long projectId = queryMapHotelPoiBidHotelInfoRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        String cityCode = null;
        HotelResponse hotelResponse = null;
        if(queryMapHotelPoiBidHotelInfoRequest.getHotelId() != null) {
            hotelResponse = hotelDao.selectHotelInfo(queryMapHotelPoiBidHotelInfoRequest.getHotelId());
            cityCode = hotelResponse.getCity();
        } else if(queryMapHotelPoiBidHotelInfoRequest.getPoiId() != null){
            ProjectPoiInfoResponse projectPoiInfoResponse = projectPoiDao.selectByProjectAndPoiId(projectId, queryMapHotelPoiBidHotelInfoRequest.getPoiId(), queryMapHotelPoiBidHotelInfoRequest.getDistance());
            cityCode = projectPoiInfoResponse.getCityCode();
        }
        // 定义返回值
        HeatMapBidInfoResponse heatMapBidInfoResponse = new HeatMapBidInfoResponse();
        heatMapBidInfoResponse.setHexagonsStatList(new ArrayList<>());

        // 查询十公里范围内的历史报价数据
        Map<Long, QueryHistoryProjectInfoResponse> queryBidHotelInfoQueryResponseMap = new HashMap<>();
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(projectId, null, cityCode, null);
        queryHistoryProjectInfoList = queryHistoryProjectInfoList.stream().filter(o -> LocationUtil.isInLngLatInDistanceRange(10, o.getLatBaidu(), o.getLngBaidu(), queryMapHotelPoiBidHotelInfoRequest.getLatBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLngBaiDu())).collect(Collectors.toList());
        queryBidHotelInfoQueryResponseMap = queryHistoryProjectInfoList.stream().collect(Collectors.toMap(QueryHistoryProjectInfoResponse::getHotelId, Function.identity()));

        if(!queryBidHotelInfoQueryResponseMap.isEmpty()) {
            List<HexagonsStat> uniqueHexagonList = new ArrayList<>();
            // 获取酒店对应六边形
            if(hotelResponse != null) {
                HotelHexagonLngLat hotelHexagonLngLat = hotelHexagonLngLatService.getByHotelIfNullThenInit(hotelResponse);
                if(hotelHexagonLngLat.getHexagonLngLat10() == null){
                    logger.error("hotelHexagonLngLat.getHexagonLngLat10() is null {}", JSON.toJSONString(hotelResponse));
                } else {
                    uniqueHexagonList = JsonUtil.jsonToList(hotelHexagonLngLat.getHexagonLngLat10(), HexagonsStat.class);
                }
            } else {
                // 生成POI对应六边形
                Set<String> uniqueHexagonStringSet = new HashSet<>();
                HexagonStatUtil.generateHexagons(new double[]{queryMapHotelPoiBidHotelInfoRequest.getLngBaiDu(), queryMapHotelPoiBidHotelInfoRequest.getLatBaiDu()}, 6, uniqueHexagonStringSet, uniqueHexagonList);
            }
            // 设置热力图统计值
            for(Long hotelId : queryBidHotelInfoQueryResponseMap.keySet()){
                QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = queryBidHotelInfoQueryResponseMap.get(hotelId);
                //判断酒店是否再六边形范围内
                for(HexagonsStat hexagonsStat : uniqueHexagonList){
                    boolean isInPolygon = LocationUtil.isInPolygon(queryHistoryProjectInfoResponse.getLngBaidu(), queryHistoryProjectInfoResponse.getLatBaidu(), hexagonsStat.getLanlatList());
                    if(isInPolygon){
                        hexagonsStat.setStat(hexagonsStat.getStat() + queryHistoryProjectInfoResponse.getRoomNightCount());
                        break;
                    }
                }
            }
            uniqueHexagonList = uniqueHexagonList.stream().filter(o -> o.getStat() > 0).collect(Collectors.toList());
            heatMapBidInfoResponse.setHexagonsStatList(uniqueHexagonList);
        }

        // 返回日历图
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(heatMapBidInfoResponse);

        return response;
    }
    @Override
    public Response updateBidOrgInfo(UpdateBidOrgInfoRequest updateBidOrgInfoRequest, UserDTO userDTO) {
        Response response = new Response();
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateBidOrgInfoRequest.getProjectIntentHotelId());
        int i = projectIntentHotelDao.updateBidOrgInfo(updateBidOrgInfoRequest);
        if (i > 0) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);

            // 记录操作日志
            Org oldOrg = projectIntentHotel.getBidOrgId() == null ? null : orgService.selectByPrimaryKey(projectIntentHotel.getBidOrgId());
            String oldOrgName = oldOrg == null ? "空" : oldOrg.getOrgName();
            Org newOrg = orgService.selectByPrimaryKey(updateBidOrgInfoRequest.getBidOrgId());

            String content = "变更报价机构从" + oldOrgName + "改为 "  + newOrg.getOrgName();
            bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, content);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("修改失败");
        }
        return response;
    }
    @Override
    public Response updateProjectSubjectInfo(UpdateProjectIntentHotelDto updateProjectIntentHotelDto) {
        Response response = new Response();
        if (updateProjectIntentHotelDto.getProjectId() == null || updateProjectIntentHotelDto.getProjectId() <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        Integer operateType = updateProjectIntentHotelDto.getOperateType();
        if (operateType == null || (operateType != 6 && operateType != 7)) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,操作类型有误");
            return response;
        }

        if (updateProjectIntentHotelDto.getCoPayerSubjectId() == null && updateProjectIntentHotelDto.getDistributorSubjectId() == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,签约主体信息为空");
            return response;
        }
        //修改企业签约主体，需要查询签约信息
        if (operateType == 6) {
            OrgSubject orgSubject = orgSubjectDao.selectOrgSubjectInfoBySubjectId(updateProjectIntentHotelDto.getDistributorSubjectId());
            if (orgSubject == null) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("企业签约主体不存在");
                return response;
            }
            updateProjectIntentHotelDto.setDistributorBank(orgSubject.getBank());
            updateProjectIntentHotelDto.setDistributorAccountName(orgSubject.getAccountName());
            updateProjectIntentHotelDto.setDistributorAccountNumber(orgSubject.getAccountNumber());
        }

        //修改公付主体信息，先要校验项目是否是第三方支付
        if (operateType == 7) {
            Project project = projectDao.selectByPrimaryKey(updateProjectIntentHotelDto.getProjectId());
            if (project == null) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("未查询到项目信息");
                return response;
            }
            if (project.getCoPayerType() == null || project.getCoPayerType() != 2) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("项目不是第三方支付，不能更改企业公付主体信息");
                return response;
            }
            OrgSubject orgSubject = orgSubjectDao.selectOrgSubjectInfoBySubjectId(updateProjectIntentHotelDto.getCoPayerSubjectId());
            if (orgSubject == null) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("公付签约主体不存在");
                return response;
            }
            updateProjectIntentHotelDto.setCoPayerBank(orgSubject.getBank());
            updateProjectIntentHotelDto.setCoPayerAccountName(orgSubject.getAccountName());
            updateProjectIntentHotelDto.setCoPayerAccountNumber(orgSubject.getAccountNumber());
            updateProjectIntentHotelDto.setCoPayerOrgId(orgSubject.getOrgId());
        }
        int i = projectIntentHotelDao.updateProjectIntentHotel(updateProjectIntentHotelDto);
        if (i > 0) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("修改失败");
        }
        return response;
    }

    @Override
    public Response queryProjectDetails(Long projectId) {
        Response response = new Response();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }
        ProjectBasicInfoResponse projectBasicInfoResponse = projectDao.selectProjectBasicInformation(projectId);
        if (projectBasicInfoResponse == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("项目不存在");
            return response;
        }
        Response orgResponse = orgService.selectOrgInfo(null, projectBasicInfoResponse.getTenderOrgId());
        if (orgResponse == null || orgResponse.getData() == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("机构信息不存在");
            return response;
        }
        //机构信息
        OrgDTO orgDTO = (OrgDTO) orgResponse.getData();
        orgDTO.setLogoUrl(HttpsUrlUtility.convertUploadToHttpsAccessUrl(orgDTO.getLogoUrl()));
        if(CollectionUtils.isNotEmpty(orgDTO.getAttachmentInfoResponseList())){
            for(AttachmentInfoResponse attachmentInfoResponse : orgDTO.getAttachmentInfoResponseList()){
                attachmentInfoResponse.setFileUrl(HttpsUrlUtility.convertUploadToHttpsAccessUrl(attachmentInfoResponse.getFileUrl()));
                attachmentInfoResponse.setThumbnailUrl(HttpsUrlUtility.convertUploadToHttpsAccessUrl(attachmentInfoResponse.getThumbnailUrl()));
            }
        }

        //项目简介
        String introduction = projectDao.selectIntroductionByProjectId(projectId);
        introduction = HttpsUrlUtility.convertUploadToHttpsAccessUrl(introduction);
        projectBasicInfoResponse.setIntroduction(introduction);

        //项目poi
        List<ProjectPoiInfoResponse> projectPoiInfoResponses = projectPoiDao.selectProjectPoiInfo(projectId);

        //项目采购策略
        ProjectHotelTendStrategy projectHotelTendStrategy = projectHotelTendStrategyDao.selectByPrimaryKey(projectId);

        ProjectDetailsQueryResponse projectDetailsQueryResponse = new ProjectDetailsQueryResponse();
        projectDetailsQueryResponse.setProjectBasicInfoResponse(projectBasicInfoResponse);
        projectDetailsQueryResponse.setOrg(orgDTO);
        projectDetailsQueryResponse.setProjectPoiInfoResponses(projectPoiInfoResponses);
        projectDetailsQueryResponse.setProjectHotelTendStrategy(projectHotelTendStrategy);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(projectDetailsQueryResponse);
        return response;
    }

    private String getWeeekDaysDesc(String applicableWeeks){
        // Group 1 price
        String applicableWeeksDesc = "";
        if (StringUtil.isValidString(applicableWeeks)) {
            String[] split = applicableWeeks.split(",");
            Integer[] sortedSplit = Arrays.stream(split).map(o -> Integer.valueOf(o)).sorted().toArray(Integer[]::new);
            StringBuffer buffer = new StringBuffer();
            for (Integer weekDay : sortedSplit) {
                String valueByKey = ApplicableWeeksTypeEnum.getValueByKey(weekDay);
                buffer.append(valueByKey + ",");
            }
            applicableWeeksDesc = buffer.substring(0, buffer.length() - 1);
        }
        return applicableWeeksDesc;
    }
    private void setExportTenderHotelPriceResponseLevelGroupPrice(ExportTenderHotelPriceResponse exportTenderHotelPriceResponse,
                                                                                            Map<Integer, List<ProjectHotelPriceGroup>> hotelLevelPriceGroupMap,
                                                                                            Map<Long, List<ProjectHotelPrice>> groupProjectHotelPriceMap,
                                                                                            int priceType){
        try {
            // 房档最多5个
            for (int i = 1; i < 6; i++) {
                List<ProjectHotelPriceGroup> levelHotelPriceGroups = hotelLevelPriceGroupMap.get(i);
                if (CollectionUtils.isEmpty(levelHotelPriceGroups)) {
                    break;
                }
                ProjectHotelPriceGroup group1 = levelHotelPriceGroups.get(0);
                ProjectHotelPriceGroup group2 = null;
                List<ProjectHotelPrice> group2HotelProjectHotelPriceList = null;
                ProjectHotelPriceGroup group3 = null;
                List<ProjectHotelPrice> group3HotelProjectHotelPriceList = null;
                ProjectHotelPriceGroup group4 = null;
                List<ProjectHotelPrice> group4HotelProjectHotelPriceList = null;

                if (levelHotelPriceGroups.size() > 1) {
                    group2 = levelHotelPriceGroups.get(1);
                    group2HotelProjectHotelPriceList = groupProjectHotelPriceMap.get(group2.getHotelPriceGroupId());
                }
                if (levelHotelPriceGroups.size() > 2) {
                    group3 = levelHotelPriceGroups.get(2);
                    group3HotelProjectHotelPriceList = groupProjectHotelPriceMap.get(group3.getHotelPriceGroupId());
                }
                if (levelHotelPriceGroups.size() > 3) {
                    group4 = levelHotelPriceGroups.get(3);
                    group4HotelProjectHotelPriceList = groupProjectHotelPriceMap.get(group4.getHotelPriceGroupId());
                }
                List<ProjectHotelPrice> group1HotelProjectHotelPriceList = groupProjectHotelPriceMap.get(group1.getHotelPriceGroupId());
                if (i == 1) {
                    exportTenderHotelPriceResponse.setLevel1Group1ApplicableWeeks(getWeeekDaysDesc(group1.getApplicableWeeks()));
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel1Group1NoBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel1Group1OneBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel1Group1TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // 设置LRA
                    exportTenderHotelPriceResponse.setLevel1Group1Lra(getYN(group1.getLra()));

                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel1Group2ApplicableWeeks(getWeeekDaysDesc(group2.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel1Group2OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group2TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }

                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel1Group2Lra(getYN(group2.getLra()));
                    }

                    // Group 3 price
                    if (group3HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel1Group3ApplicableWeeks(getWeeekDaysDesc(group3.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group3HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group3NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel1Group3OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group3TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }

                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel1Group3Lra(getYN(group3.getLra()));
                    }

                    // Group 4 price
                    if (group4HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel1Group4ApplicableWeeks(getWeeekDaysDesc(group4.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group4HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group4NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel1Group4OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel1Group4TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }

                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel1Group4Lra(getYN(group4.getLra()));
                    }

                }
                if (i == 2) {
                    // Group 1price
                    exportTenderHotelPriceResponse.setLevel2Group1ApplicableWeeks(getWeeekDaysDesc(group1.getApplicableWeeks()));
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel2Group1NoBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel2Group1OneBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel2Group1TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // 设置LRA
                    exportTenderHotelPriceResponse.setLevel2Group1Lra(getYN(group1.getLra()));
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel2Group2ApplicableWeeks(getWeeekDaysDesc(group2.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel2Group2OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group2TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel2Group2Lra(getYN(group2.getLra()));
                    }

                    // Group 3 price
                    if (group3HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel2Group3ApplicableWeeks(getWeeekDaysDesc(group3.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group3HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group3NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel2Group3OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group3TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel2Group3Lra(getYN(group3.getLra()));
                    }

                    // Group 4 price
                    if (group4HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel2Group4ApplicableWeeks(getWeeekDaysDesc(group4.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group4HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group4NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel2Group4OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel2Group4TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel2Group4Lra(getYN(group4.getLra()));
                    }
                }

                if (i == 3) {
                    // Group 1 price
                    exportTenderHotelPriceResponse.setLevel3Group1ApplicableWeeks(getWeeekDaysDesc(group1.getApplicableWeeks()));
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel3Group1NoBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel3Group1OneBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel3Group1TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel3Group1Lra(getYN(group1.getLra()));
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel3Group2ApplicableWeeks(getWeeekDaysDesc(group2.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel3Group2OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group2TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel3Group2Lra(getYN(group2.getLra()));
                    }

                    // Group 3 price
                    if (group3HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel3Group3ApplicableWeeks(getWeeekDaysDesc(group3.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group3HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group3NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel3Group3OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group3TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel3Group3Lra(getYN(group3.getLra()));
                    }

                    // Group 4 price
                    if (group4HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel3Group4ApplicableWeeks(getWeeekDaysDesc(group4.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group4HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group4NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel3Group4OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel3Group4TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel3Group4Lra(getYN(group4.getLra()));
                    }
                }
                if (i == 4) {
                    // Group 1 price
                    exportTenderHotelPriceResponse.setLevel4Group1ApplicableWeeks(getWeeekDaysDesc(group1.getApplicableWeeks()));
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel4Group1NoBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel4Group1OneBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel4Group1TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel4Group1Lra(getYN(group1.getLra()));
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel4Group2ApplicableWeeks(getWeeekDaysDesc(group2.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel4Group2OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group2TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel4Group2Lra(getYN(group2.getLra()));
                    }

                    // Group 3 price
                    if (group3HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel4Group3ApplicableWeeks(getWeeekDaysDesc(group3.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group3HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group3NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel4Group3OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group3TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel4Group3Lra(getYN(group3.getLra()));
                    }

                    // Group 4 price
                    if (group4HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel4Group4ApplicableWeeks(getWeeekDaysDesc(group4.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group4HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group4NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel4Group4OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel4Group4TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel4Group4Lra(getYN(group4.getLra()));
                    }
                }

                if (i == 5) {
                    // Group 1 price
                    exportTenderHotelPriceResponse.setLevel5Group1ApplicableWeeks(getWeeekDaysDesc(group1.getApplicableWeeks()));
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel5Group1NoBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel5Group1OneBreakfastPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel5Group1TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel5Group1Lra(getYN(group1.getLra()));
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel5Group2ApplicableWeeks(getWeeekDaysDesc(group2.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel5Group2OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group2TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel5Group2Lra(getYN(group2.getLra()));
                    }

                    // Group 3 price
                    if (group3HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel5Group3ApplicableWeeks(getWeeekDaysDesc(group3.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group3HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group3NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel5Group3OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group3TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel5Group3Lra(getYN(group3.getLra()));
                    }

                    // Group 4 price
                    if (group4HotelProjectHotelPriceList != null) {
                        exportTenderHotelPriceResponse.setLevel5Group4ApplicableWeeks(getWeeekDaysDesc(group4.getApplicableWeeks()));
                        for (ProjectHotelPrice projectHotelPrice : group4HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group4NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                exportTenderHotelPriceResponse.setLevel5Group4OneBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                exportTenderHotelPriceResponse.setLevel5Group4TwoBreakfastPrice(projectHotelPrice.getBasePrice());
                            }
                        }
                        // 设置LRA
                        exportTenderHotelPriceResponse.setLevel5Group4Lra(getYN(group4.getLra()));
                    }
                }
            }
        } catch (Exception ex){
            logger.info("hotelLevelPriceGroupMap {}", JSON.toJSONString(hotelLevelPriceGroupMap));
            logger.info("groupProjectHotelPriceMap {}", JSON.toJSONString(groupProjectHotelPriceMap.keySet()));
            logger.info("priceType {}", priceType);
            logger.error("setExportTenderHotelPriceResponseLevelGroupPrice 异常 {}", ex.getMessage(), ex.getCause());
        }
    }

    private void setExportTenderHotelHistoryPriceResponseLevelGroupPrice(ExportTenderHotelPriceResponse exportTenderHotelPriceResponse,
                                                                  Map<Integer, List<ProjectHotelPriceGroup>> hotelLevelHistoryPriceGroupMap,
                                                                  Map<Long, List<ProjectHotelPrice>> groupProjectHotelHistoryPriceMap,
                                                                  int priceType){
        try {
            if(hotelLevelHistoryPriceGroupMap == null || hotelLevelHistoryPriceGroupMap.isEmpty()){
                return;
            }
            if(groupProjectHotelHistoryPriceMap == null || groupProjectHotelHistoryPriceMap.isEmpty()){
                return;
            }
            // 房档最多5个
            for (int i = 1; i < 6; i++) {
                List<ProjectHotelPriceGroup> levelHotelPriceGroups = hotelLevelHistoryPriceGroupMap.get(i);
                if (CollectionUtils.isEmpty(levelHotelPriceGroups)) {
                    break;
                }
                ProjectHotelPriceGroup group1 = levelHotelPriceGroups.get(0);
                ProjectHotelPriceGroup group2 = null;
                List<ProjectHotelPrice> group2HotelProjectHotelPriceList = null;
                if (levelHotelPriceGroups.size() > 1) {
                    group2 = levelHotelPriceGroups.get(1);
                    group2HotelProjectHotelPriceList = groupProjectHotelHistoryPriceMap.get(group2.getHotelPriceGroupId());
                }
                List<ProjectHotelPrice> group1HotelProjectHotelPriceList = groupProjectHotelHistoryPriceMap.get(group1.getHotelPriceGroupId());
                if (i == 1) {
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel1NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel1OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel1TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                BigDecimal level1NoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel1NoBreakfastHistoryPrice();
                                if(level1NoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel1NoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel1NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                BigDecimal level1BreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel1OneBreakfastHistoryPrice();
                                if(level1BreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel1OneBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel1OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                BigDecimal level1TwoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel1TwoBreakfastHistoryPrice();
                                if(level1TwoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel1TwoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel1TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            }
                        }
                    }
                }
                if (i == 2) {
                    // Group 1price
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel2NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel2OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel2TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                BigDecimal level2NoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel2NoBreakfastHistoryPrice();
                                if(level2NoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel2NoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel2NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                BigDecimal level2BreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel2OneBreakfastHistoryPrice();
                                if(level2BreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel2OneBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel2OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                BigDecimal level2TwoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel2TwoBreakfastHistoryPrice();
                                if(level2TwoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel2TwoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel2TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            }
                        }
                    }
                }

                if (i == 3) {
                    // Group 1 price
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel3NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel3OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel3TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                BigDecimal level3NoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel3NoBreakfastHistoryPrice();
                                if(level3NoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel3NoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel3NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                BigDecimal level3BreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel3OneBreakfastHistoryPrice();
                                if(level3BreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel3OneBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel3OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                BigDecimal level3TwoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel3TwoBreakfastHistoryPrice();
                                if(level3TwoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel3TwoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel3TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            }
                        }
                    }
                }
                if (i == 4) {
                    // Group 1 price
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel4NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel4OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel4TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                BigDecimal level4NoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel4NoBreakfastHistoryPrice();
                                if(level4NoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel4NoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel4NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                BigDecimal level4BreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel4OneBreakfastHistoryPrice();
                                if(level4BreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel4OneBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel4OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                BigDecimal level4TwoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel4TwoBreakfastHistoryPrice();
                                if(level4TwoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel4TwoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel4TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            }
                        }
                    }
                }

                if (i == 5) {
                    // Group 1 price
                    for (ProjectHotelPrice projectHotelPrice : group1HotelProjectHotelPriceList) {
                        if (projectHotelPrice.getPriceType() != priceType) {
                            continue;
                        }
                        if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                            exportTenderHotelPriceResponse.setLevel5NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                            exportTenderHotelPriceResponse.setLevel5OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                            exportTenderHotelPriceResponse.setLevel5TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                        }
                    }
                    // Group 2 price
                    if (group2HotelProjectHotelPriceList != null) {
                        for (ProjectHotelPrice projectHotelPrice : group2HotelProjectHotelPriceList) {
                            if (projectHotelPrice.getPriceType() != priceType) {
                                continue;
                            }
                            if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key) {
                                BigDecimal level5NoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel5NoBreakfastHistoryPrice();
                                if(level5NoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel5NoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel5NoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                                exportTenderHotelPriceResponse.setLevel5Group2NoBreakfastPrice(projectHotelPrice.getBasePrice());
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key) {
                                BigDecimal level5BreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel5OneBreakfastHistoryPrice();
                                if(level5BreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel5OneBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel5OneBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            } else if (projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key) {
                                BigDecimal level5TwoBreakfastHistoryPrice = exportTenderHotelPriceResponse.getLevel5TwoBreakfastHistoryPrice();
                                if(level5TwoBreakfastHistoryPrice == null || projectHotelPrice.getBasePrice().compareTo(exportTenderHotelPriceResponse.getLevel5TwoBreakfastHistoryPrice()) < 0) {
                                    exportTenderHotelPriceResponse.setLevel5TwoBreakfastHistoryPrice(projectHotelPrice.getBasePrice());
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex){
            logger.info("hotelLevelHistoryPriceGroupMap {}", JSON.toJSONString(hotelLevelHistoryPriceGroupMap));
            logger.info("groupProjectHotelHistoryPriceMap {}", JSON.toJSONString(groupProjectHotelHistoryPriceMap));
            logger.info("priceType {}", priceType);
            logger.error("setExportTenderHotelPriceResponseLevelGroupPrice 异常 {}", ex.getMessage(), ex.getCause());
        }
    }

    private String getLevelTotalRoomCountDesc(int levelNo, Map<Integer, Integer> levelRoomTotalCountMap,
                                     Map<Integer, Integer> levelRoomType1CountMap,
                                     Map<Integer, Integer> levelRoomType2CountMap
                                     ){
        String result = "";
        result = result + (levelRoomType1CountMap.containsKey(levelNo) ? levelRoomType1CountMap.get(levelNo) + "," : "null,");
        result = result + (levelRoomType2CountMap.containsKey(levelNo) ? levelRoomType2CountMap.get(levelNo) + "," : "null");
        result = result + (levelRoomTotalCountMap.containsKey(levelNo) ? levelRoomTotalCountMap.get(levelNo): "null");

       return result;
    }


    @Override
    public InputStream exportTenderPriceForTransfer(Long projectId, HttpServletRequest request, HttpServletResponse httpServletResponse) throws Exception {
        List<ProjectIntentHotel> projectIntentHotelList = projectIntentHotelDao.selectByProjectIdAndBidStates(projectId, Arrays.asList(HotelBidStateEnum.BID_WINNING.bidState));

        Map<Long, List<ExportTenderHotelPriceForTransferResponse>> hotelTransferMap = new HashMap<>();
        // 查询可用日期
        PriceApplicableDay queryPriceApplicableDay = new PriceApplicableDay();
        queryPriceApplicableDay.setProjectId(projectId);
        List<PriceApplicableDay> allPriceApplicableDayList = priceApplicableDayDao.selectPriceApplicableDayList(queryPriceApplicableDay);
        Map<Long, List<PriceApplicableDay>> hotelPriceApplicableDayMap  = allPriceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDay::getHotelId));
        Map<Long, Map<Integer, String>> hotelPriceTypeApplicableDaysMap = new HashMap<>();
        for(Long hotelId : hotelPriceApplicableDayMap.keySet()){
            Map<Integer, String> priceTypeDaysMap = new HashMap<>();
            for(PriceApplicableDay priceApplicableDay : hotelPriceApplicableDayMap.get(hotelId)){
                String priceApplyDayString = DateUtil.dateToString(priceApplicableDay.getStartDate()) + "/" + DateUtil.dateToString(priceApplicableDay.getEndDate());
                priceTypeDaysMap.putIfAbsent(priceApplicableDay.getPriceType(), "");
                String priceApplicableDays = priceTypeDaysMap.get(priceApplicableDay.getPriceType());
                if(priceApplicableDay.getPriceType() == HotelPriceTypeEnum.BASE_PRICE.key || StringUtil.isEmpty(priceApplicableDays)) {
                    priceApplicableDays = priceApplyDayString;
                } else {
                    priceApplicableDays = priceApplicableDays + "," + priceApplyDayString;
                }
                priceTypeDaysMap.put(priceApplicableDay.getPriceType(), priceApplicableDays);
            }
            hotelPriceTypeApplicableDaysMap.put(hotelId, priceTypeDaysMap);
        }

        // 查询不可以日期
        ProjectHotelBidStrategyRequest queryPriceUnapplicableDay = new ProjectHotelBidStrategyRequest();
        queryPriceUnapplicableDay.setProjectId(projectId);
        List<PriceUnapplicableDay> priceUnapplicableDayList = priceUnapplicableDayDao.selectPriceUnapplicableDayList(queryPriceUnapplicableDay);
        Map<Long, List<PriceUnapplicableDay>> hotelPriceUnapplicableDayMap = priceUnapplicableDayList.stream().collect(Collectors.groupingBy(PriceUnapplicableDay::getHotelId));
        Map<Long, String> hotelPriceTypeUnApplicableDaysMap = new HashMap<>();
        for(Long hotelId : hotelPriceUnapplicableDayMap.keySet()){
            String hotelUnApplyDaysString = "";
            for(PriceUnapplicableDay priceUnapplicableDay : hotelPriceUnapplicableDayMap.get(hotelId)){
                String priceUnApplyDayString = DateUtil.dateToString(priceUnapplicableDay.getStartDate()) + "/" + DateUtil.dateToString(priceUnapplicableDay.getEndDate());
                if(StringUtil.isEmpty(hotelUnApplyDaysString)) {
                    hotelUnApplyDaysString = priceUnApplyDayString;
                } else {
                    hotelUnApplyDaysString = hotelUnApplyDaysString + "," + priceUnApplyDayString;
                }

            }
            hotelPriceTypeUnApplicableDaysMap.put(hotelId, hotelUnApplyDaysString);
        }

        // 查询房型
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(projectId, null, null);
        Map<Long, List<PriceApplicableRoomInfoResponse>> hotelPriceApplicableRoomMap = priceApplicableRoomInfoResponseList.stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getHotelId));
        Map<Long, Map<Integer, String>> hotelRoomTypeIdsMap = new HashMap<>();
        for(Long hotelId : hotelPriceApplicableRoomMap.keySet()){
            List<PriceApplicableRoomInfoResponse> allLevelRoomTypeList =  hotelPriceApplicableRoomMap.get(hotelId);
            Map<Integer, List<PriceApplicableRoomInfoResponse>> levelRoomListMap = allLevelRoomTypeList.stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getRoomLevelNo));
            Map<Integer, String> levelRoomTypeIdsMap = new HashMap<>();
            for(Integer levelNo : levelRoomListMap.keySet()){
                StringBuilder levelRoomTypeIds = new StringBuilder();
                List<PriceApplicableRoomInfoResponse> roomInfoResponseList = levelRoomListMap.get(levelNo);
                for(PriceApplicableRoomInfoResponse roomInfoResponse : roomInfoResponseList) {
                    if(roomInfoResponse.getRoomTypeId() != null) {
                        levelRoomTypeIds.append(roomInfoResponse.getRoomTypeId()).append(",");
                    }
                }
                if(levelRoomTypeIds.length() > 0) {
                    levelRoomTypeIdsMap.put(levelNo, levelRoomTypeIds.substring(0, levelRoomTypeIds.length() - 1));
                }
            }
            hotelRoomTypeIdsMap.put(hotelId, levelRoomTypeIdsMap);
        }

        // 查询所有报价
        ProjectHotelPrice queryProjectHotelPrice = new ProjectHotelPrice();
        queryProjectHotelPrice.setProjectId(projectId);
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(queryProjectHotelPrice).stream().filter(o -> o.getHotelPriceGroupId() != null).collect(Collectors.toList());
        Map<Long, List<ProjectHotelPrice>> groupHotelPriceMap = projectHotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPrice::getHotelPriceGroupId));

        // 查询项目相关价格组
        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectId(projectId);
        Map<Long, List<ProjectHotelPriceGroup>> projectHotelPriceGroupMap = projectHotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroup::getHotelId));

        // 根据价格类型准备数据
        List<ExportTenderHotelPriceForTransferResponse> exportTenderPriceForTransferResponses = new LinkedList<>();
        for(ProjectIntentHotel projectIntentHotel : projectIntentHotelList){
            if(!hotelTransferMap.containsKey(projectIntentHotel.getHotelId())){
                hotelTransferMap.put(projectIntentHotel.getHotelId(), new ArrayList<>());
            }
            // 获取价格组
            List<ProjectHotelPriceGroup> hotelPriceGroupList = projectHotelPriceGroupMap.get(projectIntentHotel.getHotelId());
            if(CollectionUtils.isEmpty(hotelPriceGroupList)){
                logger.error("数据迁移没有房档报价{}", JSON.toJSONString(projectIntentHotel));
                continue;
            }
            for(ProjectHotelPriceGroup projectHotelPriceGroup : hotelPriceGroupList){
                List<ProjectHotelPrice> priceList = groupHotelPriceMap.get(projectHotelPriceGroup.getHotelPriceGroupId());
                if(CollectionUtils.isEmpty(priceList)){
                    logger.error("数据迁移价格列表为空 {}, {}", projectIntentHotel.getHotelId(), JSON.toJSONString(projectHotelPriceGroup));
                    continue;
                }
                for(ProjectHotelPrice projectHotelPrice : priceList){
                    ExportTenderHotelPriceForTransferResponse exportTenderHotelPriceForTransferResponse = new ExportTenderHotelPriceForTransferResponse();
                    exportTenderHotelPriceForTransferResponse.setHotelId(projectIntentHotel.getHotelId());
                    exportTenderHotelPriceForTransferResponse.setContactName(projectIntentHotel.getBidContactName());
                    exportTenderHotelPriceForTransferResponse.setContactMobile(projectIntentHotel.getBidContactMobile());
                    exportTenderHotelPriceForTransferResponse.setContactEmail(projectIntentHotel.getBidContactEmail());
                    exportTenderHotelPriceForTransferResponse.setBaseTypeDay(hotelPriceTypeApplicableDaysMap.get(projectHotelPrice.getHotelId()).get(HotelPriceTypeEnum.BASE_PRICE.key));
                    if(hotelPriceTypeApplicableDaysMap.get(projectHotelPrice.getHotelId()).get(HotelPriceTypeEnum.SEASON_1_PRICE.key) != null){
                        exportTenderHotelPriceForTransferResponse.setSeason1Day(hotelPriceTypeApplicableDaysMap.get(projectHotelPrice.getHotelId()).get(HotelPriceTypeEnum.SEASON_1_PRICE.key));
                    }
                    if(hotelPriceTypeApplicableDaysMap.get(projectHotelPrice.getHotelId()).get(HotelPriceTypeEnum.SEASON_2_PRICE.key) != null){
                        exportTenderHotelPriceForTransferResponse.setSeason2Day(hotelPriceTypeApplicableDaysMap.get(projectHotelPrice.getHotelId()).get(HotelPriceTypeEnum.SEASON_2_PRICE.key));
                    }
                    if(hotelPriceTypeUnApplicableDaysMap.containsKey(projectHotelPrice.getHotelId())){
                        exportTenderHotelPriceForTransferResponse.setUnApplicableDay(hotelPriceTypeUnApplicableDaysMap.get(projectHotelPrice.getHotelId()));
                    }
                    exportTenderHotelPriceForTransferResponse.setLevelNo(String.valueOf(projectHotelPriceGroup.getRoomLevelNo()));
                    if(hotelRoomTypeIdsMap.containsKey(projectHotelPrice.getHotelId()) &&
                            hotelRoomTypeIdsMap.get(projectHotelPrice.getHotelId()).containsKey(projectHotelPriceGroup.getRoomLevelNo())){
                        exportTenderHotelPriceForTransferResponse.setRoomTypeId(hotelRoomTypeIdsMap.get(projectHotelPrice.getHotelId()).get(projectHotelPriceGroup.getRoomLevelNo()));
                    }
                    exportTenderHotelPriceForTransferResponse.setPriceType(String.valueOf(projectHotelPrice.getPriceType()));
                    exportTenderHotelPriceForTransferResponse.setBreakfastNum(String.valueOf(projectHotelPrice.getBreakfastNum()));
                    exportTenderHotelPriceForTransferResponse.setPrice(String.valueOf(projectHotelPrice.getBasePrice()));
                    // 转换成标准星期
                    exportTenderHotelPriceForTransferResponse.setApplicableWeeks(convertToJiaLiUploadWeek(projectHotelPriceGroup.getApplicableWeeks()));
                    exportTenderHotelPriceForTransferResponse.setLra(String.valueOf(projectHotelPriceGroup.getLra()));
                    exportTenderHotelPriceForTransferResponse.setRemark(projectHotelPriceGroup.getRemark());
                    exportTenderHotelPriceForTransferResponse.setCancelRestrictType(String.valueOf(projectHotelPriceGroup.getCancelRestrictType()));
                    exportTenderHotelPriceForTransferResponse.setCancelRestrictDay(String.valueOf(projectHotelPriceGroup.getCancelRestrictDay()));
                    exportTenderHotelPriceForTransferResponse.setGetCancelRestrictTime(projectHotelPriceGroup.getCancelRestrictTime());
                    exportTenderPriceForTransferResponses.add(exportTenderHotelPriceForTransferResponse);
                }
            }
        }

        Map data = new HashMap();
        data.put("reportList", exportTenderPriceForTransferResponses);
        return ExcelHelper.exportFromRemote(data, BaseConfig.getAccessFtpFileUrl() + FileTypeAndPathEnum.EXPORT_EXCEL_TEMPLATE.filePath + "/exportTransferHotelLevelPrice.xlsx");

    }

    private static String convertToJiaLiUploadWeek(String weeks){
        String uploadWeek = "";
        for(String week : weeks.split(",")){
            uploadWeek = uploadWeek +  WeekEnum.getChinaSeq(Integer.valueOf(week)) + ",";
        }
        return  uploadWeek.substring(0, uploadWeek.length()-1);
    }

    @Override
    public InputStream exportTenderPrice(Long projectId, HttpServletRequest request, HttpServletResponse httpServletResponse) throws Exception {

        // 查询项目所有酒店报价
        List<ExportTenderHotelPriceResponse> hotelTenderPriceResponses = projectHotelPriceDao.queryHotelTenderInfo(projectId);

        // 导出数据
        return exportHotelGroupTenderPriceInputStream(projectId, hotelTenderPriceResponses, false);

    }


    @Override
    public InputStream exportHotelGroupTenderPrice(Long projectId, UserDTO userDTO, HttpServletRequest request, HttpServletResponse httpServletResponse) throws Exception {
        // 查询项目所有酒店报价
        List<ExportTenderHotelPriceResponse> hotelTenderPriceResponses = projectHotelPriceDao.queryHotelGroupTenderInfo(projectId, userDTO.getOrgDTO().getOrgId(), userDTO.getHotelGroupBrandIdList());

        // 导出数据
        return exportHotelGroupTenderPriceInputStream(projectId, hotelTenderPriceResponses, true);
    }


    private InputStream exportHotelGroupTenderPriceInputStream(Long projectId, List<ExportTenderHotelPriceResponse> hotelTenderPriceResponses, boolean isHotelGroupExport) throws Exception {
        // 查询项目信息
        Project project = projectDao.selectByPrimaryKey(projectId);

        // 查询项目备注信息
        List<ProjectHotelRemarkResponse> projectHotelRemarks = projectHotelRemarkDao.selectList(projectId, null, null);
        Map<Long, List<ProjectHotelRemarkResponse>> hotelProjectRemarkListMap = projectHotelRemarks.stream().collect(Collectors.groupingBy(ProjectHotelRemarkResponse::getHotelId));

        // 查询可用日期
        PriceApplicableDay queryPriceApplicableDay = new PriceApplicableDay();
        queryPriceApplicableDay.setProjectId(projectId);
        List<PriceApplicableDay> allPriceApplicableDayList = priceApplicableDayDao.selectPriceApplicableDayList(queryPriceApplicableDay);
        Map<Long, List<PriceApplicableDay>> hotelPriceApplicableDayMap  = allPriceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDay::getHotelId));


        // 查询不可以日期
        ProjectHotelBidStrategyRequest queryPriceUnapplicableDay = new ProjectHotelBidStrategyRequest();
        queryPriceUnapplicableDay.setProjectId(projectId);
        List<PriceUnapplicableDay> priceUnapplicableDayList = priceUnapplicableDayDao.selectPriceUnapplicableDayList(queryPriceUnapplicableDay);
        Map<Long, List<PriceUnapplicableDay>> hotelPriceUnapplicableDayMap = priceUnapplicableDayList.stream().collect(Collectors.groupingBy(PriceUnapplicableDay::getHotelId));

        // 查询房型
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(projectId, null, null);
        Map<Long, List<PriceApplicableRoomInfoResponse>> hotelPriceApplicableRoomMap = priceApplicableRoomInfoResponseList.stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getHotelId));

        // 查询所有报价
        ProjectHotelPrice queryProjectHotelPrice = new ProjectHotelPrice();
        queryProjectHotelPrice.setProjectId(projectId);
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(queryProjectHotelPrice);

        // 过滤没有价格组的酒店
        HashSet<Long> noGroupIdHotelIdSet = new HashSet<>(projectHotelPriceList.stream().filter(projectHotelPrice -> projectHotelPrice.getHotelPriceGroupId() == null).map(ProjectHotelPrice::getHotelId).collect(Collectors.toList()));
        Map<Long, List<ProjectHotelPrice>> groupProjectHotelPriceMap = projectHotelPriceList.stream().filter(projectHotelPrice -> !noGroupIdHotelIdSet.contains(projectHotelPrice.getHotelId())).collect(Collectors.groupingBy(ProjectHotelPrice::getHotelPriceGroupId));
        if(CollectionUtils.isNotEmpty(noGroupIdHotelIdSet)) {
            logger.info("酒店报价存在没有分组价格projectId {}, 酒店ID {}", projectId, JSON.toJSONString(noGroupIdHotelIdSet));
        }

        // 查询酒店报价策略信息
        ProjectHotelBidStrategy queryProjectHotelBidStrategy = new ProjectHotelBidStrategy();
        queryProjectHotelBidStrategy.setProjectId(projectId);
        List<ProjectHotelBidStrategy> projectHotelBidStrategies = projectHotelBidStrategyDao.selectByProjectHotelBidStrategy(queryProjectHotelBidStrategy);
        Map<Long, ProjectHotelBidStrategy> projectHotelBidStrategyMap = projectHotelBidStrategies.stream().collect(Collectors.toMap(ProjectHotelBidStrategy::getHotelId, Function.identity()));

        // 查询项目相关价格组
        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectId(projectId);
        Map<Long, List<ProjectHotelPriceGroup>> projectHotelPriceGroupMap = projectHotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroup::getHotelId));

        // 查询历史项目相关价格组
        Map<Long, List<ProjectHotelPriceGroup>> projectHotelHistoryPriceGroupMap = new HashMap<>();
        Map<Long, List<ProjectHotelPrice>> groupProjectHotelHistoryPriceMap = new HashMap<>();
        Map<Integer, List<ProjectHotelPriceGroup>> hotelLevelHistoryPriceGroupMap = new HashMap<>();
        if(project.getRelatedProjectId() != null && project.getRelatedProjectId() > 0) {
            List<ProjectHotelPriceGroup> projectHotelHistoryPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectId(project.getRelatedProjectId());
            projectHotelHistoryPriceGroupMap = projectHotelHistoryPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroup::getHotelId));

            ProjectHotelPrice queryProjectHotelHistoryPrice = new ProjectHotelPrice();
            queryProjectHotelHistoryPrice.setProjectId(project.getRelatedProjectId());
            List<ProjectHotelPrice> projectHotelHistoryPriceList = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(queryProjectHotelHistoryPrice);
            // 过滤没有价格组的酒店
            HashSet<Long> noGroupIdHistoryHotelIdSet = new HashSet<>(projectHotelHistoryPriceList.stream().filter(projectHotelPrice -> projectHotelPrice.getHotelPriceGroupId() == null).map(ProjectHotelPrice::getHotelId).collect(Collectors.toList()));
            groupProjectHotelHistoryPriceMap = projectHotelHistoryPriceList.stream().filter(projectHotelPrice -> !noGroupIdHistoryHotelIdSet.contains(projectHotelPrice.getHotelId())).collect(Collectors.groupingBy(ProjectHotelPrice::getHotelPriceGroupId));
            if(CollectionUtils.isNotEmpty(noGroupIdHistoryHotelIdSet)) {
                logger.info("酒店历史报价存在没有分组价格projectId {}, 酒店ID {}", projectId, JSON.toJSONString(noGroupIdHistoryHotelIdSet));
            }
        }


        //计算每个酒店距离poi的距离 （酒店集团不需要poi信息）
        if(!isHotelGroupExport) {
            List<ProjectPoiInfoResponse> projectPoiInfoResponses = projectPoiDao.selectProjectPoiInfo(projectId);
            if (CollectionUtils.isNotEmpty(projectPoiInfoResponses)) {
                for (ExportTenderHotelPriceResponse hotelPriceResponse : hotelTenderPriceResponses) {
                    if (noGroupIdHotelIdSet.contains(hotelPriceResponse.getHotelId())) {
                        continue;
                    }
                    // 设置币种
                    hotelPriceResponse.setCurrencyCode("CNY");

                    if (hotelPriceResponse.getLatBaidu() == null || hotelPriceResponse.getLngBaidu() == null) {
                        continue;
                    }
                    //只需要距离最小的值
                    double minDistance = 0d;
                    String poiName = "";
                    for (ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponses) {
                        //只有相同城市的才计算距离。
                        if (!projectPoiInfoResponse.getCityCode().equals(hotelPriceResponse.getCityCode())) {
                            continue;
                        }
                        if (projectPoiInfoResponse.getLngBaiDu() == null || projectPoiInfoResponse.getLatBaiDu() == null) {
                            continue;
                        }
                        try {
                            double distance = LocationUtil.getDistance(projectPoiInfoResponse.getLatBaiDu(), projectPoiInfoResponse.getLngBaiDu(), hotelPriceResponse.getLatBaidu(), hotelPriceResponse.getLngBaidu());
                            if (minDistance == 0 || distance <= minDistance) {
                                minDistance = distance;
                                poiName = projectPoiInfoResponse.getPoiName();
                            }
                            hotelPriceResponse.setPoiName(poiName);
                            hotelPriceResponse.setPoiDistance(BigDecimal.valueOf(minDistance * 0.001).setScale(1, RoundingMode.HALF_UP).doubleValue());
                        } catch (Exception e) {
                            logger.error("计算项目poi距离异常，poiId" + projectPoiInfoResponse.getPoiId() + ",hotelPriceResponse:" + JSON.toJSONString(hotelPriceResponse));
                        }
                    }
                }
            }
        }

        // 根据价格类型准备数据
        List<ExportTenderHotelPriceResponse> exportTenderPriceResponses = new ArrayList<>();
        Map<Long, ProjectIntentHotel> intentHotelMap = new HashMap<>();
        for(ExportTenderHotelPriceResponse hotelPriceResponse : hotelTenderPriceResponses){
            if(noGroupIdHotelIdSet.contains(hotelPriceResponse.getHotelId())){
                continue;
            }
            // 过滤未报价，否决，放弃报价
            if(Objects.equals(hotelPriceResponse.getBidState(), HotelBidStateEnum.NO_BID.bidState) ||
                    Objects.equals(hotelPriceResponse.getBidState(), HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState)){
                continue;
            }
            if(hotelPriceResponse.getHotelId() == null){
                logger.info("酒店基础信息不存在 {}", hotelTenderPriceResponses );
                continue;
            }

            long hotelId = hotelPriceResponse.getHotelId();
            Map<Integer, String> levelRoomTypeMap = new HashMap<>();
            Map<Integer, String> levelRoomNameMap = new HashMap<>();
            Map<Integer, Integer> levelRoomTotalCountMap = new HashMap<>();
            Map<Integer, Integer> levelRoomType1CountMap = new HashMap<>();
            Map<Integer, Integer> levelRoomType2CountMap = new HashMap<>();

            if(hotelPriceApplicableRoomMap.containsKey(hotelId)) {
                Map<Integer, List<PriceApplicableRoomInfoResponse>> roomLevelRoomInfoMap = hotelPriceApplicableRoomMap.get(hotelId).stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getRoomLevelNo));

                for (Integer levelNo : roomLevelRoomInfoMap.keySet()) {
                    String roomTypeIds = "";
                    String roomNames = "";
                    List<PriceApplicableRoomInfoResponse> roomInfoResponseList = roomLevelRoomInfoMap.get(levelNo);
                    for (PriceApplicableRoomInfoResponse roomInfoResponse : roomInfoResponseList) {
                        roomTypeIds = roomTypeIds + roomInfoResponse.getRoomTypeId() + ",";
                        roomNames = roomNames + roomInfoResponse.getRoomTypeName() + ",";
                    }
                    roomTypeIds = roomTypeIds.substring(0, roomTypeIds.length() - 1);
                    roomNames = roomNames.substring(0, roomNames.length() - 1);
                    levelRoomTypeMap.put(levelNo, roomTypeIds);
                    levelRoomNameMap.put(levelNo, roomNames);
                }
            }

            // 获取预约报价数据
            if(!intentHotelMap.containsKey(hotelId)){
                ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectId, hotelId);
                intentHotelMap.put(hotelId, projectIntentHotel);
            }
            ProjectIntentHotel projectIntentHotel = intentHotelMap.get(hotelId);

            // 根据房档分组价格组
            List<ProjectHotelPriceGroup> hotelPriceGroupList = projectHotelPriceGroupMap.get(hotelId);
            if(hotelPriceGroupList == null){
                logger.error("hotel hotelPriceGroupList is null {}", hotelId);
                continue;
            }
            Map<Integer, List<ProjectHotelPriceGroup>> hotelLevelPriceGroupMap = hotelPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroup::getRoomLevelNo));
            for(Integer roomLevelNo : hotelLevelPriceGroupMap.keySet()){
                ProjectHotelPriceGroup projectHotelPriceGroup = hotelLevelPriceGroupMap.get(roomLevelNo).get(0);
                // 设置房间数量
                levelRoomTotalCountMap.put(roomLevelNo, projectHotelPriceGroup.getLevelTotalRoomCount());
                levelRoomType1CountMap.put(roomLevelNo, projectHotelPriceGroup.getLevelRoomType1Count());
                levelRoomType2CountMap.put(roomLevelNo, projectHotelPriceGroup.getLevelRoomType2Count());
                // Lanyon导入数存在没有房型情况
                if(BidUtil.isLanyonImport(projectIntentHotel) == RfpConstant.constant_1){
                    if(!levelRoomTypeMap.containsKey(roomLevelNo)){
                        levelRoomTypeMap.put(roomLevelNo, "");
                    } else {
                        levelRoomTypeMap.put(roomLevelNo, levelRoomTypeMap.get(roomLevelNo) + "");
                    }
                    if(!levelRoomNameMap.containsKey(roomLevelNo)){
                        levelRoomNameMap.put(roomLevelNo, projectHotelPriceGroup.getLanyonRoomDesc());
                    } else {
                        levelRoomNameMap.put(roomLevelNo, levelRoomNameMap.get(roomLevelNo) + projectHotelPriceGroup.getLanyonRoomDesc());
                    }
                }
            }

            // 适用日期
            List<PriceApplicableDay> priceApplicableDayList = hotelPriceApplicableDayMap.get(hotelId);
            Map<Integer, String> priceApplicableDayMap = new HashMap<>();
            for(PriceApplicableDay priceApplicableDay : priceApplicableDayList){
                if(!priceApplicableDayMap.containsKey(priceApplicableDay.getPriceType())){
                    priceApplicableDayMap.put(priceApplicableDay.getPriceType(), "");
                }
                String days = priceApplicableDayMap.get(priceApplicableDay.getPriceType()) + DateUtil.dateToString(priceApplicableDay.getStartDate()) + "/" + DateUtil.dateToString(priceApplicableDay.getEndDate()) + ",";
                priceApplicableDayMap.put(priceApplicableDay.getPriceType(), days);
            }

            // 不适应日期
            List<PriceUnapplicableDay> hotelPriceUnapplicableDayList = hotelPriceUnapplicableDayMap.get(hotelId);
            String unapplicableDays = "";
            Long unApplicableDayCount = 0L;
            if(!CollectionUtils.isEmpty(hotelPriceUnapplicableDayList)){
                for(PriceUnapplicableDay priceUnapplicableDay : hotelPriceUnapplicableDayList){
                    unapplicableDays = unapplicableDays + DateUtil.dateToString(priceUnapplicableDay.getStartDate()) + "/" + DateUtil.dateToString(priceUnapplicableDay.getEndDate()) + ",";
                    long day = DateUtil.getDay(priceUnapplicableDay.getStartDate(), priceUnapplicableDay.getEndDate());
                    // 开始，结束都算天，开始和结束是同天算1天
                    if(DateUtil.isEqualsDate(priceUnapplicableDay.getStartDate(), priceUnapplicableDay.getEndDate())){
                        day = 1;
                    } else {
                        day = day+1;
                    }
                    unApplicableDayCount = unApplicableDayCount + day;
                }
                if(!StringUtil.isEmpty(unapplicableDays)){
                    unapplicableDays = unapplicableDays.substring(0, unapplicableDays.length()-1);
                }
            }

            ExportTenderHotelPriceResponse baseExportTenderHotelPriceResponse = new ExportTenderHotelPriceResponse();
            BeanUtils.copyProperties(hotelPriceResponse, baseExportTenderHotelPriceResponse);
            baseExportTenderHotelPriceResponse.setCurrencyCode("CNY");
            baseExportTenderHotelPriceResponse.setModifyTimeString(baseExportTenderHotelPriceResponse.getModifyTime() != null ? cn.hutool.core.date.DateUtil.formatDateTime(baseExportTenderHotelPriceResponse.getModifyTime()) : null);
            baseExportTenderHotelPriceResponse.setPriceUnApplicableDayCount(unApplicableDayCount.intValue());
            baseExportTenderHotelPriceResponse.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);

            // 设置报价备注
            List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = hotelProjectRemarkListMap.get(baseExportTenderHotelPriceResponse.getHotelId());
            baseExportTenderHotelPriceResponse.setBidRemark(projectService.generateProjectExportBidRemark(projectHotelRemarkResponseList));

            // 设置房档房型
            baseExportTenderHotelPriceResponse.setLevel1RoomTypeIds(levelRoomTypeMap.get(RoomLevelEnum.ONE.key));
            baseExportTenderHotelPriceResponse.setLevel1RoomTypeNames(levelRoomNameMap.get(RoomLevelEnum.ONE.key));
            baseExportTenderHotelPriceResponse.setLevel1TotalRoomCount(getLevelTotalRoomCountDesc(RoomLevelEnum.ONE.key, levelRoomTotalCountMap, levelRoomType1CountMap, levelRoomType2CountMap));
            List<ProjectHotelPriceGroup> hotelPriceGroups = hotelLevelPriceGroupMap.get(RoomLevelEnum.ONE.key);
            if(CollectionUtils.isEmpty(hotelPriceGroups)){
                logger.error("导出Level one 价格组为空 hotelId: {}, hotelLevePriceGroupMap {}", hotelId, JSON.toJSONString(hotelLevelPriceGroupMap));
            }
            if(CollectionUtils.isNotEmpty(hotelPriceGroups)){
                ProjectHotelPriceGroup firstPriceGroup = hotelPriceGroups.get(0);

                // 设置取消政策
                if(firstPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.FREE.key){
                    baseExportTenderHotelPriceResponse.setCancelRestrict(CancelRestrictTypeEnum.getValueByKey(firstPriceGroup.getCancelRestrictType()));
                } else if(firstPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.PAY.key){
                    String cancelRestrict = "提前" + firstPriceGroup.getCancelRestrictDay() + "天" + firstPriceGroup.getCancelRestrictTime() + "之前可免费退改，之后不可退改";
                    baseExportTenderHotelPriceResponse.setCancelRestrict(cancelRestrict);
                } else if(firstPriceGroup.getCancelRestrictType() == CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key){
                    String cancelRestrict = "提前" + firstPriceGroup.getCancelRestrictDay() + "天" + firstPriceGroup.getCancelRestrictTime() + "点之前可免费退改，之后收取首晚房费的退订费";
                    baseExportTenderHotelPriceResponse.setCancelRestrict(cancelRestrict);
                }
                baseExportTenderHotelPriceResponse.setLevel1Group1Id(firstPriceGroup.getHotelPriceGroupId());
                baseExportTenderHotelPriceResponse.setLevel1Group1ApplicableWeeks(getWeeekDaysDesc(firstPriceGroup.getApplicableWeeks()));
                baseExportTenderHotelPriceResponse.setRemark(firstPriceGroup.getRemark());
                baseExportTenderHotelPriceResponse.setPriceTypeApplicableDates(priceApplicableDayMap.get(HotelPriceTypeEnum.BASE_PRICE.key).substring(0, priceApplicableDayMap.get(HotelPriceTypeEnum.BASE_PRICE.key).length()-1));
                baseExportTenderHotelPriceResponse.setSeasonCount(priceApplicableDayMap.size());
                baseExportTenderHotelPriceResponse.setPriceUnApplicableDay(unapplicableDays);
                baseExportTenderHotelPriceResponse.setPriceUnApplicableDayCount(unApplicableDayCount.intValue());

                // 设置酒店报价策略
                ProjectHotelBidStrategy projectHotelBidStrategy =  projectHotelBidStrategyMap.get(hotelId);
                if(projectHotelBidStrategy != null) {
                    baseExportTenderHotelPriceResponse.setSupportWifi(getYN(projectHotelBidStrategy.getSupportWifi()));
                    baseExportTenderHotelPriceResponse.setSupportIncludeTaxService(getYN(projectHotelBidStrategy.getSupportIncludeTaxService()));
                    baseExportTenderHotelPriceResponse.setSupportCoPay(getYN(projectHotelBidStrategy.getSupportCoPay()));
                    baseExportTenderHotelPriceResponse.setSupportPayAtHotel(getYN(projectHotelBidStrategy.getSupportPayAtHotel()));
                    baseExportTenderHotelPriceResponse.setSupportNoGuarantee(getYN(projectHotelBidStrategy.getSupportNoGuarantee()));
                    baseExportTenderHotelPriceResponse.setSupportPayEarlyCheckout(getYN(projectHotelBidStrategy.getSupportPayEarlyCheckout()));
                    baseExportTenderHotelPriceResponse.setSupportCheckinInfo(getYN(projectHotelBidStrategy.getSupportCheckinInfo()));
                    baseExportTenderHotelPriceResponse.setEarlyCheckinTime(projectHotelBidStrategy.getEarlyCheckinTime());
                    baseExportTenderHotelPriceResponse.setLateCheckoutTime(projectHotelBidStrategy.getLateCheckoutTime());
                    baseExportTenderHotelPriceResponse.setSupportInvoiceSixPercent("N");
                    if(projectHotelBidStrategy.getProvideInvoiceType() != null && projectHotelBidStrategy.getProvideInvoiceType() == InvoiceEnum.SPECIAL.getKey() &&
                            projectHotelBidStrategy.getProvideInvoiceTaxRate() !=null && projectHotelBidStrategy.getProvideInvoiceTaxRate().compareTo(new BigDecimal("6")) == 0
                    ) {
                        baseExportTenderHotelPriceResponse.setSupportInvoiceSixPercent("Y");
                    }
                    if(projectHotelBidStrategy.getProvideInvoiceType() == null || projectHotelBidStrategy.getProvideInvoiceTaxRate() == null){
                        baseExportTenderHotelPriceResponse.setSupportInvoiceSixPercent("");
                    }
                }
                // 设置 Level1 到 5 价格
                setExportTenderHotelPriceResponseLevelGroupPrice(baseExportTenderHotelPriceResponse,
                        hotelLevelPriceGroupMap, groupProjectHotelPriceMap, HotelPriceTypeEnum.BASE_PRICE.key);

                // 设置 Level1 到 5 历史价格
                List<ProjectHotelPriceGroup> hotelHistoryPriceGroupList = projectHotelHistoryPriceGroupMap.get(hotelId);
                if(CollectionUtils.isNotEmpty(hotelHistoryPriceGroupList)){
                    hotelLevelHistoryPriceGroupMap = hotelHistoryPriceGroupList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroup::getRoomLevelNo));
                    setExportTenderHotelHistoryPriceResponseLevelGroupPrice(baseExportTenderHotelPriceResponse,
                            hotelLevelHistoryPriceGroupMap, groupProjectHotelHistoryPriceMap, HotelPriceTypeEnum.BASE_PRICE.key);
                } else {
                    hotelLevelHistoryPriceGroupMap = null;
                }

            }

            if(levelRoomTypeMap.containsKey(RoomLevelEnum.TWO.key)){
                baseExportTenderHotelPriceResponse.setLevel2RoomTypeIds(levelRoomTypeMap.get(RoomLevelEnum.TWO.key));
                baseExportTenderHotelPriceResponse.setLevel2RoomTypeNames(levelRoomNameMap.get(RoomLevelEnum.TWO.key));
                baseExportTenderHotelPriceResponse.setLevel2TotalRoomCount(getLevelTotalRoomCountDesc(RoomLevelEnum.TWO.key, levelRoomTotalCountMap, levelRoomType1CountMap, levelRoomType2CountMap));
            }
            if(levelRoomTypeMap.containsKey(RoomLevelEnum.THREE.key)){
                baseExportTenderHotelPriceResponse.setLevel3RoomTypeIds(levelRoomTypeMap.get(RoomLevelEnum.THREE.key));
                baseExportTenderHotelPriceResponse.setLevel3RoomTypeNames(levelRoomNameMap.get(RoomLevelEnum.THREE.key));
                baseExportTenderHotelPriceResponse.setLevel3TotalRoomCount(getLevelTotalRoomCountDesc(RoomLevelEnum.THREE.key, levelRoomTotalCountMap, levelRoomType1CountMap, levelRoomType2CountMap));
            }
            if(levelRoomTypeMap.containsKey(RoomLevelEnum.FOUR.key)){
                baseExportTenderHotelPriceResponse.setLevel4RoomTypeIds(levelRoomTypeMap.get(RoomLevelEnum.FOUR.key));
                baseExportTenderHotelPriceResponse.setLevel4RoomTypeNames(levelRoomNameMap.get(RoomLevelEnum.FOUR.key));
                baseExportTenderHotelPriceResponse.setLevel4TotalRoomCount(getLevelTotalRoomCountDesc(RoomLevelEnum.FOUR.key, levelRoomTotalCountMap, levelRoomType1CountMap, levelRoomType2CountMap));
            }
            if(levelRoomTypeMap.containsKey(RoomLevelEnum.FIVE.key)){
                baseExportTenderHotelPriceResponse.setLevel5RoomTypeIds(levelRoomTypeMap.get(RoomLevelEnum.FIVE.key));
                baseExportTenderHotelPriceResponse.setLevel5RoomTypeNames(levelRoomNameMap.get(RoomLevelEnum.FIVE.key));
                baseExportTenderHotelPriceResponse.setLevel5TotalRoomCount(getLevelTotalRoomCountDesc(RoomLevelEnum.FIVE.key, levelRoomTotalCountMap, levelRoomType1CountMap, levelRoomType2CountMap));
            }
            exportTenderPriceResponses.add(baseExportTenderHotelPriceResponse);

            // Season1
            if(priceApplicableDayMap.containsKey(HotelPriceTypeEnum.SEASON_1_PRICE.key)){
                ExportTenderHotelPriceResponse season1ExportTenderHotelPriceResponse = new ExportTenderHotelPriceResponse();
                BeanUtils.copyProperties(baseExportTenderHotelPriceResponse, season1ExportTenderHotelPriceResponse);
                resetExportTenderHotelPriceResponse(season1ExportTenderHotelPriceResponse);
                season1ExportTenderHotelPriceResponse.setPriceType(HotelPriceTypeEnum.SEASON_1_PRICE.key);
                season1ExportTenderHotelPriceResponse.setPriceTypeApplicableDates(priceApplicableDayMap.get(HotelPriceTypeEnum.SEASON_1_PRICE.key).substring(0, priceApplicableDayMap.get(HotelPriceTypeEnum.SEASON_1_PRICE.key).length()-1));
                season1ExportTenderHotelPriceResponse.setSeasonCount(priceApplicableDayMap.size());
                // 设置 Level1 到 5 价格
                setExportTenderHotelPriceResponseLevelGroupPrice(season1ExportTenderHotelPriceResponse,
                        hotelLevelPriceGroupMap, groupProjectHotelPriceMap, HotelPriceTypeEnum.SEASON_1_PRICE.key);

                // 设置 Level1 到 5 历史价格
                setExportTenderHotelHistoryPriceResponseLevelGroupPrice(season1ExportTenderHotelPriceResponse,
                        hotelLevelHistoryPriceGroupMap, groupProjectHotelHistoryPriceMap, HotelPriceTypeEnum.SEASON_1_PRICE.key);
                exportTenderPriceResponses.add(season1ExportTenderHotelPriceResponse);
            }

            // Season2
            if(priceApplicableDayMap.containsKey(HotelPriceTypeEnum.SEASON_2_PRICE.key)){
                ExportTenderHotelPriceResponse season2ExportTenderHotelPriceResponse = new ExportTenderHotelPriceResponse();
                BeanUtils.copyProperties(baseExportTenderHotelPriceResponse, season2ExportTenderHotelPriceResponse);
                resetExportTenderHotelPriceResponse(season2ExportTenderHotelPriceResponse);
                season2ExportTenderHotelPriceResponse.setPriceType(HotelPriceTypeEnum.SEASON_2_PRICE.key);
                season2ExportTenderHotelPriceResponse.setPriceTypeApplicableDates(priceApplicableDayMap.get(HotelPriceTypeEnum.SEASON_2_PRICE.key).substring(0, priceApplicableDayMap.get(HotelPriceTypeEnum.SEASON_2_PRICE.key).length()-1));
                season2ExportTenderHotelPriceResponse.setSeasonCount(priceApplicableDayMap.size());
                // 设置 Level1 到 5 价格
                setExportTenderHotelPriceResponseLevelGroupPrice(season2ExportTenderHotelPriceResponse,
                        hotelLevelPriceGroupMap, groupProjectHotelPriceMap, HotelPriceTypeEnum.SEASON_2_PRICE.key);


                // 设置 Level1 到 5 历史价格
                setExportTenderHotelHistoryPriceResponseLevelGroupPrice(season2ExportTenderHotelPriceResponse,
                        hotelLevelHistoryPriceGroupMap, groupProjectHotelHistoryPriceMap, HotelPriceTypeEnum.SEASON_2_PRICE.key);
                exportTenderPriceResponses.add(season2ExportTenderHotelPriceResponse);
            }

        }
        Map data = new HashMap();
        if (CollectionUtils.isEmpty(exportTenderPriceResponses)) {
            logger.warn("未查询到酒店报价相关信息");

        } else {
            // 设置自定义策略
            QueryCustomTendStrategyRequest queryCustomTendStrategyRequest = new QueryCustomTendStrategyRequest();
            queryCustomTendStrategyRequest.setProjectId(projectId);
            List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponseList = projectCustomTendStrategyDao.queryProjectCustomTendStrategy(queryCustomTendStrategyRequest);
            Map<Long, Integer> tendStrategyIdIndexcustomMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(queryCustomTendStrategyResponseList)) {
                int j = 1;
                for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : queryCustomTendStrategyResponseList) {
                    data.put("customBidStrategy" + j, " (" + queryCustomTendStrategyResponse.getStrategyName() + ")");
                    tendStrategyIdIndexcustomMap.put(queryCustomTendStrategyResponse.getCustomTendStrategyId(), j);
                    j++;

                }

                for(ExportTenderHotelPriceResponse exportTenderHotelPriceResponse : exportTenderPriceResponses){
                    Long hotelId = exportTenderHotelPriceResponse.getHotelId();
                    List<ProjectCustomBidStrategy> projectHotelCustomBidStrategies = projectHotelBidStrategyService.queryProjectCustomBidStrategyList(projectId, hotelId);
                    if(CollectionUtils.isNotEmpty(projectHotelCustomBidStrategies)){
                        for(ProjectCustomBidStrategy projectCustomBidStrategy : projectHotelCustomBidStrategies){
                            // 项目自定义策略不存在
                            if(!tendStrategyIdIndexcustomMap.containsKey(projectCustomBidStrategy.getCustomTendStrategyId())){
                                continue;
                            }
                            int i = tendStrategyIdIndexcustomMap.get(projectCustomBidStrategy.getCustomTendStrategyId());
                            String supportProjectCustomBidStrategy = projectCustomBidStrategy.getSupportStrategyName() == RfpConstant.constant_1 ? "Y" : "N";
                            if(projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                                supportProjectCustomBidStrategy = projectCustomBidStrategy.getSupportStrategyText();
                            }
                            if(CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())){
                                supportProjectCustomBidStrategy = "";
                                for(ProjectCustomBidStrategyOption option : projectCustomBidStrategy.getOptions()){
                                    if(Objects.equals(option.getIsSupport(), RfpConstant.constant_1)){
                                        supportProjectCustomBidStrategy = supportProjectCustomBidStrategy  + option.getOptionName() + ";";
                                    }
                                }
                                if(StringUtil.isValidString(supportProjectCustomBidStrategy)){
                                    supportProjectCustomBidStrategy = supportProjectCustomBidStrategy.substring(0, supportProjectCustomBidStrategy.length() -1);
                                }
                            }
                            ExportTenderHotelPriceResponse.setSupportCustomBidStrategy(i, exportTenderHotelPriceResponse, supportProjectCustomBidStrategy);
                        }
                    }

                }
            }
        }
        data.put("reportList", exportTenderPriceResponses);

        String excelFileName = isHotelGroupExport ? "exportHotelGroupHotelLevelPrice.xlsx" : "exportHotelLevelPrice.xlsx";
        return ExcelHelper.exportFromRemote(data, BaseConfig.getAccessFtpFileUrl() + FileTypeAndPathEnum.EXPORT_EXCEL_TEMPLATE.filePath + "/" + excelFileName);
    }

    private void resetExportTenderHotelPriceResponse(ExportTenderHotelPriceResponse exportTenderHotelPriceResponse){
        exportTenderHotelPriceResponse.setLevel1Group1ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel1Group1NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group1OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group1TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group2ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel1Group2NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group2OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group2TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group1Lra("");
        exportTenderHotelPriceResponse.setLevel1Group2Lra("");
        exportTenderHotelPriceResponse.setLevel1Group3Lra("");
        exportTenderHotelPriceResponse.setLevel1Group4Lra("");
        exportTenderHotelPriceResponse.setLevel1Group3ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel1Group3NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group3OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group3TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group4ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel1Group4NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group4OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel1Group4TwoBreakfastPrice(null);




        exportTenderHotelPriceResponse.setLevel2Group1ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel2Group1NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group1OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group1TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group2ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel2Group2NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group2OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group2TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group1Lra("");
        exportTenderHotelPriceResponse.setLevel2Group2Lra("");
        exportTenderHotelPriceResponse.setLevel2Group3Lra("");
        exportTenderHotelPriceResponse.setLevel2Group4Lra("");
        exportTenderHotelPriceResponse.setLevel2Group3ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel2Group3NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group3OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group3TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group4ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel2Group4NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group4OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel2Group4TwoBreakfastPrice(null);


        exportTenderHotelPriceResponse.setLevel3Group1ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel3Group1NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group1OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group1TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group2ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel3Group2NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group2OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group2TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group1Lra("");
        exportTenderHotelPriceResponse.setLevel3Group2Lra("");
        exportTenderHotelPriceResponse.setLevel3Group3Lra("");
        exportTenderHotelPriceResponse.setLevel3Group4Lra("");
        exportTenderHotelPriceResponse.setLevel3Group3ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel3Group3NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group3OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group3TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group4ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel3Group4NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group4OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel3Group4TwoBreakfastPrice(null);

        exportTenderHotelPriceResponse.setLevel4Group1ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel4Group1NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group1OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group1TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group2ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel4Group2NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group2OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group2TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group1Lra("");
        exportTenderHotelPriceResponse.setLevel4Group2Lra("");
        exportTenderHotelPriceResponse.setLevel4Group3Lra("");
        exportTenderHotelPriceResponse.setLevel4Group4Lra("");
        exportTenderHotelPriceResponse.setLevel4Group3ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel4Group3NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group3OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group3TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group4ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel4Group4NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group4OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel4Group4TwoBreakfastPrice(null);

        exportTenderHotelPriceResponse.setLevel5Group1ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel5Group1NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group1OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group1TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group2ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel5Group2NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group2OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group2TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group1Lra("");
        exportTenderHotelPriceResponse.setLevel5Group2Lra("");
        exportTenderHotelPriceResponse.setLevel5Group3Lra("");
        exportTenderHotelPriceResponse.setLevel5Group4Lra("");
        exportTenderHotelPriceResponse.setLevel5Group3ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel5Group3NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group3OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group3TwoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group4ApplicableWeeks("");
        exportTenderHotelPriceResponse.setLevel5Group4NoBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group4OneBreakfastPrice(null);
        exportTenderHotelPriceResponse.setLevel5Group4TwoBreakfastPrice(null);
    }

    @Override
    public Response queryMapHotelPriceDetail(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse = new QueryProjectHotelPriceDetailResponse();
        Response response = new Response();
        //查询图片
        try {
            HotelImageInfoResponse hotelImageInfoResponse = new HotelImageInfoResponse();
            hotelImageInfoResponse.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
            hotelApplyOnlineService.queryAllImageUrl(hotelImageInfoResponse);
            //图片
            queryProjectHotelPriceDetailResponse.setMainImageUrl(hotelImageInfoResponse.getMainImageUrl());
            queryProjectHotelPriceDetailResponse.setRoomImageUrls(hotelImageInfoResponse.getRoomImageUrls());
            queryProjectHotelPriceDetailResponse.setTotalImageCount(hotelImageInfoResponse.getTotalImageCount());
            queryProjectHotelPriceDetailResponse.setAllImageCategories(hotelImageInfoResponse.getAllImageCategories());

        } catch (Exception ex){
            logger.error("地图获取酒店图片异常" + projectMapHotelPriceDetailRequest.getHotelId(), ex);
        }

        //
        // 查询项目总权重
        BigDecimal totalWeight = BigDecimal.ZERO;
        Project project = projectDao.selectByPrimaryKey(projectMapHotelPriceDetailRequest.getProjectId());
        // 是否为权重2.0
        if(project.getTotalWeight() == null || project.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
            ProjectHotelTendWeight projectHotelTendWeight = projectHotelTendWeightDao.selectByPrimaryKey(projectMapHotelPriceDetailRequest.getProjectId());
            if (projectHotelTendWeight != null) {
                if (projectHotelTendWeight.getWhtRoomNightState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtfRoomNight());
                }
                if (projectHotelTendWeight.getWhtRoomNightExState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtRoomNightEx());
                }
                if (projectHotelTendWeight.getWhtCityState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtCity());
                }
                if (projectHotelTendWeight.getWhtLocationState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtLocation());
                }
                if (projectHotelTendWeight.getWhtPriceAdvantState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtPriceAdvant());
                }
                if (projectHotelTendWeight.getWhtPriceAdvantExState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtPriceAdvantEx());
                }
                if (projectHotelTendWeight.getWhtTravelStandardState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtTravelStandard());
                }
                if (projectHotelTendWeight.getWhtOtaScoreState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtOtaScore());
                }
                if (projectHotelTendWeight.getWhtInvoiceState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtInvoice());
                }
                if (projectHotelTendWeight.getWhtCoPayState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtCoPay());
                }
                if (projectHotelTendWeight.getWhtBreakfastState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtBreakfast());
                }
                if (projectHotelTendWeight.getWhtLraState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtLra());
                }
                if (projectHotelTendWeight.getWhtCanceState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtCancel());
                }
                if (projectHotelTendWeight.getWhtBalanceState() == RfpConstant.constant_1) {
                    totalWeight = totalWeight.add(projectHotelTendWeight.getWhtBalance());
                }
            }

            BigDecimal customTendWeight = projectCustomTendStrategyDao.selectTotalWeight(projectMapHotelPriceDetailRequest.getProjectId());
            if (customTendWeight != null) {
                totalWeight = totalWeight.add(customTendWeight);
            }
            queryProjectHotelPriceDetailResponse.setProjectWeight(totalWeight);
        } else {
            queryProjectHotelPriceDetailResponse.setProjectWeight(project.getTotalWeight());
        }


        // 查询项目所有酒店报价信息
        BidHotelInfoQueryResponse bidHotelInfoQueryResponse = projectIntentHotelDao.queryProjectHotelBidInfo(projectMapHotelPriceDetailRequest.getProjectId(), projectMapHotelPriceDetailRequest.getHotelId());
        queryProjectHotelPriceDetailResponse.setBidWeight(bidHotelInfoQueryResponse.getBidWeight());
        queryProjectHotelPriceDetailResponse.setHotelStar(bidHotelInfoQueryResponse.getHotelStar());
        queryProjectHotelPriceDetailResponse.setRating(bidHotelInfoQueryResponse.getRating());

        // 权重2.0计算开关
        if(project.getTotalWeight() != null && project.getTotalWeight().compareTo(BigDecimal.ZERO) > 0) {
            projectHotelWeightService.setBidWeightInfo(queryProjectHotelPriceDetailResponse, project.getProjectId(), projectMapHotelPriceDetailRequest.getHotelId());
        }

        response.setData(queryProjectHotelPriceDetailResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryMapRecommendHotelPriceDetail(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        QueryProjectRecommendHotelDetailResponse queryProjectRecommendHotelDetailResponse = new QueryProjectRecommendHotelDetailResponse();
        Response response = new Response();
        //查询图片
        try {
            HotelImageInfoResponse hotelImageInfoResponse = new HotelImageInfoResponse();
            hotelImageInfoResponse.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
            hotelApplyOnlineService.queryAllImageUrl(hotelImageInfoResponse);
            //图片
            queryProjectRecommendHotelDetailResponse.setMainImageUrl(hotelImageInfoResponse.getMainImageUrl());
            queryProjectRecommendHotelDetailResponse.setRoomImageUrls(hotelImageInfoResponse.getRoomImageUrls());
            queryProjectRecommendHotelDetailResponse.setTotalImageCount(hotelImageInfoResponse.getTotalImageCount());
            queryProjectRecommendHotelDetailResponse.setAllImageCategories(hotelImageInfoResponse.getAllImageCategories());

        } catch (Exception ex){
            logger.error("地图获取酒店图片异常" + projectMapHotelPriceDetailRequest.getHotelId(), ex);
        }

        // 查询酒店信息
        HotelResponse hotelResponse = hotelDao.selectHotelInfo(projectMapHotelPriceDetailRequest.getHotelId());
        queryProjectRecommendHotelDetailResponse.setHotelId(hotelResponse.getHotelId());
        queryProjectRecommendHotelDetailResponse.setHotelName(hotelResponse.getHotelName());
        queryProjectRecommendHotelDetailResponse.setFitmentDate(hotelResponse.getFitmentDate());
        queryProjectRecommendHotelDetailResponse.setPraciceDate(hotelResponse.getPraciceDate());
        queryProjectRecommendHotelDetailResponse.setTelephone(hotelResponse.getTelephone());
        queryProjectRecommendHotelDetailResponse.setRoomCount(StringUtil.isValidString(hotelResponse.getRoomCount()) ? Integer.valueOf(hotelResponse.getRoomCount()) : null);

        // 查询酒店邀请信息
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectMapHotelPriceDetailRequest.getProjectId(), hotelResponse.getHotelId());
        if(projectIntentHotel != null){
            queryProjectRecommendHotelDetailResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryProjectRecommendHotelDetailResponse.setIsInvite(projectIntentHotel.getInviteStatus());
            queryProjectRecommendHotelDetailResponse.setLastInviteTime(projectIntentHotel.getLastInviteTime());
        }

        // 查询酒店集团和品牌名称
        List<HotelWithGroupBrandInfoResponse> hotelWithGroupBrandInfoResponseList = hotelDao.selectHotelWithGroupBrandInfoList(Lists.newArrayList(hotelResponse.getHotelId()));
        if(CollectionUtils.isNotEmpty(hotelWithGroupBrandInfoResponseList)){
            HotelWithGroupBrandInfoResponse hotelWithGroupBrandInfoResponse = hotelWithGroupBrandInfoResponseList.get(0);
            queryProjectRecommendHotelDetailResponse.setHotelGroupName(hotelWithGroupBrandInfoResponse.getHotelGroupName());
            queryProjectRecommendHotelDetailResponse.setBrandName(hotelWithGroupBrandInfoResponse.getBrandName());
        }


        // 酒店设施
        queryProjectRecommendHotelDetailResponse.setHasGym(RfpConstant.constant_0);
        queryProjectRecommendHotelDetailResponse.setHasSwimmingPool(RfpConstant.constant_0);
        queryProjectRecommendHotelDetailResponse.setHasWashHouse(RfpConstant.constant_0);

        try {
            HotelDetailInfoQueryDto hotelDetailInfoQueryDto = new HotelDetailInfoQueryDto();
            hotelDetailInfoQueryDto.setHotelId(hotelResponse.getHotelId());
            List<String> strings = new ArrayList<>();
            strings.add("hotelFacilityNew");
            hotelDetailInfoQueryDto.setSettings(strings);
            HotelDetailInfoResponseDto hotelDetailInfoResponseDto = hotelInfoFacade.queryHotelDetailInfo(hotelDetailInfoQueryDto);
            logger.info("hotelDetailInfoResponseDto: {} ", JSON.toJSONString(hotelDetailInfoResponseDto));
            if (hotelDetailInfoResponseDto == null) {
                return response;
            }
            List<HotelFacilityNewResponseDto> hotelFacilityResponseDtoList = hotelDetailInfoResponseDto.getHotelFacilityNew();
            if (CollectionUtils.isNotEmpty(hotelFacilityResponseDtoList)) {
                for(HotelFacilityNewResponseDto hotelFacilityResponseDto : hotelFacilityResponseDtoList){
                    // 0 不包括
                    if("0".equals(hotelFacilityResponseDto.getStatus())) {
                        continue;
                    }
                    // 是否包含健身房  健身室 = 529
                    if(Objects.equals(hotelFacilityResponseDto.getName(), "健身室")){
                        queryProjectRecommendHotelDetailResponse.setHasGym(RfpConstant.constant_1);
                    }
                    // 是否包含泳池 1081=泳池，室内泳池=799, 室外泳池=573,恒温泳池=827, 景观泳池=829 无边泳池=834 屋顶泳池=859
                    if(Objects.equals(hotelFacilityResponseDto.getName(),"泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "室内泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "室外泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "恒温泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "景观泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "无边泳池") ||
                            Objects.equals(hotelFacilityResponseDto.getName(), "屋顶泳池")
                    ){
                        queryProjectRecommendHotelDetailResponse.setHasSwimmingPool(RfpConstant.constant_1);
                    }
                    // 是否包含 免费wifi  506=公用区wifi 503=客房WIFI
                    if(Objects.equals(hotelFacilityResponseDto.getName(),"公用区wifi") ||
                            Objects.equals(hotelFacilityResponseDto.getName(),"客房WIFI")
                    ){
                        queryProjectRecommendHotelDetailResponse.setHasWifi(RfpConstant.constant_1);
                    }
                    //是否包含 洗衣房 482
                    if(Objects.equals(hotelFacilityResponseDto.getName(), "洗衣房")){
                        queryProjectRecommendHotelDetailResponse.setHasWashHouse(RfpConstant.constant_1);
                    }
                }
            }
        } catch (Exception ex){
            logger.error("地图获取酒店基础设施异常" + projectMapHotelPriceDetailRequest.getHotelId(), ex);
        }

       // 设置酒店亮点
        RecommendHotel recommendHotel = recommendHotelDao.selectRecommendHotelByHotelId(hotelResponse.getHotelId(), null);
        if(recommendHotel != null){
            queryProjectRecommendHotelDetailResponse.setBrightSpot(recommendHotel.getBrightSpot());
        }

        // 酒店推荐等级
        List<HotelRecommendLevelResponse> hotelRecommendLevelResponseList = projectHotelHistoryDataDao.queryHotelRecommendLevel(projectMapHotelPriceDetailRequest.getProjectId(), Arrays.asList(hotelResponse.getHotelId()));
        if (CollectionUtils.isNotEmpty(hotelRecommendLevelResponseList) && StringUtil.isValidString(hotelRecommendLevelResponseList.get(0).getRecommendLevel())) {
            queryProjectRecommendHotelDetailResponse.setRecommendLevel(hotelRecommendLevelResponseList.get(0).getRecommendLevel());
        }


        // 返回值
        response.setData(queryProjectRecommendHotelDetailResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    @Override
    public Response queryMapHotelBidLevelPrice(ProjectMapHotelPriceDetailRequest projectMapHotelPriceDetailRequest) {
        Response response = new Response();
        QueryMapHotelBidLevelPriceResponse queryMapHotelBidLevelPriceResponse = new QueryMapHotelBidLevelPriceResponse();
        Project project = projectDao.selectByPrimaryKey(projectMapHotelPriceDetailRequest.getProjectId());

        // 查询今年项目报价
        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        projectIntentHotelRequest.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
        projectIntentHotelRequest.setProjectId(project.getProjectId());
        List<ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponseList = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
        queryMapHotelBidLevelPriceResponse.setProjectHotelPriceLevelResponseList(projectHotelPriceLevelResponseList);

        // 查询去年项目报价
        if(project.getRelatedProjectId() != null && project.getRelatedProjectId() > 0){
            ProjectIntentHotelRequest lastYearprojectIntentHotelRequest = new ProjectIntentHotelRequest();
            lastYearprojectIntentHotelRequest.setProjectId(project.getRelatedProjectId());
            lastYearprojectIntentHotelRequest.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
            List<ProjectHotelPriceLevelResponse> lastYearProjectHotelPriceLevelResponseList = projectHotelPriceService.selectProjectHotelPriceLevelList(lastYearprojectIntentHotelRequest);
            queryMapHotelBidLevelPriceResponse.setLastYearProjectHotelPriceLevelResponseList(lastYearProjectHotelPriceLevelResponseList);
        }

        // 修订报价状态和议价种查询议价中备注列表
        ProjectIntentHotelRequest currenctProjectIntentHotelRequest = new ProjectIntentHotelRequest();
        currenctProjectIntentHotelRequest.setProjectId(project.getProjectId());
        currenctProjectIntentHotelRequest.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(currenctProjectIntentHotelRequest);
        List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = projectHotelRemarkDao.selectList(project.getProjectId(), projectMapHotelPriceDetailRequest.getHotelId(), null);
        queryMapHotelBidLevelPriceResponse.setProjectHotelRemarkResponseList(projectHotelRemarkResponseList);


        // 不适用日期
        List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayListByIntentHotelId(Arrays.asList(projectIntentHotel.getProjectIntentHotelId()));
        if (CollectionUtils.isNotEmpty(priceUnapplicableDays)) {
            queryMapHotelBidLevelPriceResponse.setUnapplicableDayList(priceUnapplicableDays);
        }

        // 查询报价日期设置
        ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest = new ProjectHotelBidStrategyRequest();
        projectHotelBidStrategyRequest.setProjectId(project.getProjectId());
        projectHotelBidStrategyRequest.setHotelId(projectMapHotelPriceDetailRequest.getHotelId());
        Response priceApplicableDayResponse = priceApplicableDayService.selectPriceApplicableDayListByProject(projectHotelBidStrategyRequest, false);
        if(priceApplicableDayResponse.getData() != null){
            List<PriceApplicableDay> priceApplicableDayList = (List<PriceApplicableDay>)priceApplicableDayResponse.getData();
            queryMapHotelBidLevelPriceResponse.setPriceApplicableDayList(priceApplicableDayList);
        }

        response.setData(queryMapHotelBidLevelPriceResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }



    @Override
    public Response queryMapHotelPriceBidStat(ProjectMapHotelPriceStatRequest projectMapHotelPriceStatRequest) {
        // 统计累计数据
        QueryProjectHistoryDataStatResponse queryProjectHistoryDataStatResponse = new QueryProjectHistoryDataStatResponse();
        Response response = new Response();
        //查询酒店基本信息
        QueryHotelInfoResponse queryHotelInfoResponse = projectIntentHotelDao.selectHotelInfoByHotelId(projectMapHotelPriceStatRequest.getHotelId());
        if (queryHotelInfoResponse == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("查询酒店信息失败");
            return response;
        }

        // 查询去年酒店项目报价ID
        Project project = projectDao.selectByPrimaryKey(projectMapHotelPriceStatRequest.getProjectId());

        // 3公里同档排名
        int distance = 3;

        QueryHistoryProjectInfoResponse baseQueryHistoryProjectInfoResponse = null;
        // 历史数据查询当前酒店城市的所有报价
        List<QueryHistoryProjectInfoResponse> projectHistoryDataList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(projectMapHotelPriceStatRequest.getProjectId(), null, queryHotelInfoResponse.getCityCode(), null);

        // 1： 3公里同档
        if(projectMapHotelPriceStatRequest.getStatType() == 1) {
            projectHistoryDataList = projectHistoryDataList.stream().filter(o -> LocationUtil.isInLngLatInDistanceRange(distance, queryHotelInfoResponse.getLatBaidu(), queryHotelInfoResponse.getLngBaidu(), o.getLatBaidu(), o.getLngBaidu())).collect(Collectors.toList());
        }

        // 过滤同档酒店
        List<Long> theSameLevelHotelIdList;
        if(projectMapHotelPriceStatRequest.getStatType() == 1 || projectMapHotelPriceStatRequest.getStatType() == 2) {
            BigDecimal selectHotelAvgPrice = BigDecimal.ZERO;
            for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : projectHistoryDataList) {
                if (Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), queryHotelInfoResponse.getHotelId())) {
                    selectHotelAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP);
                }
            }
            if (selectHotelAvgPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal finalSelectHotelAvgPrice = selectHotelAvgPrice;
                projectHistoryDataList = projectHistoryDataList.stream().filter(o -> isTheSameLevel(o, finalSelectHotelAvgPrice)).collect(Collectors.toList());
                theSameLevelHotelIdList = projectHistoryDataList.stream().map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
            } else {
                theSameLevelHotelIdList = new ArrayList<>();
                projectHistoryDataList = null;
            }
        } else {
            theSameLevelHotelIdList = new ArrayList<>();
        }
        if(CollectionUtils.isNotEmpty(projectHistoryDataList)){
            QueryProjectHotelBidStatResponse nightRoomBidStatResponse = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(projectMapHotelPriceStatRequest.getHotelId(), projectHistoryDataList);
            queryProjectHistoryDataStatResponse.setLastYearRoomNightCount(nightRoomBidStatResponse.getLastYearRoomNightCount());
            queryProjectHistoryDataStatResponse.setLastYearRoomNightOrder(nightRoomBidStatResponse.getLastYearRoomNightOrder());

            // 计算成交金额排名
            QueryProjectHotelBidStatResponse salesAmountRoomBidStatResponse = HotelHistoryDataStatUtil.calculateSalesAmountOrder(projectMapHotelPriceStatRequest.getHotelId(), projectHistoryDataList);
            queryProjectHistoryDataStatResponse.setLastYearAmount(salesAmountRoomBidStatResponse.getLastYearAmount());
            queryProjectHistoryDataStatResponse.setLastYearAmountOrder(salesAmountRoomBidStatResponse.getLastYearAmountOrder());

            // 计算节省金额排名
            List<QueryHistoryProjectInfoResponse> savedAmountDisplayList = projectHistoryDataList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getSavedAmount).reversed()).collect(Collectors.toList());
            BigDecimal lastSavedAmount = BigDecimal.ZERO;
            BigDecimal savedAmount = BigDecimal.ZERO;
            int savedAmountOrder = 0;
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : savedAmountDisplayList){
                if(queryHistoryProjectInfoResponse.getSavedAmount().compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                if(lastSavedAmount.compareTo(BigDecimal.ZERO) == 0 || lastSavedAmount.compareTo(queryHistoryProjectInfoResponse.getSavedAmount()) != 0){
                    savedAmountOrder = savedAmountOrder +1;
                }
                lastSavedAmount = queryHistoryProjectInfoResponse.getSavedAmount();
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), projectMapHotelPriceStatRequest.getHotelId())){
                    savedAmount = queryHistoryProjectInfoResponse.getSavedAmount();
                    break;
                }
            }
            queryProjectHistoryDataStatResponse.setSavedAmount(savedAmount);
            queryProjectHistoryDataStatResponse.setSavedAmountOrder(savedAmountOrder);

            // 查询去年服务分排名
            List<BidHotelInfoQueryResponse> lastYearCityBidHotelInfoQueryResponses = new ArrayList<>();
            if(project.getRelatedProjectId() > 0) {
                lastYearCityBidHotelInfoQueryResponses = projectIntentHotelDao.queryProjectBidHotelInfo(project.getRelatedProjectId(), queryHotelInfoResponse.getCityCode());
            }
            // 查询去年3公里同档酒店服务分排名
            if(CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
                if(CollectionUtils.isNotEmpty(theSameLevelHotelIdList)){
                    lastYearCityBidHotelInfoQueryResponses = lastYearCityBidHotelInfoQueryResponses.stream().filter(
                            o -> theSameLevelHotelIdList.contains(o.getHotelId())
                    ).collect(Collectors.toList());
                }

                QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(projectMapHotelPriceStatRequest.getHotelId(), lastYearCityBidHotelInfoQueryResponses);
                queryProjectHistoryDataStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
                queryProjectHistoryDataStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
            }
        }

        // 获取城市酒店报价
        List<BidHotelInfoQueryResponse> cityBidHotelInfoQueryResponses = projectIntentHotelDao.queryProjectBidHotelInfo(project.getProjectId(), queryHotelInfoResponse.getCityCode());
        cityBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(o -> o.getBidWeight() != null).collect(Collectors.toList());
        // 过滤3公里同档范围
        if (projectMapHotelPriceStatRequest.getStatType() == 1) {
            cityBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(
                    o -> LocationUtil.isInLngLatInDistanceRange(distance, queryHotelInfoResponse.getLatBaidu(), queryHotelInfoResponse.getLngBaidu(), o.getLatBaiDu(), o.getLngBaiDu()) &&
                    theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
          // 过滤同城同档
        } else if(projectMapHotelPriceStatRequest.getStatType() == 2) {
            cityBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(
                    o -> theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
        }
        // 权重排名
        BigDecimal lastBidWeight = null;
        BigDecimal bidWeight = null;
        int bidWeightOrder = 0;
        for (BidHotelInfoQueryResponse bidHotelInfoQueryResponse : cityBidHotelInfoQueryResponses) {
            if (lastBidWeight == null || lastBidWeight.compareTo(bidHotelInfoQueryResponse.getBidWeight()) != 0) {
                bidWeightOrder = bidWeightOrder + 1;
            }
            lastBidWeight = bidHotelInfoQueryResponse.getBidWeight();
            if (Objects.equals(bidHotelInfoQueryResponse.getHotelId(), projectMapHotelPriceStatRequest.getHotelId())) {
                bidWeight = bidHotelInfoQueryResponse.getBidWeight();
                break;
            }
        }
        queryProjectHistoryDataStatResponse.setBidWeight(bidWeight);
        queryProjectHistoryDataStatResponse.setBidWeightOrder(bidWeight == null ? 0 : bidWeightOrder);

        // OTA
        int rattingOrder =  HotelHistoryDataStatUtil.calculateOTAOrder(projectMapHotelPriceStatRequest.getHotelId(), cityBidHotelInfoQueryResponses);
        queryProjectHistoryDataStatResponse.setRating(queryHotelInfoResponse.getRating());
        queryProjectHistoryDataStatResponse.setRatingOrder(rattingOrder);

        response.setData(queryProjectHistoryDataStatResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    @Override
    public Response queryMapPoiBidStat(ProjectMapPoiStatRequest projectMapPoiStatRequest) {
        // 定义返回response
        Response response = new Response();

        // 初始化统计集合
        Map<Integer, List<QueryProjectPoiHotelStatResponse>> poiStatMap = new HashMap<>();
        poiStatMap.put(PoiStatTypeEnum.NIGHTROOM_COUNT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SALE_AMOUNT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SERVICE_POINT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.BID_WEIGHT.key, new LinkedList<>());
        poiStatMap.put(PoiStatTypeEnum.SAVED_AMOUNT.key, new LinkedList<>());

        // 查询去年酒店项目报价ID
        Project project = projectDao.selectByPrimaryKey(projectMapPoiStatRequest.getProjectId());

        // 查询项目POI信息
        ProjectPoiInfoResponse projectPoi = projectPoiDao.selectByProjectAndPoiId(projectMapPoiStatRequest.getProjectId(), projectMapPoiStatRequest.getPoiId(), projectMapPoiStatRequest.getDistance());
        if(projectPoi == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目POI不存在");
            return response;
        }

        // 历史数据查询当前酒店城市的所有报价
        List<QueryHistoryProjectInfoResponse> projectHistoryDataList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(projectMapPoiStatRequest.getProjectId(), null, projectPoi.getCityCode(), projectMapPoiStatRequest.getPoiId());
        projectHistoryDataList = projectHistoryDataList.stream().filter(o -> o.getPoiId() > 0 && o.getPoiDistance() <= projectMapPoiStatRequest.getDistance()).collect(Collectors.toList());

        // 排名的酒店ID
        Set<Long> poiHotelIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(projectHistoryDataList)){
            // 成交间夜数前十
            List<QueryHistoryProjectInfoResponse> nightRoomOrderList = projectHistoryDataList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getRoomNightCount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> nightRoomQueryProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : nightRoomOrderList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.NIGHTROOM_COUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()));
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                nightRoomQueryProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(nightRoomQueryProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.NIGHTROOM_COUNT.key, nightRoomQueryProjectPoiHotelStatResponseList);

            // 成交金额前十
            List<QueryHistoryProjectInfoResponse> saleAmountQueryProjectPoiHotelStatResponseList = projectHistoryDataList.stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getTotalAmount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> saleAmountProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : saleAmountQueryProjectPoiHotelStatResponseList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SALE_AMOUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(queryHistoryProjectInfoResponse.getTotalAmount());
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                saleAmountProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(saleAmountProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SALE_AMOUNT.key, saleAmountProjectPoiHotelStatResponseList);


            // 计算节省金额排名
            List<QueryHistoryProjectInfoResponse> savedAmountDisplayList = projectHistoryDataList.stream().filter(o -> o.getSavedAmount().compareTo(BigDecimal.ZERO) > 0).sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getSavedAmount).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> savedAmountProjectPoiHotelStatResponseList = new LinkedList<>();
            for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : savedAmountDisplayList){
                poiHotelIdSet.add(queryHistoryProjectInfoResponse.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SAVED_AMOUNT.key);
                queryProjectPoiHotelStatResponse.setStatData(queryHistoryProjectInfoResponse.getSavedAmount());
                queryProjectPoiHotelStatResponse.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                savedAmountProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(savedAmountProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SAVED_AMOUNT.key, savedAmountProjectPoiHotelStatResponseList);

            // 查询去年服务分排名
            List<Long> hotelIdList = projectHistoryDataList.stream().map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
            List<ProjectIntentHotel> lastYearProjectIntentHotelList = projectIntentHotelDao.selectByProjectId(project.getRelatedProjectId());
            lastYearProjectIntentHotelList = lastYearProjectIntentHotelList.stream().filter(o -> Objects.equals(o.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) && hotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
            lastYearProjectIntentHotelList = lastYearProjectIntentHotelList.stream().sorted(Comparator.comparing(ProjectIntentHotel::getHotelServicePoints).reversed()).collect(Collectors.toList());
            List<QueryProjectPoiHotelStatResponse> servicePointProjectPoiHotelStatResponseList = new LinkedList<>();
            for(ProjectIntentHotel projectIntentHotel : lastYearProjectIntentHotelList){
                poiHotelIdSet.add(projectIntentHotel.getHotelId());
                QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
                queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.SERVICE_POINT.key);
                queryProjectPoiHotelStatResponse.setStatData(projectIntentHotel.getHotelServicePoints());
                queryProjectPoiHotelStatResponse.setHotelId(projectIntentHotel.getHotelId());
                servicePointProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
                if(servicePointProjectPoiHotelStatResponseList.size() >= 10){
                    break;
                }
            }
            poiStatMap.put(PoiStatTypeEnum.SERVICE_POINT.key, servicePointProjectPoiHotelStatResponseList);
        }

        // 获取3公里内的酒店报价
        List<BidHotelInfoQueryResponse> bidHotelInfoQueryResponseList = projectIntentHotelDao.queryProjectBidHotelInfo(project.getProjectId(), projectPoi.getCityCode());
        Map<Long, BidHotelInfoQueryResponse> hotelProjectIntentHotelMap = bidHotelInfoQueryResponseList.stream().collect(Collectors.toMap(BidHotelInfoQueryResponse::getHotelId, Function.identity()));
        bidHotelInfoQueryResponseList = bidHotelInfoQueryResponseList.stream().filter(o -> Objects.equals(o.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) || Objects.equals(o.getBidState(), HotelBidStateEnum.NEW_BID.bidState))
                .collect(Collectors.toList());
        for(BidHotelInfoQueryResponse hotelResponse : bidHotelInfoQueryResponseList){
            if(!LocationUtil.isInLngLatInDistanceRange(projectMapPoiStatRequest.getDistance(), projectPoi.getLatBaiDu(), projectPoi.getLngBaiDu(), hotelResponse.getLatBaiDu(), hotelResponse.getLngBaiDu())){
                hotelProjectIntentHotelMap.remove(hotelResponse.getHotelId());
            }
        }

        // 计算排名
        List<BidHotelInfoQueryResponse> orderProjectIntentHotelList = new ArrayList<>(hotelProjectIntentHotelMap.values()).stream()
                .filter(o -> o.getBidWeight() != null && o.getBidWeight().compareTo(BigDecimal.ZERO) > 0).sorted(Comparator.comparing(BidHotelInfoQueryResponse::getBidWeight).reversed()).collect(Collectors.toList());
        List<QueryProjectPoiHotelStatResponse> bidWeightProjectPoiHotelStatResponseList = new LinkedList<>();
        for(BidHotelInfoQueryResponse bidHotelInfoQueryResponse : orderProjectIntentHotelList){
            poiHotelIdSet.add(bidHotelInfoQueryResponse.getHotelId());
            QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse = new QueryProjectPoiHotelStatResponse();
            queryProjectPoiHotelStatResponse.setPoiStatType(PoiStatTypeEnum.BID_WEIGHT.key);
            queryProjectPoiHotelStatResponse.setStatData(bidHotelInfoQueryResponse.getBidWeight());
            queryProjectPoiHotelStatResponse.setHotelId(bidHotelInfoQueryResponse.getHotelId());
            queryProjectPoiHotelStatResponse.setHotelName(bidHotelInfoQueryResponse.getHotelName());
            bidWeightProjectPoiHotelStatResponseList.add(queryProjectPoiHotelStatResponse);
            if(bidWeightProjectPoiHotelStatResponseList.size() >= 10){
                break;
            }
        }
        poiStatMap.put(PoiStatTypeEnum.BID_WEIGHT.key, bidWeightProjectPoiHotelStatResponseList);

        // 设置hotelName
        if(!poiHotelIdSet.isEmpty()) {
            List<HotelResponse> hotelResponseList = hotelDao.selectHotelInfoByIds(new ArrayList<>(poiHotelIdSet));
            Map<Long, HotelResponse> hotelResponseMap = hotelResponseList.stream().collect(Collectors.toMap(HotelResponse::getHotelId, Function.identity()));
            for(Integer poiStatType : poiStatMap.keySet()){
                List<QueryProjectPoiHotelStatResponse> queryProjectPoiHotelStatResponses = poiStatMap.get(poiStatType);
                for(QueryProjectPoiHotelStatResponse queryProjectPoiHotelStatResponse : queryProjectPoiHotelStatResponses){
                    HotelResponse hotelResponse = hotelResponseMap.get(queryProjectPoiHotelStatResponse.getHotelId());
                    if(hotelResponse != null) {
                        queryProjectPoiHotelStatResponse.setHotelName(hotelResponse.getHotelName());
                    }
                }
            }
        }

        // 设置返回response
        response.setData(poiStatMap);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }
    @Override
    public Response updatePriceGroupLocked(UpdatePriceGroupLockedRequest updatePriceGroupLockedRequest, UserDTO userDTO){
        Response response = new Response();
        projectHotelPriceGroupDao.updateLocked(updatePriceGroupLockedRequest.getHotelPriceGroupId(), updatePriceGroupLockedRequest.getIsLocked());
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response updateBidContactInfo(UpdateBidContactInfoRequest updateBidContactInfoRequest, UserDTO userDTO){
        Response response = new Response();

        // 查询酒店报价项目
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateBidContactInfoRequest.getProjectIntentHotelId());

        // 值不同，修改值
        if(!Objects.equals(projectIntentHotel.getBidContactName(), updateBidContactInfoRequest.getBidContactName()) ||
                !Objects.equals(projectIntentHotel.getBidContactMobile(), updateBidContactInfoRequest.getBidContactMobile()) ||
                !Objects.equals(projectIntentHotel.getBidContactEmail(), updateBidContactInfoRequest.getBidContactEmail())
        ){
            projectIntentHotel.setBidContactName(updateBidContactInfoRequest.getBidContactName());
            projectIntentHotel.setBidContactMobile(updateBidContactInfoRequest.getBidContactMobile());
            projectIntentHotel.setBidContactEmail(updateBidContactInfoRequest.getBidContactEmail());
            projectIntentHotel.setModifier(userDTO.getOperator());
            int updateRecord = projectIntentHotelDao.updateBidContact(projectIntentHotel);
            if(updateRecord != 1){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("更新失败");
                return response;
            }

            // 记录操作日志
            BidOperateLog bidOperateLog = new BidOperateLog();
            bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            bidOperateLog.setOperator(userDTO.getOperator());
            StringBuffer operateContentSb = new StringBuffer();
            operateContentSb.append("将报价人");
            operateContentSb.append(" ");
            if(StringUtil.isValidString(projectIntentHotel.getBidContactName())) {
                operateContentSb.append(projectIntentHotel.getBidContactName());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(projectIntentHotel.getBidContactMobile())) {
                operateContentSb.append(projectIntentHotel.getBidContactMobile());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(projectIntentHotel.getBidContactEmail())) {
                operateContentSb.append(projectIntentHotel.getBidContactEmail());
            }
            operateContentSb.append(" 修改为 ");
            if(StringUtil.isValidString(updateBidContactInfoRequest.getBidContactName())) {
                operateContentSb.append(updateBidContactInfoRequest.getBidContactName());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(updateBidContactInfoRequest.getBidContactMobile())) {
                operateContentSb.append(updateBidContactInfoRequest.getBidContactMobile());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(updateBidContactInfoRequest.getBidContactEmail())) {
                operateContentSb.append(updateBidContactInfoRequest.getBidContactEmail());
            }
            bidOperateLog.setOperateContent(operateContentSb.toString());
            bidOperateLogDao.insertBidOperateLog(bidOperateLog);

        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response updateHotelGroupBidContactInfo(UpdateHotelGroupBidContactInfoRequest updateHotelGroupBidContactInfoRequest, UserDTO userDTO){
        Response response = new Response();

        // 查询酒店报价项目
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateHotelGroupBidContactInfoRequest.getProjectIntentHotelId());

        // 值不同，修改值
        if(!Objects.equals(projectIntentHotel.getHotelGroupBidContactName(), updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactName()) ||
                !Objects.equals(projectIntentHotel.getHotelGroupBidContactMobile(), updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactMobile()) ||
                !Objects.equals(projectIntentHotel.getHotelGroupBidContactEmail(), updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactEmail())
        ){
            projectIntentHotel.setHotelGroupBidContactName(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactName());
            projectIntentHotel.setHotelGroupBidContactMobile(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactMobile());
            projectIntentHotel.setHotelGroupBidContactEmail(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactEmail());
            projectIntentHotel.setModifier(userDTO.getOperator());
            int updateRecord = projectIntentHotelDao.updateHotelGroupBidContactInfoOnly(projectIntentHotel);
            if(updateRecord != 1){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("更新失败");
                return response;
            }

            // 记录操作日志
            BidOperateLog bidOperateLog = new BidOperateLog();
            bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            bidOperateLog.setOperator(userDTO.getOperator());
            StringBuffer operateContentSb = new StringBuffer();
            operateContentSb.append("将酒店集团报价人");
            operateContentSb.append(" ");
            if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactName())) {
                operateContentSb.append(projectIntentHotel.getHotelGroupBidContactName());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactMobile())) {
                operateContentSb.append(projectIntentHotel.getHotelGroupBidContactMobile());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(projectIntentHotel.getHotelGroupBidContactEmail())) {
                operateContentSb.append(projectIntentHotel.getHotelGroupBidContactEmail());
            }
            operateContentSb.append(" 修改为 ");
            if(StringUtil.isValidString(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactName())) {
                operateContentSb.append(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactName());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactMobile())) {
                operateContentSb.append(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactMobile());
                operateContentSb.append(", ");
            }
            if(StringUtil.isValidString(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactEmail())) {
                operateContentSb.append(updateHotelGroupBidContactInfoRequest.getHotelGroupBidContactEmail());
            }
            bidOperateLog.setOperateContent(operateContentSb.toString());
            bidOperateLogDao.insertBidOperateLog(bidOperateLog);

        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    @Override
    public Response updateBidHotelSubjectInfo(UpdateBidHotelSubjectInfoRequest updateBidHotelSubjectInfo, UserDTO userDTO){
        Response response = new Response();
        // 查询酒店报价项目
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateBidHotelSubjectInfo.getProjectIntentHotelId());

        // 值不同，修改值
        if(!Objects.equals(projectIntentHotel.getHotelSubjectId(), updateBidHotelSubjectInfo.getSubjectId())
        ){
            OrgSubject orgSubject = null;
            if(projectIntentHotel.getHotelOrgId() != null && projectIntentHotel.getHotelSubjectId() == null) {
                List<Long> orgIds = new ArrayList<>();
                orgIds.add(projectIntentHotel.getHotelOrgId());
                List<OrgSubject> orgSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(orgIds);
                if (CollectionUtils.isNotEmpty(orgSubjects)) {
                    orgSubject = orgSubjects.get(0);
                }
            } else if(projectIntentHotel.getHotelSubjectId() != null) {
                orgSubject = orgSubjectDao.selectByPrimaryKey(projectIntentHotel.getHotelSubjectId());
            }


            // 查询更新后的酒店主体信息
            OrgSubject updateOrgSubject = null;
            if(updateBidHotelSubjectInfo.getSubjectId() == null ) {
                List<Long> orgIds = new ArrayList<>();
                orgIds.add(projectIntentHotel.getHotelOrgId());
                List<OrgSubject> orgSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(orgIds);
                if (CollectionUtils.isNotEmpty(orgSubjects)) {
                    updateOrgSubject = orgSubjects.get(0);
                }
            } else if(updateBidHotelSubjectInfo.getSubjectId() != null) {
                updateOrgSubject = orgSubjectDao.selectByPrimaryKey(updateBidHotelSubjectInfo.getSubjectId());
            }

            // 更新签约主体信息
            projectIntentHotel.setHotelOrgId(updateOrgSubject == null ? null : updateOrgSubject.getOrgId());
            projectIntentHotel.setHotelSubjectId(updateOrgSubject == null ? null : updateOrgSubject.getSubjectId());
            projectIntentHotel.setHotelBank(updateOrgSubject == null ? null : updateOrgSubject.getBank());
            projectIntentHotel.setHotelAccountName(updateOrgSubject == null ? null : updateOrgSubject.getAccountName());
            projectIntentHotel.setHotelAccountNumber(updateOrgSubject == null ? null : updateOrgSubject.getAccountNumber());
            projectIntentHotel.setHotelBankAccountType(updateOrgSubject == null ? null : updateOrgSubject.getAccountType());
            projectIntentHotel.setModifier(userDTO.getOperator());
            int updateRecord = projectIntentHotelDao.updateBidHotelSubjectInfo(projectIntentHotel);
            if(updateRecord != 1){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("更新失败");
                return response;
            }

            // 记录操作日志
            BidOperateLog bidOperateLog = new BidOperateLog();
            bidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            bidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            bidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            bidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            bidOperateLog.setOperator(userDTO.getOperator());
            StringBuffer operateContentSb = new StringBuffer();
            operateContentSb.append("酒店签约主体由");
            if(orgSubject != null) {
                operateContentSb.append(" ");
                if (StringUtil.isValidString(orgSubject.getSubjectName())) {
                    operateContentSb.append(orgSubject.getSubjectName());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(orgSubject.getCertCode())) {
                    operateContentSb.append(orgSubject.getCertCode());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(orgSubject.getBank())) {
                    operateContentSb.append(orgSubject.getBank());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(orgSubject.getAccountNumber())) {
                    operateContentSb.append(orgSubject.getAccountNumber());
                }
            } else {
                operateContentSb.append("空");
            }
            operateContentSb.append(" 修改为 ");
            if(updateOrgSubject != null) {
                operateContentSb.append(" ");
                if (StringUtil.isValidString(updateOrgSubject.getSubjectName())) {
                    operateContentSb.append(updateOrgSubject.getSubjectName());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(updateOrgSubject.getCertCode())) {
                    operateContentSb.append(updateOrgSubject.getCertCode());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(updateOrgSubject.getBank())) {
                    operateContentSb.append(updateOrgSubject.getBank());
                    operateContentSb.append(", ");
                }
                if (StringUtil.isValidString(updateOrgSubject.getAccountNumber())) {
                    operateContentSb.append(updateOrgSubject.getAccountNumber());
                }
            } else {
                operateContentSb.append("空");
            }
            bidOperateLog.setOperateContent(operateContentSb.toString());
            bidOperateLogDao.insertBidOperateLog(bidOperateLog);

        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    private String generateApplyDayKey(PriceApplicableDay priceApplicableDay) {
        return priceApplicableDay.getPriceType() + "_" + DateUtil.dateToString(priceApplicableDay.getStartDate()) + "_" + DateUtil.dateToString(priceApplicableDay.getEndDate());
    }
    private String generateUpdateBidApplyDayDtoKey(UpdateBidApplyDayDto updateBidApplyDayDto) {
        return updateBidApplyDayDto.getPriceType() == null ? "" : updateBidApplyDayDto.getPriceType() + "_" + DateUtil.dateToString(updateBidApplyDayDto.getStartDate()) + "_" + DateUtil.dateToString(updateBidApplyDayDto.getEndDate());
    }


    @Override
    public Response updateCertsUrl(UpdateCertsUrlRequest updateCertsUrlRequest, UserDTO userDTO){
        Response response = new Response();
        projectIntentHotelDao.updateCertsUrl(updateCertsUrlRequest);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateBidLevelRoom(UpdateBidLevelRoomRequest updateBidLevelRoomRequest, UserDTO userDTO){
        Response response = new Response();
        // 查询酒店报价项目
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateBidLevelRoomRequest.getProjectIntentHotelId());





        // 查询已经配置房型
        PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
        priceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        //priceApplicableRoom.setRoomLevelNo(updateBidLevelRoomRequest.getRoomLevelNo());
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);

        // 检查其他房档房型是否重复
        List<Long> otherLevelRoomTypeIdList = priceApplicableRoomInfoResponseList.stream().filter(o -> !Objects.equals(o.getRoomLevelNo(), updateBidLevelRoomRequest.getRoomLevelNo())).map(PriceApplicableRoomInfoResponse::getRoomTypeId).collect(Collectors.toList());
        for(UpdateBidLevelRoomDto updateBidLevelRoomDto : updateBidLevelRoomRequest.getRoomTypeList()){
            if(otherLevelRoomTypeIdList.contains(updateBidLevelRoomDto.getRoomTypeId())){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("其他房档存在相同房型");
                return response;
            }
        }

        priceApplicableRoomInfoResponseList = priceApplicableRoomInfoResponseList.stream().filter(o -> Objects.equals(o.getRoomLevelNo(), updateBidLevelRoomRequest.getRoomLevelNo())).collect(Collectors.toList());
        List<Long> applicabeRoomTypeIdList = priceApplicableRoomInfoResponseList.stream().map(PriceApplicableRoomInfoResponse::getRoomTypeId).collect(Collectors.toList());

        BidOperateLog updateBidOperateLog = new BidOperateLog();
        updateBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        updateBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
        updateBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
        updateBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
        updateBidOperateLog.setOperator(userDTO.getOperator());

        // 组装操作日期内容
        String applicableRoomOperateContent = "";
        if(CollectionUtils.isNotEmpty(priceApplicableRoomInfoResponseList)) {
            StringBuilder applicableRoomContent = new StringBuilder();
            for (PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse : priceApplicableRoomInfoResponseList) {
                applicableRoomContent.append(priceApplicableRoomInfoResponse.getRoomTypeName() );
                applicableRoomContent.append("(").append(priceApplicableRoomInfoResponse.getRoomTypeId()).append(")");
                applicableRoomContent.append(",");
            }
            if(applicableRoomContent.length() > 0){
                applicableRoomOperateContent = applicableRoomContent.substring(0, applicableRoomContent.length()-1);
            }
        }
        String updateRoomOperateContent = "";
        if(CollectionUtils.isNotEmpty(updateBidLevelRoomRequest.getRoomTypeList())){
            StringBuilder updateRoomContent = new StringBuilder();
            for (UpdateBidLevelRoomDto updateBidLevelRoomDto : updateBidLevelRoomRequest.getRoomTypeList()) {
                // Lanyon upload room type id is null
                if(updateBidLevelRoomDto.getRoomTypeId() == null){
                    continue;
                }
                updateRoomContent.append(updateBidLevelRoomDto.getRoomTypeName() );
                updateRoomContent.append("(").append(updateBidLevelRoomDto.getRoomTypeId()).append(")");
                updateRoomContent.append(",");
            }
            if(updateRoomContent.length() > 0){
                updateRoomOperateContent = updateRoomContent.substring(0, updateRoomContent.length()-1);
            }

        }

        List<PriceApplicableRoom> priceApplicableRoomList = new ArrayList<>();
        int i=0;
        for(UpdateBidLevelRoomDto updateBidLevelRoomDto : updateBidLevelRoomRequest.getRoomTypeList()){
            if(updateBidLevelRoomDto.getRoomTypeId() == null){
                continue;
            }
            i++;
            PriceApplicableRoom addPriceApplicableRoom = new PriceApplicableRoom();
            addPriceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
            addPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
            addPriceApplicableRoom.setRoomTypeId(updateBidLevelRoomDto.getRoomTypeId());
            addPriceApplicableRoom.setRoomLevelNo(updateBidLevelRoomRequest.getRoomLevelNo());
            addPriceApplicableRoom.setCreator(userDTO.getOperator());
            addPriceApplicableRoom.setDisplayOrder(i);
            priceApplicableRoomList.add(addPriceApplicableRoom);
        }

        if(CollectionUtils.isEmpty(updateBidLevelRoomRequest.getRoomTypeList()) && CollectionUtils.isNotEmpty(applicabeRoomTypeIdList)){
            updateBidOperateLog.setOperateContent("删除" + RoomLevelEnum.getValueByKey(updateBidLevelRoomRequest.getRoomLevelNo()) + "房型 " + applicableRoomOperateContent);
            priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), updateBidLevelRoomRequest.getRoomLevelNo());
        } else if(CollectionUtils.isNotEmpty(priceApplicableRoomList) && CollectionUtils.isNotEmpty(updateBidLevelRoomRequest.getRoomTypeList()) && CollectionUtils.isEmpty(applicabeRoomTypeIdList)){
            updateBidOperateLog.setOperateContent("新增" + RoomLevelEnum.getValueByKey(updateBidLevelRoomRequest.getRoomLevelNo()) + "房型 " + updateRoomOperateContent);
            priceApplicableRoomDao.insertBatch(priceApplicableRoomList);
        } else if(!applicableRoomOperateContent.equals(updateRoomOperateContent)){
            priceApplicableRoomDao.deleteByProjectHotelRoomLevelNo(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), updateBidLevelRoomRequest.getRoomLevelNo());
            priceApplicableRoomDao.insertBatch(priceApplicableRoomList);
            updateBidOperateLog.setOperateContent("将" + RoomLevelEnum.getValueByKey(updateBidLevelRoomRequest.getRoomLevelNo()) + "房型 " + applicableRoomOperateContent + " 修改为 " + updateRoomOperateContent);
        }
        // 记录操作日志
        if(StringUtil.isValidString(updateBidOperateLog.getOperateContent())){
            bidOperateLogDao.insertBidOperateLog(updateBidOperateLog);
        }

        // 修改房型数量
        ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
        queryProjectHotelPriceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        queryProjectHotelPriceGroup.setRoomLevelNo(updateBidLevelRoomRequest.getRoomLevelNo());
        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
        ProjectHotelPriceGroup projectHotelPriceGroup = projectHotelPriceGroupList.get(0);
        if(!Objects.equals(projectHotelPriceGroup.getLevelTotalRoomCount(), updateBidLevelRoomRequest.getLevelTotalRoomCount()) ||
                !Objects.equals(projectHotelPriceGroup.getLevelRoomType1Count(), updateBidLevelRoomRequest.getLevelRoomType1Count()) ||
                !Objects.equals(projectHotelPriceGroup.getLevelRoomType2Count(), updateBidLevelRoomRequest.getLevelRoomType2Count())
        ) {
            BidOperateLog updateRoomCountBidOperateLog = new BidOperateLog();
            updateRoomCountBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            updateRoomCountBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            updateRoomCountBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            updateRoomCountBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            updateRoomCountBidOperateLog.setOperator(userDTO.getOperator());
            String stringBuilder = "将" +
                    RoomLevelEnum.getValueByKey(updateBidLevelRoomRequest.getRoomLevelNo()) +
                    "房型数量 " +
                    "大床房房型数" +
                    (projectHotelPriceGroup.getLevelRoomType1Count() == null ? "空" : projectHotelPriceGroup.getLevelRoomType1Count()) +
                    ", 双床房型数" +
                    (projectHotelPriceGroup.getLevelRoomType2Count() == null ? "空" : projectHotelPriceGroup.getLevelRoomType2Count()) +
                    ", 房型总数" +
                    (projectHotelPriceGroup.getLevelTotalRoomCount() == null ? "空" : projectHotelPriceGroup.getLevelTotalRoomCount()) +
                    " 修改为 " +
                    "大床房房型数" +
                    (updateBidLevelRoomRequest.getLevelRoomType1Count() == null ? "空" : updateBidLevelRoomRequest.getLevelRoomType1Count()) +
                    ", 双床房型数" +
                    (updateBidLevelRoomRequest.getLevelRoomType2Count() == null ? "空" : updateBidLevelRoomRequest.getLevelRoomType2Count()) +
                    ", 房型总数" +
                    (updateBidLevelRoomRequest.getLevelTotalRoomCount() == null ? "空" : updateBidLevelRoomRequest.getLevelTotalRoomCount());
            updateRoomCountBidOperateLog.setOperateContent(stringBuilder);
            projectHotelPriceGroup.setLevelTotalRoomCount(updateBidLevelRoomRequest.getLevelTotalRoomCount());
            projectHotelPriceGroup.setLevelRoomType1Count(updateBidLevelRoomRequest.getLevelRoomType1Count());
            projectHotelPriceGroup.setLevelRoomType2Count(updateBidLevelRoomRequest.getLevelRoomType2Count());
            projectHotelPriceGroupDao.updateLevelRoomCount(projectHotelPriceGroup);
            bidOperateLogDao.insertBidOperateLog(updateRoomCountBidOperateLog);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateBidDay(UpdateBidDayRequest updateBidDayRequest, UserDTO userDTO){
        Response response = new Response();

        // 查询酒店报价项目
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(updateBidDayRequest.getProjectIntentHotelId());

        // 比较PriceApplyDate
        Map<Integer, List<UpdateBidApplyDayDto>> priceUpdateBidApplyDayMap = updateBidDayRequest.getBidApplyDayList().stream().collect(Collectors.groupingBy(UpdateBidApplyDayDto::getPriceType));

        PriceApplicableDay queryPriceApplicableDay = new PriceApplicableDay();
        queryPriceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
        queryPriceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
        List<PriceApplicableDay> priceApplicableDayList = priceApplicableDayDao.selectPriceApplicableDayList(queryPriceApplicableDay);
        Map<Integer, List<PriceApplicableDay>> priceApplyDayMap = priceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDay::getPriceType));

        // 操作日志
        List<BidOperateLog> bidOperateLogList = new ArrayList<>();
        for(Integer priceType : priceApplyDayMap.keySet()){
            List<PriceApplicableDay> priceTypeApplicableDayList = priceApplyDayMap.get(priceType);
            // DB price applyDay content
            StringBuilder applicableDayContent = new StringBuilder();
            for(PriceApplicableDay priceApplicableDay : priceTypeApplicableDayList){
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getStartDate()));
                applicableDayContent.append("至");
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getEndDate()));
                applicableDayContent.append(",");
            }

            List<PriceApplicableDay> addPriceApplicableDayList = new ArrayList<>();
            if(priceUpdateBidApplyDayMap.containsKey(priceType)){
                StringBuilder updateApplicableDayContent = new StringBuilder();
                for(UpdateBidApplyDayDto updateBidApplyDayDto : priceUpdateBidApplyDayMap.get(priceType)){
                    updateApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getStartDate()));
                    updateApplicableDayContent.append("至");
                    updateApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getEndDate()));
                    updateApplicableDayContent.append(",");
                    PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
                    priceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                    priceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                    priceApplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                    priceApplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                    priceApplicableDay.setPriceType(updateBidApplyDayDto.getPriceType());
                    priceApplicableDay.setCreator(userDTO.getOperator());
                    addPriceApplicableDayList.add(priceApplicableDay);
                }
                if(!applicableDayContent.toString().contentEquals(updateApplicableDayContent)){
                    // 删除旧日期
                    priceApplicableDayDao.deleteByProjectAndHotelIdAndPriceType(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), priceType);
                    // 新增新日期
                    priceApplicableDayDao.insertBatch(addPriceApplicableDayList);

                    BidOperateLog updateBidOperateLog = new BidOperateLog();
                    updateBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    updateBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
                    updateBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
                    updateBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                    updateBidOperateLog.setOperator(userDTO.getOperator());
                    updateBidOperateLog.setOperateContent("将" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                                    applicableDayContent.substring(0, applicableDayContent.length()-1) + " 修改为 " +  updateApplicableDayContent.substring(0, updateApplicableDayContent.length()-1));
                    bidOperateLogList.add(updateBidOperateLog);
                }
            // 删除价格类型对应日期
            } else {
                // 删除旧日期
                priceApplicableDayDao.deleteByProjectAndHotelIdAndPriceType(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId(), priceType);
                BidOperateLog deleteBidOperateLog = new BidOperateLog();
                deleteBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                deleteBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
                deleteBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
                deleteBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
                deleteBidOperateLog.setOperator(userDTO.getOperator());
                deleteBidOperateLog.setOperateContent("删除" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                        applicableDayContent.substring(0,applicableDayContent.length()-1));
                bidOperateLogList.add(deleteBidOperateLog);
            }
        }
        // 检查是否存在新增报价类型
        for(Integer priceType : priceUpdateBidApplyDayMap.keySet()){
            if(priceApplyDayMap.containsKey(priceType)){
                continue;
            }
            // 新增
            StringBuilder applicableDayContent = new StringBuilder();
            List<PriceApplicableDay> addPriceApplicableDayList = new ArrayList<>();
            for(UpdateBidApplyDayDto updateBidApplyDayDto : priceUpdateBidApplyDayMap.get(priceType)){
                PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
                priceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                priceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceApplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                priceApplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                priceApplicableDay.setPriceType(updateBidApplyDayDto.getPriceType());
                priceApplicableDay.setCreator(userDTO.getOperator());
                addPriceApplicableDayList.add(priceApplicableDay);

                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getStartDate()));
                applicableDayContent.append("至");
                applicableDayContent.append(DateUtil.dateToString(priceApplicableDay.getEndDate()));
                applicableDayContent.append(",");
            }
            BidOperateLog addBidOperateLog = new BidOperateLog();
            addBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            addBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            addBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            addBidOperateLog.setOperator(userDTO.getOperator());
            addBidOperateLog.setOperateContent("新增" + HotelPriceTypeEnum.getDateNameByKey(priceType) + ": " +
                    applicableDayContent.substring(0, applicableDayContent.length()-1));
            bidOperateLogList.add(addBidOperateLog);
            priceApplicableDayDao.insertBatch(addPriceApplicableDayList);
        }

        // 不适用日期
        ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest = new ProjectHotelBidStrategyRequest();
        projectHotelBidStrategyRequest.setProjectIntentHotelId(updateBidDayRequest.getProjectIntentHotelId());
        projectHotelBidStrategyRequest.setProjectId(projectIntentHotel.getProjectId());
        projectHotelBidStrategyRequest.setHotelId(projectIntentHotel.getHotelId());
        List<PriceUnapplicableDay> unapplicableDayList = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);
        StringBuilder unApplicableDayContent = new StringBuilder();
        if(CollectionUtils.isNotEmpty(unapplicableDayList)){
            for(PriceUnapplicableDay priceUnapplicableDay : unapplicableDayList) {
                unApplicableDayContent.append(DateUtil.dateToString(priceUnapplicableDay.getStartDate()));
                unApplicableDayContent.append("至");
                unApplicableDayContent.append(DateUtil.dateToString(priceUnapplicableDay.getEndDate()));
                unApplicableDayContent.append(",");
            }
        }
        StringBuilder updateUnApplicableDayContent = new StringBuilder();
        if(CollectionUtils.isNotEmpty(updateBidDayRequest.getBidUnApplyDayList())) {
            for(UpdateBidApplyDayDto updateBidApplyDayDto : updateBidDayRequest.getBidUnApplyDayList()) {
                updateUnApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getStartDate()));
                updateUnApplicableDayContent.append("至");
                updateUnApplicableDayContent.append(DateUtil.dateToString(updateBidApplyDayDto.getEndDate()));
                updateUnApplicableDayContent.append(",");
            }
        }

        if(!unApplicableDayContent.toString().contentEquals(updateUnApplicableDayContent)){
            BidOperateLog addBidOperateLog = new BidOperateLog();
            addBidOperateLog.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            addBidOperateLog.setProjectId(projectIntentHotel.getProjectId());
            addBidOperateLog.setHotelId(projectIntentHotel.getHotelId());
            addBidOperateLog.setOrgTypeId(userDTO.getOrgDTO().getOrgType());
            addBidOperateLog.setOperator(userDTO.getOperator());

            List<PriceUnapplicableDay> addPriceUnapplicableDayList = new ArrayList<>();
            for(UpdateBidApplyDayDto updateBidApplyDayDto : updateBidDayRequest.getBidUnApplyDayList()) {
                PriceUnapplicableDay priceUnapplicableDay = new PriceUnapplicableDay();
                priceUnapplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                priceUnapplicableDay.setProjectId(projectIntentHotel.getProjectId());
                priceUnapplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceUnapplicableDay.setStartDate(updateBidApplyDayDto.getStartDate());
                priceUnapplicableDay.setEndDate(updateBidApplyDayDto.getEndDate());
                priceUnapplicableDay.setCreator(userDTO.getOperator());
                addPriceUnapplicableDayList.add(priceUnapplicableDay);
            }
            // 新增不适用日期集合
            if(unApplicableDayContent.length() == 0  && updateUnApplicableDayContent.length() != 0){
                priceUnapplicableDayDao.insertBatch(addPriceUnapplicableDayList);
                addBidOperateLog.setOperateContent("新增不适用日期: " +
                        updateUnApplicableDayContent.substring(0, updateUnApplicableDayContent.length()-1));
                bidOperateLogList.add(addBidOperateLog);
            } else if(unApplicableDayContent.length() != 0  && updateUnApplicableDayContent.length() == 0){
                PriceUnapplicableDay deletePriceUnapplicableDay = new PriceUnapplicableDay();
                deletePriceUnapplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                deletePriceUnapplicableDay.setProjectId(projectIntentHotel.getProjectId());
                deletePriceUnapplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceUnapplicableDayDao.deleteByPriceUnapplicableDay(deletePriceUnapplicableDay);
                addBidOperateLog.setOperateContent("删除不适用日期: " +
                        unApplicableDayContent.substring(0, unApplicableDayContent.length()-1));
                bidOperateLogList.add(addBidOperateLog);
            } else {
                PriceUnapplicableDay deletePriceUnapplicableDay = new PriceUnapplicableDay();
                deletePriceUnapplicableDay.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                deletePriceUnapplicableDay.setProjectId(projectIntentHotel.getProjectId());
                deletePriceUnapplicableDay.setHotelId(projectIntentHotel.getHotelId());
                priceUnapplicableDayDao.deleteByPriceUnapplicableDay(deletePriceUnapplicableDay);
                priceUnapplicableDayDao.insertBatch(addPriceUnapplicableDayList);
                addBidOperateLog.setOperateContent("将不适用日期: " +
                        unApplicableDayContent.substring(0, unApplicableDayContent.length()-1) + " 修改为 " +
                        updateUnApplicableDayContent.substring(0, updateUnApplicableDayContent.length()-1)
                );
                bidOperateLogList.add(addBidOperateLog);
            }
        }

        // 新增操作日志
        if(CollectionUtils.isNotEmpty(bidOperateLogList)){
            bidOperateLogDao.insertBatch(bidOperateLogList);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    @Override
    public Response queryPriceDetail(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest, QueryProjectHotelPriceDetailResponse queryProjectHotelPriceDetailResponse) {
        Response response = new Response();
        //查询酒店基本信息
        QueryHotelInfoResponse queryHotelInfoResponse = projectIntentHotelDao.selectHotelInfoByHotelId(projectHotelBidStrategyRequest.getHotelId());
        if (queryHotelInfoResponse == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("查询酒店信息失败");
            return response;
        }
        queryProjectHotelPriceDetailResponse.setBrandName(queryHotelInfoResponse.getBrandName());
        queryProjectHotelPriceDetailResponse.setCountry(queryHotelInfoResponse.getCountryCode());
        queryProjectHotelPriceDetailResponse.setCityName(queryHotelInfoResponse.getCityName());
        queryProjectHotelPriceDetailResponse.setCityCode(queryHotelInfoResponse.getCityCode());
        queryProjectHotelPriceDetailResponse.setProvinceName(queryHotelInfoResponse.getProvinceName());
        queryProjectHotelPriceDetailResponse.setHotelAddress(queryHotelInfoResponse.getHotelAddress());
        if(StringUtil.isValidString(queryHotelInfoResponse.getPraciceDate())) {
            queryProjectHotelPriceDetailResponse.setPraciceDate(DateUtil.stringToDate(queryHotelInfoResponse.getPraciceDate()));
        }
        queryProjectHotelPriceDetailResponse.setFitmentDate(queryHotelInfoResponse.getFitmentData());
        if(StringUtil.isValidString(queryHotelInfoResponse.getRoomNum())){
            queryProjectHotelPriceDetailResponse.setLayerCount(Integer.valueOf(queryHotelInfoResponse.getRoomNum()));
        }

        queryProjectHotelPriceDetailResponse.setRating(queryHotelInfoResponse.getRating());
        queryProjectHotelPriceDetailResponse.setLatBaiDu(queryHotelInfoResponse.getLatBaidu());
        queryProjectHotelPriceDetailResponse.setLngBaiDu(queryHotelInfoResponse.getLngBaidu());

        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectHotelBidStrategyRequest.getProjectIntentHotelId());
        if(projectIntentHotel == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);

            response.setMsg("查询酒店邀请信息失败");
            return response;
        }
        //已中签的才展示酒店服务分
        if(projectIntentHotel.getBidState() != null && HotelBidStateEnum.BID_WINNING.bidState.intValue() == projectIntentHotel.getBidState()) {
            queryProjectHotelPriceDetailResponse.setHotelServicePoints(projectIntentHotel.getHotelServicePoints());
        }
        queryProjectHotelPriceDetailResponse.setBidOrgId(projectIntentHotel.getBidOrgId());
        queryProjectHotelPriceDetailResponse.setHotelOrgId(projectIntentHotel.getHotelOrgId());
        queryProjectHotelPriceDetailResponse.setCertsUrl(projectIntentHotel.getCertsUrl());
        queryProjectHotelPriceDetailResponse.setBidWeight(projectIntentHotel.getBidWeight());
        queryProjectHotelPriceDetailResponse.setIsLanyonImport(BidUtil.isLanyonImport(projectIntentHotel));
        queryProjectHotelPriceDetailResponse.setLanyonImportDataCount(0);
        if(Objects.equals(queryProjectHotelPriceDetailResponse.getIsLanyonImport(), RfpConstant.constant_1)){
            int lanyonDataCount = lanyonImportDataDao.getDataCountByProjectHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
            queryProjectHotelPriceDetailResponse.setLanyonImportDataCount(lanyonDataCount);
        }
        queryProjectHotelPriceDetailResponse.setRejectNegotiationRemark(projectIntentHotel.getRejectNegotiationRemark());
        queryProjectHotelPriceDetailResponse.setBidOrgType(projectIntentHotel.getBidOrgType());
        CountDownLatch countDownLatch = new CountDownLatch(3);
        //查询项目poi信息用于计算每个酒店距离poi的距离
        rfpCommonExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    List<ProjectPoiInfoResponse> projectPoiInfoResponses = projectPoiDao.selectProjectPoiInfo(projectHotelBidStrategyRequest.getProjectId());
                    //计算每个酒店距离poi的距离
                    if (CollectionUtils.isNotEmpty(projectPoiInfoResponses)) {

                        if (queryHotelInfoResponse.getLatBaidu() == null || queryHotelInfoResponse.getLngBaidu() == null) {
                            return;
                        }
                        //只需要距离最小的值
                        double minDistance = 0d;
                        String poiName = "";
                        for (ProjectPoiInfoResponse projectPoiInfoRespons : projectPoiInfoResponses) {
                            //只有相同城市的才计算距离。
                            if (!queryHotelInfoResponse.getCityCode().equals(projectPoiInfoRespons.getCityCode())) {
                                continue;
                            }
                            if (projectPoiInfoRespons.getLngBaiDu() == null || projectPoiInfoRespons.getLatBaiDu() == null) {
                                continue;
                            }
                            try {
                                double distance = LocationUtil.getDistance(projectPoiInfoRespons.getLatBaiDu(), projectPoiInfoRespons.getLngBaiDu(), queryHotelInfoResponse.getLatBaidu(), queryHotelInfoResponse.getLngBaidu());
                                if (minDistance == 0 || distance <= minDistance) {
                                    minDistance = distance;
                                    poiName = projectPoiInfoRespons.getPoiName();
                                }
                            } catch (Exception e) {
                                logger.error("计算项目poi距离异常，poiId" + projectPoiInfoRespons.getPoiId() + ",projectHotelBidStrategyRequest:"+ JSON.toJSONString(projectHotelBidStrategyRequest));
                            }
                        }
                        if (minDistance > 0 && StringUtil.isValidString(poiName)) {
                            queryProjectHotelPriceDetailResponse.setPoiNameDistance(poiName + new BigDecimal(String.valueOf(minDistance)).divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP) + "km");
                        }
                    }
                } catch (Exception e) {
                    logger.error("计算项目poi距离异常,projectHotelBidStrategyRequest:"+ JSON.toJSONString(projectHotelBidStrategyRequest));
                } finally {
                    countDownLatch.countDown();
                }
            }
        });

        //查询图片
        rfpCommonExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try{
                    HotelImageInfoResponse hotelImageInfoResponse = new HotelImageInfoResponse();
                    hotelImageInfoResponse.setHotelId(projectHotelBidStrategyRequest.getHotelId());
                    hotelApplyOnlineService.queryAllImageUrl(hotelImageInfoResponse);
                    queryProjectHotelPriceDetailResponse.setMainImageUrl(hotelImageInfoResponse.getMainImageUrl());
                    queryProjectHotelPriceDetailResponse.setRoomImageUrls(hotelImageInfoResponse.getRoomImageUrls());
                    queryProjectHotelPriceDetailResponse.setTotalImageCount(hotelImageInfoResponse.getTotalImageCount());
                    queryProjectHotelPriceDetailResponse.setAllImageCategories(hotelImageInfoResponse.getAllImageCategories());
                } catch (Exception e) {
                    logger.error("查询图片信息异常，酒店id:"+ projectHotelBidStrategyRequest.getHotelId(),e);
                } finally {
                    countDownLatch.countDown();
                }
            }
        });

        //查询签约主体(只需要默认主体)
        rfpCommonExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if(projectIntentHotel.getHotelSubjectId() != null){
                        OrgSubject orgSubject = orgSubjectDao.selectOrgSubjectInfoBySubjectId(projectIntentHotel.getHotelSubjectId());
                        queryProjectHotelPriceDetailResponse.setOrgSubject(orgSubject);
                    } else if(projectIntentHotel.getBidOrgId() == null){
                        OrgSubject orgSubject = orgSubjectDao.selectDefaultSubjectByHotelId(projectIntentHotel.getHotelId());
                        queryProjectHotelPriceDetailResponse.setOrgSubject(orgSubject);
                    }else {
                        List<Long> orgIds = new ArrayList<>();
                        orgIds.add(projectIntentHotel.getBidOrgId());
                        List<OrgSubject> orgSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(orgIds);
                        if (CollectionUtils.isNotEmpty(orgSubjects)) {
                            queryProjectHotelPriceDetailResponse.setOrgSubject(orgSubjects.get(0));
                        }
                    }
                }catch (Exception e){
                    logger.error("查询签约主体信息，projectHotelBidStrategyRequest:"+  JSON.toJSONString(projectHotelBidStrategyRequest),e);
                }finally {
                    countDownLatch.countDown();
                }
            }
        });

        // 报价房档明细
        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        projectIntentHotelRequest.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        List<ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponses = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
        if (CollectionUtils.isNotEmpty(projectHotelPriceLevelResponses)) {
            queryProjectHotelPriceDetailResponse.setHotelPriceLevelResponseList(projectHotelPriceLevelResponses);
        }

        // 不适用日期
        List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);
        if (CollectionUtils.isNotEmpty(priceUnapplicableDays)) {
            queryProjectHotelPriceDetailResponse.setUnapplicableDayList(priceUnapplicableDays);
        }

        // 查询报价日期设置
        Response priceApplicableDayResponse = priceApplicableDayService.selectPriceApplicableDayListByProject(projectHotelBidStrategyRequest, false);
        if(priceApplicableDayResponse.getData() != null){
            List<PriceApplicableDay> priceApplicableDayList = (List<PriceApplicableDay>)priceApplicableDayResponse.getData();
            queryProjectHotelPriceDetailResponse.setPriceApplicableDayList(priceApplicableDayList);
        }

        // 查询操作日志
        BidOperateLog bidOperateLog = new BidOperateLog();
        bidOperateLog.setProjectIntentHotelId(projectHotelBidStrategyRequest.getProjectIntentHotelId());
        List<BidOperateLog> bidOperateLogList = bidOperateLogDao.queryOperateLogList(bidOperateLog);
        queryProjectHotelPriceDetailResponse.setBidOperateLogList(bidOperateLogList);

        // 查询自动上线信息
        Project project = projectDao.selectByPrimaryKey(projectIntentHotel.getProjectId());
        queryProjectHotelPriceDetailResponse.setIsAutoConfigGoOnline(project.getIsAutoConfigGoOnline());
        queryProjectHotelPriceDetailResponse.setProjectOrgId(project.getTenderOrgId());;
        if(project.getIsAutoConfigGoOnline() == RfpConstant.constant_1){
            queryProjectHotelPriceDetailResponse.setOnlineDistributorCode(project.getOnlineDistributorCode());
            queryProjectHotelPriceDetailResponse.setOnlineInvoiceSet(project.getOnlineInvoiceSet());
            queryProjectHotelPriceDetailResponse.setOnlineServiceFeeSet(project.getOnlineServiceFeeSet());
            queryProjectHotelPriceDetailResponse.setOnlineServiceFeeType(project.getOnlineServiceFeeType());
            queryProjectHotelPriceDetailResponse.setOnlineServiceFee(project.getOnlineServiceFee());
            queryProjectHotelPriceDetailResponse.setReceiveOrderMethod(projectIntentHotel.getReceiveOrderMethod());
            queryProjectHotelPriceDetailResponse.setTaxDiffIncreaseRate(projectIntentHotel.getTaxDiffIncreaseRate());

            // 检查是否有lra报价
            boolean isLraLock = false;
            List<ProjectHotelPriceLevelResponse>  priceLevelResponseList = queryProjectHotelPriceDetailResponse.getHotelPriceLevelResponseList();
            for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : priceLevelResponseList){
                List<ProjectHotelPriceGroupResponse> projectHotelPriceGroupResponseList = projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList();
                for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList){
                    if(projectHotelPriceGroupResponse.getLra() == RfpConstant.constant_1){
                        isLraLock = true;
                        break;
                    }
                }
            }
            queryProjectHotelPriceDetailResponse.setLraLock(isLraLock);
        }

        // 修订报价查询议价中备注信息
        List<ProjectHotelRemarkResponse> projectHotelRemarkResponseList = projectHotelRemarkDao.selectList(project.getProjectId(), projectIntentHotel.getHotelId(), null);
        queryProjectHotelPriceDetailResponse.setProjectHotelRemarkResponseList(projectHotelRemarkResponseList);


        // 查询酒店推荐等级
        List<HotelRecommendLevelResponse> hotelRecommendLevelResponseList = projectHotelHistoryDataDao.queryHotelRecommendLevel(project.getProjectId(), Arrays.asList(queryHotelInfoResponse.getHotelId()));
        if(CollectionUtils.isNotEmpty(hotelRecommendLevelResponseList) && StringUtil.isValidString(hotelRecommendLevelResponseList.get(0).getRecommendLevel())){
            queryProjectHotelPriceDetailResponse.setRecommendLevel(Integer.valueOf(hotelRecommendLevelResponseList.get(0).getRecommendLevel()));
        }

        // 查询是否生成合同
        List<Contract> contractList = contractDao.selectByProjectBussinessId(Arrays.asList(projectIntentHotel.getProjectIntentHotelId()));
        if(CollectionUtils.isNotEmpty(contractList)){
            queryProjectHotelPriceDetailResponse.setIsGeneratedContract(RfpConstant.constant_1);
        } else {
            queryProjectHotelPriceDetailResponse.setIsGeneratedContract(RfpConstant.constant_0);
        }

        // 权重2.0计算开关
        if(project.getTotalWeight() != null && project.getTotalWeight().compareTo(BigDecimal.ZERO) > 0) {
            projectHotelWeightService.setBidWeightInfo(queryProjectHotelPriceDetailResponse, project.getProjectId(), projectIntentHotel.getHotelId());
        }

        boolean noTimeout = true;
        try {
            //超时返回false
            noTimeout = countDownLatch.await(15000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            logger.error("报价信息查询失败，请求参数：projectHotelBidStrategyRequest :" +  JSON.toJSONString(projectHotelBidStrategyRequest), e);
        }
        if (!noTimeout) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
            return response;
        }
        response.setData(queryProjectHotelPriceDetailResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }


    @Override
    public Response queryUnpricedHotelManagement(QueryUnpricedManagementRequest queryUnpricedManagementRequest) {
        Response response = new Response();

        if(queryUnpricedManagementRequest == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if(queryUnpricedManagementRequest.getBidDateFrom() == null || queryUnpricedManagementRequest.getBidDateTo() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("日期不能为空");
            return response;
        }
        Date bidDateForm = DateUtil.stringToDate(queryUnpricedManagementRequest.getBidDateFrom());
        Date bidDateTo = cn.hutool.core.date.DateUtil.offsetDay(DateUtil.stringToDate(queryUnpricedManagementRequest.getBidDateTo()), 1) ;
        if(cn.hutool.core.date.DateUtil.betweenMonth(bidDateForm, bidDateTo, false) > 13){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("日期跨度调整最大13个月");
            return response;
        }
        queryUnpricedManagementRequest.setBidDateTo(DateUtil.dateToString(bidDateTo));
        PageHelper.startPage(queryUnpricedManagementRequest.getCurrentPage(), queryUnpricedManagementRequest.getPageSize());
        List<QueryUnpricedManagementResponse> queryUnpricedManagementResponses = projectIntentHotelDao.queryUnpricedHotelManagement(queryUnpricedManagementRequest);
        if(CollectionUtils.isNotEmpty(queryUnpricedManagementResponses)){
            for (QueryUnpricedManagementResponse queryUnpricedManagementRespons : queryUnpricedManagementResponses) {
                //计算累计时长
                Date createTime = queryUnpricedManagementRespons.getCreateTime();
                if(createTime != null){
                    Date date = new Date();
                    LocalDateTime startTime = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime endTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    Duration duration = Duration.between(startTime, endTime);

                    long days = duration.toDays(); // 获取相差的天数
                    long hours = duration.toHours() % 24; // 获取相差的小时数（不足一天的部分）
                    queryUnpricedManagementRespons.setAccumulatedDuration(days + "天" + hours + "小时");
                }
            }
        }

        response.setData(PageUtil.makePageResult(queryUnpricedManagementResponses));
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response processingBidState(ProcessingBidStateRequest processingBidStateRequest) {
        Response response = new Response();
        if(processingBidStateRequest.getProjectIntentHotelId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        Integer bidState = processingBidStateRequest.getBidState();
        if(bidState == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg(ReturnResultEnum.PARAMETER_EXCEPTION.message);
            return response;
        }
        if(HotelBidStateEnum.NO_BID.bidState != bidState && HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState != bidState){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("报价状态有误");
            return response;
        }
        //不放弃报价且没有填写备注，直接返回成功
        String unPriceRemark = processingBidStateRequest.getUnPriceRemark();
        if(HotelBidStateEnum.NO_BID.bidState == bidState && !StringUtil.isValidString(unPriceRemark)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        }
        if(HotelBidStateEnum.WITHDRAW_THE_QUOTATION.bidState == bidState && !StringUtil.isValidString(unPriceRemark)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("放弃报价需要填写备注信息");
            return response;
        }
        //备注长度不能大于128
        if(StringUtil.isValidString(unPriceRemark) && unPriceRemark.length() > 128){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("备注长度不能超过128");
            return response;
        }
        Set<Long> projectIntentHotelIds = new HashSet<>();
        projectIntentHotelIds.add(processingBidStateRequest.getProjectIntentHotelId());
        processingBidStateRequest.setProjectIntentHotelIds(projectIntentHotelIds);
        int i = projectIntentHotelDao.processingBidState(processingBidStateRequest);
        if(i==1) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        }else{
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response batchHotelRegister(BatchHotelRegisterRequest batchHotelRegisterRequest) {
        Response response = new Response();
        List<QueryRegisterHotelInfoResponse> queryRegisterHotelInfoResponses = projectIntentHotelDao.queryRegisterHotelInfo(batchHotelRegisterRequest);
        if(CollectionUtils.isEmpty(queryRegisterHotelInfoResponses)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("未查询到意向酒店信息");
            return response;
        }
        //key :酒店名称  value：注册失败原因
        Map<String,String> errorMsgMap = new HashMap<>();
        int successCount = 0;//注册成功数
        for (QueryRegisterHotelInfoResponse queryRegisterHotelInfoResponse : queryRegisterHotelInfoResponses) {
            String hotelName = queryRegisterHotelInfoResponse.getHotelName();
            Long hotelId = queryRegisterHotelInfoResponse.getHotelId();
            String contactName = queryRegisterHotelInfoResponse.getContactName();
            if(!StringUtil.isValidString(contactName)){
                errorMsgMap.put(hotelName,"机构联系人姓名为空");
                continue;
            }

            String contactMobile = queryRegisterHotelInfoResponse.getContactMobile();
            String contactEmail = queryRegisterHotelInfoResponse.getContactEmail();
            if(!ValidateUtil.isValidMobileNo(contactMobile)){
                errorMsgMap.put(hotelName,"机构联系人手机号无效");
                continue;
            }

            if (StringUtil.isValidString(contactEmail) && !ValidateUtil.isValidEmail(contactEmail)) {
                contactEmail = null;
            }
            OrgDTO orgDTO = new OrgDTO();
            orgDTO.setHotelName(hotelName);
            orgDTO.setHotelId(hotelId);
            orgDTO.setOrgName(hotelName);
            orgDTO.setOrgType(OrgTypeEnum.HOTEL.key);
            orgDTO.setCanProviderCoPay(StateEnum.Invalid.key);
            orgDTO.setState(StateEnum.Effective.key);
            orgDTO.setCreator(batchHotelRegisterRequest.getOperator());
            orgDTO.setContactName(contactName);
            orgDTO.setContactMobile(contactMobile);
            orgDTO.setContactEmail(contactEmail);
            Response addOrgResponse = orgService.addOrg(orgDTO);
            if(addOrgResponse.getResult() != ReturnResultEnum.SUCCESS.errorNo){
                errorMsgMap.put(hotelName,addOrgResponse.getMsg());
                continue;
            }
            Long orgId =(Long) addOrgResponse.getData();
            UserDTO user = new UserDTO();
            user.setMobile(contactMobile);
            user.setUserName(contactName);
            user.setOrgId(orgId);
            user.setRoleCodeType(RoleCodeEnum.ADMIN.key);
            user.setCreator(batchHotelRegisterRequest.getOperator());
            user.setState(StateEnum.Effective.key);
            Response addUserResponse = userService.insertUser(user);
            if(addUserResponse.getResult() != ReturnResultEnum.SUCCESS.errorNo){
                errorMsgMap.put(hotelName,addUserResponse.getMsg());
                continue;
            }
            successCount ++;
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        errorMsgMap.put("failCount",String.valueOf(errorMsgMap.size()));
        errorMsgMap.put("successCount",String.valueOf(successCount));
        response.setData(errorMsgMap);
        return response;
    }

    @Override
    public Response assignPlatformUser(AssignPlatformUserRequest assignPlatformUserRequest) {
        Response response = new Response();
        if(CollectionUtils.isEmpty(assignPlatformUserRequest.getProjectIntentHotelIds())){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("酒店意向id参数为空");
            return response;
        }

        if(assignPlatformUserRequest.getPlatformContactUid() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("跟进人信息为空");
            return response;
        }

        if(!StringUtil.isValidString(assignPlatformUserRequest.getPlatformContactName())){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("跟进人信息为空");
            return response;
        }
        projectIntentHotelDao.assignPlatformUser(assignPlatformUserRequest);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryPerformanceInfo(QueryPerformanceInfoRequest queryPerformanceInfoRequest) {
        Response response = new Response();
        if(queryPerformanceInfoRequest.getProjectId() == null){
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("项目id参数为空");
            return response;
        }

        // 检查项目信息
        Project project = projectDao.selectByPrimaryKey(queryPerformanceInfoRequest.getProjectId());
        if(project == null){
            response.setResult(ReturnResultEnum.RESOURCE_NOT_FOUND.errorNo);
            response.setMsg("项目不存在");
            return response;
        }
        if(project.getProjectState() == ProjectStateEnum.NOT_STARTED.key || project.getProjectState() == ProjectStateEnum.BID_ABANDONED.key){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目状态错误");
            return response;
        }
        if(project.getPriceMonitorStartDate() == null || project.getPriceMonitorEndDate() == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目协议报价日期范围为空");
            return response;
        }

        // 设置查询日期
        queryPerformanceInfoRequest.setStartDate(DateUtil.dateToString(project.getPriceMonitorStartDate()));
        queryPerformanceInfoRequest.setEndDate(DateUtil.dateToString(project.getPriceMonitorEndDate()));

        // 酒店集团查询
        if(queryPerformanceInfoRequest.getHotelGroupOrgId() != null) {
            QueryProjectIntentHotelGroupRequest queryProjectIntentHotelGroupRequest = new QueryProjectIntentHotelGroupRequest();
            queryProjectIntentHotelGroupRequest.setProjectIds(Arrays.asList(queryPerformanceInfoRequest.getProjectId()));
            queryProjectIntentHotelGroupRequest.setHotelGroupOrgId(queryPerformanceInfoRequest.getHotelGroupOrgId());
            if(queryPerformanceInfoRequest.getUserId() != null){
                queryProjectIntentHotelGroupRequest.setHotelGroupContactUid(queryPerformanceInfoRequest.getUserId());
            }

            List<ProjectIntentHotelGroup> projectIntentHotelGroupList = projectIntentHotelGroupDao.queryIntentHotelGroupInfo(queryProjectIntentHotelGroupRequest);
            if(CollectionUtils.isEmpty(projectIntentHotelGroupList)){
                logger.info("projectIntentHotelGroupDao.queryIntentHotelGroupInfo " + JSON.toJSONString(queryProjectIntentHotelGroupRequest));
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg("未查到酒店集团相关项目履约情况");
                return response;
            }
        }

        // 查询项目分销商信息
        List<String> distributorCodeList = orderMonitorConfigDao.queryOrderMonitorConfig(Arrays.asList(project.getProjectId())).stream().map(o -> o.getDistributorCode()).collect(Collectors.toList());
        queryPerformanceInfoRequest.setDistributorCodeList(distributorCodeList);

        // 根据酒店分组查询酒店履约情况
        PageHelper.startPage(queryPerformanceInfoRequest.getCurrentPage(), queryPerformanceInfoRequest.getPageSize());
        List<QueryPerformanceInfoResponse> result = hotelViolationsMonitorDao.queryViolationMonitorGroupByHotelId(queryPerformanceInfoRequest);
        PageResult pageResult = PageUtil.makePageResult(result);
        if(CollectionUtils.isNotEmpty(pageResult.getList())){
            for(Object object : pageResult.getList()) {
                QueryPerformanceInfoResponse queryPerformanceInfoResponse = (QueryPerformanceInfoResponse)object;
                queryPerformanceInfoResponse.setProjectName(project.getProjectName());
                queryPerformanceInfoResponse.setStartDate(queryPerformanceInfoRequest.getStartDate());
                queryPerformanceInfoResponse.setEndDate(queryPerformanceInfoRequest.getEndDate());
            }

        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);

        return response;
    }

    public void exportPerformanceInfo(QueryPerformanceInfoRequest queryPerformanceInfoRequest, HttpServletResponse httpServletResponse) throws IOException {
        SXSSFWorkbook workbook = null;
        try {
            // 创建Excel
            workbook = new SXSSFWorkbook();
            // 创建页
            Sheet sheet = workbook.createSheet();
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("序号");
            headerRow.createCell(1).setCellValue("城市");
            headerRow.createCell(2).setCellValue("项目名称");
            headerRow.createCell(3).setCellValue("酒店名称");
            headerRow.createCell(4).setCellValue("集团名称");
            headerRow.createCell(5).setCellValue("品牌");
            headerRow.createCell(6).setCellValue("项目期间累计夜间量");
            headerRow.createCell(7).setCellValue("酒店服务分");
            headerRow.createCell(8).setCellValue("违规次数");
            headerRow.createCell(9).setCellValue("更新时间");

            int currentPage = 1;
            int pageSize = 1000;
            while (true) {
                queryPerformanceInfoRequest.setCurrentPage(currentPage);
                queryPerformanceInfoRequest.setPageSize(pageSize);
                Response response = queryPerformanceInfo(queryPerformanceInfoRequest);
                logger.info("exportPerformanceInfo " + currentPage);
                if (response.getData() == null) {
                    break;
                }
                PageResult<QueryPerformanceInfoResponse> pageResult = (PageResult<QueryPerformanceInfoResponse>) response.getData();
                if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }
                setExcelData(pageResult.getList(), sheet);

                if (pageResult.getList().size() < pageSize) {
                    break;
                }
                currentPage++;
            }
            String fileName = "酒店履约情况" + DateUtil.dateToString(new Date(), "yyyyMMddHHmmss") + ".xlsx";
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1));
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("导出酒店履约情况：" + JSON.toJSONString(queryPerformanceInfoRequest), e);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 下载签约状态导入模版
     */
    public void downloadProjectBidStateTemplate(HttpServletResponse httpServletResponse){
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        InputStream inputStreamInfo = null;
        try {
            inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath ,"projectBidStateTemplate.xlsx");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename="+new String("签约状态导入模版.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("签约状态导入模版模板下载异常", e);
        } finally {
            if (inputStreamInfo != null) {
                try {
                    inputStreamInfo.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error("关闭文件转数据流失败", e);
                }
            }
        }
    }

    /**
     * 导入签约状态
     */
    public Response importHotelBidState(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws Exception {
        Response response = new Response();
        Project project = projectDao.selectByPrimaryKey(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目信息不存在");
            return response;
        }

        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());

        // 获得有多少行数据 不能多于10000行
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        logger.info("checkFormat rowSize=" + rowSize);
        if(rowSize<=1){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入数据为空");
            return response;
        }

        if (rowSize > 10001) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("最多上传10000个历史项目信息");
            return response;
        }
        //校验标题是否符合规范
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("上传文件为空");
            return response;
        }
        for(int k=0; k<3; k++) {
            Cell cell = titleRow.getCell(k);
            String headName = cell.getStringCellValue();
            logger.info("checkFormat headIndex=" + k + " headName=" + headName);
            Integer keyByValue = ImportHotelBidStateEnum.getKeyByValue(headName);
            if (keyByValue == null || keyByValue.intValue() != k) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("EXCEL标题不符合规范，请下载签约状态导入模板");
                return response;
            }
        }

        List<ImportUpdateBidStateDto> importProjectIntentHotelList = new ArrayList<>();
        List<String> invalidDataList = new ArrayList<>();
        for (int i = 1; i < rowSize; i++) {
            Row dataRow = sheet.getRow(i);
            try {
                if (dataRow == null) {
                    continue;
                }

                // 检查酒店ID
                String fcHotelIdString = CommonUtil.getCellValue(dataRow.getCell(0));
                if(!StringUtil.isValidString(fcHotelIdString)){
                    invalidDataList.add("第"+ (i+1) + "行请输入正确的酒店id，不能为空");
                    continue;
                }
                if (!NumberUtils.isNumber(fcHotelIdString)) {
                    invalidDataList.add("第"+ (i+1) + "行酒店id无效：" + fcHotelIdString+"，请输入正确的酒店id");
                    continue;
                }
                //校验房仓酒店id是否有效
                Long hotelId = Long.valueOf(fcHotelIdString);
                ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectId, hotelId);
                if (projectIntentHotel == null) {
                    invalidDataList.add("第"+ (i+1) + "行酒店报价为空：" + fcHotelIdString+"，请输入正确的酒店id");
                    continue;
                }

                // 检查签约状态
                String bidStateValue = CommonUtil.getCellValue(dataRow.getCell(1));
                if(!StringUtil.isValidString(bidStateValue)){
                    invalidDataList.add("第"+ (i+1) + "行请输入正确的签约状态，不能为空");
                    continue;
                }
                Integer bidState = HotelBidStateEnum.getKeyByValue(bidStateValue);
                if(bidState == null){
                    invalidDataList.add("第"+ (i+1) + "行请输入正确的签约状态，不存在签约状态：" + bidStateValue);
                    continue;
                }

                // 检查remark
                String remark =  CommonUtil.getCellValue(dataRow.getCell(2));;
                if(StringUtil.isValidString(remark) && remark.length() > 2000){
                    invalidDataList.add("第"+ (i+1) + "行请输入留言文本超过2000字符");
                    continue;
                }

                ImportUpdateBidStateDto importProjectIntentHotel = new ImportUpdateBidStateDto();
                importProjectIntentHotel.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                importProjectIntentHotel.setProjectId(projectIntentHotel.getProjectId());
                importProjectIntentHotel.setHotelId(projectIntentHotel.getHotelId());
                importProjectIntentHotel.setBidState(bidState);
                importProjectIntentHotel.setRemark(remark);
                importProjectIntentHotel.setRowIndex(i+1);
                importProjectIntentHotelList.add(importProjectIntentHotel);
            } catch (Exception e) {
                logger.error("导入签约状态异常：" ,e);
                invalidDataList.add("第"+ (i+1) + "行签约状态异常");
            }
        }

        // 检查不通过 返回
        if(CollectionUtils.isNotEmpty(invalidDataList)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg("导入签约状态检查失败");
            response.setData(invalidDataList);
            return response;
        }

        // 导入数据
        List<ImportUpdateBidStateDto> updateSucceededList = new ArrayList<>();
        List<List<ImportUpdateBidStateDto>> splitLists = ListUtil.split(importProjectIntentHotelList, 1000);
        for (List<ImportUpdateBidStateDto> splitList : splitLists) {
            for(ImportUpdateBidStateDto projectIntentHotel : splitList) {
                UpdateProjectIntentHotelDto updateProjectIntentHotelDto = new UpdateProjectIntentHotelDto();
                updateProjectIntentHotelDto.setOperateType(8);
                updateProjectIntentHotelDto.setProjectId(projectId);
                updateProjectIntentHotelDto.setHotelIds(Lists.newArrayList(projectIntentHotel.getHotelId()));
                updateProjectIntentHotelDto.setBidState(projectIntentHotel.getBidState());
                if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.REJECT_NEGOTIATION.bidState)){
                    updateProjectIntentHotelDto.setRejectNegotiationRemark(projectIntentHotel.getRemark());
                } else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) ||
                        Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.REJECTED.bidState)){
                    updateProjectIntentHotelDto.setRemark(projectIntentHotel.getRemark());
                }
                updateProjectIntentHotelDto.setUploadRemark(projectIntentHotel.getRemark());
                Response updateResponse = projectService.updateProjectIntentHotel(updateProjectIntentHotelDto, userDTO);
                if(updateResponse == null){
                    invalidDataList.add("第"+ projectIntentHotel.getRowIndex() + "行签约状态修改异常：空");
                } else if(!Objects.equals(updateResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                    invalidDataList.add("第"+ projectIntentHotel.getRowIndex() + "行签约状态修改失败：" + updateResponse.getMsg());
                } else {
                    updateSucceededList.add(projectIntentHotel);
                }
            }
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("导入状态修改成功数："+ updateSucceededList.size() + ",失败数："+ invalidDataList.size());
        response.setData(invalidDataList);
        return response;
    }


    /**
     * 下载签约房型模版
     */
    public void downloadProjectBidRoomTypeTemplate(HttpServletResponse httpServletResponse){
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        InputStream inputStreamInfo = null;
        try {
            inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath ,"projectBidRoomTypeTemplate.xlsx");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename="+new String("签约房型批量更新模板.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("签约状态导入模版模板下载异常", e);
        } finally {
            if (inputStreamInfo != null) {
                try {
                    inputStreamInfo.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error("关闭文件转数据流失败", e);
                }
            }
        }
    }


    /**
     * 导入批量更新签约房型
     */
    public Response importHotelBidRoomType(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws Exception {
        Response response = new Response();
        Project project = projectDao.selectByPrimaryKey(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目信息不存在");
            return response;
        }

        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());

        // 获得有多少行数据 不能多于10000行
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        logger.info("checkFormat rowSize=" + rowSize);
        if(rowSize<=1){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入数据为空");
            return response;
        }

        if (rowSize > 10001) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("最多上传10000个历史项目信息");
            return response;
        }
        //校验标题是否符合规范
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("上传文件为空");
            return response;
        }
        for(int k=0; k<3; k++) {
            Cell cell = titleRow.getCell(k);
            String headName = cell.getStringCellValue();
            logger.info("checkFormat headIndex=" + k + " headName=" + headName);
            Integer keyByValue = ImportHotelBidRoomTypeEnum.getKeyByValue(headName);
            if (keyByValue == null || keyByValue.intValue() != k) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("EXCEL标题不符合规范，请下载签约状态导入模板");
                return response;
            }
        }

        Map<Long, ProjectIntentHotel> projectIntentHotelMap = new HashMap<>();
        Map<Long, List<Integer>> projectIntentHotelRowIndexMap = new HashMap<>();
        Map<Long, List<PriceApplicableRoom>> uploadPriceApplicableRoomMap = new HashMap<>();
        Map<Long, Map<Long, RoomInfoResponse>> projectIntentHotelRoomInfoMap = new HashMap<>();
        List<String> invalidDataList = new ArrayList<>();
        for (int i = 1; i < rowSize; i++) {
            Row dataRow = sheet.getRow(i);
            try {
                if (dataRow == null) {
                    continue;
                }

                // 检查酒店ID
                String fcHotelIdString = CommonUtil.getCellValue(dataRow.getCell(0));
                if(!StringUtil.isValidString(fcHotelIdString)){
                    invalidDataList.add("第"+ (i+1) + "行：酒店id为空");
                    continue;
                }
                if (!NumberUtils.isNumber(fcHotelIdString)) {
                    invalidDataList.add("第"+ (i+1) + "行：酒店id：" + fcHotelIdString+"无效");
                    continue;
                }
                //校验房仓酒店id是否有效
                Long hotelId = Long.valueOf(fcHotelIdString);
                ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(projectId, hotelId);
                if (projectIntentHotel == null) {
                    invalidDataList.add("第"+ (i+1) + "行：未找到酒店id：" + fcHotelIdString+"，请检查是否填写正确");
                    continue;
                }

                // 检查房档序号
                String roomLevelNoStr = CommonUtil.getCellValue(dataRow.getCell(1));
                if(!StringUtil.isValidString(roomLevelNoStr)){
                    invalidDataList.add("第"+ (i+1) + "行：房档序号为空");
                    continue;
                }
                if (!NumberUtils.isNumber(roomLevelNoStr)) {
                    invalidDataList.add("第"+ (i+1) + "行：房档序号：" + roomLevelNoStr+"无效");
                    continue;
                }
                int roomLevelNo = Integer.valueOf(roomLevelNoStr);
                int maxRoomLevelNo = projectHotelPriceGroupDao.selectMaxRoomLevelNo(projectIntentHotel.getProjectIntentHotelId(), null, null);
                if(roomLevelNo <= 0 || roomLevelNo > maxRoomLevelNo){
                    invalidDataList.add("第"+ (i+1) + "行：房档序号：" + roomLevelNoStr+"无效");
                    continue;
                }

                // 检查房型ID
                String roomTypeIdsStr = CommonUtil.getCellValue(dataRow.getCell(2));
                if(!StringUtil.isValidString(roomTypeIdsStr)){
                    invalidDataList.add("第"+ (i+1) + "行：房型ID为空");
                    continue;
                }
                List<String> roomTypeIdStrList = Arrays.asList(roomTypeIdsStr.split(","));
                Set<Long> roomTypeIdSet = new HashSet<>();
                for(String roomTypeIdStr : roomTypeIdStrList){
                    if (!NumberUtils.isNumber(roomTypeIdStr)) {
                        invalidDataList.add("第"+ (i+1) + "行：房型ID：" + roomTypeIdStr+"无效");
                        continue;
                    }
                    roomTypeIdSet.add(Long.valueOf(roomTypeIdStr));
                }
                // 检查房型是否属于酒店
                Map<Long, RoomInfoResponse> roomInfoResponsesMap = new HashMap<>();
                List<RoomInfoResponse> roomInfoResponseList = hotelDao.selectHotelRoomInfoList(projectIntentHotel.getHotelId());
                if (!CollectionUtils.isEmpty(roomInfoResponseList)) {
                    roomInfoResponsesMap = roomInfoResponseList.stream().collect(Collectors.toMap(RoomInfoResponse::getRoomId, Function.identity()));
                } else {
                    invalidDataList.add("第"+ (i+1) + "行：酒店获取房型信息异常：" + projectIntentHotel.getHotelId());
                    continue;
                }

                // 检查房型是否存在其他房档案
                List<PriceApplicableRoom> priceApplicableRoomList = priceApplicableRoomDao.selectAllPriceApplicableRoom(projectIntentHotel.getProjectIntentHotelId());

                Map<Long, RoomInfoResponse> roomInfoMap = new HashMap<>();
                List<PriceApplicableRoom> uploadPriceApplicableRoomList = new ArrayList<>();
                int displayOrder =0;
                for(Long roomTypeId : roomTypeIdSet){
                    displayOrder++;
                    RoomInfoResponse roomInfoResponse = roomInfoResponsesMap.get(roomTypeId);
                    if(roomInfoResponse == null){
                        invalidDataList.add("第"+ (i+1) + "行：房型id: " + roomTypeId + "与酒店不匹配");
                        continue;
                    }
                    roomInfoMap.put(roomTypeId, roomInfoResponse);
                    boolean roomLevelValidateResult = true;
                    for(PriceApplicableRoom priceApplicableRoom : priceApplicableRoomList){
                        if(priceApplicableRoom.getIsDeleted() == RfpConstant.constant_1){
                            continue;
                        }
                        if(Objects.equals(priceApplicableRoom.getRoomTypeId(), roomTypeId) && priceApplicableRoom.getRoomLevelNo() != roomLevelNo){
                            invalidDataList.add("第"+ (i+1) + "行: 房型id:" + roomTypeId +"已经存在房档" + priceApplicableRoom.getRoomLevelNo() + "中");
                            roomLevelValidateResult = false;
                        }
                    }
                    if(!roomLevelValidateResult){
                        continue;
                    }


                    // 检查上传文件是否已经存在该房型
                    roomLevelValidateResult = true;
                    List<PriceApplicableRoom> otherLevelRoomList = uploadPriceApplicableRoomMap.get(projectIntentHotel.getProjectIntentHotelId());
                    if(CollectionUtils.isNotEmpty(otherLevelRoomList)){
                        for(PriceApplicableRoom priceApplicableRoom : otherLevelRoomList){
                            if(Objects.equals(priceApplicableRoom.getRoomTypeId(), roomInfoResponse.getRoomId())){
                                invalidDataList.add("第"+ (i+1) + "行: 房型id:" + roomTypeId +"已经存在上传文档房档" + priceApplicableRoom.getRoomLevelNo() + "中");
                                roomLevelValidateResult = false;
                                break;
                            }
                        }
                    }
                    if(!roomLevelValidateResult){
                        continue;
                    }

                    PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
                    priceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    priceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                    priceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                    priceApplicableRoom.setRoomLevelNo(roomLevelNo);
                    priceApplicableRoom.setDisplayOrder(displayOrder);
                    priceApplicableRoom.setRoomTypeId(roomTypeId);
                    priceApplicableRoom.setCreator(userDTO.getOperator());
                    priceApplicableRoom.setModifier(userDTO.getModifier());
                    uploadPriceApplicableRoomList.add(priceApplicableRoom);
                }
                if(projectIntentHotelMap.containsKey(projectIntentHotel.getProjectIntentHotelId())){
                    projectIntentHotelRowIndexMap.get(projectIntentHotel.getProjectIntentHotelId()).add(i+1);
                    uploadPriceApplicableRoomMap.get(projectIntentHotel.getProjectIntentHotelId()).addAll(uploadPriceApplicableRoomList);
                } else {
                    List<Integer> rowIndexList = new ArrayList<>();
                    rowIndexList.add(i + 1);
                    projectIntentHotelRowIndexMap.put(projectIntentHotel.getProjectIntentHotelId(), rowIndexList);
                    projectIntentHotelMap.put(projectIntentHotel.getProjectIntentHotelId(), projectIntentHotel);
                    uploadPriceApplicableRoomMap.put(projectIntentHotel.getProjectIntentHotelId(), uploadPriceApplicableRoomList);
                }
                if(!projectIntentHotelRoomInfoMap.containsKey(projectIntentHotel.getProjectIntentHotelId())){
                    projectIntentHotelRoomInfoMap.put(projectIntentHotel.getProjectIntentHotelId(), new HashMap<>());
                }
                projectIntentHotelRoomInfoMap.get(projectIntentHotel.getProjectIntentHotelId()).putAll(roomInfoMap);
            } catch (Exception e) {
                logger.error("更新房型校验异常：" ,e);
                invalidDataList.add("第"+ (i+1) + "行：更新房型校验异常");
            }
        }

        // 检查不通过 返回
        if(CollectionUtils.isNotEmpty(invalidDataList)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg("导入批量更新房型检查失败");
            response.setData(invalidDataList);
            return response;
        }

        // 导入数据
        int uploadSucceedHotelCount = 0;
        int uploadFailedHotelCount = 0;
        for(Long projectIntentHotelId : uploadPriceApplicableRoomMap.keySet()){
            List<Integer> rowIndexList = projectIntentHotelRowIndexMap.get(projectIntentHotelId);
            try {
                ProjectIntentHotel projectIntentHotel = projectIntentHotelMap.get(projectIntentHotelId);
                Map<Integer, List<PriceApplicableRoom>> roomLevelRoomListMap = uploadPriceApplicableRoomMap.get(projectIntentHotelId).stream().collect(Collectors.groupingBy(PriceApplicableRoom::getRoomLevelNo));
                for(Integer roomLevelNo : roomLevelRoomListMap.keySet()) {
                    priceApplicableRoomService.uploadPriceLevelRoom(userDTO, projectIntentHotel,
                            roomLevelNo, roomLevelRoomListMap.get(roomLevelNo), projectIntentHotelRoomInfoMap.get(projectIntentHotelId));
                }
                uploadSucceedHotelCount++;
            } catch (Exception ex){
                uploadFailedHotelCount++;
                logger.error("行更新房型异常：" ,ex);
                invalidDataList.add("第"+ rowIndexList.toString() + "行更新房型异常");
            }
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("批量更新房型成功酒店数："+ uploadSucceedHotelCount + ",失败酒店数："+ uploadFailedHotelCount);
        response.setData(invalidDataList);
        return response;
    }

    @Override
    public void downloadProjectBidContactInfoAndPromiseTemplate(HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        try (InputStream inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath, "projectBidContactInfoAndPromiseTemplate.xlsx")) {
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + new String("批量更新联系信息及承诺信息模板.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("批量更新联系信息及承诺信息模板下载异常", e);
        }
    }

    @Override
    public Response importProjectBidContactInfoAndCommitment(MultipartFile uploadFile, UserDTO userDTO, Long projectId) throws IOException, InvalidFormatException {
        Map<Long, ImportProjectBidContactInfoAndCommitmentDto> importHotelDataMap = new HashMap<>();

        // 校验
        Response response = validateProjectBidContactInfoAndCommitment(uploadFile, projectId, userDTO, importHotelDataMap);
        if (!Objects.equals(response.getResult(), ReturnResultEnum.SUCCESS.errorNo)) {
            return response;
        }

        // 更新报价
        batchUpdateBidContactInfoAndCommitment(userDTO, importHotelDataMap);

        return new Response(ReturnResultEnum.SUCCESS.errorNo, ReturnResultEnum.SUCCESS.getMessage());
    }

    /**
     * 批量更新
     */
    private void batchUpdateBidContactInfoAndCommitment(UserDTO userDTO, Map<Long, ImportProjectBidContactInfoAndCommitmentDto> importHotelDataMap) {
        importHotelDataMap.forEach((hotelId, importDto) -> {
            // 更新联系人信息
            updateProjectIntentHotel(userDTO, importDto);

            // 更新酒店服务承诺
            updateProjectHotelBidStrategy(userDTO, importDto);

            // 更新自定义报价策略
            updateCustomBidStrategy(importDto);
        });
    }

    /**
     * 更新自定义报价策略
     */
    private void updateCustomBidStrategy(ImportProjectBidContactInfoAndCommitmentDto importDto) {
        List<ProjectCustomBidStrategy> projectCustomBidStrategies = importDto.getProjectCustomBidStrategies();
        if (CollectionUtils.isNotEmpty(projectCustomBidStrategies)) {
            projectCustomBidStrategyDao.batchMergeProjectCustomBidStrategy(projectCustomBidStrategies);
        }
        // 更新选项
        List<ProjectCustomBidStrategyOption> options = projectCustomBidStrategies.stream().filter(e -> CollectionUtils.isNotEmpty(e.getOptions()))
            .flatMap(e -> e.getOptions().stream()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(options)){
            projectCustomBidStrategyOptionDao.batchMergeProjectCustomBidStrategyOption(options);
        }
    }

    /**
     * 更新联系人信息
     */
    private void updateProjectIntentHotel(UserDTO userDTO, ImportProjectBidContactInfoAndCommitmentDto importDto) {
        if (Objects.nonNull(importDto.getBidContactEmail()) || Objects.nonNull(importDto.getBidContactMobile()) ||
            Objects.nonNull(importDto.getBidContactName()) || Objects.nonNull(importDto.getHotelGroupBidContactEmail()) ||
            Objects.nonNull(importDto.getHotelGroupBidContactMobile()) || Objects.nonNull(importDto.getHotelGroupBidContactName())) {

            ProjectIntentHotel projectIntentHotel = new ProjectIntentHotel();
            projectIntentHotel.setProjectIntentHotelId(importDto.getProjectIntentHotelId());
            projectIntentHotel.setBidContactEmail(importDto.getBidContactEmail());
            projectIntentHotel.setBidContactMobile(importDto.getBidContactMobile());
            projectIntentHotel.setBidContactName(importDto.getBidContactName());
            projectIntentHotel.setHotelGroupBidContactEmail(importDto.getHotelGroupBidContactEmail());
            projectIntentHotel.setHotelGroupBidContactMobile(importDto.getHotelGroupBidContactMobile());
            projectIntentHotel.setHotelGroupBidContactName(importDto.getHotelGroupBidContactName());
            projectIntentHotel.setModifier(userDTO.getOperator());
            projectIntentHotelDao.updateBidContactInfo(projectIntentHotel);
        }
    }

    /**
     * 更新酒店服务承诺
     */
    private void updateProjectHotelBidStrategy(UserDTO userDTO, ImportProjectBidContactInfoAndCommitmentDto importDto) {
        // 任意一个不为空才更新
        if (Objects.nonNull(importDto.getSupportPayAtHotel()) || Objects.nonNull(importDto.getSupportCoPay()) ||
            Objects.nonNull(importDto.getSupportNoGuarantee()) || Objects.nonNull(importDto.getSupportCheckinInfo()) ||
            Objects.nonNull(importDto.getSupportPayEarlyCheckout()) || Objects.nonNull(importDto.getSupportWifi()) ||
            Objects.nonNull(importDto.getSupportIncludeTaxService())) {

            ProjectHotelBidStrategy projectHotelBidStrategy = new ProjectHotelBidStrategy();
            projectHotelBidStrategy.setProjectIntentHotelId(importDto.getProjectIntentHotelId());
            projectHotelBidStrategy.setSupportPayAtHotel(importDto.getSupportPayAtHotel());
            projectHotelBidStrategy.setSupportCoPay(importDto.getSupportCoPay());
            projectHotelBidStrategy.setSupportNoGuarantee(importDto.getSupportNoGuarantee());
            projectHotelBidStrategy.setSupportCheckinInfo(importDto.getSupportCheckinInfo());
            projectHotelBidStrategy.setSupportPayEarlyCheckout(importDto.getSupportPayEarlyCheckout());
            projectHotelBidStrategy.setSupportWifi(importDto.getSupportWifi());
            projectHotelBidStrategy.setSupportIncludeTaxService(importDto.getSupportIncludeTaxService());
            projectHotelBidStrategy.setModifier(userDTO.getOperator());
            projectHotelBidStrategyDao.updateByPrimaryKeySelective(projectHotelBidStrategy);
        }
    }

    /**
     * 校验
     */
    private Response validateProjectBidContactInfoAndCommitment(MultipartFile uploadFile, Long projectId, UserDTO userDTO,
                                                                Map<Long, ImportProjectBidContactInfoAndCommitmentDto> importHotelDataMap) throws IOException, InvalidFormatException {
        // 校验项目是否存在
        Project project = projectDao.selectByPrimaryKey(projectId);
        if (project == null) {
            return new Response(ReturnResultEnum.FAILED.errorNo, "项目信息不存在");
        }

        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());
        // 校验行数
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        logger.info("文件行数 : {}", rowSize);
        if (rowSize <= 2) {
            return new Response(ReturnResultEnum.FAILED.errorNo, "导入数据为空");
        }
        if (rowSize > 10002) {
            return new Response(ReturnResultEnum.FAILED.errorNo, "最多上传 10000 条数据");
        }
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            return new Response(ReturnResultEnum.FAILED.errorNo, "上传文件为空");
        }

        // 校验表头
        for (int k = 0; k < ImportProjectBidContactInfoAndPromiseEnum.values().length; k++) {
            Cell cell = titleRow.getCell(k);
            String headName = cell.getStringCellValue();
            Integer keyByValue = ImportProjectBidContactInfoAndPromiseEnum.getKeyByValue(headName);
            if (keyByValue == null || keyByValue != k) {
                return new Response(ReturnResultEnum.FAILED.errorNo, "EXCEL标题不符合规范，请下载批量更新联系信息及承诺信息导入模板");
            }
        }

        // 批量查询项目的有效报价(除了未报价和放弃报价, 其他都是有效的报价状态)
        ArrayList<Integer> validateBidStates = CollUtil.newArrayList(
            HotelBidStateEnum.NEW_BID.bidState,
            HotelBidStateEnum.UNDER_NEGOTIATION.bidState,
            HotelBidStateEnum.BID_WINNING.bidState,
            HotelBidStateEnum.REJECTED.bidState,
            HotelBidStateEnum.UPDATED_BID.bidState,
            HotelBidStateEnum.REJECT_NEGOTIATION.bidState);
        List<ProjectIntentHotel> validBidList = projectIntentHotelDao.selectByProjectIdAndBidStates(projectId, validateBidStates);
        Map<Long, ProjectIntentHotel> validBidMap = validBidList.stream().collect(Collectors.toMap(ProjectIntentHotel::getHotelId, item -> item));

        // 查询项目自定义导购策略
        List<QueryCustomTendStrategyResponse> customTendStrategyResponseList = projectService.queryProjectCustomTendStrategy(projectId);
        Map<Integer, QueryCustomTendStrategyResponse> customTendStrategyMap = new HashMap<>(customTendStrategyResponseList.size());
        for (int i = 0; i < customTendStrategyResponseList.size(); i++) {
            customTendStrategyMap.put(i, customTendStrategyResponseList.get(i));
        }

        // 按行处理
        List<String> invalidMsgList = new ArrayList<>();
        for (int i = 2; i < rowSize; i++) {
            int rowNum = i + 1;
            Row dataRow = sheet.getRow(i);
            ImportProjectBidContactInfoAndCommitmentDto importDto = new ImportProjectBidContactInfoAndCommitmentDto();
            try {
                if (dataRow == null) {
                    continue;
                }
                // 校验酒店 id
                String hotelId = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.FCPROPCODE.getKey()));
                if (!StringUtil.isValidString(hotelId)) {
                    invalidMsgList.add("第" + rowNum + "行房仓酒店ID为空");
                    continue;
                }

                // 已处理过的酒店, 报错
                if (importHotelDataMap.containsKey(Long.valueOf(hotelId))) {
                    invalidMsgList.add("第" + rowNum + "行房仓酒店ID重复");
                    continue;
                }
                importDto.setHotelId(Long.valueOf(hotelId));

                // 校验报价是否存在
                if (!validBidMap.containsKey(Long.valueOf(hotelId))) {
                    invalidMsgList.add(String.format("第%s行酒店ID未匹配到有效报价", rowNum));
                    continue;
                }
                importDto.setProjectIntentHotelId(validBidMap.get(Long.valueOf(hotelId)).getProjectIntentHotelId());

                // 设置酒店联系人信息
                importDto.setBidContactName(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.SALES_NAME.getKey())));
                importDto.setBidContactMobile(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.SALES_MOBILE.getKey())));
                importDto.setBidContactEmail(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.PROPSALESGENREMAIL.getKey())));
                importDto.setHotelGroupBidContactName(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_GROUP_SALES_NAME.getKey())));
                importDto.setHotelGroupBidContactEmail(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_GROUP_PROPSALESGENREMAIL.getKey())));
                importDto.setHotelGroupBidContactMobile(CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_GROUP_SALES_MOBILE.getKey())));

                // 校验服务承诺, 总共 7 个
                // 1. 酒店是否须支持员工到店付款
                String supportPayAtHotelStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_1.key));
                if (StringUtil.isValidString(supportPayAtHotelStr)) {
                    if (!ValidateUtil.isYOrN(supportPayAtHotelStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportPayAtHotel(CommonUtil.convertYNToNumber(supportPayAtHotelStr));
                }

                // 2. 酒店是否须支持公司统一支付
                String supportCoPayStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_2.key));
                if (StringUtil.isValidString(supportCoPayStr)) {
                    if (!ValidateUtil.isYOrN(supportCoPayStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportCoPay(CommonUtil.convertYNToNumber(supportCoPayStr));
                }

                // 3. 酒店是否须支持到店付免担保
                String supportNoGuaranteeStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_3.key));
                if (StringUtil.isValidString(supportNoGuaranteeStr)) {
                    if (!ValidateUtil.isYOrN(supportNoGuaranteeStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportNoGuarantee(CommonUtil.convertYNToNumber(supportNoGuaranteeStr));
                }

                // 4. 酒店是否须支持提供入住明细信息
                String supportCheckinInfoStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_4.key));
                if (StringUtil.isValidString(supportCheckinInfoStr)) {
                    if (!ValidateUtil.isYOrN(supportCheckinInfoStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportCheckinInfo(CommonUtil.convertYNToNumber(supportCheckinInfoStr));
                }

                // 5. 酒店是否须支持提前离店按实际入住金额收款
                String supportPayEarlyCheckoutStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_5.key));
                if (StringUtil.isValidString(supportPayEarlyCheckoutStr)) {
                    if (!ValidateUtil.isYOrN(supportPayEarlyCheckoutStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportPayEarlyCheckout(CommonUtil.convertYNToNumber(supportPayEarlyCheckoutStr));
                }

                // 6. 酒店房间是否提供免费 WIFI 服务
                String supportWifiStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_6.key));
                if (StringUtil.isValidString(supportWifiStr)) {
                    if (!ValidateUtil.isYOrN(supportWifiStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportWifi(CommonUtil.convertYNToNumber(supportWifiStr));
                }

                // 7. 酒店报价是否须包含税费和服务费
                String supportIncludeTaxServiceStr = CommonUtil.getCellValue(dataRow.getCell(ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_7.key));
                if (StringUtil.isValidString(supportIncludeTaxServiceStr)) {
                    if (!ValidateUtil.isYOrN(supportIncludeTaxServiceStr)) {
                        invalidMsgList.add(String.format("第%s行酒店服务承诺数据填写格式异常", rowNum));
                        continue;
                    }
                    importDto.setSupportIncludeTaxService(CommonUtil.convertYNToNumber(supportIncludeTaxServiceStr));
                }

                // 8 - 20 都需要为空
                boolean validateOtherHotelServiceCommitmentRes = this.validateOtherHotelServiceCommitment(dataRow);
                if (!validateOtherHotelServiceCommitmentRes) {
                    invalidMsgList.add(String.format("第%s行酒店服务承诺数据未对应到酒店服务承诺选项", rowNum));
                    continue;
                }

                // 校验自定义采购策略
                int k = 0;
                List<String> customStrategyErrorMsg = new ArrayList<>();
                List<ProjectCustomBidStrategy> projectCustomBidStrategies = new ArrayList<>();
                for (int j = ImportProjectBidContactInfoAndPromiseEnum.USERDEFINED1.getKey(); j <= ImportProjectBidContactInfoAndPromiseEnum.USERDEFINED100.getKey(); j++) {
                    String customStrategyStr = CommonUtil.getCellValue(dataRow.getCell(j));
                    if (StringUtil.isValidString(customStrategyStr) && !customTendStrategyMap.containsKey(k)) {
                        customStrategyErrorMsg.add(String.format("%s填写的自定义问题答案没有对应的问题", ImportProjectBidContactInfoAndPromiseEnum.getValueByKey(j)));
                        continue;
                    }
                    if (!StringUtil.isValidString(customStrategyStr)) {
                        continue;
                    }

                    QueryCustomTendStrategyResponse customTendStrategyResponse = customTendStrategyMap.get(k);
                    ProjectCustomBidStrategy projectCustomBidStrategy = new ProjectCustomBidStrategy();
                    projectCustomBidStrategy.setSupportStrategyName(customTendStrategyResponse.getSupportStrategyName());
                    if (CustomStrategyTypeEnum.isOptionType(customTendStrategyResponse.getStrategyType())) {
                        // 这里不需要每次都生成
                        Set<String> optionNames = customTendStrategyResponse.getOptions().stream().map(CustomStrategyOptionVO::getOptionName).collect(Collectors.toSet());
                        // 按分号分割
                        Set<String> importOptions = Arrays.stream(customStrategyStr.split("[;；]")).collect(Collectors.toSet());
                        // 校验导入的选项是否都存在
                        if (!optionNames.containsAll(importOptions)) {
                            customStrategyErrorMsg.add(String.format("%s填写的选项不存在", ImportProjectBidContactInfoAndPromiseEnum.getValueByKey(j)));
                            continue;
                        }
                        if (CustomStrategyTypeEnum.RADIO.key == customTendStrategyResponse.getStrategyType() && importOptions.size() > 1) {
                            customStrategyErrorMsg.add(String.format("%s填写的选项为单选，只能选择一个", ImportProjectBidContactInfoAndPromiseEnum.getValueByKey(j)));
                            continue;
                        }

                        // 设置选项请求
                        List<ProjectCustomBidStrategyOption> projectCustomBidStrategyOptions = new ArrayList<>();
                        customTendStrategyResponse.getOptions().forEach(option -> {
                            ProjectCustomBidStrategyOption projectCustomBidStrategyOption = new ProjectCustomBidStrategyOption();
                            projectCustomBidStrategyOption.setProjectId(projectId);
                            projectCustomBidStrategyOption.setHotelId(importDto.getHotelId());
                            projectCustomBidStrategyOption.setProjectIntentHotelId(importDto.getProjectIntentHotelId());
                            projectCustomBidStrategyOption.setCustomTendStrategyId(option.getStrategyId());
                            projectCustomBidStrategyOption.setOptionId(option.getOptionId());
                            projectCustomBidStrategyOption.setOptionName(option.getOptionName());
                            projectCustomBidStrategyOption.setIsSupport(importOptions.contains(option.getOptionName()) ? RfpConstant.constant_1 : RfpConstant.constant_0);
                            projectCustomBidStrategyOption.setCreator(userDTO.getOperator());
                            projectCustomBidStrategyOption.setModifier(userDTO.getModifier());
                            projectCustomBidStrategyOptions.add(projectCustomBidStrategyOption);
                        });
                        projectCustomBidStrategy.setOptions(projectCustomBidStrategyOptions);
                    } else if (CustomStrategyTypeEnum.YSE_OR_NO.key == customTendStrategyResponse.getStrategyType()) {
                        if (!ValidateUtil.isYOrN(customStrategyStr)) {
                            customStrategyErrorMsg.add(String.format("%s填写的选项格式不正确", ImportProjectBidContactInfoAndPromiseEnum.getValueByKey(j)));
                            continue;
                        }
                        projectCustomBidStrategy.setSupportStrategyName(CommonUtil.convertYNToNumber(customStrategyStr));
                    } else if (CustomStrategyTypeEnum.TEXT.key == customTendStrategyResponse.getStrategyType()) {
                        if (customStrategyStr.length() > 512) {
                            customStrategyErrorMsg.add(String.format("%s填写的选项长度不能超过512个字符", ImportProjectBidContactInfoAndPromiseEnum.getValueByKey(j)));
                            continue;
                        }
                        // 设置文本内容
                        projectCustomBidStrategy.setSupportStrategyText(customStrategyStr);
                    }
                    projectCustomBidStrategy.setProjectId(projectId);
                    projectCustomBidStrategy.setHotelId(importDto.getHotelId());
                    projectCustomBidStrategy.setProjectIntentHotelId(importDto.getProjectIntentHotelId());
                    projectCustomBidStrategy.setStrategyName(customTendStrategyResponse.getStrategyName());
                    projectCustomBidStrategy.setCustomTendStrategyId(customTendStrategyResponse.getCustomTendStrategyId());
                    projectCustomBidStrategy.setStrategyType(customTendStrategyResponse.getStrategyType());
                    projectCustomBidStrategy.setCreator(userDTO.getOperator());
                    projectCustomBidStrategy.setModifier(userDTO.getOperator());
                    projectCustomBidStrategies.add(projectCustomBidStrategy);

                    k++;
                }
                importDto.setProjectCustomBidStrategies(projectCustomBidStrategies);

                // 存在错误信息, 跳过
                if (!customStrategyErrorMsg.isEmpty()) {
                    invalidMsgList.add(String.format("第%s行自定义采购策略数据填写异常：%s", rowNum, String.join(",", customStrategyErrorMsg)));
                    continue;
                }

                importHotelDataMap.put(importDto.getHotelId(), importDto);
            } catch (Throwable e) {
                logger.error("处理第 {} 行数据时发生异常", rowNum, e);
                invalidMsgList.add("第" + rowNum + "行数据处理异常");
            }
        }

        // 校验不通过, 先返回
        if (!invalidMsgList.isEmpty()) {
            logger.error("检查失败信息：{}", JSON.toJSONString(invalidMsgList));
            Response response = new Response();
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setData(invalidMsgList);
            return response;
        }

        // 返回成功
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    /**
     * 校验其他酒店服务承诺
     */
    private boolean validateOtherHotelServiceCommitment(Row dataRow) {
        // 遍历查询 8-20 酒店服务选项是否都为空
        for (int k = ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_8.key; k <= ImportProjectBidContactInfoAndPromiseEnum.HOTEL_SERVICE_COMMITMENT_20.key; k++) {
            String otherHotelServiceCommitment = CommonUtil.getCellValue(dataRow.getCell(k));
            if (StringUtil.isValidString(otherHotelServiceCommitment)) {
                return false;
            }
        }
        return true;
    }


    private void setExcelData(List<QueryPerformanceInfoResponse> list, Sheet sheet) {
        int index = 0;
        for (QueryPerformanceInfoResponse data : list) {
            index++;
            Row dataRow = sheet.createRow(sheet.getLastRowNum() + 1);
            dataRow.createCell(0).setCellValue(index);
            dataRow.createCell(1).setCellValue(data.getCityName());
            dataRow.createCell(2).setCellValue(data.getProjectName());
            dataRow.createCell(3).setCellValue(data.getHotelName());
            dataRow.createCell(4).setCellValue(data.getHotelGroupName());
            dataRow.createCell(5).setCellValue(data.getBrandName());
            dataRow.createCell(6).setCellValue(data.getTotalProcurementVolume());
            dataRow.createCell(7).setCellValue(data.getHotelServicePoints().toString());
            dataRow.createCell(8).setCellValue(data.getViolationsCount());
            dataRow.createCell(9).setCellValue(DateUtil.dateToString(data.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
    }

    private String getYN(Integer booleanInteger){
        if(booleanInteger == null || booleanInteger == 0){
            return "N";
        }
        return "Y";
    }

    /**
     *  判断是否为同档房
     * **/
    private boolean isTheSameLevel(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse, BigDecimal baseAvgPrice){
        BigDecimal avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP);
        if(baseAvgPrice.compareTo(BigDecimal.ZERO) >0 && Math.abs(baseAvgPrice.subtract(avgPrice).intValue()) <= 50){
            return  true;
        }
        return false;
    }




}


