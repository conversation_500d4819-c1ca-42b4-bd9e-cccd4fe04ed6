package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.util.ConfigUtil;
import com.fangcang.rfp.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName RedisService
 * @Description redis服务，db3
 */
public class RedisService {

    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

//    private static JedisSentinelPool jedisPool = init();

    private static JedisPool jedisPool = init();

    private RedisService() {

    }

    public static Jedis getResource() {
        return jedisPool.getResource();
    }

    public static String get(String key) throws Exception {
        String value = null;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            value = jedis.get(key);
        } catch (Exception e) {
            logger.error("get error.", e);
            throw new RuntimeException("RedisService get error");
        } finally {
            close(jedis);
        }
        return value;
    }

    public static String set(String key, String value) throws Exception {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.set(key, value);
        } catch (Exception e) {
            logger.error("set error.", e);
            throw new RuntimeException("RedisService set error");
        } finally {
            close(jedis);
        }
    }

    public static Long del(String... keys) throws Exception {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            return jedis.del(keys);
        } catch (Exception e) {
            logger.error("del error.", e);
            throw new RuntimeException("RedisService del error");
        } finally {
            close(jedis);
        }
    }

    /**
     * 通过key向指定的value值追加值
     *
     * @param key
     * @param str
     * @return 成功返回 添加后value的长度 失败 返回 添加的 value 的长度 异常返回0L
     */
    public static Long append(String key, String str) throws Exception {
        Long res = null;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.append(key, str);
        } catch (Exception e) {
            logger.error("append error", e);
            throw new RuntimeException("RedisService append error");
        } finally {
            close(jedis);
        }
        return 0L;
    }

    /**
     * 判断key是否存在
     *
     * @param key
     * @return true OR false
     */
    public static Boolean exists(String key) throws Exception {
        Jedis jedis = null;
        Boolean exists = false;
        try {
            jedis = jedisPool.getResource();
            exists = jedis.exists(key);
        } catch (Exception e) {
            logger.error("exists error", e);
            throw new RuntimeException("RedisService exists error");
        } finally {
            close(jedis);
        }
        return exists;
    }

    /**
     * 设置key value,如果key已经存在则返回0,nx==> not exist
     *
     * @param key
     * @param value
     * @return 成功返回1 如果存在返回 0 发生异常返回-1
     */
    public static Long setnx(String key, String value) throws Exception {
        Jedis jedis = null;
        Long result = 0L;
        try {
            jedis = jedisPool.getResource();
            result = jedis.setnx(key, value);
        } catch (Exception e) {
            logger.error("setnx error", e);
            result = -1L;
            throw new RuntimeException("RedisService exists error");
        } finally {
            close(jedis);
        }
        return result;
    }

    /**
     * 设置key value并制定这个键值的有效期
     *
     * @param key
     * @param value
     * @param seconds 单位:秒
     * @return 成功返回OK 失败和异常返回null
     */
    public static String setex(String key, String value, int seconds) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.setex(key, seconds, value);
        } catch (Exception e) {
            logger.error("setex error", e);
            throw new RuntimeException("RedisService setex error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 设置key这个键值的有效期
     *
     * @param key
     * @param seconds 单位:秒
     * @return 成功返回1 失败和异常返回0
     */
    public static Long expire(String key, int seconds) throws Exception {
        Long res = 0L;
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.expire(key, seconds);
        } catch (Exception e) {
            logger.error("expire error", e);
            throw new RuntimeException("RedisService expire error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key 和offset 从指定的位置开始将原先value替换 下标从0开始,offset表示从offset下标开始替换
     * 如果替换的字符串长度过小则会这样 example: value : <EMAIL> str : abc 从下标7开始替换 则结果为
     * RES : bigsea.abc.cn
     *
     * @param key
     * @param str
     * @param offset 下标位置
     * @return 返回替换后 value 的长度
     */
    public static Long setrange(String key, String str, int offset) throws Exception {
        Jedis jedis = null;
        Long result = 0L;
        try {
            jedis = jedisPool.getResource();
            result = jedis.setrange(key, offset, str);
        } catch (Exception e) {
            logger.error("setrange error", e);
            throw new RuntimeException("RedisService setrange error");
        } finally {
            close(jedis);
        }
        return result;
    }

    /**
     * 通过批量的key获取批量的value
     *
     * @param keys string数组 也可以是一个key
     * @return 成功返回value的集合, 失败返回null的集合 ,异常返回空
     */
    public static List<String> mget(String... keys) throws Exception {
        Jedis jedis = null;
        List<String> values = null;
        try {
            jedis = jedisPool.getResource();
            values = jedis.mget(keys);
        } catch (Exception e) {
            logger.error("mget error", e);
            throw new RuntimeException("RedisService mget error");
        } finally {
            close(jedis);
        }
        return values;
    }

    /**
     * 批量的设置key:value,可以一个 example: obj.mset(new
     * String[]{"key2","value1","key2","value2"})
     *
     * @param keysvalues
     * @return 成功返回OK 失败 异常 返回 null
     */
    public static String mset(String... keysvalues) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.mset(keysvalues);
        } catch (Exception e) {
            logger.error("mset error", e);
            throw new RuntimeException("RedisService mset error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 批量的设置key:value,可以一个,如果key已经存在则会失败,操作会回滚 example: obj.msetnx(new
     * String[]{"key2","value1","key2","value2"})
     *
     * @param keysvalues
     * @return 成功返回1 失败返回0
     */
    public static Long msetnx(String... keysvalues) throws Exception {
        Jedis jedis = null;
        Long res = 0L;
        try {
            jedis = jedisPool.getResource();
            res = jedis.msetnx(keysvalues);
        } catch (Exception e) {
            logger.error("msetnx error", e);
            throw new RuntimeException("RedisService msetnx error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 设置key的值,并返回一个旧值
     *
     * @param key
     * @param value
     * @return 旧值 如果key不存在 则返回null
     */
    public static String getset(String key, String value) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.getSet(key, value);
        } catch (Exception e) {
            logger.error("getset error", e);
            throw new RuntimeException("RedisService getset error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过下标 和key 获取指定下标位置的 value
     *
     * @param key
     * @param startOffset 开始位置 从0 开始 负数表示从右边开始截取
     * @param endOffset
     * @return 如果没有返回null
     */
    public static String getrange(String key, int startOffset, int endOffset) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.getrange(key, startOffset, endOffset);
        } catch (Exception e) {
            logger.error("getrange error", e);
            throw new RuntimeException("RedisService getrange error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key 对value进行加值+1操作,当value不是int类型时会返回错误,当key不存在是则value为1
     *
     * @param key
     * @return 加值后的结果
     */
    public static Long incr(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.incr(key);
        } catch (Exception e) {
            logger.error("incr error", e);
            throw new RuntimeException("RedisService incr error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key给指定的value加值,如果key不存在,则这是value为该值
     *
     * @param key
     * @param integer
     * @return
     */
    public static Long incrBy(String key, Long integer) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.incrBy(key, integer);
        } catch (Exception e) {
            logger.error("incrBy error", e);
            throw new RuntimeException("RedisService incrBy error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 对key的值做减减操作,如果key不存在,则设置key为-1
     *
     * @param key
     * @return
     */
    public static Long decr(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.decr(key);
        } catch (Exception e) {
            logger.error("decr error", e);
            throw new RuntimeException("RedisService decr error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 减去指定的值
     *
     * @param key
     * @param integer
     * @return
     */
    public static Long decrBy(String key, Long integer) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.decrBy(key, integer);
        } catch (Exception e) {
            logger.error("decrBy error", e);
            throw new RuntimeException("RedisService decrBy error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key获取value值的长度
     *
     * @param key
     * @return 失败返回null
     */
    public static Long serlen(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.strlen(key);
        } catch (Exception e) {
            logger.error(" serlen error", e);
            throw new RuntimeException("RedisService strlen error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key给field设置指定的值,如果key不存在,则先创建
     *
     * @param key
     * @param field 字段
     * @param value
     * @return 如果存在返回0 异常返回null
     */
    public static Long hset(String key, String field, String value) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hset(key, field, value);
        } catch (Exception e) {
            logger.error(" hset error", e);
            throw new RuntimeException("RedisService hset error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key给field设置指定的值,如果key不存在则先创建,如果field已经存在,返回0
     *
     * @param key
     * @param field
     * @param value
     * @return
     */
    public static Long hsetnx(String key, String field, String value) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hsetnx(key, field, value);
        } catch (Exception e) {
            logger.error(" hsetnx error", e);
            throw new RuntimeException("RedisService hsetnx error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key同时设置 hash的多个field
     *
     * @param key
     * @param hash
     * @return 返回OK 异常返回null
     */
    public static String hmset(String key, Map<String, String> hash) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hmset(key, hash);
        } catch (Exception e) {
            logger.error(" hmset error", e);
            throw new RuntimeException("RedisService hmset error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key 和 field 获取指定的 value
     *
     * @param key
     * @param field
     * @return 没有返回null
     */
    public static String hget(String key, String field) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hget(key, field);
        } catch (Exception e) {
            logger.error(" hget error", e);
            throw new RuntimeException("RedisService hget error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key 和 fields 获取指定的value 如果没有对应的value则返回null
     *
     * @param key
     * @param fields 可以使 一个String 也可以是 String数组
     * @return
     */
    public static List<String> hmget(String key, String... fields) throws Exception {
        Jedis jedis = null;
        List<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hmget(key, fields);
        } catch (Exception e) {
            logger.error(" hmget error", e);
            throw new RuntimeException("RedisService hmget error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key给指定的field的value加上给定的值
     *
     * @param key
     * @param field
     * @param value
     * @return
     */
    public static Long hincrby(String key, String field, Long value) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hincrBy(key, field, value);
        } catch (Exception e) {
            logger.error(" hincrby error", e);
            throw new RuntimeException("RedisService hincrby error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key和field判断是否有指定的value存在
     *
     * @param key
     * @param field
     * @return
     */
    public static Boolean hexists(String key, String field) throws Exception {
        Jedis jedis = null;
        Boolean res = false;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hexists(key, field);
        } catch (Exception e) {
            logger.error(" hexists error", e);
            throw new RuntimeException("RedisService hexists error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回field的数量
     *
     * @param key
     * @return
     */
    public static Long hlen(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hlen(key);
        } catch (Exception e) {
            logger.error("hlen  error", e);
            throw new RuntimeException("RedisService hlen error");
        } finally {
            close(jedis);
        }
        return res;

    }

    /**
     * 通过key 删除指定的 field
     *
     * @param key
     * @param fields 可以是 一个 field 也可以是 一个数组
     * @return
     */
    public static Long hdel(String key, String... fields) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hdel(key, fields);
        } catch (Exception e) {
            logger.error(" hdel error", e);
            throw new RuntimeException("RedisService hdel error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回所有的field
     *
     * @param key
     * @return
     */
    public static Set<String> hkeys(String key) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hkeys(key);
        } catch (Exception e) {
            logger.error(" hkeys error", e);
            throw new RuntimeException("RedisService hkeys error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回所有和key有关的value
     *
     * @param key
     * @return
     */
    public static List<String> hvals(String key) throws Exception {
        Jedis jedis = null;
        List<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hvals(key);
        } catch (Exception e) {
            logger.error(" hvals error", e);
            throw new RuntimeException("RedisService hvals error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key获取所有的field和value
     *
     * @param key
     * @return
     */
    public static Map<String, String> hgetall(String key) throws Exception {
        Jedis jedis = null;
        Map<String, String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.hgetAll(key);
        } catch (Exception e) {
            logger.error(" hgetall error", e);
            throw new RuntimeException("RedisService hgetall error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key向list头部添加字符串
     * </p>
     *
     * @param key
     * @param strs 可以使一个string 也可以使string数组
     * @return 返回list的value个数
     */
    public static Long lpush(String key, String... strs) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lpush(key, strs);
        } catch (Exception e) {
            logger.error(" lpush error", e);
            throw new RuntimeException("RedisService lpush error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key向list尾部添加字符串
     * </p>
     *
     * @param key
     * @param strs 可以使一个string 也可以使string数组
     * @return 返回list的value个数
     */
    public static Long rpush(String key, String... strs) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.rpush(key, strs);
        } catch (Exception e) {
            logger.error(" rpush error", e);
            throw new RuntimeException("RedisService rpush error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key设置list指定下标位置的value
     * </p>
     * <p>
     * 如果下标超过list里面value的个数则报错
     * </p>
     *
     * @param key
     * @param index 从0开始
     * @param value
     * @return 成功返回OK
     */
    public static String lset(String key, Long index, String value) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lset(key, index, value);
        } catch (Exception e) {
            logger.error(" lset error", e);
            throw new RuntimeException("RedisService lset error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key从对应的list中删除指定的count个 和 value相同的元素
     * </p>
     *
     * @param key
     * @param count 当count为0时删除全部
     * @param value
     * @return 返回被删除的个数
     */
    public static Long lrem(String key, long count, String value) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lrem(key, count, value);
        } catch (Exception e) {
            logger.error(" lrem error", e);
            throw new RuntimeException("RedisService lrem error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key保留list中从strat下标开始到end下标结束的value值
     * </p>
     *
     * @param key
     * @param start
     * @param end
     * @return 成功返回OK
     */
    public static String ltrim(String key, long start, long end) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.ltrim(key, start, end);
        } catch (Exception e) {
            logger.error(" ltrim error", e);
            throw new RuntimeException("RedisService ltrim error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key从list的头部删除一个value,并返回该value
     * </p>
     *
     * @param key
     * @return
     */
    public static String lpop(String key) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lpop(key);
        } catch (Exception e) {
            logger.error(" lpop error", e);
            throw new RuntimeException("RedisService lpop error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key从list尾部删除一个value,并返回该元素
     * </p>
     *
     * @param key
     * @return
     */
    public static String rpop(String key) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.rpop(key);
        } catch (Exception e) {
            logger.error(" rpop error", e);
            throw new RuntimeException("RedisService rpop error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key从一个list的尾部删除一个value并添加到另一个list的头部,并返回该value
     * </p>
     * <p>
     * 如果第一个list为空或者不存在则返回null
     * </p>
     *
     * @param srckey
     * @param dstkey
     * @return
     */
    public static String rpoplpush(String srckey, String dstkey) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.rpoplpush(srckey, dstkey);
        } catch (Exception e) {
            logger.error(" rpoplpush error", e);
            throw new RuntimeException("RedisService rpoplpush error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取list中指定下标位置的value
     * </p>
     *
     * @param key
     * @param index
     * @return 如果没有返回null
     */
    public static String lindex(String key, long index) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lindex(key, index);
        } catch (Exception e) {
            logger.error(" lindex error", e);
            throw new RuntimeException("RedisService lindex error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key返回list的长度
     * </p>
     *
     * @param key
     * @return
     */
    public static Long llen(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.llen(key);
        } catch (Exception e) {
            logger.error(" llen error", e);
            throw new RuntimeException("RedisService llen error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取list指定下标位置的value
     * </p>
     * <p>
     * 如果start 为 0 end 为 -1 则返回全部的list中的value
     * </p>
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static List<String> lrange(String key, long start, long end) throws Exception {
        Jedis jedis = null;
        List<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.lrange(key, start, end);
        } catch (Exception e) {
            logger.error(" lrange error", e);
            throw new RuntimeException("RedisService lrange error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key向指定的set中添加value
     * </p>
     *
     * @param key
     * @param members 可以是一个String 也可以是一个String数组
     * @return 添加成功的个数
     */
    public static Long sadd(String key, String... members) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sadd(key, members);
        } catch (Exception e) {
            logger.error(" sadd error", e);
            throw new RuntimeException("RedisService sadd error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key删除set中对应的value值
     * </p>
     *
     * @param key
     * @param members 可以是一个String 也可以是一个String数组
     * @return 删除的个数
     */
    public static Long srem(String key, String... members) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.srem(key, members);
        } catch (Exception e) {
            logger.error(" srem error", e);
            throw new RuntimeException("RedisService srem error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key随机删除一个set中的value并返回该值
     * </p>
     *
     * @param key
     * @return
     */
    public static String spop(String key) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.spop(key);
        } catch (Exception e) {
            logger.error(" spop error", e);
            throw new RuntimeException("RedisService spop error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中的差集
     * </p>
     * <p>
     * 以第一个set为标准
     * </p>
     *
     * @param keys 可以使一个string 则返回set中所有的value 也可以是string数组
     * @return
     */
    public static Set<String> sdiff(String... keys) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sdiff(keys);
        } catch (Exception e) {
            logger.error(" sdiff error", e);
            throw new RuntimeException("RedisService sdiff error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中的差集并存入到另一个key中
     * </p>
     * <p>
     * 以第一个set为标准
     * </p>
     *
     * @param dstkey 差集存入的key
     * @param keys   可以使一个string 则返回set中所有的value 也可以是string数组
     * @return
     */
    public static Long sdiffstore(String dstkey, String... keys) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sdiffstore(dstkey, keys);
        } catch (Exception e) {
            logger.error(" sdiffstore error", e);
            throw new RuntimeException("RedisService sdiffstore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取指定set中的交集
     * </p>
     *
     * @param keys 可以使一个string 也可以是一个string数组
     * @return
     */
    public static Set<String> sinter(String... keys) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sinter(keys);
        } catch (Exception e) {
            logger.error(" sinter error", e);
            throw new RuntimeException("RedisService sinter error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取指定set中的交集 并将结果存入新的set中
     * </p>
     *
     * @param dstkey
     * @param keys   可以使一个string 也可以是一个string数组
     * @return
     */
    public static Long sinterstore(String dstkey, String... keys) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sinterstore(dstkey, keys);
        } catch (Exception e) {
            logger.error(" sinterstore error", e);
            throw new RuntimeException("RedisService sinterstore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key返回所有set的并集
     * </p>
     *
     * @param keys 可以使一个string 也可以是一个string数组
     * @return
     */
    public static Set<String> sunion(String... keys) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sunion(keys);
        } catch (Exception e) {
            logger.error(" sunion error", e);
            throw new RuntimeException("RedisService sunion error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key返回所有set的并集,并存入到新的set中
     * </p>
     *
     * @param dstkey
     * @param keys   可以使一个string 也可以是一个string数组
     * @return
     */
    public static Long sunionstore(String dstkey, String... keys) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sunionstore(dstkey, keys);
        } catch (Exception e) {
            logger.error(" sunionstore error", e);
            throw new RuntimeException("RedisService sunionstore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key将set中的value移除并添加到第二个set中
     * </p>
     *
     * @param srckey 需要移除的
     * @param dstkey 添加的
     * @param member set中的value
     * @return
     */
    public static Long smove(String srckey, String dstkey, String member) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.smove(srckey, dstkey, member);
        } catch (Exception e) {
            logger.error(" smove error", e);
            throw new RuntimeException("RedisService smove error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中value的个数
     * </p>
     *
     * @param key
     * @return
     */
    public static Long scard(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.scard(key);
        } catch (Exception e) {
            logger.error(" scard error", e);
            throw new RuntimeException("RedisService scard error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key判断value是否是set中的元素
     * </p>
     *
     * @param key
     * @param member
     * @return
     */
    public static Boolean sismember(String key, String member) throws Exception {
        Jedis jedis = null;
        Boolean res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.sismember(key, member);
        } catch (Exception e) {
            logger.error(" sismember error", e);
            throw new RuntimeException("RedisService sismember error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中随机的value,不删除元素
     * </p>
     *
     * @param key
     * @return
     */
    public static String srandmember(String key) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.srandmember(key);
        } catch (Exception e) {
            logger.error(" srandmember error", e);
            throw new RuntimeException("RedisService srandmember error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中指定数量的value,不删除元素
     * </p>
     *
     * @param key
     * @return
     */
    public static List<String> srandmember(String key, int count) throws Exception {
        Jedis jedis = null;
        List<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.srandmember(key, count);
        } catch (Exception e) {
            logger.error(" srandmember error", e);
            throw new RuntimeException("RedisService srandmember error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * <p>
     * 通过key获取set中所有的value
     * </p>
     *
     * @param key
     * @return
     */
    public static Set<String> smembers(String key) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.smembers(key);
        } catch (Exception e) {
            logger.error(" smembers error", e);
            throw new RuntimeException("RedisService smembers error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key向zset中添加value,score,其中score就是用来排序的 如果该value已经存在则根据score更新元素
     *
     * @param key
     * @param scoreMembers
     * @return
     */
    public static Long zadd(String key, Map<String, Double> scoreMembers) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zadd(key, scoreMembers);
        } catch (Exception e) {
            logger.error(" zadd error", e);
            throw new RuntimeException("RedisService zadd error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key向zset中添加value,score,其中score就是用来排序的 如果该value已经存在则根据score更新元素
     *
     * @param key
     * @param score
     * @param member
     * @return
     */
    public static Long zadd(String key, double score, String member) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zadd(key, score, member);
        } catch (Exception e) {
            logger.error(" zadd error", e);
            throw new RuntimeException("RedisService zadd error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key删除在zset中指定的value
     *
     * @param key
     * @param members 可以使一个string 也可以是一个string数组
     * @return
     */
    public static Long zrem(String key, String... members) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrem(key, members);
        } catch (Exception e) {
            logger.error(" zrem error", e);
            throw new RuntimeException("RedisService zrem error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key增加该zset中value的score的值
     *
     * @param key
     * @param score
     * @param member
     * @return
     */
    public static Double zincrby(String key, double score, String member) throws Exception {
        Jedis jedis = null;
        Double res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zincrby(key, score, member);
        } catch (Exception e) {
            logger.error(" zincrby error", e);
            throw new RuntimeException("RedisService zincrby error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回zset中value的排名 下标从小到大排序
     *
     * @param key
     * @param member
     * @return
     */
    public static Long zrank(String key, String member) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrank(key, member);
        } catch (Exception e) {
            logger.error(" zrank error", e);
            throw new RuntimeException("RedisService zrank error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回zset中value的排名 下标从大到小排序
     *
     * @param key
     * @param member
     * @return
     */
    public static Long zrevrank(String key, String member) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrevrank(key, member);
        } catch (Exception e) {
            logger.error(" zrevrank error", e);
            throw new RuntimeException("RedisService zrevrank error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key将获取score从start到end中zset的value socre从大到小排序 当start为0 end为-1时返回全部
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Set<String> zrevrange(String key, long start, long end) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrevrange(key, start, end);
        } catch (Exception e) {
            logger.error(" zrevrange error", e);
            throw new RuntimeException("RedisService zrevrange error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回指定score内zset中的value
     *
     * @param key
     * @param max
     * @param min
     * @return
     */
    public static Set<String> zrangebyscore(String key, String max, String min) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrevrangeByScore(key, max, min);
        } catch (Exception e) {
            logger.error(" zrangebyscore error", e);
            throw new RuntimeException("RedisService zrevrangeByScore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回指定score内zset中的value
     *
     * @param key
     * @param max
     * @param min
     * @return
     */
    public static Set<String> zrangeByScore(String key, double max, double min) throws Exception {
        Jedis jedis = null;
        Set<String> res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zrevrangeByScore(key, max, min);
        } catch (Exception e) {
            logger.error(" zrangeByScore error", e);
            throw new RuntimeException("RedisService zrevrangeByScore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 返回指定区间内zset中value的数量
     *
     * @param key
     * @param min
     * @param max
     * @return
     */
    public static Long zcount(String key, String min, String max) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zcount(key, min, max);
        } catch (Exception e) {
            logger.error(" zcount error", e);
            throw new RuntimeException("RedisService zcount error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key返回zset中的value个数
     *
     * @param key
     * @return
     */
    public static Long zcard(String key) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zcard(key);
        } catch (Exception e) {
            logger.error(" zcard error", e);
            throw new RuntimeException("RedisService zcard error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key获取zset中value的score值
     *
     * @param key
     * @param member
     * @return
     */
    public static Double zscore(String key, String member) throws Exception {
        Jedis jedis = null;
        Double res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zscore(key, member);
        } catch (Exception e) {
            logger.error(" zscore error", e);
            throw new RuntimeException("RedisService zscore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key删除给定区间内的元素
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Long zremrangeByRank(String key, long start, long end) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zremrangeByRank(key, start, end);
        } catch (Exception e) {
            logger.error(" zremrangeByRank error", e);
            throw new RuntimeException("RedisService zremrangeByRank error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key删除指定score内的元素
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public static Long zremrangeByScore(String key, double start, double end) throws Exception {
        Jedis jedis = null;
        Long res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.zremrangeByScore(key, start, end);
        } catch (Exception e) {
            logger.error(" zremrangeByScore error", e);
            throw new RuntimeException("RedisService zremrangeByScore error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 通过key判断值得类型
     *
     * @param key
     * @return
     */
    public static String type(String key) throws Exception {
        Jedis jedis = null;
        String res = null;
        try {
            jedis = jedisPool.getResource();
            res = jedis.type(key);
        } catch (Exception e) {
            logger.error(" type error", e);
            throw new RuntimeException("RedisService type error");
        } finally {
            close(jedis);
        }
        return res;
    }

    /**
     * 关闭jedis
     *
     * @param jedis
     */
    public static void close(Jedis jedis) {
        if (null != jedis) {
            try {
                jedis.close();
            } catch (Exception e) {
                logger.error(" jedis close error ", e);
            }
        }
    }

//    public static JedisSentinelPool init() {
//        JedisSentinelPool jedisPool = null;
//        try {
//            ConfigUtil.loadConfig("redis-config.properties");
//            JedisPoolConfig poolConfig = new JedisPoolConfig();
//            // 控制一个pool可分配多少个jedis实例，通过pool.getResource()来获取；
//            // 如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)。
//            poolConfig.setMaxTotal(Integer.parseInt(ConfigUtil.getConfig("redis.maxTotal")));
//            // 控制一个pool最多有多少个状态为idle(空闲的)的jedis实例。
//            poolConfig.setMaxIdle(Integer.parseInt(ConfigUtil.getConfig("redis.maxIdle")));
//            // 表示当borrow(引入)一个jedis实例时，最大的等待时间，如果超过等待时间，则直接抛出JedisConnectio
//            // nException；
//            poolConfig.setMaxWaitMillis(Integer.parseInt(ConfigUtil.getConfig("redis.maxWaitMillis")));
//            // 在borrow一个jedis实例时，是否提前进行validate操作；如果为true，则得到的jedis实例均是可用的；
//            poolConfig.setTestOnBorrow(Boolean.parseBoolean(ConfigUtil.getConfig("redis.testOnBorrow")));
//            Set<String> sentinels = new HashSet<String>();
//            String[] servers = ConfigUtil.getConfig("redis.hostAndPort").split("\\|");
//            for (String server : servers) {
//                sentinels.add(server);
//            }
//            String masterName = StringUtils.isBlank(ConfigUtil.getConfig("redis.masterName")) ? "mymaster" : ConfigUtil.getConfig("redis.masterName");
//            String passWord = StringUtils.isBlank(ConfigUtil.getConfig("redis.password")) ? null : ConfigUtil.getConfig("redis.password");
//            int redisDb = StringUtils.isBlank(ConfigUtil.getConfig("redis.redisDb")) ? RedisConstant.DB_INDEX : Integer.parseInt(ConfigUtil.getConfig("redis.redisDb"));
//            jedisPool = new JedisSentinelPool(masterName, sentinels, poolConfig, Integer.parseInt(ConfigUtil.getConfig("redis.timeout")), passWord, redisDb);
//            logger.info("init redis service success.master:" + jedisPool.getCurrentHostMaster().getHost() + ":" + jedisPool.getCurrentHostMaster().getPort());
//        } catch (Exception e) {
//            logger.error("init redis service failure.", e);
//        }
//        return jedisPool;
//    }
//高版本redis
public static JedisPool init() {
    JedisPool jedisPool = null;
    try {
        ConfigUtil.loadConfig("redis-config.properties");
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        // 控制一个pool可分配多少个jedis实例，通过pool.getResource()来获取；
        // 如果赋值为-1，则表示不限制；如果pool已经分配了maxActive个jedis实例，则此时pool的状态为exhausted(耗尽)。
        poolConfig.setMaxTotal(Integer.parseInt(ConfigUtil.getConfig("redis.maxTotal")));
        // 控制一个pool最多有多少个状态为idle(空闲的)的jedis实例。
        poolConfig.setMaxIdle(Integer.parseInt(ConfigUtil.getConfig("redis.maxIdle")));
        // 表示当borrow(引入)一个jedis实例时，最大的等待时间，如果超过等待时间，则直接抛出JedisConnectio
        // nException；
        poolConfig.setMaxWaitMillis(Integer.parseInt(ConfigUtil.getConfig("redis.maxWaitMillis")));
        // 在borrow一个jedis实例时，是否提前进行validate操作；如果为true，则得到的jedis实例均是可用的；
        poolConfig.setTestOnBorrow(Boolean.parseBoolean(ConfigUtil.getConfig("redis.testOnBorrow")));

        int redisDb = StringUtils.isBlank(ConfigUtil.getConfig("redis.redisDb")) ? RedisConstant.DB_INDEX : Integer.parseInt(ConfigUtil.getConfig("redis.redisDb"));
        String passWord = StringUtils.isBlank(ConfigUtil.getConfig("redis.passwordVip")) ? null : ConfigUtil.getConfig("redis.passwordVip");
        jedisPool = new JedisPool(poolConfig, ConfigUtil.getConfig("redis.hostVip"), Integer.parseInt(ConfigUtil.getConfig("redis.portVip")), Integer.parseInt(ConfigUtil.getConfig("redis.timeout")), passWord, redisDb);
    } catch (Exception e) {
        logger.error("init redis service failure.", e);
    }
    return jedisPool;
}
}
