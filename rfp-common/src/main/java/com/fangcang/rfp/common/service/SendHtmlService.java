package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.response.QueryViolationsDailyPriceResponse;
import com.fangcang.rfp.common.dto.response.QueryViolationsOrderDetailResponse;
import com.fangcang.rfp.common.entity.ProjectIntentHotel;

/**
 * <AUTHOR>
 * @date 2024/1/29 18:09
 */
public interface SendHtmlService {

    /**
     * 发送酒店报价违规信息
     */
    boolean sendhotelPriceViolationsMonitor(QueryViolationsDailyPriceResponse queryViolationsDailyPriceResponse, ProjectIntentHotel projectIntentHotel, boolean isRecheckSend) throws Exception;

    /**
     * 发送酒店订单违规信息
     */
    boolean sendhotelOrderViolationsMonitor(QueryViolationsOrderDetailResponse queryViolationsOrderDetailResponse, ProjectIntentHotel projectIntentHotel) throws Exception;

}
