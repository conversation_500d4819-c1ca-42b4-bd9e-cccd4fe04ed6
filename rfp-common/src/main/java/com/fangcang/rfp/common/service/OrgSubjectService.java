package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.request.OrgSubjectDto;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.OrgSubjectRequest;
import com.fangcang.rfp.common.entity.OrgSubject;

import java.util.List;

/**
 * 机构签约主体业务
 * @auther chenjianhua
 * @date 2022/9/1
*/
public interface OrgSubjectService {

	/**
	 * 分页查询签约主体
	 * @param orgSubjectRequest
	 * @param type 1-分页 2-不分页
	 * @return
	 */
	Response queryOrgSubjectPage(OrgSubjectRequest orgSubjectRequest,Integer type) ;


	/**
	 * 查询有公付能力的签约主体
	 * @return
	 */
	Response selectPaySubjectList(OrgSubjectRequest orgSubjectRequest);


	/**
	 * 根据ID查签约主体
	 * @return
	 */
	Response selectByPrimaryKey(Long subjectId);

	/**
	 * 根据机构ID查默认签约主体
	 * @param orgIds
	 * @return
	 */
	Response selectDefaultSubjectByOrgId(List<Long> orgIds);

	/**
	 * 新增签约主体
	 * @param orgSubjectDto
	 * @return
	 */
	Response addOrgSubject(OrgSubjectDto orgSubjectDto);

	/**
	 * 删除签约主体
	 * @param orgSubject
	 * @return
	 */
	Response deleteOrgSubject(OrgSubject orgSubject);

	/**
	 * 更新签约主体
	 * @param orgSubjectDto
	 * @return
	 */
	Response updateOrgSubject(OrgSubjectDto orgSubjectDto);

	/**
	 * 设置完当前签约主体为默认后，把其他的签约主体修改为不默认
	 * @param orgSubject
	 * @return
	 */
	Response setDefaultOrgSubject(OrgSubjectDto orgSubject);

	/**
	 * 实名认证
	 * @param orgSubject
	 * @return
	 */
	Response realNameAuthentication(OrgSubject orgSubject) throws Exception;

	/**
	 * 授权
	 * @param orgSubject
	 * @return
	 */
	Response authorization(OrgSubject orgSubject) throws Exception;

	/**
	 * 预览授权
	 * @param subjectId
	 * @param modifyer
	 * @return
	 * @throws Exception
	 */
	Response previewAuthorization(Long subjectId,String modifyer) throws Exception;


	/**
	 * 实名认证确认和取消按钮
	 * @param subjectId
	 */
	void selectCompanyVerifyAndUpdate(Long subjectId) throws Exception;

	/**
	 * 授权确认和取消按钮
	 * @param subjectId
	 */
	void selectCompanyAuthAndUpdate(Long subjectId) throws Exception;

	/**
	 * 查询酒店默认签约主体信息
	 * @param hotelId
	 * @return
	 */
	OrgSubject queryHotelDefaultSubject(Long hotelId);


	/**
	 * 查询酒店集团默认签约主体信息
	 * @param hotelGroupOrgId
	 * @return
	 */
	OrgSubject queryHotelGroupDefaultSubject(Long hotelGroupOrgId);
}
