package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.Permission;
import com.fangcang.rfp.common.dto.PermissionBo;
import com.fangcang.rfp.common.entity.UPermission;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PermissionService {

	int deleteByPrimaryKey(Long id);

    Permission insertSelective(Permission record);

    UPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Permission record);

    int updateByPrimaryKey(Permission record);

	Map<String, Object> deletePermissionById(String ids);

	List<PermissionBo> selectPermissionById(Long id);

	Map<String, Object> addPermission2Role(Long roleId,String ids);

	Map<String, Object> deleteByRids(String roleIds);
	
	//根据用户ID查询权限（permission），放入到Authorization里。
	Set<String> findPermissionByUserId(Long userId);
}
