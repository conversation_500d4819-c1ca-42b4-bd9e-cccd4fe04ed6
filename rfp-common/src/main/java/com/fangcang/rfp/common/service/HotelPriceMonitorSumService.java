package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.count.HotelPriceMonitorSumRequest;
import com.fangcang.rfp.common.dto.request.count.CityPriceMonitorRequest;
import com.fangcang.rfp.common.dto.request.count.PoiBindHotelRequest;
import com.fangcang.rfp.common.dto.request.count.PoiPriceMonitorRequest;
import com.fangcang.rfp.common.dto.response.MonitorRoomResponse;
import com.fangcang.rfp.common.entity.HotelPriceMonitor;
import com.fangcang.rfp.common.entity.HotelPriceMonitorSum;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/17 16:55
 */
public interface HotelPriceMonitorSumService {

    void insert(HotelPriceMonitorSum record);

    /**
     * 查询酒店报价监控未来1-3天列表
     * @param hotelPriceMonitorSumRequest
     * @return
     */
    Response selectHotelPriceMonitorSumOneThreeList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest, UserDTO userDTO);

    /**
     * 查询酒店报价监控未来4-15天列表
     * @param hotelPriceMonitorSumRequest
     * @return
     */
    Response selectHotelPriceMonitorSumFourFifList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest);

    Response selectMonitorDetail(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest);

    Response selectHotelPriceMonitorDetailResponse(HotelPriceMonitor hotelPriceMonitor);

    /**
     * 查询降价率、涨价率、满房率
     * @param hotelPriceMonitorSumRequest
     * @return
     */
    Response selectDpUpFullPctList(HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest);

    /**
     * 查询城市报价监控列表
     * @param cityPriceMonitorRequest
     * @return
     */
    Response selectCityPriceMonitorList(CityPriceMonitorRequest cityPriceMonitorRequest);

    /**
     * 查询POI报价监控列表
     * @param poiPriceMonitorRequest
     * @return
     */
    Response selectPoiPriceMonitorList(PoiPriceMonitorRequest poiPriceMonitorRequest);

    /**
     * 查询POI管理监控酒店列表
     * @param poiPriceMonitorRequest
     * @return
     */
    Response selectPoiManageMonitorHotelList(PoiPriceMonitorRequest poiPriceMonitorRequest);

    /**
     * 批量绑定
     * @param poiPriceMonitorRequest
     * @return
     */
    Response batchBind(PoiBindHotelRequest poiPriceMonitorRequest);

    /**
     * 解绑
     * @param poiPriceMonitorRequest
     * @return
     */
    Response unBind(PoiBindHotelRequest poiPriceMonitorRequest);

    /**
     * 查询绑定酒店管理列表
     * @param poiPriceMonitorRequest
     * @return
     */
    Response selectBingHotelList(PoiBindHotelRequest poiPriceMonitorRequest);

    /**
     * 查询绑定详情
     * @param poiPriceMonitorRequest
     * @return
     */
    Response selectBingHotelDetail(PoiBindHotelRequest poiPriceMonitorRequest);

    /**
     *  根据unque 查询monitor Sum
     */
    HotelPriceMonitorSum selectByUnique(HotelPriceMonitorSum hotelPriceMonitorSum);

    /**
     * 查询监控项目房型列表
     */
    List<MonitorRoomResponse> selectMonitorRoomList(Long projectId, Long hotelId);

    /**
     * 导出价格监控汇总
     */
    void exportHotelPriceMonitorSumList(UserDTO userDTO, HotelPriceMonitorSumRequest hotelPriceMonitorSumRequest, HttpServletResponse httpServletResponse) throws Exception;

}
