package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.RoleDTO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RoleService {

	int deleteByPrimaryKey(Long id);

    int insertSelective(RoleDTO record);

    RoleDTO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RoleDTO record);

    int updateByPrimaryKey(RoleDTO record);

	Map<String, Object> deleteRoleById(String ids);

	Set<String> findRoleByUserId(Long userId);

	List<RoleDTO> findNowAllPermission(Map<String,Object> map);
	
	/**
	 * @[简要描述]根据用户Id查询角色列表
	 * @[详细描述]
	 * @作    者:yangjunjun
	 * @日    期：2016年12月21日上午11:30:36
	 * @param userId
	 * @return    
	 * @return: List<Role>
	 */
	List<RoleDTO> selectRoleByUserId(Long userId);

	/**
	 * 获取系统角色
	 * @param userId
	 * @return
	 */
	String getSystemRoleCodeByUserId(Long userId) throws RuntimeException;
}
