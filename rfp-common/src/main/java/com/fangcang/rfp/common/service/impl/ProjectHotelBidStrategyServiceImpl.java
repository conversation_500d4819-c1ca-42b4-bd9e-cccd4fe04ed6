package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.enums.BreakfastNumEnum;
import com.fangcang.enums.HotelStarEnum;
import com.fangcang.hotel.base.api.dto.query.HotelSummaryInfoQueryDto;
import com.fangcang.hotel.base.api.dto.response.HotelSummaryInfoResponseDto;
import com.fangcang.hotel.base.api.facade.HotelInfoFacade;
import com.fangcang.rfp.common.cache.CachedHotelManager;
import com.fangcang.rfp.common.cache.CachedProjectManager;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.*;
import com.fangcang.rfp.common.dto.common.OrgRelatedHotelDTO;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.exception.ServiceException;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.*;
import com.fangcang.tmc.hub.api.request.baseinfo.HotelInfoRequest;
import com.fangcang.tmc.hub.api.request.product.HotelLowestPriceRequest;
import com.fangcang.tmc.hub.api.response.baseinfo.HotelInfoResponse;
import com.fangcang.tmc.hub.api.response.product.HotelLowestPriceResponse;
import com.fangcang.tmc.hub.api.response.product.HotelPriceItem;
import com.fangcang.util.DateUtil;
import com.fangcang.util.JsonUtil;
import com.fangcang.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.BaseFont;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @auther cjh
 * @description 投标策略服务
 * @date 2022/10/22
 */
@Service
public class ProjectHotelBidStrategyServiceImpl implements ProjectHotelBidStrategyService {

    private static Logger logger = LoggerFactory.getLogger(ProjectHotelBidStrategyServiceImpl.class);

    @Autowired
    private ProjectHotelBidStrategyDao projectHotelBidStrategyDao;

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;

    @Autowired
    private ProjectHotelTendStrategyDao projectHotelTendStrategyDao;

    @Autowired
    private RecommendHotelDao recommendHotelDao;

    @Autowired
    private OrgSubjectDao orgSubjectDao;

    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private PriceUnapplicableDayDao priceUnapplicableDayDao;

    @Autowired
    private ContractTemplateDao contractTemplateDao;

    @Autowired
    private ProjectHotelPriceService projectHotelPriceService;

    @Autowired
    private PriceUnapplicableDayService priceUnapplicableDayService;

    @Autowired
    private HotelInfoFacade hotelInfoFacade;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private HotelDao hotelDao;

    @Autowired
    private LanyonImportService lanyonImportService;

    @Autowired
    private com.fangcang.hotel.delivery.tmc.common.tmchub.api.service.TmcHubApiManager tmcHubApiManager;

    @Autowired
    private ProjectCustomTendStrategyDao projectCustomTendStrategyDao;

    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;

    @Autowired
    private ProjectCustomBidStrategyDao projectCustomBidStrategyDao;

    @Autowired
    private PriceApplicableDayService priceApplicableDayService;

    @Autowired
    private ProjectHotelBidTempInfoDao projectHotelBidTempInfoDao;

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;

    @Autowired
    private ProjectHotelPriceGroupService projectHotelPriceGroupService;

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;

    @Autowired
    private AmadeusService amadeusService;

    @Autowired
    private ProjectHotelHistoryDataDao projectHotelHistoryDataDao;

    @Autowired
    private ProjectPoiDao projectPoiDao;

    @Autowired
    private CachedProjectManager cachedProjectManager;

    @Autowired
    private CachedHotelManager cachedHotelManager;

    @Autowired
    private ProjectLastYearCityStatDao projectLastYearCityStatDao;

    @Autowired
    private BidOperateLogService bidOperateLogService;

    @Autowired
    private ProjectIntentHotelService projectIntentHotelService;

    @Autowired
    private OrgDao orgDao;

    @Autowired
    private OrgRelatedHotelDao orgRelatedHotelDao;

    @Autowired
    private ProjectInviteHotelDao projectInviteHotelDao;
    @Autowired
    private ProjectHotelRemarkDao projectHotelRemarkDao;

    @Autowired
    private ProjectIntentHotelGroupDao projectIntentHotelGroupDao;
    @Autowired
    private HotelRelatedCertService hotelRelatedCertService;
    @Autowired
    private ProjectCustomBidStrategyOptionDao projectCustomBidStrategyOptionDao;
    @Autowired
    private LanyonImportDataDao lanyonImportDataDao;
    @Autowired
    private AreadataDao areadataDao;
    @Autowired
    private TemplateEngine templateEngine;

    @Override
    public Response saveBidTempleTender(BidStrategyResponse bidStrategyResponse, String creator) {
        Response response = new Response();
        ProjectHotelBidTempInfo projectHotelBidTempInfo = projectHotelBidTempInfoDao.selectByProjectIntentHotelId(bidStrategyResponse.getProjectIntentHotelId());
        int insertOrUpdateCount = 0;
        if(projectHotelBidTempInfo == null){
            projectHotelBidTempInfo = new ProjectHotelBidTempInfo();
            projectHotelBidTempInfo.setBidInfoJson(JsonUtil.objectToJson(bidStrategyResponse));
            projectHotelBidTempInfo.setProjectIntentHotelId(bidStrategyResponse.getProjectIntentHotelId());
            projectHotelBidTempInfo.setCreator(creator);
            insertOrUpdateCount = projectHotelBidTempInfoDao.insert(projectHotelBidTempInfo);
        } else {
            projectHotelBidTempInfo.setBidInfoJson(JsonUtil.objectToJson(bidStrategyResponse));
            projectHotelBidTempInfo.setModifier(creator);
            insertOrUpdateCount = projectHotelBidTempInfoDao.update(projectHotelBidTempInfo);

        }
        if(insertOrUpdateCount == 0){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("保存失败");
            return response;
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg("保存成功");
        return response;
    }

    @Override
    public Response getBidTempleTender(Long projectIntentHotelId, Integer isInvited, UserDTO userDTO) {
        Response response = new Response();

        // 验证用户是否合法
        if(isInvited != null && isInvited == RfpConstant.constant_1) {
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
            if (Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTEL.key)) {
                List<Long> hotelIdList = orgRelatedHotelDao.queryHotelIdByHotelOrgId(userDTO.getOrgDTO().getOrgId());
                if (!hotelIdList.contains(projectIntentHotel.getHotelId())) {
                    response.setResult(ReturnResultEnum.PERMISSION_DENIED.errorNo);
                    response.setMsg(ReturnResultEnum.PERMISSION_DENIED.message);
                    return response;
                }
            } else if (Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)) {
                HotelResponse hotelResponse = hotelDao.selectHotelInfo(projectIntentHotel.getHotelId());
                Long hotelBrand = Long.valueOf(hotelResponse.getHotelBrand());
                if (!userDTO.getHotelGroupBrandIdList().contains(hotelBrand)) {
                    response.setResult(ReturnResultEnum.PERMISSION_DENIED.errorNo);
                    response.setMsg(ReturnResultEnum.PERMISSION_DENIED.message);
                    return response;
                }
            }
        }

        ProjectHotelBidTempInfo projectHotelBidTempInfo = projectHotelBidTempInfoDao.selectByProjectIntentHotelId(projectIntentHotelId);
        if(projectHotelBidTempInfo != null){
            response.setData(JsonUtil.jsonToBean(projectHotelBidTempInfo.getBidInfoJson(), BidStrategyResponse.class));
        }
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response submitTender(BidStrategyResponse bidStrategyResponse, UserDTO userDTO) {
        Response response = new Response();
        try {
            ProjectIntentHotel projectIntentHotelResult = projectIntentHotelDao.selectByPrimaryKey(bidStrategyResponse.getProjectIntentHotelId());
            boolean isNeedValidateBid = BidUtil.isNeedValidateBid(projectIntentHotelResult, this);
            if (projectIntentHotelResult.getBidState().equals(HotelBidStateEnum.BID_WINNING.bidState)) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("已中签状态不允许重新提交报价");
                return response;
            }
            if (projectIntentHotelResult.getBidState().equals(HotelBidStateEnum.REJECTED.bidState)) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("已否决状态不允许重新提交报价");
                return response;
            }
            if (projectIntentHotelResult.getBidState().equals(HotelBidStateEnum.REJECT_NEGOTIATION.bidState)) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("保持原价状态不允许重新提交报价");
                return response;
            }
            if (Objects.equals(projectIntentHotelResult.getHotelGroupApproveStatus(), HotelGroupApproveStatusEnum.WAITING.key)) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("待审核报价不允许重新提交报价");
                return response;
            }
            List<ProjectCustomBidStrategy> projectCustomBidStrategies = bidStrategyResponse.getProjectCustomBidStrategies();
            if(CollectionUtils.isNotEmpty(projectCustomBidStrategies)) {
                for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies) {
                    if(CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())){
                        if(CollectionUtils.isEmpty(projectCustomBidStrategy.getOptions())){
                            logger.error("其他承若项目未选择选项 {}", JSON.toJSONString(projectCustomBidStrategy));
                            response.setResult(ReturnResultEnum.FAILED.errorNo);
                            response.setMsg("其他承若项目未选择选项");
                            return response;
                        }
                    }
                }
            }

            Boolean addFlag = false;
            // 判断投标策略有没有数据，有就更新，没有就新增
            ProjectHotelBidStrategy projectHotelBidStrategy = projectHotelBidStrategyDao.selectByPrimaryKey(bidStrategyResponse.getProjectIntentHotelId());
            if (projectHotelBidStrategy != null) {
                // 更新
                BeanUtils.copyProperties(bidStrategyResponse,projectHotelBidStrategy);
                projectHotelBidStrategy.setModifier(bidStrategyResponse.getCreator());
                projectHotelBidStrategyDao.updateByPrimaryKeySelective(projectHotelBidStrategy);
            } else {
                addFlag = true;
                projectHotelBidStrategy = new ProjectHotelBidStrategy();
                BeanUtils.copyProperties(bidStrategyResponse, projectHotelBidStrategy);

                // 项目ID和酒店ID联合索引
                ProjectHotelBidStrategy bidStrategyResult = projectHotelBidStrategyDao.selectByProjectIdAndHotelID(projectHotelBidStrategy);
                if (bidStrategyResult != null) {
                    response.setResult(ReturnResultEnum.FAILED.errorNo);
                    response.setMsg("报价策略主键冲突");
                    return response;
                }
                // 新增
                projectHotelBidStrategyDao.insert(projectHotelBidStrategy);
            }

            //新增或修改自定义投标策略
            List<QueryCustomTendStrategyResponse> projectCustomTendStrategys = projectService.queryProjectCustomTendStrategy(projectIntentHotelResult.getProjectId());
            Map<Long, QueryCustomTendStrategyResponse> projectCustomTendStrategyMap = projectCustomTendStrategys.stream().collect(Collectors.toMap(QueryCustomTendStrategyResponse::getCustomTendStrategyId, v -> v));
            if(CollectionUtils.isNotEmpty(projectCustomBidStrategies)){
                List<ProjectCustomBidStrategyOption> customBidStrategyOptions = new ArrayList<>();
                for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies) {
                    QueryCustomTendStrategyResponse queryCustomTendStrategyResponse = projectCustomTendStrategyMap.get(projectCustomBidStrategy.getCustomTendStrategyId());
                    projectCustomBidStrategy.setProjectIntentHotelId(projectIntentHotelResult.getProjectIntentHotelId());
                    projectCustomBidStrategy.setCreator(bidStrategyResponse.getCreator());
                    projectCustomBidStrategy.setModifier(bidStrategyResponse.getCreator());
                    projectCustomBidStrategy.setHotelId(projectIntentHotelResult.getHotelId());
                    projectCustomBidStrategy.setProjectId(projectIntentHotelResult.getProjectId());
                    projectCustomBidStrategy.setStrategyType(queryCustomTendStrategyResponse.getStrategyType());
                    if(projectCustomBidStrategy.getSupportStrategyName() == null){
                        projectCustomBidStrategy.setSupportStrategyName(0);
                    }
                    if(!StringUtil.isValidString(projectCustomBidStrategy.getSupportStrategyText()) || CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())) {
                        projectCustomBidStrategy.setSupportStrategyText("");
                    }
                    if(CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())){
                        for(ProjectCustomBidStrategyOption option : projectCustomBidStrategy.getOptions()){
                            option.setProjectIntentHotelId(projectIntentHotelResult.getProjectIntentHotelId());
                            option.setCreator(bidStrategyResponse.getCreator());
                            option.setCustomTendStrategyId(projectCustomBidStrategy.getCustomTendStrategyId());
                            option.setModifier(bidStrategyResponse.getCreator());
                            option.setHotelId(projectIntentHotelResult.getHotelId());
                            option.setProjectId(projectIntentHotelResult.getProjectId());
                            option.setIsSupport(option.getIsSupport() == null ? RfpConstant.constant_0 : option.getIsSupport());
                        }
                        customBidStrategyOptions.addAll(projectCustomBidStrategy.getOptions());
                    }
                }
                projectCustomBidStrategyDao.batchMergeProjectCustomBidStrategy(projectCustomBidStrategies);
                if(CollectionUtils.isNotEmpty(customBidStrategyOptions)){
                    projectCustomBidStrategyOptionDao.batchMergeProjectCustomBidStrategyOption(customBidStrategyOptions);
                }
            }

            // 计算权重分
            BigDecimal bigWeight = isNeedValidateBid ? calculateWeight(bidStrategyResponse.getProjectId(), bidStrategyResponse) : null;

            // 检查酒店证书

            // 根据项目ID查项目机构ID
            OrgSubject companyDefaultSubject = new OrgSubject();
            OrgSubject payDefaultSubject = new OrgSubject();
            ProjectBasicInfoResponse projectBasicInfoResponse = new ProjectBasicInfoResponse();
            Response projectBasicInformation = projectService.queryProjectBasicInformation(bidStrategyResponse.getProjectId());
            if (projectBasicInformation.getData() != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);
                // 检查自动上线是否选择接单方式
                if(projectBasicInfoResponse.getIsAutoConfigGoOnline() == RfpConstant.constant_1){
                    if(bidStrategyResponse.getReceiveOrderMethod() == null){
                        response.setResult(ReturnResultEnum.FAILED.errorNo);
                        response.setMsg("请选择接单方式");
                        return response;
                    }
                }
                // 查企业默认签约主体信息
                ArrayList<Long> defaultIds = new ArrayList<>();
                defaultIds.add(projectBasicInfoResponse.getTenderOrgId());
                List<OrgSubject> defaultSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(defaultIds);
                if (!CollectionUtils.isEmpty(defaultSubjects)) {
                    BeanUtils.copyProperties(defaultSubjects.get(0),companyDefaultSubject);
                }

                // 查公付机构默认签约主体信息
                if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                    ArrayList<Long> payIds = new ArrayList<>();
                    payIds.add(projectBasicInfoResponse.getCoPayerOrgId());
                    List<OrgSubject> paySubjects = orgSubjectDao.selectDefaultSubjectByOrgId(payIds);
                    if (!CollectionUtils.isEmpty(paySubjects)) {
                        BeanUtils.copyProperties(paySubjects.get(0),payDefaultSubject);
                    }
                }
            }

            // 更新投标人信息、投标状态、权重
            ProjectIntentHotel projectIntentHotel = new ProjectIntentHotel();
            BeanUtils.copyProperties(bidStrategyResponse,projectIntentHotel);
            projectIntentHotel.setModifier(bidStrategyResponse.getCreator());
            projectIntentHotel.setBidContactName(bidStrategyResponse.getBidContactName());
            projectIntentHotel.setBidWeight(bigWeight);
            projectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);

            // 判断酒店集团是否需要审核
            HotelResponse hotelResponse = hotelDao.selectHotelInfo(projectIntentHotelResult.getHotelId());
            boolean isNeedHotelGroupApprove = projectIntentHotelService.isNeedHotelGroupApprove(projectIntentHotelResult.getProjectId(), hotelResponse, userDTO);
           // 需要酒店集团审核
            if(isNeedHotelGroupApprove){
                projectIntentHotel.setHotelGroupApproveStatus(HotelGroupApproveStatusEnum.WAITING.key);
                projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
            }

            // 议价中报价并且不需要酒店集团审核提交状态为修改报价
            if(Objects.equals(projectIntentHotelResult.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) && !isNeedHotelGroupApprove) {
                projectIntentHotel.setBidState(HotelBidStateEnum.UPDATED_BID.bidState);
                bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, "签约状态更新为 " + HotelBidStateEnum.UPDATED_BID.value);
            } else {
                bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO,  OrgTypeEnum.getValueByKey(userDTO.getOrgDTO().getOrgType()) + "提交报价. 签约状态为" + HotelBidStateEnum.getValueByKey(projectIntentHotel.getBidState()));
            }

            // 自动上线设置值
            if(projectBasicInfoResponse.getIsAutoConfigGoOnline() == RfpConstant.constant_1){
                /**
                 * 设置接单方式
                 */
                projectIntentHotel.setReceiveOrderMethod(bidStrategyResponse.getReceiveOrderMethod());
                /**
                 * 税费设置：提供以下两个选项 （如需补税差为设置税差加幅，后续自动化时会用到）
                 * 企业统一收专6发票，非专6补税差 （专3补3.4%，专1补5.8%，普票补6.8%）
                 * 企业不补税差，非专6收普票
                 */
                projectIntentHotel.setTaxDiffIncreaseRate(BigDecimal.ZERO);
                if(Objects.equals(projectBasicInfoResponse.getOnlineInvoiceSet(), OnlineInvoiceSetEnum.NEED_TAX_INCREASE.key)){
                    if(projectHotelBidStrategy.getProvideInvoiceType() == InvoiceEnum.COMMON.getKey()){
                        projectIntentHotel.setTaxDiffIncreaseRate(new BigDecimal("6.8"));
                    } else if(projectHotelBidStrategy.getProvideInvoiceType() == InvoiceEnum.SPECIAL.getKey() &&
                            projectHotelBidStrategy.getProvideInvoiceTaxRate().compareTo(BigDecimal.ONE) == 0
                    ){
                        projectIntentHotel.setTaxDiffIncreaseRate(new BigDecimal("5.8"));
                    } else if(projectHotelBidStrategy.getProvideInvoiceType() == InvoiceEnum.SPECIAL.getKey() &&
                            projectHotelBidStrategy.getProvideInvoiceTaxRate().compareTo(new BigDecimal("3")) == 0
                    ) {
                        projectIntentHotel.setTaxDiffIncreaseRate(new BigDecimal("3.4"));
                    }
                }
            }

            /**
             *  新增报价时才更新，更新报价时不更新
             *  公付签约主体ID、企业签约主体ID、企业开户银行、企业开户名、企业银行账户
             *  公付签约主体开户银行、提供公付服务签约主体机构id、公付签约主体开户名、公付签约主体银行账户
             */
            if (addFlag) {

                if (companyDefaultSubject.getSubjectId() != null) {
                    projectIntentHotel.setDistributorSubjectId(companyDefaultSubject.getSubjectId());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getBank())) {
                    projectIntentHotel.setDistributorBank(companyDefaultSubject.getBank());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountName())) {
                    projectIntentHotel.setDistributorAccountName(companyDefaultSubject.getAccountName());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountNumber())) {
                    projectIntentHotel.setDistributorAccountNumber(companyDefaultSubject.getAccountNumber());
                }

                // 只有项目选择了第三方公付服务才更新
                if (CoPayerTypeEnum.THIRD_PARTY_PAYMENT.key.equals(projectBasicInfoResponse.getCoPayerType())) {
                    if (payDefaultSubject.getSubjectId() != null) {
                        projectIntentHotel.setCoPayerSubjectId(payDefaultSubject.getSubjectId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getBank())) {
                        projectIntentHotel.setCoPayerBank(payDefaultSubject.getBank());
                    }
                    if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                        projectIntentHotel.setCoPayerOrgId(projectBasicInfoResponse.getCoPayerOrgId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountName())) {
                        projectIntentHotel.setCoPayerAccountName(payDefaultSubject.getAccountName());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountNumber())) {
                        projectIntentHotel.setCoPayerAccountNumber(payDefaultSubject.getAccountNumber());
                    }
                }
            }
            projectIntentHotel.setBidOrgId(userDTO.getOrgDTO().getOrgId());
            projectIntentHotel.setBidOrgType(userDTO.getOrgDTO().getOrgType());
            projectIntentHotelDao.updateBidContactInfo(projectIntentHotel);

            // 酒店集团更新报价联系方式，避免覆盖默认模板报价联系方式
            if(Objects.equals(projectIntentHotel.getBidOrgType(), OrgTypeEnum.HOTELGROUP.key)) {
                projectIntentHotelDao.updateHotelAndHotelGroupBidContactInfo(projectIntentHotel);
            }

            // 更新报价主体信息
            if(bidStrategyResponse.getHotelSubjectId() != null){
                OrgSubject orgSubject = orgSubjectDao.selectByPrimaryKey(bidStrategyResponse.getHotelSubjectId());
                projectIntentHotel.setHotelOrgId(orgSubject.getOrgId());
                projectIntentHotel.setHotelSubjectId(orgSubject.getSubjectId());
                projectIntentHotel.setHotelBank(orgSubject.getBank());
                projectIntentHotel.setHotelBankAccountType(orgSubject.getAccountType());
                projectIntentHotel.setHotelAccountName(orgSubject.getAccountName());
                projectIntentHotel.setHotelAccountNumber(orgSubject.getAccountNumber());
                projectIntentHotel.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                projectIntentHotel.setModifier(userDTO.getOperator());
                projectIntentHotelDao.updateBidHotelSubjectInfo(projectIntentHotel);
            }

            // 报价日期设置
            priceApplicableDayService.insertOrUpdateProjectHotelPriceApplicableDay(false, bidStrategyResponse.getProjectId(),
                    bidStrategyResponse.getHotelId(), bidStrategyResponse.getPriceApplicableDayList(), bidStrategyResponse.getCreator());

            // 编辑不适用日期
            PriceUnapplicableDayDto priceUnapplicableDayDto = new PriceUnapplicableDayDto();
            BeanUtils.copyProperties(bidStrategyResponse,priceUnapplicableDayDto);

            List<PriceUnapplicableDay> unapplicableDayList = bidStrategyResponse.getUnapplicableDayList();
            priceUnapplicableDayDto.setPriceUnapplicableDayList(unapplicableDayList);
            priceUnapplicableDayService.insertPriceUnapplicableDay(priceUnapplicableDayDto);

            // 设置leve1 第一个roomType为关注房型
            MonitorRoomResponse monitorRoomResponse = projectHotelPriceService.selectDefaultMonitorRoomResponse(projectIntentHotelResult.getProjectId(), projectIntentHotelResult.getHotelId());
            if(monitorRoomResponse != null) {
                HotelPriceMonitorRoom hotelPriceMonitorRoom = new HotelPriceMonitorRoom();
                BeanUtils.copyProperties(monitorRoomResponse, hotelPriceMonitorRoom);
                hotelPriceMonitorRoom.setCreator(RfpConstant.CREATOR);
                HotelPriceMonitorRoom deleteHotelPriceMonitorRoom = new HotelPriceMonitorRoom();
                deleteHotelPriceMonitorRoom.setProjectId(projectIntentHotelResult.getProjectId());
                deleteHotelPriceMonitorRoom.setHotelId(projectIntentHotelResult.getHotelId());
                hotelPriceMonitorRoomDao.deleteData(deleteHotelPriceMonitorRoom);
                int insertResult = hotelPriceMonitorRoomDao.insert(hotelPriceMonitorRoom);
                if (insertResult == 0) {
                    throw new RuntimeException("提交报价失败, 关注房型失败");
                }
            }

            // 删除临时报价
            projectHotelBidTempInfoDao.deleteByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());

            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);

        } catch (Exception e) {
            logger.error("提交投标失败",e);
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("提交报价失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return response;
    }

    @Override
    public List<ProjectCustomBidStrategy> queryProjectCustomBidStrategyList(Long projectId, Long hotelId){
        QueryProjectCustomBidStrategyDto queryProjectCustomBidStrategyDto = new QueryProjectCustomBidStrategyDto();
        queryProjectCustomBidStrategyDto.setProjectId(projectId);
        queryProjectCustomBidStrategyDto.setHotelId(hotelId);
        List<ProjectCustomBidStrategy> projectCustomBidStrategies = projectCustomBidStrategyDao.queryProjectCustomBidStrategyByDisplayOrderAsc(queryProjectCustomBidStrategyDto);
        boolean hasOptionType = false;
        for(ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies){
            if(CustomStrategyTypeEnum.isOptionType(projectCustomBidStrategy.getStrategyType())){
                hasOptionType = true;
                break;
            }
        }
        if(hasOptionType){
            List<ProjectCustomBidStrategyOption> options = projectCustomBidStrategyOptionDao.queryProjectCustomBidStrategyOptions(projectId, hotelId);
            Map<Long, List<ProjectCustomBidStrategyOption>> optionsMap = options.stream().collect(Collectors.groupingBy(ProjectCustomBidStrategyOption::getCustomTendStrategyId));
            for(ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies){
                List<ProjectCustomBidStrategyOption> strategyOptions = optionsMap.get(projectCustomBidStrategy.getCustomTendStrategyId());
                if(CollectionUtils.isNotEmpty(strategyOptions)){
                    projectCustomBidStrategy.setOptions(strategyOptions);
                }
            }
        }
        return projectCustomBidStrategies;
    }

    @Override
    public void generateBidPdf(Long projectIntentHotelId, HttpServletResponse response) throws IOException {
        // 1. 渲染 Thymeleaf 模板为 HTML
        Map<String, Object> data = new HashMap<>();
        data.put("contractNo", 123);
        data.put("hotelName", "大酒店");
        // 设置数据
        Context context = new Context();
        context.setVariables(data);

        String htmlContent = templateEngine.process("hotelWinningBidConfirmationLetterTemplate", context);

        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=销售报告.pdf");

        ITextRenderer renderer = new ITextRenderer();

        try (InputStream is = getClass().getResourceAsStream("/font/SIMSUN.TTF")) {
            if (is == null) {
                throw new RuntimeException("字体文件未找到: /font/SIMSUN.TTF");
            }
            renderer.getFontResolver().addFont(is, "SimSun", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED, null);
        }
        // 添加中文字体支持
        renderer.setDocumentFromString(htmlContent);
        renderer.layout();
        renderer.createPDF(response.getOutputStream());

    }

    @Override
    public Response selectProjectHotelBidStrategy(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest) {
        Response response = new Response();
        BidStrategyResponse bidStrategyResponse = new BidStrategyResponse();

        // 亮点
        RecommendHotel recommendHotel = recommendHotelDao.selectRecommendHotelByHotelId(projectHotelBidStrategyRequest.getHotelId(),1);
        if (recommendHotel != null && StringUtils.isNotEmpty(recommendHotel.getBrightSpot())) {
            bidStrategyResponse.setBrightSpot(recommendHotel.getBrightSpot());
        }

        // 第一轮投标时间
        Response projectBasicInformation = projectService.queryProjectBasicInformation(projectHotelBidStrategyRequest.getProjectId());
        if (projectBasicInformation != null && projectBasicInformation.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            ProjectBasicInfoResponse projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);
            bidStrategyResponse.setFirstBidStartTime(projectBasicInfoResponse.getFirstBidStartTime());
            bidStrategyResponse.setFirstBidEndTime(projectBasicInfoResponse.getFirstBidEndTime());
            bidStrategyResponse.setNeedOnlineContracts(projectBasicInfoResponse.getNeedOnlineContracts());
            bidStrategyResponse.setIsAutoConfigGoOnline(projectBasicInfoResponse.getIsAutoConfigGoOnline());
        }

        // 项目投标测试
        ProjectHotelTendStrategy projectHotelTendStrategy = projectHotelTendStrategyDao.selectByPrimaryKey(projectHotelBidStrategyRequest.getProjectId());
        if(projectHotelTendStrategy != null){
            bidStrategyResponse.setIsIncludeSubjectCert(projectHotelTendStrategy.getIsIncludeSubjectCert());
            bidStrategyResponse.setIsIncludeSpecialTradesCert(projectHotelTendStrategy.getIsIncludeSpecialTradesCert());
            bidStrategyResponse.setIsIncludeHygieneCert(projectHotelTendStrategy.getIsIncludeHygieneCert());
            bidStrategyResponse.setIsIncludeFireSafetyCert(projectHotelTendStrategy.getIsIncludeFireSafetyCert());
        }

        // 投标策略
        ProjectHotelBidStrategy resultBidStrategy = projectHotelBidStrategyDao.selectByPrimaryKey(projectHotelBidStrategyRequest.getProjectIntentHotelId());
        if (resultBidStrategy != null) {
            BeanUtils.copyProperties(resultBidStrategy, bidStrategyResponse);
        }

        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        BeanUtils.copyProperties(projectHotelBidStrategyRequest, projectIntentHotelRequest);
        // 投标信息、状态、权重
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
        if (projectIntentHotel != null) {
            bidStrategyResponse.setBidOrgType(projectIntentHotel.getBidOrgType());
            bidStrategyResponse.setNeedValidateBid(BidUtil.isNeedValidateBid(projectIntentHotel, this));
            bidStrategyResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            if (projectIntentHotel.getBidState() != null) {
                bidStrategyResponse.setBidState(projectIntentHotel.getBidState());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getBidContactName())) {
                bidStrategyResponse.setBidContactName(projectIntentHotel.getBidContactName());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getBidContactMobile())) {
                bidStrategyResponse.setBidContactMobile(projectIntentHotel.getBidContactMobile());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getBidContactEmail())) {
                bidStrategyResponse.setBidContactEmail(projectIntentHotel.getBidContactEmail());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getHotelGroupBidContactName())) {
                bidStrategyResponse.setHotelGroupBidContactName(projectIntentHotel.getHotelGroupBidContactName());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getHotelGroupBidContactMobile())) {
                bidStrategyResponse.setHotelGroupBidContactMobile(projectIntentHotel.getHotelGroupBidContactMobile());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getHotelGroupBidContactEmail())) {
                bidStrategyResponse.setHotelGroupBidContactEmail(projectIntentHotel.getHotelGroupBidContactEmail());
            }
            if (projectIntentHotel.getBidWeight() != null) {
                bidStrategyResponse.setBidWeight(projectIntentHotel.getBidWeight());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getRemark())) {
                bidStrategyResponse.setRemark(projectIntentHotel.getRemark());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getEmployeeRight())) {
                bidStrategyResponse.setEmployeeRight(projectIntentHotel.getEmployeeRight());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getEmployeeRightFileUrl())) {
                bidStrategyResponse.setEmployeeRightFileUrl(HttpsUrlUtility.convertUploadToHttpsAccessUrl(projectIntentHotel.getEmployeeRightFileUrl()));
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getSpecialTradesCertUrl())) {
                bidStrategyResponse.setSpecialTradesCertUrl(projectIntentHotel.getSpecialTradesCertUrl());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getHygieneLicenseCertUrl())) {
                bidStrategyResponse.setHygieneLicenseCertUrl(projectIntentHotel.getHygieneLicenseCertUrl());
            }
            if (StringUtils.isNotEmpty(projectIntentHotel.getFireSafetyCertUrl())) {
                bidStrategyResponse.setFireSafetyCertUrl(projectIntentHotel.getFireSafetyCertUrl());
            }

            bidStrategyResponse.setReceiveOrderMethod(projectIntentHotel.getReceiveOrderMethod());
            // 是否为Lanyon导入
            bidStrategyResponse.setIsLanyonImport(BidUtil.isLanyonImport(projectIntentHotel));
            bidStrategyResponse.setLanyonImportDataCount(0);
            if(Objects.equals(bidStrategyResponse.getIsLanyonImport(), RfpConstant.constant_1)){
                int lanyonDataCount = lanyonImportDataDao.getDataCountByProjectHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
                bidStrategyResponse.setLanyonImportDataCount(lanyonDataCount);
            }

        }

        // 报价房档明细
        List<ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponses = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
        if (CollectionUtils.isNotEmpty(projectHotelPriceLevelResponses)) {
            bidStrategyResponse.setHotelPriceLevelResponseList(projectHotelPriceLevelResponses);
        }

        // 转换的报价显示 报价明细
        if(CollectionUtils.isEmpty(projectHotelPriceLevelResponses) || projectHotelPriceLevelResponses.get(0).getProjectHotelPriceGroupResponseList().get(0).getCreator().equals("Convert")) {
            List<ProjectHotelPriceResponse> projectHotelPriceResponses = projectHotelPriceService.selectProjectHotelPriceList(projectIntentHotelRequest);
            if (CollectionUtils.isNotEmpty(projectHotelPriceResponses)) {
                bidStrategyResponse.setHotelPriceResponseList(projectHotelPriceResponses);
            }
        }

        // 不适用日期
        List<PriceUnapplicableDay> priceUnapplicableDays = priceUnapplicableDayDao.selectPriceUnapplicableDayList(projectHotelBidStrategyRequest);
        if (CollectionUtils.isNotEmpty(priceUnapplicableDays)) {
            bidStrategyResponse.setUnapplicableDayList(priceUnapplicableDays);
        }

        // 查询报价日期设置
        Response priceApplicableDayResponse = priceApplicableDayService.selectPriceApplicableDayListByProject(projectHotelBidStrategyRequest, false);
        if(priceApplicableDayResponse.getData() != null){
            List<PriceApplicableDay> priceApplicableDayList = (List<PriceApplicableDay>)priceApplicableDayResponse.getData();
            bidStrategyResponse.setPriceApplicableDayList(priceApplicableDayList);
        }

        // 查询项目备注信息
        List<ProjectHotelRemarkResponse> projectHotelRemarks = projectHotelRemarkDao.selectList(projectHotelBidStrategyRequest.getProjectId(), projectHotelBidStrategyRequest.getHotelId(), null);
        bidStrategyResponse.setProjectHotelRemarkResponseList(projectHotelRemarks);

        //自定义投标策略
        List<ProjectCustomBidStrategy> projectCustomBidStrategies = queryProjectCustomBidStrategyList(projectHotelBidStrategyRequest.getProjectId(),projectHotelBidStrategyRequest.getHotelId());

        //项目自定义采购策略
        List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses = projectService.queryProjectCustomTendStrategy(projectHotelBidStrategyRequest.getProjectId());
        //项目自定义采购策略为空，但是自定义投标策略不为空，则需要删除旧的自定义投标策略
        if (CollectionUtils.isEmpty(queryCustomTendStrategyResponses)) {
            if (CollectionUtils.isNotEmpty(projectCustomBidStrategies)) {
                List<Long> oldCustomTendStrategyIdList = new ArrayList<>();
                for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies) {
                    oldCustomTendStrategyIdList.add(projectCustomBidStrategy.getCustomTendStrategyId());
                }
                projectCustomBidStrategyDao.batchDeleteProjectCustomBidStrategy(projectHotelBidStrategyRequest.getProjectId(), projectHotelBidStrategyRequest.getHotelId(), oldCustomTendStrategyIdList);
                projectCustomBidStrategyOptionDao.batchDelete(projectHotelBidStrategyRequest.getProjectId(), projectHotelBidStrategyRequest.getHotelId(), oldCustomTendStrategyIdList);
            }
        } else {
            //项目自定义采购策略不为空，且自定义投标策略不为空，则需要删除自定义投标策略中不存在的项目自定义采购策略
            Map<Long, ProjectCustomBidStrategy> projectCustomBidStrategyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(projectCustomBidStrategies)) {
                for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies) {
                    projectCustomBidStrategyMap.put(projectCustomBidStrategy.getCustomTendStrategyId(), projectCustomBidStrategy);
                }
                List<Long> oldCustomTendStrategyIdList = new ArrayList<>(projectCustomBidStrategyMap.keySet());
                for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : queryCustomTendStrategyResponses) {
                    Long customTendStrategyId = queryCustomTendStrategyResponse.getCustomTendStrategyId();
                    //投标策略中已经有策略了，则直接过滤，留下来的是无效的策略，需要删除
                    //投标策略中没有的需要新增到投标策略
                    if (oldCustomTendStrategyIdList.contains(customTendStrategyId)) {
                        oldCustomTendStrategyIdList.remove(customTendStrategyId);
                    } else {
                        ProjectCustomBidStrategy projectCustomBidStrategy = new ProjectCustomBidStrategy();
                        projectCustomBidStrategy.setCustomTendStrategyId(customTendStrategyId);
                        projectCustomBidStrategy.setProjectIntentHotelId(projectHotelBidStrategyRequest.getProjectIntentHotelId());
                        projectCustomBidStrategy.setProjectId(projectHotelBidStrategyRequest.getProjectId());
                        projectCustomBidStrategy.setHotelId(projectHotelBidStrategyRequest.getHotelId());
                        projectCustomBidStrategy.setStrategyName(queryCustomTendStrategyResponse.getStrategyName());
                        projectCustomBidStrategy.setStrategyType(queryCustomTendStrategyResponse.getStrategyType());
                        if(CollectionUtils.isNotEmpty(queryCustomTendStrategyResponse.getOptions())) {
                            List<ProjectCustomBidStrategyOption> options = new ArrayList<>();
                            for (CustomStrategyOptionVO customStrategyOptionVO : queryCustomTendStrategyResponse.getOptions()) {
                                ProjectCustomBidStrategyOption projectCustomBidStrategyOption = new ProjectCustomBidStrategyOption();
                                projectCustomBidStrategyOption.setCustomTendStrategyId(customStrategyOptionVO.getStrategyId());
                                projectCustomBidStrategyOption.setOptionName(customStrategyOptionVO.getOptionName());
                                projectCustomBidStrategyOption.setOptionId(customStrategyOptionVO.getOptionId());
                                projectCustomBidStrategyOption.setProjectIntentHotelId(projectHotelBidStrategyRequest.getProjectIntentHotelId());
                                projectCustomBidStrategyOption.setProjectId(projectHotelBidStrategyRequest.getProjectId());
                                projectCustomBidStrategyOption.setHotelId(projectHotelBidStrategyRequest.getHotelId());
                                projectCustomBidStrategyOption.setIsSupport(RfpConstant.constant_0);
                                options.add(projectCustomBidStrategyOption);
                            }
                            projectCustomBidStrategy.setOptions(options);
                        }
                        projectCustomBidStrategyMap.put(customTendStrategyId, projectCustomBidStrategy);
                    }
                }
                //无效的投标自定义策略
                if (oldCustomTendStrategyIdList != null && !oldCustomTendStrategyIdList.isEmpty()) {
                    projectCustomBidStrategyDao.batchDeleteProjectCustomBidStrategy(projectHotelBidStrategyRequest.getProjectId(), projectHotelBidStrategyRequest.getHotelId(),oldCustomTendStrategyIdList);
                    for (Long aLong : oldCustomTendStrategyIdList) {
                        projectCustomBidStrategyMap.remove(aLong);
                    }
                    projectCustomBidStrategyOptionDao.batchDelete(projectHotelBidStrategyRequest.getProjectId(), projectHotelBidStrategyRequest.getHotelId(),oldCustomTendStrategyIdList);
                }
            } else {
                for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : queryCustomTendStrategyResponses) {
                    Long customTendStrategyId = queryCustomTendStrategyResponse.getCustomTendStrategyId();
                    ProjectCustomBidStrategy projectCustomBidStrategy = new ProjectCustomBidStrategy();
                    projectCustomBidStrategy.setCustomTendStrategyId(customTendStrategyId);
                    projectCustomBidStrategy.setProjectIntentHotelId(projectHotelBidStrategyRequest.getProjectIntentHotelId());
                    projectCustomBidStrategy.setProjectId(projectHotelBidStrategyRequest.getProjectId());
                    projectCustomBidStrategy.setHotelId(projectHotelBidStrategyRequest.getHotelId());
                    projectCustomBidStrategy.setStrategyType(queryCustomTendStrategyResponse.getStrategyType());
                    projectCustomBidStrategy.setStrategyName(queryCustomTendStrategyResponse.getStrategyName());
                    if(CollectionUtils.isNotEmpty(queryCustomTendStrategyResponse.getOptions())) {
                        List<ProjectCustomBidStrategyOption> options = new ArrayList<>();
                        for (CustomStrategyOptionVO customStrategyOptionVO : queryCustomTendStrategyResponse.getOptions()) {
                            ProjectCustomBidStrategyOption projectCustomBidStrategyOption = new ProjectCustomBidStrategyOption();
                            projectCustomBidStrategyOption.setCustomTendStrategyId(customStrategyOptionVO.getStrategyId());
                            projectCustomBidStrategyOption.setOptionName(customStrategyOptionVO.getOptionName());
                            projectCustomBidStrategyOption.setOptionId(customStrategyOptionVO.getOptionId());
                            projectCustomBidStrategyOption.setProjectIntentHotelId(projectHotelBidStrategyRequest.getProjectIntentHotelId());
                            projectCustomBidStrategyOption.setProjectId(projectHotelBidStrategyRequest.getProjectId());
                            projectCustomBidStrategyOption.setHotelId(projectHotelBidStrategyRequest.getHotelId());
                            projectCustomBidStrategyOption.setIsSupport(RfpConstant.constant_0);
                            options.add(projectCustomBidStrategyOption);
                        }
                        projectCustomBidStrategy.setOptions(options);
                    }
                    projectCustomBidStrategyMap.put(customTendStrategyId, projectCustomBidStrategy);
                }
            }
            if(projectCustomBidStrategyMap != null && projectCustomBidStrategyMap.size()>0){
                bidStrategyResponse.setProjectCustomBidStrategies(new ArrayList<>(projectCustomBidStrategyMap.values()));
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(bidStrategyResponse);
        return response;
    }

    @Override
    public Response isProjectHotelBidStrategy(BidStrategyResponse bidStrategyResponse) {
        Response response = new Response();

        //校验报价酒店是否在白名单，在白名单中的酒店不受采购策略限制，无需进行采购策略校验。
        boolean isHotelWhite = isBidWhiteHotel(bidStrategyResponse.getProjectId(), bidStrategyResponse.getHotelId());
        if(isHotelWhite){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            return response;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        long totalUnapplicableDays = 0l;
        // 判断不适用日期是否交叉
        if (CollectionUtils.isNotEmpty(bidStrategyResponse.getUnapplicableDayList())) {
            for (int i = 0; i < bidStrategyResponse.getUnapplicableDayList().size(); i++) {
                if (i+1 < bidStrategyResponse.getUnapplicableDayList().size()) {
                    if (!TimeUtil.isTimeCross(bidStrategyResponse.getUnapplicableDayList().get(i).getStartDate(),
                            bidStrategyResponse.getUnapplicableDayList().get(i).getEndDate(),
                            bidStrategyResponse.getUnapplicableDayList().get(i+1).getStartDate(),
                            bidStrategyResponse.getUnapplicableDayList().get(i+1).getEndDate())) {
                        response.setResult(ReturnResultEnum.FAILED.errorNo);
                        response.setMsg("不适用日期时间交叉");
                        return response;
                    }
                }
                long day = DateUtil.getDay(bidStrategyResponse.getUnapplicableDayList().get(i).getStartDate(), bidStrategyResponse.getUnapplicableDayList().get(i).getEndDate());
                // 开始，结束都算天，开始和结束是同天算1天
                if(DateUtil.isEqualsDate(bidStrategyResponse.getUnapplicableDayList().get(i).getStartDate(), bidStrategyResponse.getUnapplicableDayList().get(i).getEndDate())){
                    day = 1;
                } else {
                    day = day+1;
                }
                totalUnapplicableDays = totalUnapplicableDays + day;
            }
        }

        long totalApplicableDays = 0l;
        Set<Integer> priceTypeSet = new HashSet<>();
        // 判断Season日期是否超过限制
        for(PriceApplicableDay priceApplicableDay : bidStrategyResponse.getPriceApplicableDayList()){
            priceTypeSet.add(priceApplicableDay.getPriceType());
            if(priceApplicableDay.getPriceType() == HotelPriceTypeEnum.BASE_PRICE.key){
                continue;
            }

            long day = DateUtil.getDay(priceApplicableDay.getStartDate(), priceApplicableDay.getEndDate());
            // 开始，结束都算天，开始和结束是同天算1天
            if(DateUtil.isEqualsDate(priceApplicableDay.getStartDate(), priceApplicableDay.getEndDate())) {
                day = 1;
            } else {
                day = day+1;
            }
            totalApplicableDays = totalApplicableDays + day;

        }

        // 查询企业项目采购策略
        Boolean projectHotelBidStrategyFlag = true;
        ArrayList<String> msgList = new ArrayList<>();

        // 检查报价类型
        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        projectIntentHotelRequest.setProjectId(bidStrategyResponse.getProjectId());
        projectIntentHotelRequest.setProjectIntentHotelId(bidStrategyResponse.getProjectIntentHotelId());
        projectIntentHotelRequest.setHotelId(bidStrategyResponse.getHotelId());
        List<ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponseList = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
        if (!CollectionUtils.isEmpty(projectHotelPriceLevelResponseList)) {
            // 检查报价Season和Season日期是否一致
            boolean validateApplicableDayResult = true;
            for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : projectHotelPriceLevelResponseList){
                for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                    for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()){
                        if(!priceTypeSet.contains(projectHotelPrice.getPriceType())){
                            projectHotelBidStrategyFlag = false;
                            validateApplicableDayResult = false;
                            msgList.add("您提交了" + HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "格，未维护" +HotelPriceTypeEnum.getEnumByKey(projectHotelPrice.getPriceType()).value + "格有效日期，请先维护好对应有效日期再提交");
                            break;
                        }
                    }
                    if(!validateApplicableDayResult){
                        break;
                    }
                }
                if(!validateApplicableDayResult){
                    break;
                }
            }

            // 检查报价是否包括所有season价格
            TreeSet<Integer> priceTypeIdSet = new TreeSet<>();
            bidStrategyResponse.getPriceApplicableDayList().forEach(item -> {
                priceTypeIdSet.add(item.getPriceType());
            });
            Set<String> validatePriceTypeSet = new HashSet<>();
            for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : projectHotelPriceLevelResponseList){
                for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                    Set<Integer> groupPriceTypeSet = projectHotelPriceGroupResponse.getHotelPriceList().stream().map(ProjectHotelPrice::getPriceType).collect(Collectors.toSet());
                    for(Integer priceType : priceTypeIdSet){
                        if(!groupPriceTypeSet.contains(priceType)){
                            validatePriceTypeSet.add(HotelPriceTypeEnum.getEnumByKey(priceType).value + " 日期已经维护, "+HotelPriceTypeEnum.getEnumByKey(priceType).value+"格不可为空");
                        }
                    }
                }
            }
            if(!validatePriceTypeSet.isEmpty()){
                projectHotelBidStrategyFlag = false;
                msgList.addAll(validatePriceTypeSet);
            }
        }

        Response projectHotelTendStrategyResponse = projectService.queryProjectHotelTendStrategy(bidStrategyResponse.getProjectId());
        if (projectHotelTendStrategyResponse != null && projectHotelTendStrategyResponse.getData() != null) {
            ProjectHotelTendStrategy TendStrategy = objectMapper.convertValue(projectHotelTendStrategyResponse.getData(), ProjectHotelTendStrategy.class);
            if (TendStrategy == null) {
                projectHotelBidStrategyFlag = true;
            }else {
                // 判断投标策略是否满足企业项目的策略
                // 承诺
                if (TendStrategy.getSupportPayAtHotel().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportPayAtHotel().equals(bidStrategyResponse.getSupportPayAtHotel())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("到店付款不满足采购策略");
                    }
                }
                if (TendStrategy.getSupportCoPay().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportCoPay().equals(bidStrategyResponse.getSupportCoPay())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("公司统一支付不满足采购策略");
                    }
                }
                if (TendStrategy.getSupportNoGuarantee().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportNoGuarantee().equals(bidStrategyResponse.getSupportNoGuarantee())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("免担保不满足采购策略");
                    }
                }
                if (TendStrategy.getSupportCheckinInfo().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportCheckinInfo().equals(bidStrategyResponse.getSupportCheckinInfo())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("入住明细信息不满足采购策略");
                    }
                }
                if (TendStrategy.getSupportPayEarlyCheckout().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportPayEarlyCheckout().equals(bidStrategyResponse.getSupportPayEarlyCheckout())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("提前离店付款不满足采购策略");
                    }
                }
                // 入离政策
                if (TendStrategy.getSupportEarlyCheckin().equals(StateEnum.Effective.key)) {
                    if (Integer.valueOf(TendStrategy.getSupportEarlyCheckinTime().substring(0, 2)) < Integer.valueOf(bidStrategyResponse.getEarlyCheckinTime().substring(0, 2))) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("最早入住时间不满足采购策略");
                    }
                }
                if (TendStrategy.getSupportLateCheckout().equals(StateEnum.Effective.key)) {
                    if (Integer.valueOf(TendStrategy.getSupportLateCheckoutTime().substring(0, 2)) > Integer.valueOf(bidStrategyResponse.getLateCheckoutTime().substring(0, 2))) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("最晚退房时间不满足采购策略");
                    }
                }
                /**
                 // 结算信息
                 if (TendStrategy.getSupportMonthlyBalance().equals(StateEnum.Effective.key)) {
                 if (!TendStrategy.getSupportMonthlyBalance().equals(bidStrategyResponse.getSupportMonthlyBalance())) {
                 projectHotelBidStrategyFlag = false;
                 msgList.add("是否须支持月结不满足采购策略");
                 }
                 }
                 // 佣金信息
                 if (TendStrategy.getSupportIncludeCommission().equals(StateEnum.Effective.key)) {
                 BigDecimal tendCommission = TendStrategy.getTendCommission();
                 BigDecimal bidCommission = bidStrategyResponse.getTendCommission();
                 if (bidCommission == null || tendCommission.compareTo(bidCommission) > 0) {
                 projectHotelBidStrategyFlag = false;
                 msgList.add("佣金信息不满足采购策略");
                 }
                 }
                 **/
                // 发票信息
                if (TendStrategy.getProvideInvoice().equals(StateEnum.Effective.key)) {
                    if (!bidStrategyResponse.getProvideInvoiceType().equals(InvoiceEnum.COMMON.getKey()) && !bidStrategyResponse.getProvideInvoiceType().equals(InvoiceEnum.SPECIAL.getKey())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("发票信息不满足采购策略");
                    }
                }
                //酒店是否须支持可提供税率为6%增值税专用发票：1-是，0-否
                if (TendStrategy.getSupportVatInvoice().equals(StateEnum.Effective.key)) {
                    if (bidStrategyResponse.getProvideInvoiceType().equals(InvoiceEnum.SPECIAL.getKey())) {
                        BigDecimal provideInvoiceTaxRate = bidStrategyResponse.getProvideInvoiceTaxRate();
                        BigDecimal bigDecimal = new BigDecimal(6);
                        if (!provideInvoiceTaxRate.equals(bigDecimal)) {
                            projectHotelBidStrategyFlag = false;
                            msgList.add("报价需要提供税率为6%增值税专用发票");
                        }
                    // 酒店必须提供6%专票
                    } else {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("报价需要提供税率为6%增值税专用发票");
                    }
                }
                // 酒店报价包含税费和服务费
                if (TendStrategy.getSupportIncludeTaxService().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportIncludeTaxService().equals(bidStrategyResponse.getSupportIncludeTaxService())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("酒店报价不包含税费和服务费");
                    }
                }
                // 酒店房间提供免费WIFI服务
                if (TendStrategy.getSupportWifi().equals(StateEnum.Effective.key)) {
                    if (!TendStrategy.getSupportWifi().equals(bidStrategyResponse.getSupportWifi())) {
                        projectHotelBidStrategyFlag = false;
                        msgList.add("酒店报价不满足提供免费WIFI服务");
                    }
                }

                //酒店报价中产品不适用日期数总和不能超过N天
                if(TendStrategy.getSupportMaxUnapplicableDay() != null && TendStrategy.getSupportMaxUnapplicableDay() == 1
                        && TendStrategy.getMaxUnapplicableDay() != null && TendStrategy.getMaxUnapplicableDay().intValue()< totalUnapplicableDays){
                    projectHotelBidStrategyFlag = false;
                    msgList.add("酒店报价中产品不适用日期数总和不能超过"+TendStrategy.getMaxUnapplicableDay()+"天");
                }

                //酒店报价中产品适用日期数总和不能超过N天
                if(TendStrategy.getSupportSeasonDayLimit() != null && TendStrategy.getSupportSeasonDayLimit() == 1 &&
                        TendStrategy.getMaxSeasonDay() != null &&
                        TendStrategy.getMaxSeasonDay().intValue()< totalApplicableDays){
                    projectHotelBidStrategyFlag = false;
                    msgList.add("酒店按Season报价天数总和不能超过"+TendStrategy.getMaxSeasonDay()+"天");
                }


                if (!CollectionUtils.isEmpty(projectHotelPriceLevelResponseList)) {
                    projectHotelPriceService.isSatisfyProjectTender(bidStrategyResponse.getProjectId(), projectHotelPriceLevelResponseList, response);
                    if (response.getData() != null) {
                        projectHotelBidStrategyFlag = false;
                        msgList.addAll((List)response.getData());
                    }
                }

                // 全周日期策略检查
                if(TendStrategy.getIsIncludeAllWeeklyDay() == RfpConstant.constant_1){
                    boolean isIncludeAllWeeklyDay = true;
                    for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : projectHotelPriceLevelResponseList){
                        List<ProjectHotelPriceGroupResponse> groupResponseList = projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList();
                        for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : groupResponseList){
                            Set<Integer> groupWeeks = new HashSet<>();
                            String[] weekStrArray = projectHotelPriceGroupResponse.getApplicableWeeks().split(",");
                            for(String weekday : weekStrArray){
                                groupWeeks.add(Integer.valueOf(weekday));
                            }
                            if(groupWeeks.size() != 7){
                                projectHotelBidStrategyFlag = false;
                                isIncludeAllWeeklyDay = false;
                                msgList.add("企业要求酒店报价必须为全周适用价");
                                break;
                            }
                        }
                        if(!isIncludeAllWeeklyDay){
                            break;
                        }
                    }
                }

                // 是否包含营业执照
                if(Objects.equals(TendStrategy.getIsIncludeSubjectCert(), RfpConstant.constant_1)){
                    if(bidStrategyResponse.getHotelSubjectId() == null){
                        projectHotelBidStrategyFlag = false;
                        msgList.add("未选择酒店签约主体");
                    } else {
                        OrgSubject orgSubject = orgSubjectDao.selectByPrimaryKey(bidStrategyResponse.getHotelSubjectId());
                        if (orgSubject == null || StringUtils.isEmpty(orgSubject.getCertUrl())) {
                            projectHotelBidStrategyFlag = false;
                            msgList.add("酒店签约主体营业执照未上传");
                        }
                    }
                }
                if(Objects.equals(TendStrategy.getIsIncludeSpecialTradesCert(), RfpConstant.constant_1) ||
                        Objects.equals(TendStrategy.getIsIncludeHygieneCert(), RfpConstant.constant_1) ||
                        Objects.equals(TendStrategy.getIsIncludeFireSafetyCert(), RfpConstant.constant_1)
                ){
                    List<HotelRelatedCert> hotelRelatedCertList  = hotelRelatedCertService.queryListByHotelId(bidStrategyResponse.getHotelId());
                    Map<Integer, HotelRelatedCert>hotelRelatedCertMap = hotelRelatedCertList.stream().collect(Collectors.toMap(HotelRelatedCert::getCertType, Function.identity()));
                    if(Objects.equals(TendStrategy.getIsIncludeSpecialTradesCert(), RfpConstant.constant_1)){
                        HotelRelatedCert hotelRelatedCert = hotelRelatedCertMap.get(HotelCertTypeEnum.SPECIAL_TRADES.key);
                        if(hotelRelatedCert == null || StringUtils.isEmpty(hotelRelatedCert.getCertUrl())){
                            projectHotelBidStrategyFlag = false;
                            msgList.add("酒店特种行业经营许可证未上传，请上传证照后再提交");
                        }
                    }
                    if(Objects.equals(TendStrategy.getIsIncludeHygieneCert(), RfpConstant.constant_1)){
                        HotelRelatedCert hotelRelatedCert = hotelRelatedCertMap.get(HotelCertTypeEnum.HYGIENE_LICENSE.key);
                        if(hotelRelatedCert == null || StringUtils.isEmpty(hotelRelatedCert.getCertUrl())){
                            projectHotelBidStrategyFlag = false;
                            msgList.add("酒店卫生许可证未上传，请上传证照后再提交");
                        }
                    }
                    if(Objects.equals(TendStrategy.getIsIncludeFireSafetyCert(), RfpConstant.constant_1)){
                        HotelRelatedCert hotelRelatedCert = hotelRelatedCertMap.get(HotelCertTypeEnum.FIRE_SAFETY.key);
                        if(hotelRelatedCert == null || StringUtils.isEmpty(hotelRelatedCert.getCertUrl())){
                            projectHotelBidStrategyFlag = false;
                            msgList.add("酒店消防合格证未上传，请上传证照后再提交");
                        }
                    }
                }
            }
        }

        //自定义采购策略
        QueryCustomTendStrategyRequest queryCustomTendStrategyRequest = new QueryCustomTendStrategyRequest();
        queryCustomTendStrategyRequest.setProjectId(bidStrategyResponse.getProjectId());
        List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses = projectCustomTendStrategyDao.queryProjectCustomTendStrategy(queryCustomTendStrategyRequest);
        if(CollectionUtils.isNotEmpty(queryCustomTendStrategyResponses)){
            Map<Long,ProjectCustomBidStrategy> projectCustomBidStrategyMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(bidStrategyResponse.getProjectCustomBidStrategies())){
                for (ProjectCustomBidStrategy projectCustomBidStrategy : bidStrategyResponse.getProjectCustomBidStrategies()) {
                    projectCustomBidStrategyMap.put(projectCustomBidStrategy.getCustomTendStrategyId(),projectCustomBidStrategy);
                }
            }
            for (QueryCustomTendStrategyResponse queryCustomTendStrategyRespons : queryCustomTendStrategyResponses) {
                Long customTendStrategyId = queryCustomTendStrategyRespons.getCustomTendStrategyId();
                Integer supportStrategyName = queryCustomTendStrategyRespons.getSupportStrategyName();
                String strategyName = queryCustomTendStrategyRespons.getStrategyName();
                if(queryCustomTendStrategyRespons.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key && supportStrategyName != null && supportStrategyName == 1){
                    if(projectCustomBidStrategyMap.containsKey(customTendStrategyId)){
                        ProjectCustomBidStrategy projectCustomBidStrategy = projectCustomBidStrategyMap.get(customTendStrategyId);
                        if(projectCustomBidStrategy.getSupportStrategyName() == null || projectCustomBidStrategy.getSupportStrategyName() != 1){
                            projectHotelBidStrategyFlag = false;
                            msgList.add(strategyName +":需支持");
                        }
                    }else{
                        projectHotelBidStrategyFlag = false;
                        msgList.add(strategyName +":需支持");
                    }
                }
            }
        }
        if (projectHotelBidStrategyFlag) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        }else {
            response.setData(msgList);
            ArrayList<String> strings = new ArrayList<>();
            List list = objectMapper.convertValue(response.getData(), List.class);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Object o : list) {
                    strings.add(o.toString());
                }
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                response.setData(strings);
            }
        }
        return response;
    }

    @Override
    public Response selectProjectConcatTemplateList(ProjectHotelBidStrategyRequest projectHotelBidStrategyRequest) {
        Response response = new Response();
        ArrayList<ProjectConcatTemplateResponse> projectConcatTemplateResponseArrayList = new ArrayList<>();
        /**
         * 指定项目合同：
         * 平台通用模板：机构ID为0，模板类型为公共，业务类型为差旅
         * 自定义模板：企业机构ID，模板类型为自定义模板，业务类型为差旅
         *
         * 统一支付服务商：
         * 自己付款：无
         * 第三方：机构ID为统一支付ID，模板类型为自定义    业务类型为公付
         */
        Response projectBasicInformation = projectService.queryProjectBasicInformation(projectHotelBidStrategyRequest.getProjectId());
        ObjectMapper objectMapper = new ObjectMapper();
        ProjectBasicInfoResponse projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);

        // 判断是公共模板还是自定义模板
        ContractTemplateRequest contractTemplateRequest = new ContractTemplateRequest();

        List<ContractTemplateDto> contractTemplateDtos = null;
        if (projectBasicInfoResponse.getContractTemplateType().equals(ContractTemplateTypeEnum.COMMON_TEMPLATE.key)) {
            contractTemplateRequest.setOrgId(0L);
            contractTemplateRequest.setTemplateType(ContractTemplateTypeEnum.COMMON_TEMPLATE.key);
            contractTemplateRequest.setTemplateBizType(TemplateBizTypeEnum.HOTEL_TRAVEL.key);
            contractTemplateDtos = contractTemplateDao.queryContractTemplateList(contractTemplateRequest);
        } else if (projectBasicInfoResponse.getContractTemplateType().equals(ContractTemplateTypeEnum.CUSTOMIZE_TEMPLATE.key)) {
            contractTemplateRequest.setOrgId(projectBasicInfoResponse.getTenderOrgId());
            contractTemplateRequest.setTemplateType(ContractTemplateTypeEnum.CUSTOMIZE_TEMPLATE.key);
            contractTemplateRequest.setTemplateBizType(TemplateBizTypeEnum.HOTEL_TRAVEL.key);
            contractTemplateDtos = contractTemplateDao.queryContractTemplateList(contractTemplateRequest);
        }
        if (!CollectionUtils.isEmpty(contractTemplateDtos)) {
            ProjectConcatTemplateResponse travelHotelResponse = new ProjectConcatTemplateResponse();
            travelHotelResponse.setTemplateName("差旅酒店协议");
            travelHotelResponse.setMainTemplateUrl(contractTemplateDtos.get(0).getMainTemplateUrl());
            projectConcatTemplateResponseArrayList.add(travelHotelResponse);
        }

        // 判断是否选择公付服务
        if (CoPayerTypeEnum.THIRD_PARTY_PAYMENT.key.equals(projectBasicInfoResponse.getCoPayerType())) {
            // 根据公付机构ID查合同模板
            contractTemplateRequest.setOrgId(projectBasicInfoResponse.getCoPayerOrgId());
            contractTemplateRequest.setTemplateType(ContractTemplateTypeEnum.CUSTOMIZE_TEMPLATE.key);
            contractTemplateRequest.setTemplateBizType(TemplateBizTypeEnum.CO_PAY.key);
            List<ContractTemplateDto> coPayContractTemplateDtos = contractTemplateDao.queryContractTemplateList(contractTemplateRequest);
            if (CollectionUtils.isNotEmpty(coPayContractTemplateDtos)) {
                ProjectConcatTemplateResponse coPayResponse = new ProjectConcatTemplateResponse();
                coPayResponse.setTemplateName("公付协议");
                coPayResponse.setMainTemplateUrl(coPayContractTemplateDtos.get(0).getMainTemplateUrl());
                projectConcatTemplateResponseArrayList.add(coPayResponse);
            }
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(projectConcatTemplateResponseArrayList);
        return response;
    }

    @Override
    public void downloadHotelGroupBidTemplate(HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        InputStream inputStreamInfo = null;
        try {
            inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath ,"hotelGroupBidTemplate.xlsx");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename="+new String("酒店集团报价导入模板.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("酒店集团报价导入模板下载异常", e);
        } finally {
            if (inputStreamInfo != null) {
                try {
                    inputStreamInfo.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error("关闭文件转数据流失败", e);
                }
            }
        }
    }

    @Override
    public void downloadHotelGroupStandBidTemplate(HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        InputStream inputStreamInfo = null;
        try {
            inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath ,"hotelGroupStandBidTaskTemplate.xlsx");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename="+new String("酒店集团标准报价导入模板.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("酒店集团标准报价导入模板下载异常", e);
        } finally {
            if (inputStreamInfo != null) {
                try {
                    inputStreamInfo.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error("关闭文件转数据流失败", e);
                }
            }
        }
    }

    public void downloadLanyonBidTemplate(HttpServletResponse httpServletResponse){
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setContentType("application/octet-stream");

        // 转成数据流
        InputStream inputStreamInfo = null;
        try {
            inputStreamInfo = FtpAssistUtil.readFile(FileTypeAndPathEnum.IMPORT_EXCEL_TEMPLATE.filePath ,"lanyonBidTemplate.xlsx");
            httpServletResponse.setHeader("Content-disposition", "attachment;filename="+new String("Lanyon报价导入模板.xlsx".getBytes(), StandardCharsets.ISO_8859_1));
            IOUtils.copy(inputStreamInfo, httpServletResponse.getOutputStream());
            httpServletResponse.flushBuffer();
        } catch (Exception e) {
            logger.error("Lanyon报价导入模板下载异常", e);
        } finally {
            if (inputStreamInfo != null) {
                try {
                    inputStreamInfo.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error("关闭文件转数据流失败", e);
                }
            }
        }
    }

    /**
     * 转换日期格式兼容 Lanyon 和 MYR RFP
     * @param cell
     * @return
     */
    private Date convertCellToDate(Cell cell){
        Date result = null;
        try {
            try {
                result = cell.getDateCellValue();
            } catch (Exception ex) {
                logger.error("convertCellToDate exception ", ex);
                String rawDateString = cell.getStringCellValue();
                String dateString = rawDateString;
                // 格式化 2024/1/1
                if(StringUtils.isNotEmpty(rawDateString) && rawDateString.contains("/")){
                    String[] dataStringArray = rawDateString.split("/");
                    int month = Integer.valueOf(dataStringArray[1]);
                    int day = Integer.valueOf(dataStringArray[2]);
                    dateString = dataStringArray[0] + "-" + String.format("%02d", month) + "-" + String.format("%02d", day);
                }
                result = DateUtil.stringToDate(dateString);
            }
        } catch (Exception ex){
            logger.error("convertCellToDate service exception ", ex);
            throw new ServiceException(String.valueOf(cell.getColumnIndex()), ex);
        }
        return result;
    }

    /**
     * 检查 Uom值是否合法
     * @param uom
     * @return
     */
    private boolean validateUom(String uom){
        if("P".equalsIgnoreCase(uom) || "F".equalsIgnoreCase(uom) || "N".equalsIgnoreCase(uom) || StringUtil.isEmpty(uom)){
            return true;
        }
        return false;
    }
    private boolean validateIsInclude(String isInclude){
        if("Y".equalsIgnoreCase(isInclude) || "N".equalsIgnoreCase(isInclude) || StringUtil.isEmpty(isInclude)){
            return true;
        }
        return false;
    }
    @Override
    public Response validateUploadLanyonBidData(MultipartFile uploadFile, String operator,
                                                Long projectId, Map<Long, ImportLanyonBidDto> importHotelDataMap,
                                                Map<Long, List<Map<String, String>>> lanyonImportDataMap, UserDTO userDTO) throws Exception {
        Response response = new Response();
        Project project = projectDao.selectByPrimaryKey(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目信息不存在");
            return response;
        }

        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());

        // 获得有多少行数据 不能多于10000行
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        logger.info("checkFormat rowSize=" + rowSize);
        if(rowSize<=1){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入数据为空");
            return response;
        }

        if (rowSize > 10001) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("最多上传10000个Lanyon酒店信息信息");
            return response;
        }
        //校验标题是否符合规范
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("上传文件为空");
            return response;
        }

        // （1）	校验模板是否是LANYON模板
        List<LanyonImportColumn> lanyonImportColumns = lanyonImportService.selectLanyonImportColumns();
        int k=0;
        List<String> unkonwnHeaderNameList = new ArrayList<>();
        Map<Integer, String> headerMap = new HashMap<>();
        for(LanyonImportColumn lanyonImportColumn : lanyonImportColumns){
            Cell cell = titleRow.getCell(k);
            if(cell == null){
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("导入模板中，"+ lanyonImportColumn.getColumnCode()+", 列未包含在已支持LANYON导入列表内，请新增后重新导入");
                return response;
            }
            String headName = cell.getStringCellValue();
            if (!lanyonImportColumn.getColumnCode().equals(headName)) {
                unkonwnHeaderNameList.add(headName);
            }
            headerMap.put(k, lanyonImportColumn.getColumnCode());
            k++;
        }
        if(CollectionUtils.isNotEmpty(unkonwnHeaderNameList)){
            logger.info("未知headNames： " + JSON.toJSONString(unkonwnHeaderNameList));
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入模板中，"+ JSON.toJSONString(unkonwnHeaderNameList) +", 未包含在已支持LANYON导入列表内，请删除后重新导入");
            return response;
        }

        // 查找项目自定义策略
        QueryCustomTendStrategyRequest queryCustomTendStrategyRequest = new QueryCustomTendStrategyRequest();
        queryCustomTendStrategyRequest.setProjectId(projectId);
        List<QueryCustomTendStrategyResponse> customTendStrategyResponseList = projectCustomTendStrategyDao.queryProjectCustomTendStrategy(queryCustomTendStrategyRequest);
        // 查找已经报价酒店
        List<ProjectIntentHotel> projectIntentHotelList = projectIntentHotelDao.selectByProjectId(projectId);
        Map<Long, ProjectIntentHotel> projectIntentHotelMap = projectIntentHotelList.stream().collect(Collectors.toMap(ProjectIntentHotel::getHotelId, Function.identity()));

        List<String> invalidMsgList = new ArrayList<>();
        Map<Long, List<PriceApplicableDay>> hotelPriceAppleDayMap = new HashMap<>();
        Map<Long, List<ImportLanyonBidDto>> importHotelDataListMap = new HashMap<>();
        Set<String> hotelBreakfastSet = new HashSet<>();
        for (int i = 1; i < rowSize; i++) {
            int columnIndex = 0;
            Row dataRow = sheet.getRow(i);
            try {
                if (dataRow == null) {
                    continue;
                }
                // 酒店编码
                String propCode = CommonUtil.getCellValue(dataRow.getCell(0));
                if(!StringUtil.isValidString(propCode)){
                    invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键PROPCODE项目信息，请补充后重新导入");
                    continue;
                }
                String internalHotelCode = CommonUtil.getCellValue(dataRow.getCell(1));
                if(!StringUtil.isValidString(internalHotelCode)){
                    invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键INTERNALHOTELCODE项目信息，请补充后重新导入");
                    continue;
                }

                // Amadeus集团代码
                String amadeusChaincode = CommonUtil.getCellValue(dataRow.getCell(52));
                String amadeusPropcode = CommonUtil.getCellValue(dataRow.getCell(53));
                if(StringUtil.isEmpty(amadeusChaincode)){
                    invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键AMADEUS_CHAINCODE项目信息，请补充后重新导入");
                    continue;
                }
                // Amadeus酒店代码
                if(StringUtil.isEmpty(amadeusPropcode)){
                    invalidMsgList.add("导入模板，第"+ (i+1) + "行少关键AMADEUS_PROPCODE项目信息，请补充后重新导入");
                    continue;
                }
                String amadeusCode = amadeusChaincode+amadeusPropcode;
                Long hotelId = amadeusService.getHotelIdByAmadeusCode(amadeusCode);
                //Long hotelId = Long.valueOf(amadeusPropcode);
                if(hotelId == null){
                    invalidMsgList.add("导入模板，第"+ (i+1) + "酒店编号" + amadeusChaincode + "," + amadeusPropcode + "未匹配到报价系统酒店ID");
                    continue;
                }

                // （3）	校验关键信息是否为空
                List<String> nullCodeList = new ArrayList<>();
                String rfpName =  CommonUtil.getCellValue(dataRow.getCell(236));
                if(!StringUtil.isValidString(rfpName)){
                    nullCodeList.add("RFP_NAME");
                }
                String rfpPhone =  CommonUtil.getCellValue(dataRow.getCell(240));
                if(!StringUtil.isValidString(rfpPhone)){
                    nullCodeList.add("RFP_PHONE");
                }
                String rfpEmail =  CommonUtil.getCellValue(dataRow.getCell(241));
                if(!StringUtil.isValidString(rfpEmail)){
                    nullCodeList.add("RFP_EMAIL");
                }
                String roomType1Define = CommonUtil.getCellValue(dataRow.getCell(66));
                if(!StringUtil.isValidString(roomType1Define)){
                    nullCodeList.add("ROOMTYPE1DEFINE");
                }
                //Date season1Start
                if(dataRow.getCell(72) == null){
                    nullCodeList.add("SEASON1START");
                }
                //  Date season1End
                if(dataRow.getCell(73) == null){
                    nullCodeList.add("SEASON1END");
                }
                String cancPol = getCellCancPol(dataRow.getCell(179));
                if(!StringUtil.isValidString(cancPol)){
                    nullCodeList.add("CANC_POL");
                }
                String breakInclude = CommonUtil.getCellValue(dataRow.getCell(211));
                if(!StringUtil.isValidString(breakInclude)){
                    nullCodeList.add("BREAK_INCLUDE");
                }
                if(CollectionUtils.isNotEmpty(nullCodeList)){
                    String codes = "";
                    for(String code : nullCodeList){
                        codes = codes + code + ",";
                    }
                    codes = codes.substring(0,codes.length()-1);
                    invalidMsgList.add("第" + (i + 1) + "行缺少关键信息" + codes + "，请补充关键信息后，重新导入。");
                    continue;
                }
                // LRA_S1_RT1_SGL LRA_时段1_房型1_单人入住
                String lraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(74));
                // LRA_S1_RT1_DBL LRA_时段1_房型1_双人入住
                String lraS1Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(75));
                // NLRA_S1_RT1_SGL NLRA_时段1_房型1_单人入住
                String nlraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(80));
                // NLRA_S1_RT1_DBL NLRA_时段1_房型1_双人入住
                String nlraS1Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(81));
                if(StringUtil.isEmpty(lraS1Rt1Sgl) && StringUtil.isEmpty(lraS1Rt1Dbl) &&
                        StringUtil.isEmpty(nlraS1Rt1Sgl) && StringUtil.isEmpty(nlraS1Rt1Dbl)){
                    invalidMsgList.add("第" + (i + 1) + "行模板缺少SEASON1  RT1 关键报价信息，请补充模板后重新导入。");
                    continue;
                }

                // 检查酒店是否存在其他机构已经报价， 如果存在并且是中签，不允许导入
                ProjectIntentHotel intentHotel = projectIntentHotelMap.get(hotelId);

                if(intentHotel != null && (
                        Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.NEW_BID.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.BID_WINNING.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.UPDATED_BID.bidState) ||
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.REJECTED.bidState) || (
                                Objects.equals(intentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState) &&
                                        (intentHotel.getBidUploadSource() == null || !Objects.equals(intentHotel.getBidUploadSource(), BidUploadSourceEnum.LANYON.key))
                        )
                )) {
                    invalidMsgList.add("第" + (i + 1) + "行酒店在当前项目已有" + HotelBidStateEnum.getValueByKey(intentHotel.getBidState()) + "状态，请与酒店联系，或删除对应酒店导入信息后，重新导入");
                    continue;
                }

                // 初始化数据
                ImportLanyonBidDto importLanyonBidDto = new ImportLanyonBidDto();
                importLanyonBidDto.setHotelId(hotelId);
                importLanyonBidDto.setContactName(rfpName);
                importLanyonBidDto.setContactMobile(rfpPhone);
                importLanyonBidDto.setContactEmail(rfpEmail);

                // 房型描述
                importLanyonBidDto.setLevelRoomTypeDescMap(new HashMap<>());
                roomType1Define = roomType1Define.replaceAll("/",",");
                importLanyonBidDto.getLevelRoomTypeDescMap().put(RoomLevelEnum.ONE.key, roomType1Define);
                // ROOMTYPE2DEFINE
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(68)))){
                    String roomType2Define = CommonUtil.getCellValue(dataRow.getCell(68)).replaceAll("/",",");
                    importLanyonBidDto.getLevelRoomTypeDescMap().put(RoomLevelEnum.TWO.key, roomType2Define);
                }
                // ROOMTYPE3DEFINE
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(70)))){
                    String roomType3Define = CommonUtil.getCellValue(dataRow.getCell(70)).replaceAll("/",",");
                    importLanyonBidDto.getLevelRoomTypeDescMap().put(RoomLevelEnum.THREE.key, roomType3Define);
                }
                // ROOMTYPE1NUMBER
                importLanyonBidDto.setLevelRoomTypeNumberMap(new HashMap<>());
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(67)))) {
                    importLanyonBidDto.getLevelRoomTypeNumberMap().put(RoomLevelEnum.ONE.key, Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(67))));
                }
                // ROOMTYPE2NUMBER
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(69)))) {
                    importLanyonBidDto.getLevelRoomTypeNumberMap().put(RoomLevelEnum.TWO.key, Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(69))));
                }
                // ROOMTYPE3NUMBER
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(71)))) {
                    importLanyonBidDto.getLevelRoomTypeNumberMap().put(RoomLevelEnum.THREE.key, Integer.valueOf(CommonUtil.getCellValue(dataRow.getCell(71))));
                }

                // 协议价日期
                List<PriceApplicableDay> allPriceApplicableDayList = new ArrayList<>();
                // 基础协议价日期 SEASON1START
                //hotelLeveBaseDayMap.put(hotelId, baseDay);
                Date season1Start = convertCellToDate(dataRow.getCell(72));
                Date season1End = convertCellToDate(dataRow.getCell(73));
                PriceApplicableDay basePriceApplicableDay = convertSinglePriceApplicableDay(projectId, hotelId, season1Start, season1End, HotelPriceTypeEnum.BASE_PRICE.key);
                allPriceApplicableDayList.add(basePriceApplicableDay);

                // Season1 SEASON2START 日期
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(92))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(93)))) {
                    PriceApplicableDay season1PriceApplicableDay = convertSinglePriceApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(92)), convertCellToDate(dataRow.getCell(93)), HotelPriceTypeEnum.SEASON_1_PRICE.key);
                    allPriceApplicableDayList.add(season1PriceApplicableDay);
                }
                // Season2 SEASON3START 日期
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(112))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(113)))) {
                    PriceApplicableDay season2PriceApplicableDay = convertSinglePriceApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(112)), convertCellToDate(dataRow.getCell(113)), HotelPriceTypeEnum.SEASON_2_PRICE.key);
                    allPriceApplicableDayList.add(season2PriceApplicableDay);
                }
                hotelPriceAppleDayMap.put(hotelId, allPriceApplicableDayList);
                importLanyonBidDto.setPriceApplicableDayList(allPriceApplicableDayList);

                // 客户定制内容 243(客户定制内容1) -> 272(客户定制内容30)
                List<ProjectCustomBidStrategy> projectCustomBidStrategyList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(customTendStrategyResponseList)){
                    int index=0;
                    int colIndexBegin = 243;
                    int colIndexTo = 272;
                    for(QueryCustomTendStrategyResponse customTendStrategyResponse : customTendStrategyResponseList){
                        int colIndexFrom = colIndexBegin + index;
                        String supportStrategyValue = CommonUtil.getCellValue(dataRow.getCell(colIndexFrom));
                        if(StringUtil.isValidString(supportStrategyValue)) {
                            ProjectCustomBidStrategy projectCustomBidStrategy = new ProjectCustomBidStrategy();
                            projectCustomBidStrategy.setCustomTendStrategyId(customTendStrategyResponse.getCustomTendStrategyId());
                            projectCustomBidStrategy.setHotelId(hotelId);
                            projectCustomBidStrategy.setProjectId(projectId);
                            projectCustomBidStrategy.setStrategyName(customTendStrategyResponse.getStrategyName());
                            projectCustomBidStrategy.setSupportStrategyName(customTendStrategyResponse.getSupportStrategyName());
                            projectCustomBidStrategy.setStrategyType(customTendStrategyResponse.getStrategyType());
                            projectCustomBidStrategy.setCreator(userDTO.getOperator());
                            if(customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key &&
                                    (supportStrategyValue.equals("Y") || supportStrategyValue.equals("N"))){
                                projectCustomBidStrategy.setSupportStrategyName(supportStrategyValue.equals("Y") ? RfpConstant.constant_1 : RfpConstant.constant_0);
                                projectCustomBidStrategyList.add(projectCustomBidStrategy);
                            } else if(customTendStrategyResponse.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                                projectCustomBidStrategy.setSupportStrategyText(supportStrategyValue);
                                projectCustomBidStrategyList.add(projectCustomBidStrategy);
                            } else {
                                logger.info("Lanyon导入无效自定义策略: {}, {}", JSON.toJSONString(projectCustomBidStrategy), JSON.toJSONString(customTendStrategyResponse));
                            }
                        }
                        index++;
                        if(colIndexFrom >  colIndexTo){
                            break;
                        }
                    }
                    importLanyonBidDto.setProjectCustomBidStrategyList(projectCustomBidStrategyList);
                }

                // 不适应日期
                List<PriceUnapplicableDay> priceUnapplicableDayList = new ArrayList<>();
                // BD1_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(325))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(326)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId,  convertCellToDate(dataRow.getCell(325)), convertCellToDate(dataRow.getCell(326))));
                }
                // BD2_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(334))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(335)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(334)), convertCellToDate(dataRow.getCell(335))));
                }
                // BD3_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(343))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(344)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(343)), convertCellToDate(dataRow.getCell(344))));
                }
                // BD4_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(352))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(353)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(352)), convertCellToDate(dataRow.getCell(353))));
                }
                // BD5_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(361))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(362)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(361)),  convertCellToDate(dataRow.getCell(362))));
                }
                // BD6_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(370))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(371)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(370)),  convertCellToDate(dataRow.getCell(371))));
                }
                // BD7_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(379))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(380)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(379)), convertCellToDate(dataRow.getCell(380))));
                }
                // BD8_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(388))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(389)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(388)), convertCellToDate(dataRow.getCell(389))));
                }
                // BD9_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(397))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(398)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(397)), convertCellToDate(dataRow.getCell(398))));
                }
                // BD10_START
                if(StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(406))) && StringUtil.isValidString(CommonUtil.getCellValue(dataRow.getCell(407)))) {
                    priceUnapplicableDayList.add(convertLanyonPriceUnApplicableDay(projectId, hotelId, convertCellToDate(dataRow.getCell(406)), convertCellToDate(dataRow.getCell(407))));
                }
                importLanyonBidDto.setUnApplicableDayList(priceUnapplicableDayList);

                // 解析房档价格信息
                Map<Integer, List<ImportLanyonHotelPriceGroupBidDto>> lavelPriceGroupMap = new HashMap<>();
                importLanyonBidDto.setLevelPriceGroupMap(lavelPriceGroupMap);

                // 检查早餐报价是否重复
                if(hotelBreakfastSet.contains(hotelId + breakInclude)){
                    invalidMsgList.add("第" + (i + 1) + "酒店报价重复：存在相同酒店是否包含早餐 (" +breakInclude+ ")，请修改导入信息后，重新导入" );
                    continue;
                }
                hotelBreakfastSet.add(hotelId + breakInclude);

                // 是否包含早餐
                boolean isIncludeBreakfast = "Y".equalsIgnoreCase(breakInclude);
                importLanyonBidDto.setIsIncludeBreakfast(isIncludeBreakfast ? RfpConstant.constant_1 : RfpConstant.constant_0);
                // Price 1 增值税
                boolean isIncludePrice1 = false;
                BigDecimal price1Fee = BigDecimal.ZERO;
                // 增值税 VATGSTRM_INCLUDE 含客房增值税 VATGSTRM_FEE
                String price1Include = CommonUtil.getCellValue(dataRow.getCell(194));
                if(!validateIsInclude(price1Include)){
                    invalidMsgList.add("第" + (i + 1) + "是否含客房增值税：" + price1Include +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price1Uom = CommonUtil.getCellValue(dataRow.getCell(193));
                if(!validateUom(price1Uom)){
                    invalidMsgList.add("第" + (i + 1) + "客房增值税计税方案：" + price1Uom +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price1FeeStr =  CommonUtil.getCellValue(dataRow.getCell(192));
                if(StringUtil.isValidString(price1Include) && StringUtil.isValidString(price1Uom) && StringUtil.isValidString(price1FeeStr)) {
                    isIncludePrice1 = "Y".equalsIgnoreCase(price1Include);
                    price1Fee = new BigDecimal(price1FeeStr);
                }

                // 含餐饮增值税
                boolean isIncludePrice2 = false;
                BigDecimal price2Fee = BigDecimal.ZERO;
                String price2Include = CommonUtil.getCellValue(dataRow.getCell(197));
                if(!validateIsInclude(price2Include)){
                    invalidMsgList.add("第" + (i + 1) + "含餐饮增值税：" + price2Include +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price2Uom = CommonUtil.getCellValue(dataRow.getCell(196));
                if(!validateUom(price2Uom)){
                    invalidMsgList.add("第" + (i + 1) + "餐饮增值税计税方案：" + price2Uom +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price2FeeStr = CommonUtil.getCellValue(dataRow.getCell(195));
                if(StringUtil.isValidString(price2Include) && StringUtil.isValidString(price2Uom) && StringUtil.isValidString(price2FeeStr)) {
                    isIncludePrice2 = "Y".equalsIgnoreCase(price2Include);
                    price2Fee = new BigDecimal(price2FeeStr);
                }

                // 服务费 SERVICE_INCLUDE
                boolean isIncludePrice3 = false;
                BigDecimal price3Fee = BigDecimal.ZERO;
                String price3Include = CommonUtil.getCellValue(dataRow.getCell(200));
                if(!validateIsInclude(price3Include)){
                    invalidMsgList.add("第" + (i + 1) + "否含服务费：" + price3Include +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price3Uom = CommonUtil.getCellValue(dataRow.getCell(199));
                if(!validateUom(price3Uom)){
                    invalidMsgList.add("第" + (i + 1) + "服务费计税方案：" + price3Uom +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price3FeeStr = CommonUtil.getCellValue(dataRow.getCell(198));
                if(StringUtil.isValidString(price3Include) && StringUtil.isValidString(price3Uom) && StringUtil.isValidString(price3FeeStr)) {
                    isIncludePrice3 = "Y".equalsIgnoreCase(price3Include);
                    price3Fee = new BigDecimal(price3FeeStr);
                }

                // 含占费用 OCC_INCLUDE
                boolean isIncludePrice4 = false;
                BigDecimal price4Fee = BigDecimal.ZERO;
                String price4Include = CommonUtil.getCellValue(dataRow.getCell(203));
                if(!validateIsInclude(price4Include)){
                    invalidMsgList.add("第" + (i + 1) + "含占用费：" + price4Include +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price4Uom = CommonUtil.getCellValue(dataRow.getCell(202));
                if(!validateUom(price4Uom)){
                    invalidMsgList.add("第" + (i + 1) + "含占用费计税方案：" + price4Uom +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }

                // 税费加收数值（百分比或固定值)
                String price4FeeStr = CommonUtil.getCellValue(dataRow.getCell(201));
                if(StringUtil.isValidString(price4Include) && StringUtil.isValidString(price4Uom) && StringUtil.isValidString(price4FeeStr)) {
                    isIncludePrice4 = "Y".equalsIgnoreCase(price4Include);
                    price4Fee = new BigDecimal(price4FeeStr);
                }

                // 含占费用 OTHERTX_FEE_INCL
                boolean isIncludePrice5 = false;
                BigDecimal price5Fee = BigDecimal.ZERO;
                String price5Include = CommonUtil.getCellValue(dataRow.getCell(207));
                if(!validateIsInclude(price5Include)){
                    invalidMsgList.add("第" + (i + 1) + "含其他税费：" + price5Include +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }

                // 计税方案 税费计算方案  P 表示加百分比  F 表示加固定值
                String price5Uom = CommonUtil.getCellValue(dataRow.getCell(205));
                if(!validateUom(price5Uom)){
                    invalidMsgList.add("第" + (i + 1) + "其他税费计税方案：" + price5Uom +"不合法，请修改导入信息后，重新导入" );
                    continue;
                }
                // 税费加收数值（百分比或固定值)
                String price5FeeStr = CommonUtil.getCellValue(dataRow.getCell(204));
                if(StringUtil.isValidString(price5Include) && StringUtil.isValidString(price5Uom) && StringUtil.isValidString(price5FeeStr)) {
                    isIncludePrice5 = "Y".equalsIgnoreCase(price5Include);
                    price5Fee = new BigDecimal(price5FeeStr);
                }

                // 取消政策 CANC_POL
                String cancelRestrictTime = "";
                if(cancPol.contains("AM")){
                    cancelRestrictTime = cancPol.substring(0, cancPol.length() - 2);
                    if(cancelRestrictTime.length() == 1) {
                        cancelRestrictTime = "0" + cancelRestrictTime;
                    }
                    cancelRestrictTime = cancelRestrictTime + ":00";
                } else if(cancPol.contains("PM")){
                    cancelRestrictTime = cancPol.substring(0, cancPol.length() - 2);
                    cancelRestrictTime = String.valueOf(Integer.valueOf(cancelRestrictTime) + 12);
                    cancelRestrictTime = cancelRestrictTime + ":00";
                } else if (cancPol.length() > 6){ //兼容18:00:00 格式
                    cancelRestrictTime = cancPol.substring(0,5);
                }

                // LRA_S1_RT1_SGL LRA_时段1_房型1_单人入住
                if(StringUtil.isValidString(lraS1Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }

                // 房档1 Season1价格 LRA_S2_RT1_SGL LRA_时段2_房型1_单人入住
                String lraS2Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(94));
                if(StringUtil.isValidString(lraS2Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT1_SGL 时段3_房型1_单人入住
                String lraS3Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(114));
                if(StringUtil.isValidString(lraS3Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }

                // LRA_S1_RT1_DBL Leve1 LRA_时段1_房型1_双人入住
                if(StringUtil.isValidString(lraS1Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt1Dbl), isIncludeBreakfast, "双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S2_RT1_DBL Leve1 LRA_时段2_房型1_双人入住
                String lraS2Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(95));
                if(StringUtil.isValidString(lraS2Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt1Dbl), isIncludeBreakfast, "双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT1_DBL Leve1 LRA_时段3_房型1_双人入住
                String lraS3Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(115));
                if(StringUtil.isValidString(lraS3Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt1Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S1_RT1_SGL 房档1+NLRA+单人入住
                //String nlraS1Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(80));
                if(StringUtil.isValidString(nlraS1Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }

                // NLRA_S2_RT1_SGL 房档1+NLRA+单人入住
                String nlraS2Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(100));
                if(StringUtil.isValidString(nlraS2Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT1_SGL 房档1+NLRA+单人入住
                String nlraS3Rt1Sgl = CommonUtil.getCellValue(dataRow.getCell(120));
                if(StringUtil.isValidString(nlraS3Rt1Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt1Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S1_RT1_DBL 房档1+NLRA+双人入住
                if(StringUtil.isValidString(nlraS1Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt1Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S2_RT1_DBL 房档1+NLRA+双人入住
                String nlraS2Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(100));
                if(StringUtil.isValidString(nlraS2Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt1Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT1_DBL 房档1+NLRA+双人入住
                String nlraS3Rt1Dbl = CommonUtil.getCellValue(dataRow.getCell(121));
                if(StringUtil.isValidString(nlraS3Rt1Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.ONE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt1Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 房档2
                // LRA_S1_RT2_SGL LRA_时段1_房型2_单人入住
                String lraS1Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(76));
                if(StringUtil.isValidString(lraS1Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S2_RT2_SGL LRA_时段3_房型2_单人入住
                String lraS2Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(96));
                if(StringUtil.isValidString(lraS2Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT2_SGL LRA_时段3_房型2_单人入住
                String lraS3Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(116));
                if(StringUtil.isValidString(lraS3Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S1_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS1Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(77));
                if(StringUtil.isValidString(lraS1Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S2_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS2Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(97));
                if(StringUtil.isValidString(lraS2Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT2_DBL // LRA_时段1_房型2_双人入住
                String lraS3Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(117));
                if(StringUtil.isValidString(lraS3Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 第三批次：房档2+NLRA+单人入住
                // NLRA_S1_RT2_SGL  NLRA_时段1_房型2_单人入住
                String nlraS1Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(82));
                if(StringUtil.isValidString(nlraS1Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S2_RT2_SGL  NLRA_时段2_房型2_单人入住
                String nlraS2Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(102));
                if(StringUtil.isValidString(nlraS2Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT2_SGL  NLRA_时段3_房型2_单人入住
                String nlraS3Rt2Sgl = CommonUtil.getCellValue(dataRow.getCell(122));
                if(StringUtil.isValidString(nlraS3Rt2Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt2Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                //第四批次：房档2+NLRA+双人入住
                //NLRA_S1_RT2_DBL，NLRA_S2_RT2_DBL，NLRA_S3_RT2_DBL
                String nlraS1Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(83));
                if(StringUtil.isValidString(nlraS1Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S2_RT2_DBL
                String nlraS2Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(103));
                if(StringUtil.isValidString(nlraS2Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT2_DBL
                String nlraS3Rt2Dbl = CommonUtil.getCellValue(dataRow.getCell(123));
                if(StringUtil.isValidString(nlraS3Rt2Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.TWO.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt2Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 房档三
                // 第一批次：房档3+LRA+单人入住
                //LRA_S1_RT3_SGL，LRA_S2_RT3_SGL，LRA_S3_RT3_SGL
                String lraS1Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(78));
                if(StringUtil.isValidString(lraS1Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt3Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S2_RT3_SGL
                String lraS2Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(98));
                if(StringUtil.isValidString(lraS2Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt3Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT3_SGL
                String lraS3Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(118));
                if(StringUtil.isValidString(lraS3Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt3Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 第二批次：房档3+LRA+双人入住
                //LRA_S1_RT3_DBL，LRA_S2_RT3_DBL，LRA_S3_RT3_DBL
                String lraS1Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(79));
                if(StringUtil.isValidString(lraS1Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(lraS1Rt3Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S2_RT3_DBL
                String lraS2Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(99));
                if(StringUtil.isValidString(lraS2Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(lraS2Rt3Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // LRA_S3_RT3_DBL
                String lraS3Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(119));
                if(StringUtil.isValidString(lraS3Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_1,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(lraS3Rt3Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                //第三批次：房档3+NLRA+单人入住
                //NLRA_S1_RT3_SGL，NLRA_S2_RT3_SGL，NLRA_S3_RT3_SGL
                String nlraS1Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(84));
                if(StringUtil.isValidString(nlraS1Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt3Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S2_RT3_SGL
                String nlraS2Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(104));
                if(StringUtil.isValidString(nlraS2Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt3Sgl), isIncludeBreakfast,"单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT3_SGL
                String nlraS3Rt3Sgl = CommonUtil.getCellValue(dataRow.getCell(124));
                if(StringUtil.isValidString(nlraS3Rt3Sgl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.ONE.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt3Sgl),isIncludeBreakfast, "单人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 第四批次：房档3+NLRA+双人入住
                //NLRA_S1_RT3_DBL，NLRA_S2_RT3_DBL，NLRA_S3_RT3_DBL
                String nlraS1Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(85));
                if(StringUtil.isValidString(nlraS1Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.BASE_PRICE.key, new BigDecimal(nlraS1Rt3Dbl), isIncludeBreakfast,"双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S2_RT3_DBL
                String nlraS2Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(105));
                if(StringUtil.isValidString(nlraS2Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_1_PRICE.key, new BigDecimal(nlraS2Rt3Dbl),isIncludeBreakfast, "双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // NLRA_S3_RT3_DBL
                String nlraS3Rt3Dbl = CommonUtil.getCellValue(dataRow.getCell(125));
                if(StringUtil.isValidString(nlraS3Rt3Dbl)) {
                    addLevelPrice(importLanyonBidDto,
                            RoomLevelEnum.THREE.key,
                            isIncludeBreakfast ? BreakfastNumEnum.TWO.key : BreakfastNumEnum.ZERO.key,cancelRestrictTime, RfpConstant.constant_0,
                            HotelPriceTypeEnum.SEASON_2_PRICE.key, new BigDecimal(nlraS3Rt3Dbl),isIncludeBreakfast, "双人入住",
                            isIncludePrice1, price1Uom, price1Fee,
                            isIncludePrice2, price2Uom, price2Fee,
                            isIncludePrice3, price3Uom, price3Fee,
                            isIncludePrice4, price4Uom, price4Fee,
                            isIncludePrice5, price5Uom, price5Fee
                    );
                }
                // 增加酒店报价
                if(!importHotelDataListMap.containsKey(hotelId)) {
                    importHotelDataListMap.put(hotelId, new ArrayList<>());
                }
                importHotelDataListMap.get(hotelId).add(importLanyonBidDto);
                Map<String, String> hotelLanyonDataMap = new HashMap<>();
                // 增加lanyon 原始数据
                for(Integer index : headerMap.keySet()){
                    String code = headerMap.get(index);
                    String value = CommonUtil.getCellValue2(dataRow.getCell(index));
                    if(code.equals("CANC_POL")){
                        value = getCellCancPol(dataRow.getCell(index));
                    }
                    hotelLanyonDataMap.put(code, value);
                }
                if(!lanyonImportDataMap.containsKey(hotelId)){
                    lanyonImportDataMap.put(hotelId, new ArrayList<>());
                }
                lanyonImportDataMap.get(hotelId).add(hotelLanyonDataMap);
            } catch (ServiceException serviceException) {
                logger.error("导入酒店集团报价服务异常：", serviceException);
                invalidMsgList.add("第" + (i + 1) + "行" + serviceException.getMessage() + "列导入异常");
            } catch (Exception e) {
                logger.error("导入酒店集团报价异常：" ,e);
                invalidMsgList.add("第"+ (i+1) + "行导入异常");
            }
            // 多余200错误数据，不检查
            if(invalidMsgList.size() > 200){
                break;
            }
        }

        // 合并酒店包含无早和含早的报价数据
        if(invalidMsgList.size() == 0) {
            for (Long hotelId : importHotelDataListMap.keySet()) {
                List<ImportLanyonBidDto> importHotelDataList = importHotelDataListMap.get(hotelId);
                int hotelIndex = 0;
                try {
                    for (ImportLanyonBidDto importHotelData : importHotelDataList) {
                        if (hotelIndex == 0) {
                            importHotelDataMap.put(hotelId, importHotelData);
                        } else if (hotelIndex == 1) {
                            // 合并报价
                            Map<Integer, List<ImportLanyonHotelPriceGroupBidDto>> newLevelPriceGroupMap = new HashMap<>();
                            ImportLanyonBidDto firstImportLanyonBidDto = importHotelDataMap.get(hotelId);
                            if (!Objects.equals(firstImportLanyonBidDto.getIsIncludeBreakfast(), importHotelData.getIsIncludeBreakfast())) {
                                Map<Integer, List<ImportLanyonHotelPriceGroupBidDto>> levelPriceGroupMap1 = firstImportLanyonBidDto.getLevelPriceGroupMap();
                                Map<Integer, List<ImportLanyonHotelPriceGroupBidDto>> levelPriceGroupMap2 = importHotelData.getLevelPriceGroupMap();
                                Set<Integer> roomLevelNoSet = new HashSet<>();
                                roomLevelNoSet.addAll(levelPriceGroupMap1.keySet());
                                roomLevelNoSet.addAll(levelPriceGroupMap2.keySet());
                                roomLevelNoSet = roomLevelNoSet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
                                for (Integer roomLevelNo : roomLevelNoSet) {
                                    List<ImportLanyonHotelPriceGroupBidDto> levelPriceGroupList1 = levelPriceGroupMap1.get(roomLevelNo);
                                    List<ImportLanyonHotelPriceGroupBidDto> levelPriceGroupList2 = levelPriceGroupMap2.get(roomLevelNo);
                                    // 存在一个为空 直接使用不为空的报价
                                    if (CollectionUtils.isNotEmpty(levelPriceGroupList1) && CollectionUtils.isEmpty(levelPriceGroupList2)) {
                                        newLevelPriceGroupMap.put(roomLevelNo, levelPriceGroupList1);
                                    } else if (CollectionUtils.isEmpty(levelPriceGroupList1) && CollectionUtils.isNotEmpty(levelPriceGroupList2)) {
                                        newLevelPriceGroupMap.put(roomLevelNo, levelPriceGroupList2);
                                    } else {
                                        // 如果都不为空合并报价
                                        Map<String, ImportLanyonHotelPriceGroupBidDto> levelPriceGroup1Map = levelPriceGroupList1.stream().collect(Collectors.toMap(ImportLanyonHotelPriceGroupBidDto::generateKey, Function.identity()));
                                        Map<String, ImportLanyonHotelPriceGroupBidDto> levelPriceGroup2Map = levelPriceGroupList2.stream().collect(Collectors.toMap(ImportLanyonHotelPriceGroupBidDto::generateKey, Function.identity()));
                                        Map<String, ImportLanyonHotelPriceGroupBidDto> levelPriceGroupMap = new HashMap<>();
                                        Set<String> levelPriceGroupKeySet = new HashSet<>();
                                        levelPriceGroupKeySet.addAll(levelPriceGroup1Map.keySet());
                                        levelPriceGroupKeySet.addAll(levelPriceGroup2Map.keySet());
                                        for (String key : levelPriceGroupKeySet) {
                                            if (levelPriceGroup1Map.containsKey(key) && !levelPriceGroup2Map.containsKey(key)) {
                                                levelPriceGroupMap.put(key, levelPriceGroup1Map.get(key));
                                            } else if (!levelPriceGroup1Map.containsKey(key) && levelPriceGroup2Map.containsKey(key)) {
                                                levelPriceGroupMap.put(key, levelPriceGroup2Map.get(key));
                                            } else {
                                                // 合并报价到同一个group, 无早价格放前面
                                                ImportLanyonHotelPriceGroupBidDto levelPriceGroup1 = levelPriceGroup1Map.get(key);
                                                ImportLanyonHotelPriceGroupBidDto levelPriceGroup2 = levelPriceGroup2Map.get(key);
                                                String remark = "";
                                                if (firstImportLanyonBidDto.getIsIncludeBreakfast() == RfpConstant.constant_0) {
                                                    levelPriceGroupMap.put(key, levelPriceGroup1);
                                                    levelPriceGroupMap.get(key).getImportLanyonHotelPriceBidDtos().addAll(levelPriceGroup2.getImportLanyonHotelPriceBidDtos());
                                                    remark = "无早" + levelPriceGroup1.getRemark() + ";" + " 含早" + levelPriceGroup2.getRemark();
                                                } else {
                                                    levelPriceGroupMap.put(key, levelPriceGroup2);
                                                    levelPriceGroupMap.get(key).getImportLanyonHotelPriceBidDtos().addAll(levelPriceGroup1.getImportLanyonHotelPriceBidDtos());
                                                    remark = "无早" + levelPriceGroup2.getRemark() + ";" + " 含早" + levelPriceGroup1.getRemark();
                                                }
                                                String levelPriceGroup1Remark = levelPriceGroup1.getRemark();
                                                String levelPriceGroup2Remark = levelPriceGroup2.getRemark();
                                                if(!Objects.equals(levelPriceGroup1Remark, levelPriceGroup2Remark)){
                                                    levelPriceGroupMap.get(key).setRemark(remark);
                                                }

                                            }
                                        }
                                        newLevelPriceGroupMap.put(roomLevelNo, new ArrayList<>(levelPriceGroupMap.values()));
                                    }
                                }
                                // 设置合并后的报价
                                importHotelDataMap.get(hotelId).setLevelPriceGroupMap(newLevelPriceGroupMap);
                            } else {
                                invalidMsgList.add("存在相同酒店相同含早或者无早数据 " + hotelId);
                                logger.error("Lanyon导入数据错误，存在相同酒店相同含早或者无早数据 {}", hotelId);
                            }
                            // 最多允许两条
                        } else {
                            invalidMsgList.add("存在相同酒店相同含早或者无早数据 " + hotelId);
                            logger.error("Lanyon导入数据错误，存在相同酒店相同含早或者无早数据 {}", hotelId);
                        }
                        hotelIndex++;
                    }
                } catch (Exception ex) {
                    invalidMsgList.add("Lanyon导入数据错误，合并相同酒店早餐异常 " + hotelId);
                    logger.info("Lanyon导入数据错误，合并相同酒店早餐异常 "+ ExceptionUtility.getDetailedExceptionString(ex));
                    logger.error("Lanyon导入数据错误，合并相同酒店早餐异常", ex);
                }
            }
        }

        if(!invalidMsgList.isEmpty()){
            logger.error("检查失败信息：{}", JSON.toJSONString(invalidMsgList));
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(invalidMsgList);
        return response;
    }



    // 格式话**
    private String formatStatData(String statData){
        if(statData.length() <= 2){
            return "**";
        }
        StringBuffer result = new StringBuffer();
        result.append(statData.substring(0,1));
        String subString = statData.substring(1, statData.length());
        for(int i=0; i<10; i++){
            subString = subString.replaceAll(String.valueOf(i), "*");
        }
        result.append(subString);
        return result.toString();

    }

    @Override
    public Response rejectNegotiation(RejectNegotiationRequest rejectNegotiationRequest, UserDTO userDTO){
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);

        // 查询报价
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(rejectNegotiationRequest.getProjectId(), rejectNegotiationRequest.getHotelId());
        if(projectIntentHotel == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("未找到酒店报价");
        } else if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.UNDER_NEGOTIATION.bidState)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("不是议价中报价不能拒绝报价");
        }
        ProjectIntentHotel updateProjectIntentHotel = new ProjectIntentHotel();
        updateProjectIntentHotel.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
        updateProjectIntentHotel.setBidState(HotelBidStateEnum.REJECT_NEGOTIATION.bidState);
        updateProjectIntentHotel.setRejectNegotiationRemark(rejectNegotiationRequest.getRejectNegotiationRemark());
        updateProjectIntentHotel.setModifier(userDTO.getOperator());
        int updateRecord = projectIntentHotelDao.updateProjectIntentHotelBidState(updateProjectIntentHotel);
        if(updateRecord > 0){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);

            // 记录操作日志
            String operateContent = "签约状态更新为 " + HotelBidStateEnum.REJECT_NEGOTIATION.value + " ，留言：" + updateProjectIntentHotel.getRejectNegotiationRemark();
            bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, operateContent);

            // 记录备注日志
            ProjectHotelRemark projectHotelRemark = new ProjectHotelRemark();
            projectHotelRemark.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            projectHotelRemark.setProjectId(projectIntentHotel.getProjectId());
            projectHotelRemark.setHotelId(projectIntentHotel.getHotelId());
            projectHotelRemark.setRemarkType(HotelBidStateEnum.getNameByKey(HotelBidStateEnum.REJECT_NEGOTIATION.bidState));
            projectHotelRemark.setRemark(rejectNegotiationRequest.getRejectNegotiationRemark());
            projectHotelRemark.setCreator(userDTO.getOperator());
            projectHotelRemarkDao.insert(projectHotelRemark);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg(ReturnResultEnum.FAILED.message);
        }

        return response;
    }

    @Override
    public boolean isBidWhiteHotel(Long projectId, Long hotelId) {
        boolean result = false;
        //校验报价酒店是否在白名单，在白名单中的酒店不受采购策略限制，无需进行采购策略校验。
        List<Long> hotelIdList = cachedProjectManager.queryProjectWhiteHotelIdList(projectId, HotelWhitTypeEnum.BID_NO_VALIDATE.key);
        if(CollectionUtils.isNotEmpty(hotelIdList) && hotelIdList.contains(hotelId)){
            result = true;
        }
        return result;
    }

    public Response queryHotelGroupBidMapInvitedHotelList(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest){
        Response response = new Response();
        Long projectId = queryHotelGroupBidMapHotelListRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }

        // 分页查询推荐酒店
        PageHelper.startPage(queryHotelGroupBidMapHotelListRequest.getCurrentPage(), queryHotelGroupBidMapHotelListRequest.getPageSize());
        List<BidRecommendHotelInfoQueryResponse> bidRecommendHotelInfoQueryResponses = projectIntentHotelDao.queryHotelGroupBidMapInvitedHotelList(queryHotelGroupBidMapHotelListRequest);
        PageResult pageResult = PageUtil.makePageResult(bidRecommendHotelInfoQueryResponses);

        // 若精确选中酒店不在酒店推荐列表，则表示酒店集团要替非推荐酒店报价，在酒店推荐页签显示所选酒店，推荐等级为空，地图显示所选酒店为我的酒店位置
        if(queryHotelGroupBidMapHotelListRequest.getHotelId() != null && CollectionUtils.isEmpty(pageResult.getList())){
            BidRecommendHotelInfoQueryResponse bidRecommendHotelInfoQueryResponse = projectIntentHotelDao.
                    queryNoRecommendHotelInfo(queryHotelGroupBidMapHotelListRequest.getProjectId(), queryHotelGroupBidMapHotelListRequest.getHotelId());
            if(bidRecommendHotelInfoQueryResponse != null){
                bidRecommendHotelInfoQueryResponse.setProjectId(queryHotelGroupBidMapHotelListRequest.getProjectId());
                pageResult.getList().add(bidRecommendHotelInfoQueryResponse);
                pageResult.setTotalCount(1);
            }
        }

        // 返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }


    public Response queryHotelGroupBidMapInvitedCityStat(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest){
        Response response = new Response();
        Long projectId = queryHotelGroupBidMapHotelListRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }

        // 定义返回值
        ProjectBidStatCountVO result = new ProjectBidStatCountVO();
        result.setProjectId(queryHotelGroupBidMapHotelListRequest.getProjectId());

        // 查询城市统计
        List<ProjectBidBrandStatInfoVO> projectBidBrandStatInfoVOList = projectIntentHotelDao.queryHotelGroupBidMapInvitedCityStat(queryHotelGroupBidMapHotelListRequest)
                .stream().filter(item -> StringUtil.isValidString(item.getCityCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(projectBidBrandStatInfoVOList)){
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
            response.setData(result);
            return response;
        }
        // 城市统计
        Map<String, ProjectBidStatCountItemVO> cityCountMap = new HashMap<>();
        Map<String, List<ProjectBidBrandStatInfoVO>> cityListMap = projectBidBrandStatInfoVOList.stream().collect(Collectors.groupingBy(ProjectBidBrandStatInfoVO::getCityCode));
        List<String> cityCodeList = new ArrayList<>(cityListMap.keySet());
        Map<String, AreadataDto> areadataDtoMap = areadataDao.queryByCodes(cityCodeList, 3).stream().collect(Collectors.toMap(AreadataDto::getDataCode, Function.identity())) ;
        for(String cityCode : cityListMap.keySet()){
            if(!cityCountMap.containsKey(cityCode)){
                ProjectBidStatCountItemVO itemVO = new ProjectBidStatCountItemVO();
                AreadataDto areadataDto = areadataDtoMap.get(cityCode);
                itemVO.setCode(cityCode);
                itemVO.setName(areadataDto == null? cityCode : areadataDto.getDataName());
                itemVO.setCount(0);
                cityCountMap.put(cityCode, itemVO);
            }
            ProjectBidStatCountItemVO cityCountVO = cityCountMap.get(cityCode);
            for(ProjectBidBrandStatInfoVO record : cityListMap.get(cityCode)){
                cityCountVO.setCount(cityCountVO.getCount() + record.getCityBidCount());
            }
        }
        result.setCityList(new ArrayList<>(cityCountMap.values()));
        // 排序
        result.getCityList().sort(Comparator.comparingInt(ProjectBidStatCountItemVO::getCount).reversed());

        // 返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(result);
        return response;
    }

    public Response queryHotelGroupBidMapRecommendHotelList(QueryHotelGroupBidMapHotelListRequest queryHotelGroupBidMapHotelListRequest){
        Response response = new Response();
        Long projectId = queryHotelGroupBidMapHotelListRequest.getProjectId();
        if (projectId == null || projectId <= 0) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("参数异常,项目id不能为空");
            return response;
        }

        // 分页查询推荐酒店
        PageHelper.startPage(queryHotelGroupBidMapHotelListRequest.getCurrentPage(), queryHotelGroupBidMapHotelListRequest.getPageSize());
        List<BidRecommendHotelInfoQueryResponse> bidRecommendHotelInfoQueryResponses = projectIntentHotelDao.queryHotelGroupRecommendHotelInfo(queryHotelGroupBidMapHotelListRequest);
        PageResult pageResult = PageUtil.makePageResult(bidRecommendHotelInfoQueryResponses);

        // 若精确选中酒店不在酒店推荐列表，则表示酒店集团要替非推荐酒店报价，在酒店推荐页签显示所选酒店，推荐等级为空，地图显示所选酒店为我的酒店位置
        if(queryHotelGroupBidMapHotelListRequest.getHotelId() != null && CollectionUtils.isEmpty(pageResult.getList())){
            BidRecommendHotelInfoQueryResponse bidRecommendHotelInfoQueryResponse = projectIntentHotelDao.queryNoRecommendHotelInfo(queryHotelGroupBidMapHotelListRequest.getProjectId(), queryHotelGroupBidMapHotelListRequest.getHotelId());
            if(bidRecommendHotelInfoQueryResponse != null){
                bidRecommendHotelInfoQueryResponse.setProjectId(queryHotelGroupBidMapHotelListRequest.getProjectId());
                pageResult.getList().add(bidRecommendHotelInfoQueryResponse);
                pageResult.setTotalCount(1);
            }
        }

        // 返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(pageResult);
        return response;
    }

    @Override
    public Response queryHotelGroupHotelBidStatus(QueryBidMapHotelInfo queryBidMapHotelInfo, UserDTO userDTO) {
        Response response = new Response();

        // 定义返回值
        QueryHotelGroupHotelBidStatusResponse queryHotelGroupHotelBidStatusResponse = new QueryHotelGroupHotelBidStatusResponse();

        // 查询酒店项目报价信息
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(queryBidMapHotelInfo.getProjectId(), queryBidMapHotelInfo.getHotelId());

        // 未报价，未邀约
        if(projectIntentHotel == null){
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
        }
        // 若酒店已点击过“代报价”，相当于酒店集团机构建立了邀约信息，若未完成报价，下次进来按钮仅剩“代报价”，可以继续报价。
        else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState) && projectIntentHotel.getInviteStatus() == RfpConstant.constant_0){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
        }
        //  若酒店已点击过“通知酒店”，相当于酒店机构建立了邀约信息，若仍未报价，下次进来按钮仅剩“再次通知酒店”，点击可以重新发送邀约邮件邀约短信提醒。
        else if(Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState) && projectIntentHotel.getInviteStatus() == RfpConstant.constant_1){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_1);
        }
        // 若已提交报价，这里无按钮，用文字提示“酒店已提交报价，不能重复提交”
        else if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)){
            queryHotelGroupHotelBidStatusResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            queryHotelGroupHotelBidStatusResponse.setIsBid(RfpConstant.constant_1);
            queryHotelGroupHotelBidStatusResponse.setCanDoBid(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotel(RfpConstant.constant_0);
            queryHotelGroupHotelBidStatusResponse.setCanDoNotifyHotelAgain(RfpConstant.constant_0);
        }

        // 查询是否有品牌限制
        ProjectIntentHotelGroup projectIntentHotelGroup = projectIntentHotelGroupDao.selectByProjectAndOrgId(queryBidMapHotelInfo.getProjectId(), userDTO.getOrgDTO().getOrgId());
        queryHotelGroupHotelBidStatusResponse.setIsBrandLimit(RfpConstant.constant_1);
        if(projectIntentHotelGroup != null) {
            queryHotelGroupHotelBidStatusResponse.setIsBrandLimit(projectIntentHotelGroup.getIsBrandLimit());
        }
        // 查询酒店集团是否包含酒店品牌
        HotelResponse hotelResponse = cachedHotelManager.selectHotelInfo(queryBidMapHotelInfo.getHotelId());
        if(hotelResponse.getHotelBrand() == null){
            logger.error("hotelResponse.getHotelBrand() is null {}", JSON.toJSONString(hotelResponse));
        }
        if(userDTO.getHotelGroupBrandIdList() == null){
            logger.error("userDTO.getHotelGroupBrandIdList() is null {}", JSON.toJSONString(userDTO));
        }
        if(hotelResponse.getHotelBrand() == null || !userDTO.getHotelGroupBrandIdList().contains(Long.valueOf(hotelResponse.getHotelBrand()))){
            queryHotelGroupHotelBidStatusResponse.setIsHotelGroupBrand(RfpConstant.constant_0);
        } else {
            queryHotelGroupHotelBidStatusResponse.setIsHotelGroupBrand(RfpConstant.constant_1);
        }

        // 返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(queryHotelGroupHotelBidStatusResponse);
        return response;

    }

    @Override
    public Response notifyHotelBid(QueryBidMapHotelInfo queryBidMapHotelInfo) {
        Response response = new Response();
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByProjectHotelId(queryBidMapHotelInfo.getProjectId(), queryBidMapHotelInfo.getHotelId());

        // 不存在建立邀约关系
        if(projectIntentHotel == null){
            projectIntentHotel = new ProjectIntentHotel();
            projectIntentHotel.setProjectId(queryBidMapHotelInfo.getProjectId());
            projectIntentHotel.setHotelId(queryBidMapHotelInfo.getHotelId());
            projectIntentHotel.setInviteStatus(RfpConstant.constant_1);
            projectIntentHotel.setLastInviteTime(new Date());
            projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
            projectIntentHotel.setSendMailStatus(RfpConstant.constant_0);
            projectIntentHotel.setIsUpload(RfpConstant.constant_0);
            projectIntentHotel.setHotelServicePoints(new BigDecimal(100));
            List<RecommendHotel> recommendHotels = recommendHotelDao.selectRecommendHotelByHotelIds(Arrays.asList(queryBidMapHotelInfo.getHotelId()));
            if(CollectionUtils.isNotEmpty(recommendHotels)){
                for (RecommendHotel recommendHotel : recommendHotels) {
                    projectIntentHotel.setContactName(recommendHotel.getContactName());
                    projectIntentHotel.setContactMobile(recommendHotel.getContactMobile());
                    projectIntentHotel.setContactEmail(recommendHotel.getContactEmail());
                }
            } else {
                Org org = orgDao.getOrgByHotelId(queryBidMapHotelInfo.getHotelId());
                if(org != null){
                    projectIntentHotel.setContactName(org.getContactName());
                    projectIntentHotel.setContactMobile(org.getContactMobile());
                    projectIntentHotel.setContactEmail(org.getContactEmail());
                }
            }
            projectIntentHotelDao.insertProjectIntentHotel(projectIntentHotel);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(projectIntentHotel.getProjectIntentHotelId());
        return response;
    }

    @Override
    public Response queryBidMapHotelInfo(QueryBidMapHotelInfo queryBidMapHotelInfo) {
        // 定义返回值
        QueryBidMapHotelInfoResponse queryBidMapHotelInfoResponse = new QueryBidMapHotelInfoResponse();

        // 查询项目
        Project project = cachedProjectManager.getById(queryBidMapHotelInfo.getProjectId());

        // 查询去年城市的历史成交信息
        HotelResponse hotelResponse = cachedHotelManager.selectHotelInfo(queryBidMapHotelInfo.getHotelId());
        queryBidMapHotelInfoResponse.setHotelId(queryBidMapHotelInfo.getHotelId());
        queryBidMapHotelInfoResponse.setHotelName(hotelResponse.getHotelName());
        queryBidMapHotelInfoResponse.setHotelStar(hotelResponse.getHotelStar());
        queryBidMapHotelInfoResponse.setHotelStarString(HotelStarEnum.getValueByKey(Integer.parseInt(hotelResponse.getHotelStar())));
        queryBidMapHotelInfoResponse.setLngBaiDu(hotelResponse.getLngBaidu());
        queryBidMapHotelInfoResponse.setLatBaiDu(hotelResponse.getLatBaidu());
        queryBidMapHotelInfoResponse.setCityName(hotelResponse.getCityName());

        // 设置统计值
        ProjectLastYearCityStat projectLastYearCityStat = projectLastYearCityStatDao.selectByProjectIdAndCityCode(project.getProjectId(), hotelResponse.getCity());
        if(projectLastYearCityStat != null && projectLastYearCityStat.getTotalRoomNight() > 0) {
            BigDecimal totalRoomNight500Risk = BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight500()).divide(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight()), 4, RoundingMode.HALF_UP);
            BigDecimal totalRoomNight400To500Risk = BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight400To500()).divide(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight()), 4, RoundingMode.HALF_UP);
            BigDecimal totalRoomNight300To400Risk = BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight300To400()).divide(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight()), 4, RoundingMode.HALF_UP);
            BigDecimal totalRoomNight200To300Risk = BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight200To300()).divide(BigDecimal.valueOf(projectLastYearCityStat.getTotalRoomNight()), 4, RoundingMode.HALF_UP);
            BigDecimal decimal100 = new BigDecimal("100");
            queryBidMapHotelInfoResponse.setLastYearTotalRoomNight(formatStatData(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalRoomNight())));
            queryBidMapHotelInfoResponse.setLastYearTotalSalesAmount(formatStatData(NumberUtil.decimalFormat(",###", projectLastYearCityStat.getTotalSalesAmount())));
            queryBidMapHotelInfoResponse.setTotalRoomNight500(String.valueOf(totalRoomNight500Risk.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight400To500(String.valueOf(totalRoomNight400To500Risk.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight300To400(String.valueOf(totalRoomNight300To400Risk.multiply(decimal100).intValue()));
            queryBidMapHotelInfoResponse.setTotalRoomNight200To300(String.valueOf(totalRoomNight200To300Risk.multiply(decimal100).intValue()));
            int totalRoomNight200 = 100 - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight500()) - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight400To500())
                    - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight300To400()) - Integer.parseInt(queryBidMapHotelInfoResponse.getTotalRoomNight200To300());
            queryBidMapHotelInfoResponse.setTotalRoomNight200(String.valueOf(totalRoomNight200));
        } else {
            queryBidMapHotelInfoResponse.setLastYearTotalRoomNight("--");
            queryBidMapHotelInfoResponse.setLastYearTotalSalesAmount("--");
        }
        // 设置酒店去年校交易间夜数
        QueryHistoryProjectInfoResponse baseQueryHistoryProjectInfoResponse = null;
        int hotelAvgPrice = 0;
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(project.getProjectId(), null, hotelResponse.getCity(), null);
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), hotelResponse.getHotelId())){
                baseQueryHistoryProjectInfoResponse = queryHistoryProjectInfoResponse;
                queryBidMapHotelInfoResponse.setLatestYearRoomNight(queryHistoryProjectInfoResponse.getRoomNightCount());
                queryBidMapHotelInfoResponse.setLatestYearOrder(queryHistoryProjectInfoResponse.getCityOrder());
                if(queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0 &&
                        queryHistoryProjectInfoResponse.getRoomNightCount() > 0
                ) {
                    hotelAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()),0 ,RoundingMode.HALF_UP).intValue();
                }
                break;
            }
        }
        List<Long> theSameLevelHotelIdList;
        if(hotelAvgPrice > 0){
            int finalHotelAvgPrice1 = hotelAvgPrice;
            theSameLevelHotelIdList = queryHistoryProjectInfoResponseList.stream().filter(o -> isTheSameLevel(finalHotelAvgPrice1, o)).map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
        } else {
            theSameLevelHotelIdList = new ArrayList<>();
        }

        // 查询项目所有酒店报价信息
        List<BidHotelInfoQueryResponse> cityBidHotelInfoQueryResponses = projectIntentHotelDao.queryProjectBidHotelInfo(queryBidMapHotelInfo.getProjectId(), hotelResponse.getCity());

        // 根据经纬度过滤符合条件的3公里范围酒店报价信息
        List<BidHotelInfoQueryResponse> threeHotelBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(3, o.getLatBaiDu(), o.getLngBaiDu(), hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu())
        ).collect(Collectors.toList());
        List<HotelBidMapHotelInfoResponse> bidMapHotelInfoResponses = new ArrayList<>();
        threeHotelBidHotelInfoQueryResponses.forEach(item -> {
            if(queryBidMapHotelInfo.getHotelId() != null && Objects.equals(item.getHotelId(), queryBidMapHotelInfo.getHotelId())){
                return;
            }
            HotelBidMapHotelInfoResponse hotelInfoResponse = new HotelBidMapHotelInfoResponse();
            hotelInfoResponse.setHotelName("******酒店");
            hotelInfoResponse.setLatBaiDu(item.getLatBaiDu());
            hotelInfoResponse.setLngBaiDu(item.getLngBaiDu());
            hotelInfoResponse.setBidState(item.getBidState());
            bidMapHotelInfoResponses.add(hotelInfoResponse);
        });
        queryBidMapHotelInfoResponse.setBidHotelInfoQueryResponseList(bidMapHotelInfoResponses);


        // 查询项目3公里范围内POI
        List<ProjectPoiInfoResponse> allProjectPoiInfoResponseList = projectPoiDao.selectMapProjectPoiInfo(project.getProjectId(), null, hotelResponse.getCity(), 3);
        List<ProjectPoiInfoResponse> hotelProjectPoiInfoResponseList = allProjectPoiInfoResponseList.stream().filter(
                o -> LocationUtil.isInLngLatInDistanceRange(3, o.getLatBaiDu(), o.getLngBaiDu(), hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu())
        ).collect(Collectors.toList());
        queryBidMapHotelInfoResponse.setHotelProjectPoiInfoResponseList(hotelProjectPoiInfoResponseList);

        QueryProjectHotelBidStatResponse threeQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        int finalHotelAvgPrice = hotelAvgPrice;

        // 过滤3同档公里历史数据
        List<QueryHistoryProjectInfoResponse> threeHistoryProjectInfoResponseList = queryHistoryProjectInfoResponseList.stream().filter(
                o -> theSameLevelHotelIdList.contains(o.getHotelId()) && LocationUtil.isInLngLatInDistanceRange(3, hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu(), o.getLatBaidu(), o.getLngBaidu())
        ).collect(Collectors.toList());
        // 3公里同档结算间夜数排名
        QueryProjectHotelBidStatResponse calculateThreeHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotelResponse.getHotelId(), threeHistoryProjectInfoResponseList);
        queryBidMapHotelInfoResponse.setTheSameLevelOrder(calculateThreeHotelNightRoomOrder.getLastYearRoomNightOrder());
        //threeQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(Math.max(calculateThreeHotelNightRoomOrder.getLastYearRoomNightCount(), 0));
        threeQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateThreeHotelNightRoomOrder.getLastYearRoomNightOrder());

        // 3公里同档结算间夜数金额
        QueryProjectHotelBidStatResponse calculateThreeHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotelResponse.getHotelId(), threeHistoryProjectInfoResponseList);
        // threeQueryProjectHotelBidStatResponse.setLastYearAmount(calculateThreeHotelSalesAmountOrder.getLastYearAmount());
        threeQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateThreeHotelSalesAmountOrder.getLastYearAmountOrder());

        List<BidHotelInfoQueryResponse> lastYearCityBidHotelInfoQueryResponses = new ArrayList<>();
        if(project.getRelatedProjectId() > 0) {
            lastYearCityBidHotelInfoQueryResponses = projectIntentHotelDao.queryProjectBidHotelInfo(project.getRelatedProjectId(), hotelResponse.getCity());
        }
        // 查询去年3公里同档酒店服务分排名
        if(CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
            List<BidHotelInfoQueryResponse> threeLastYearBidHotelInfoQueryResponses = lastYearCityBidHotelInfoQueryResponses.stream().filter(
                    o -> theSameLevelHotelIdList.contains(o.getHotelId()) && LocationUtil.isInLngLatInDistanceRange(3, o.getLatBaiDu(), o.getLngBaiDu(), hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu())
            ).collect(Collectors.toList());
            QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(queryBidMapHotelInfo.getHotelId(), threeLastYearBidHotelInfoQueryResponses);
            threeQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
            threeQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
        }

        // 查询OTA排名 (3公里同档范围)
        List<BidHotelInfoQueryResponse> ratingHotelBidHotelInfoQueryResponses = threeHotelBidHotelInfoQueryResponses.stream().filter(o -> theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
        int rattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(queryBidMapHotelInfo.getHotelId(),ratingHotelBidHotelInfoQueryResponses);
        threeQueryProjectHotelBidStatResponse.setRatingOrder(rattingOrder);
        threeQueryProjectHotelBidStatResponse.setRating(hotelResponse.getRatting());
        // 设置3公里范围同档
        queryBidMapHotelInfoResponse.setThreeQueryProjectHotelBidStatResponse(threeQueryProjectHotelBidStatResponse);

        // --------------------------------------------------------------------------------------------------------------
        // 城市同档
        QueryProjectHotelBidStatResponse cityLevelQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        List<QueryHistoryProjectInfoResponse> cityLevelHistoryProjectInfoResponseList = queryHistoryProjectInfoResponseList.stream().filter(
                o -> isTheSameLevel(finalHotelAvgPrice, o)).collect(Collectors.toList());
        QueryProjectHotelBidStatResponse calculateCityLevelHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotelResponse.getHotelId(), cityLevelHistoryProjectInfoResponseList);
        //cityLevelQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(calculateCityLevelHotelNightRoomOrder.getLastYearRoomNightCount());
        cityLevelQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateCityLevelHotelNightRoomOrder.getLastYearRoomNightOrder());

        // 城市同档成交间夜数金额
        QueryProjectHotelBidStatResponse calculateCityLevelHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotelResponse.getHotelId(), cityLevelHistoryProjectInfoResponseList);
        //cityLevelQueryProjectHotelBidStatResponse.setLastYearAmount(calculateCityLevelHotelSalesAmountOrder.getLastYearAmount());
        cityLevelQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateCityLevelHotelSalesAmountOrder.getLastYearAmountOrder());

        // 城市同档查询去年服务分排名
        if(CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
            List<BidHotelInfoQueryResponse> cityLevelLastYearBidHotelInfoQueryResponses = lastYearCityBidHotelInfoQueryResponses.stream().filter(
                    o -> theSameLevelHotelIdList.contains(o.getHotelId())
            ).collect(Collectors.toList());
            QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(queryBidMapHotelInfo.getHotelId(), cityLevelLastYearBidHotelInfoQueryResponses);
            cityLevelQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
            cityLevelQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
        }

        // 城市同档 OTA
        List<BidHotelInfoQueryResponse> cityLevelRatingHotelBidHotelInfoQueryResponses = cityBidHotelInfoQueryResponses.stream().filter(o -> theSameLevelHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
        int cityLevelRattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(queryBidMapHotelInfo.getHotelId(), cityLevelRatingHotelBidHotelInfoQueryResponses);
        cityLevelQueryProjectHotelBidStatResponse.setRatingOrder(cityLevelRattingOrder);
        cityLevelQueryProjectHotelBidStatResponse.setRating(hotelResponse.getRatting());
        queryBidMapHotelInfoResponse.setCityTheSameLevelQueryProjectHotelBidStatResponse(cityLevelQueryProjectHotelBidStatResponse);

        //-------------------------------------------------------------------------------------------------------

        //城市排名
        QueryProjectHotelBidStatResponse cityQueryProjectHotelBidStatResponse = new QueryProjectHotelBidStatResponse();
        if(baseQueryHistoryProjectInfoResponse != null) { // 酒店去年历史数据不存在，不需要计算城市排名
            //城市结算间夜数排名
            QueryProjectHotelBidStatResponse calculateCityHotelNightRoomOrder = HotelHistoryDataStatUtil.calculateHotelNightRoomOrder(hotelResponse.getHotelId(), queryHistoryProjectInfoResponseList);
            //cityQueryProjectHotelBidStatResponse.setLastYearRoomNightCount(calculateCityHotelNightRoomOrder.getLastYearRoomNightCount());
            cityQueryProjectHotelBidStatResponse.setLastYearRoomNightOrder(calculateCityHotelNightRoomOrder.getLastYearRoomNightOrder());

            // 城市成交间夜数金额
            QueryProjectHotelBidStatResponse calculateCityHotelSalesAmountOrder = HotelHistoryDataStatUtil.calculateSalesAmountOrder(hotelResponse.getHotelId(), queryHistoryProjectInfoResponseList);
            // cityQueryProjectHotelBidStatResponse.setLastYearAmount(calculateCityHotelSalesAmountOrder.getLastYearAmount());
            cityQueryProjectHotelBidStatResponse.setLastYearAmountOrder(calculateCityHotelSalesAmountOrder.getLastYearAmountOrder());

            // 城市查询去年服务分排名
            if (CollectionUtils.isNotEmpty(lastYearCityBidHotelInfoQueryResponses)) {
                QueryProjectHotelBidStatResponse queryProjectHotelBidStatResponse = HotelHistoryDataStatUtil.calculateHotelServicePointOrder(queryBidMapHotelInfo.getHotelId(), lastYearCityBidHotelInfoQueryResponses);
                cityQueryProjectHotelBidStatResponse.setLastYearServicePoint(queryProjectHotelBidStatResponse.getLastYearServicePoint());
                cityQueryProjectHotelBidStatResponse.setLastYearServicePointOrder(queryProjectHotelBidStatResponse.getLastYearServicePointOrder());
            }
        }

        // 城市 OTA
        cityQueryProjectHotelBidStatResponse.setRating(hotelResponse.getRatting());
        int cityRattingOrder = HotelHistoryDataStatUtil.calculateOTAOrder(queryBidMapHotelInfo.getHotelId(), cityBidHotelInfoQueryResponses);
        cityQueryProjectHotelBidStatResponse.setRatingOrder(cityRattingOrder);
        cityQueryProjectHotelBidStatResponse.setRating(hotelResponse.getRatting());
        // 设置城市统计
        queryBidMapHotelInfoResponse.setCityQueryProjectHotelBidStatResponse(cityQueryProjectHotelBidStatResponse);

        // 是否为推荐酒店
        RecommendHotel recommendHotel = recommendHotelDao.selectRecommendHotelByHotelId(hotelResponse.getHotelId(), RfpConstant.constant_1);
        queryBidMapHotelInfoResponse.setIsRecommendHotel(recommendHotel != null ? RfpConstant.constant_1 : RfpConstant.constant_0);

        // 是否意向酒店
        ProjectInviteHotel projectInviteHotel = projectInviteHotelDao.queryByProjectHotelId(project.getProjectId(), hotelResponse.getHotelId());
        queryBidMapHotelInfoResponse.setIsInvitedHotel(projectInviteHotel != null ? RfpConstant.constant_1 : RfpConstant.constant_0);

        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(queryBidMapHotelInfoResponse);

        return response;
    }

    // 是否为同档报价
    private boolean isTheSameLevel(int baseAvgPrice, QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse){
        if(baseAvgPrice == 0){
            return false;
        }
        if(queryHistoryProjectInfoResponse.getRoomNightCount() > 0 && queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            int avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP).intValue();
            if (baseAvgPrice > 0 && Math.abs(baseAvgPrice - avgPrice) <= 50) {
                return true;
            }
        }
        return false;
    }

    private void addLevelPrice(ImportLanyonBidDto importLanyonBidDto, Integer levelNo, Integer breakfastNum,
                               String cancelRestrictTime,
                               Integer lra,
                               Integer priceType, BigDecimal price, boolean isIncludeBreakfast, String priceDesc, // 单人入住,双人入住
                               boolean isIncludePrice1, String price1Uom, BigDecimal price1Fee,
                               boolean isIncludePrice2, String price2Uom, BigDecimal price2Fee,
                               boolean isIncludePrice3, String price3Uom, BigDecimal price3Fee,
                               boolean isIncludePrice4, String price4Uom, BigDecimal price4Fee,
                               boolean isIncludePrice5, String price5Uom, BigDecimal price5Fee
    ){
        // 新增房当
        if(!importLanyonBidDto.getLevelPriceGroupMap().containsKey(levelNo)){
            importLanyonBidDto.getLevelPriceGroupMap().put(levelNo, new ArrayList<>());
        }

        // 查询是否存在相同报价组
        ImportLanyonHotelPriceGroupBidDto importHotelGroupBidDto = null;
        List<ImportLanyonHotelPriceGroupBidDto> importLanyonHotelPriceGroupBidDtoList = importLanyonBidDto.getLevelPriceGroupMap().get(levelNo);
        for(ImportLanyonHotelPriceGroupBidDto importLanyonHotelPriceGroupBidDto : importLanyonHotelPriceGroupBidDtoList){
            if(Objects.equals(importLanyonHotelPriceGroupBidDto.getLra(), lra) &&
                    Objects.equals(importLanyonHotelPriceGroupBidDto.getCancelRestrictTime(), cancelRestrictTime) &&
                    Objects.equals(importLanyonHotelPriceGroupBidDto.getImportLanyonHotelPriceBidDtos().get(0).getPriceDesc(), priceDesc)
            ){
                importHotelGroupBidDto = importLanyonHotelPriceGroupBidDto;
            }
        }
        // 不存在相同报价组，初始一个
        if(importHotelGroupBidDto == null) {
            importHotelGroupBidDto = new ImportLanyonHotelPriceGroupBidDto();

            // 设置房档
            importHotelGroupBidDto.setLevelNo(levelNo);
            // 设置LRA
            importHotelGroupBidDto.setLra(lra);
            // 退订信息
            importHotelGroupBidDto.setCancelRestrictType(CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key);
            importHotelGroupBidDto.setCancelRestrictTime(cancelRestrictTime);
            importHotelGroupBidDto.setCancelRestrictDay(0);

            // 初始化价格
            List<ImportLanyonHotelPriceBidDto> hotelPriceBidDtoList = new LinkedList<>();
            importHotelGroupBidDto.setImportLanyonHotelPriceBidDtos(hotelPriceBidDtoList);
            importHotelGroupBidDto.setRemark("");
            importLanyonBidDto.getLevelPriceGroupMap().get(levelNo).add(importHotelGroupBidDto);

        }
        // 设置价格
        ImportLanyonHotelPriceBidDto importLanyonHotelPriceBidDto = new ImportLanyonHotelPriceBidDto();
        importLanyonHotelPriceBidDto.setBreakfastNum(breakfastNum);
        importLanyonHotelPriceBidDto.setPriceType(priceType);
        importLanyonHotelPriceBidDto.setPriceDesc(priceDesc);

        // 设置备注
        String remark = StringUtil.isEmpty(importHotelGroupBidDto.getRemark()) ? "" : importHotelGroupBidDto.getRemark() + ",";
        Map<BigDecimal, String> adjustPriceMap = adjustLanyonPriceByFee(price,  remark + priceDesc,
                isIncludePrice1, price1Uom, price1Fee,
                isIncludePrice2, price2Uom, price2Fee,
                isIncludePrice3, price3Uom, price3Fee,
                isIncludePrice4, price4Uom, price4Fee,
                isIncludePrice5, price5Uom, price5Fee);
        for (BigDecimal adjustPrice : adjustPriceMap.keySet()) {
            // Set price
            importLanyonHotelPriceBidDto.setBasePrice(adjustPrice);
            // Set remark
            importHotelGroupBidDto.setRemark(adjustPriceMap.get(adjustPrice));
        }
        importHotelGroupBidDto.getImportLanyonHotelPriceBidDtos().add(importLanyonHotelPriceBidDto);
    }

    private Map<BigDecimal, String> adjustLanyonPriceByFee(
            BigDecimal basePrice,
            String remark,
            boolean isIncludePrice1, String price1Uom, BigDecimal price1Fee,
            boolean isIncludePrice2, String price2Uom, BigDecimal price2Fee,
            boolean isIncludePrice3, String price3Uom, BigDecimal price3Fee,
            boolean isIncludePrice4, String price4Uom, BigDecimal price4Fee,
            boolean isIncludePrice5, String price5Uom, BigDecimal price5Fee
    ) {
        BigDecimal result = basePrice;
        boolean isAdustPrice = false;
        remark = remark  + "含税价=" + basePrice;
        Map<BigDecimal, String> resultMap = new HashMap<>();
        BigDecimal ZERO_POINT_ZERO_ONE = new BigDecimal("0.01");
        if(!isIncludePrice1 && price1Fee.compareTo(BigDecimal.ZERO) > 0){
            isAdustPrice = true;
            if("P".equalsIgnoreCase(price1Uom)){
                result = result.add(basePrice.multiply(price1Fee.multiply(ZERO_POINT_ZERO_ONE)));
                remark = remark + "+(" + basePrice + "*" + price1Fee.multiply(ZERO_POINT_ZERO_ONE) + ")";
            } else if("F".equalsIgnoreCase(price1Uom)){
                result = result.add(price1Fee);
                remark = remark + "+(" + basePrice + "+" + price1Fee + ")";
            }
        }
        if(!isIncludePrice2 && price2Fee.compareTo(BigDecimal.ZERO) > 0){
            isAdustPrice = true;
            if("P".equalsIgnoreCase(price2Uom)){
                result = result.add(basePrice.multiply(price2Fee.multiply(ZERO_POINT_ZERO_ONE)));
                remark = remark + "+(" + basePrice + "*" + price2Fee.multiply(ZERO_POINT_ZERO_ONE) + ")";
            } else if("F".equalsIgnoreCase(price2Uom)){
                result = result.add(price2Fee);
                remark = remark + "+(" + basePrice + "+" + price2Fee + ")";
            }
        }
        if(!isIncludePrice3 && price3Fee.compareTo(BigDecimal.ZERO) > 0){
            isAdustPrice = true;
            if("P".equalsIgnoreCase(price3Uom)){
                result = result.add(basePrice.multiply(price3Fee.multiply(ZERO_POINT_ZERO_ONE)));
                remark = remark + "+(" + basePrice + "*" + price3Fee.multiply(ZERO_POINT_ZERO_ONE) + ")";
            } else if("F".equalsIgnoreCase(price3Uom)){
                result = result.add(price3Fee);
                remark = remark + "+(" + basePrice + "+" + price3Fee + ")";
            }
        }
        if(!isIncludePrice4 && price4Fee.compareTo(BigDecimal.ZERO) > 0){
            isAdustPrice = true;
            if("P".equalsIgnoreCase(price4Uom)){
                result = result.add(basePrice.multiply(price4Fee.multiply(ZERO_POINT_ZERO_ONE)));
                remark = remark + "+(" + basePrice + "*" + price4Fee.multiply(ZERO_POINT_ZERO_ONE) + ")";
            } else if("F".equalsIgnoreCase(price4Uom)){
                result = result.add(price4Fee);
                remark = remark + "+(" + basePrice + "+" + price4Fee + ")";
            }
        }
        if(!isIncludePrice5 && price5Fee.compareTo(BigDecimal.ZERO) > 0){
            isAdustPrice = true;
            if("P".equalsIgnoreCase(price5Uom)){
                result = result.add(basePrice.multiply(price5Fee.multiply(ZERO_POINT_ZERO_ONE)));
                remark = remark + "+(" + basePrice + "*" + price5Fee.multiply(ZERO_POINT_ZERO_ONE) + ")";
            } else if("F".equalsIgnoreCase(price5Uom)){
                result = result.add(price5Fee);
                remark = remark + "+(" + basePrice + "+" + price5Fee + ")";
            }
        }
        if(isAdustPrice){
            result = NumberUtil.round(result, 2);
            remark = remark + "=" + result;
        }
        resultMap.put(result, remark);
        return resultMap;

    }

    private String generatePriceKey(ProjectHotelPriceGroup priceGroup, ProjectHotelPrice hotelPrice){
        return priceGroup.getRoomLevelNo() +"_" + priceGroup.getLra() + "_" +
                priceGroup.getLevelTotalRoomCount() + "_" + priceGroup.getCancelRestrictType() +  "_" +
                hotelPrice.getBreakfastNum() + "_" + hotelPrice.getPriceType();
    }

    @Override
    public Response validateUploadHotelGroupBidData(MultipartFile uploadFile, String operator,
                                                    Long projectId,
                                                    Map<Long, List<ImportHotelGroupBidDto>> importHotelDataMap,
                                                    UserDTO userDTO) throws Exception {
        Response response = new Response();
        Project project = projectDao.selectByPrimaryKey(projectId);
        if(project == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("项目信息不存在");
            return response;
        }

        Workbook wb = WorkbookFactory.create(uploadFile.getInputStream());

        // 获得有多少行数据 不能多于10000行
        Sheet sheet = wb.getSheetAt(0);
        int rowSize = sheet.getPhysicalNumberOfRows();
        logger.info("checkFormat rowSize=" + rowSize);
        if(rowSize<=1){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("导入数据为空");
            return response;
        }

        if (rowSize > 10001) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("最多上传10000个邀请酒店信息信息");
            return response;
        }
        //校验标题是否符合规范
        Row titleRow = sheet.getRow(0);
        if (titleRow == null) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("上传文件为空");
            return response;
        }
        for (int k = 0; k < 19; k++) {
            Cell cell = titleRow.getCell(k);
            String headName = cell.getStringCellValue();
            logger.info("checkFormat headIndex=" + k + " headName=" + headName);
            Integer keyByValue = ImportHotelGroupBidEnum.getKeyByValue(headName);
            if (keyByValue == null || keyByValue.intValue() != k) {
                response.setResult(ReturnResultEnum.FAILED.errorNo);
                response.setMsg("EXCEL标题不符合规范，请下载邀请酒店导入模版");
                return response;
            }
        }

        // 查找已经报价酒店
        List<ProjectIntentHotel> projectIntentHotelList = projectIntentHotelDao.selectByProjectId(projectId);
        Map<Long, ProjectIntentHotel>  projectIntentHotelMap = projectIntentHotelList.stream().collect(Collectors.toMap(ProjectIntentHotel::getHotelId, Function.identity()));

        List<String> invalidMsgList = new ArrayList<>();
        List<Long> hotelIds = new ArrayList<>();
        Map<Long, String> hotelLeveBaseDayMap = new HashMap<>(); // <hotelId, columnDay>
        Map<Long, String> hotelLeveSeason1DayMap = new HashMap<>(); // <hotelId, columnDay>
        Map<Long, String> hotelLeveSeason2DayMap = new HashMap<>(); // <hotelId, columnDay>
        Map<Long, String> hotelUnApplicableDayMap = new HashMap<>(); // <hotelId, columnDay>
        Map<Long, List<PriceApplicableDay>> hotelPriceAppleDayMap = new HashMap<>();
        Map<String, String> hotelLeveRoomTypeIdMap = new HashMap<>(); // <hotelId_levelNo, columnRoomTypeIds>
        Map<Long, List<Long>> hotelRoomTypeIdListMap = new HashMap<>(); // <hotelId, roomTypeIdList>
        Map<Long, RoomInfoResponse> roomInfoResponseMap = new HashMap<>(); // <RoomId, RoomInfoResponse>
        for (int i = 1; i < rowSize; i++) {
            Row dataRow = sheet.getRow(i);
            try {
                if (dataRow == null) {
                    continue;
                }

                String fcHotelIdString = CommonUtil.getCellValue(dataRow.getCell(0));
                if(!StringUtil.isValidString(fcHotelIdString)){
                    invalidMsgList.add("第"+ (i+1) + "行酒店id为空");
                    continue;
                }
                Long hotelId = Long.valueOf(fcHotelIdString);
                if (!hotelIds.contains(hotelId)) {
                    HotelResponse hotelResponse = hotelDao.selectHotelInfo(Long.valueOf(fcHotelIdString));
                    if (hotelResponse == null) {
                        invalidMsgList.add("第" + (i + 1) + "行酒店id无效:" + fcHotelIdString);
                        continue;
                    }
                    hotelIds.add(hotelId);
                }

                // 检查酒店是否存在其他机构已经报价， 如果存在并且是中签，不允许导入
                ProjectIntentHotel intentHotel = projectIntentHotelMap.get(hotelId);
                if(intentHotel != null && intentHotel.getInviteStatus() == RecommendHotelBindInviteStatusEnum.INVITED.key && intentHotel.getBidState() != HotelBidStateEnum.NO_BID.bidState.intValue()){
                    invalidMsgList.add("第" + (i + 1) + "行酒店已经存在其他机构报价:" + HotelBidStateEnum.getValueByKey(intentHotel.getBidState()) +"状态" + fcHotelIdString );
                    continue;
                }
                if(intentHotel != null && intentHotel.getBidState() != HotelBidStateEnum.NO_BID.bidState){
                    invalidMsgList.add("第" + (i + 1) + "行酒店报价状态为" + HotelBidStateEnum.getValueByKey(intentHotel.getBidState()) +"状态不能导入报价" );
                    continue;
                }
                if(!importHotelDataMap.containsKey(hotelId)){
                    importHotelDataMap.put(hotelId, new LinkedList<>());
                }

                // 初始化数据
                ImportHotelGroupBidDto importHotelGroupBidDto = new ImportHotelGroupBidDto();
                importHotelGroupBidDto.setHotelId(hotelId);
                importHotelGroupBidDto.setContactName(CommonUtil.getCellValue(dataRow.getCell(1)));
                importHotelGroupBidDto.setContactMobile(CommonUtil.getCellValue(dataRow.getCell(2)));
                importHotelGroupBidDto.setContactEmail(CommonUtil.getCellValue(dataRow.getCell(3)));


                String baseDay = CommonUtil.getCellValue(dataRow.getCell(4));
                if(!StringUtil.isValidString(baseDay)){
                    invalidMsgList.add("第"+ (i+1) + "行基础协议价有效期为空");
                    continue;
                }
                String season1Day = CommonUtil.getCellValue(dataRow.getCell(5));
                String season2Day = CommonUtil.getCellValue(dataRow.getCell(6));
                if(hotelPriceAppleDayMap.containsKey(hotelId)){
                    if(!Objects.equals(hotelLeveBaseDayMap.get(hotelId), baseDay)){
                        invalidMsgList.add("第"+ (i+1) + "行基础协议价日期不同");
                        continue;
                    }
                    if(!Objects.equals(hotelLeveSeason1DayMap.get(hotelId), season1Day)){
                        invalidMsgList.add("第"+ (i+1) + "行Season1日期不同");
                        continue;
                    }
                    if(!Objects.equals(hotelLeveSeason2DayMap.get(hotelId), season2Day)){
                        invalidMsgList.add("第"+ (i+1) + "行Season2日期不同");
                        continue;
                    }
                }  else {
                    hotelLeveBaseDayMap.put(hotelId, baseDay);
                    hotelLeveSeason1DayMap.put(hotelId, season1Day);
                    hotelLeveSeason2DayMap.put(hotelId, season2Day);

                    // 获取basePriceApplicableDay
                    List<PriceApplicableDay> basePriceApplicableDayList = convertPriceApplicableDay(projectId, hotelId, baseDay, HotelPriceTypeEnum.BASE_PRICE.key);
                    List<PriceApplicableDay> season1PriceApplicableDayList = convertPriceApplicableDay(projectId, hotelId, season1Day, HotelPriceTypeEnum.SEASON_1_PRICE.key);
                    List<PriceApplicableDay> season2PriceApplicableDayList = convertPriceApplicableDay(projectId, hotelId, season2Day, HotelPriceTypeEnum.SEASON_2_PRICE.key);

                    // 检查season 日期是否交叉
                    List<PriceApplicableDay> allPriceApplicableDayList = new ArrayList<>();
                    allPriceApplicableDayList.addAll(basePriceApplicableDayList);
                    allPriceApplicableDayList.addAll(season1PriceApplicableDayList);
                    allPriceApplicableDayList.addAll(season2PriceApplicableDayList);
                    Response compareSeasonDayResponse = priceApplicableDayService.comparePriceApplicableDay(allPriceApplicableDayList, true);
                    if (compareSeasonDayResponse != null) {
                        invalidMsgList.add("第" + (i + 1) + "行" +compareSeasonDayResponse.getMsg());
                        continue;
                    }
                    hotelPriceAppleDayMap.put(hotelId, allPriceApplicableDayList);
                }

                importHotelGroupBidDto.setPriceApplicableDayList(hotelPriceAppleDayMap.get(hotelId));

                // 检查不适应日期
                String unApplicableDay = CommonUtil.getCellValue(dataRow.getCell(7));
                if(hotelUnApplicableDayMap.containsKey(hotelId)){
                    if(!Objects.equals(hotelUnApplicableDayMap.get(hotelId), unApplicableDay)){
                        invalidMsgList.add("第"+ (i+1) + "行不适用日期不同");
                        continue;
                    }
                } else {
                    hotelUnApplicableDayMap.put(hotelId, unApplicableDay);
                }
                if (StringUtil.isValidString(unApplicableDay)) {
                    // 检查数据是否合法
                    List<PriceUnapplicableDay> priceUnapplicableDayList = convertPriceUnApplicableDay(projectId, hotelId, unApplicableDay);
                    importHotelGroupBidDto.setUnApplicableDayList(priceUnapplicableDayList);

                }

                // 检查房档
                String levelNo = CommonUtil.getCellValue(dataRow.getCell(8));
                if(!StringUtil.isValidString(levelNo)){
                    invalidMsgList.add("第"+ (i+1) + "行*房档等级能为空");
                    continue;
                }
                Integer roomLevelNo = Integer.valueOf(levelNo);
                importHotelGroupBidDto.setLevelNo(roomLevelNo);

                // 房型检查
                if(!hotelRoomTypeIdListMap.containsKey(hotelId)){
                    List<RoomInfoResponse> roomResponseList = hotelDao.selectHotelRoomInfoList(hotelId);
                    List<Long> roomTypeIdList = new ArrayList<>();
                    for(RoomInfoResponse roomInfoResponse : roomResponseList){
                        roomTypeIdList.add(roomInfoResponse.getRoomId());
                        roomInfoResponseMap.put(roomInfoResponse.getRoomId(), roomInfoResponse);
                    }
                    hotelRoomTypeIdListMap.put(hotelId, roomTypeIdList);
                }

                String hotelLevelKey = fcHotelIdString + "_" + levelNo;
                String roomTypeIdStr = CommonUtil.getCellValue(dataRow.getCell(9));

                /**
                 *  支持lanyon 报价导入，允许房型id为空
                if(!StringUtil.isValidString(roomTypeIdStr)){
                    invalidMsgList.add("第"+ (i+1) + "行*房仓房型ID为空");
                    continue;
                }
                 **/
                if(StringUtils.isNotEmpty(roomTypeIdStr)) {
                    String[] roomTypeIds = roomTypeIdStr.split(",");
                    List<Long> roomTypeIdList = Arrays.stream(roomTypeIds).map(Long::valueOf).collect(Collectors.toList());
                    if (hotelLeveRoomTypeIdMap.containsKey(hotelLevelKey)) {
                        if (!Objects.equals(hotelLeveRoomTypeIdMap.get(hotelLevelKey), roomTypeIdStr)) {
                            invalidMsgList.add("第" + (i + 1) + "行相同房档对应房型不同");
                            continue;
                        }
                    } else {
                        Set<Long> roomTypeIdSet = new HashSet<>(roomTypeIdList);
                        if (roomTypeIdList.size() != roomTypeIdSet.size()) {
                            invalidMsgList.add("第" + (i + 1) + "行存在相同房型ID");
                            continue;
                        }
                        List<Long> hotelRoomTypeIdList = hotelRoomTypeIdListMap.get(hotelId);
                        if (!CollectionUtil.containsAll(hotelRoomTypeIdList, roomTypeIdList)) {
                            invalidMsgList.add("第" + (i + 1) + "行存在酒店不包含房型ID");
                            continue;
                        }
                        // 检查是否存在重复房型
                        boolean containRoomType = false;
                        for (String key : hotelLeveRoomTypeIdMap.keySet()) {
                            if (!key.startsWith(hotelId + "_")) {
                                continue;
                            }
                            String keyRoomTypeIdStr = hotelLeveRoomTypeIdMap.get(key);
                            String[] keyRoomTypeIds = keyRoomTypeIdStr.split(",");
                            List<Long> keyRoomTypeIdList = Arrays.stream(keyRoomTypeIds).map(Long::valueOf).collect(Collectors.toList());

                            for (Long roomTypeId : roomTypeIdList) {
                                if (keyRoomTypeIdList.contains(roomTypeId)) {
                                    containRoomType = true;
                                    invalidMsgList.add("第" + (i + 1) + "行存在酒店房档重复房型" + roomTypeId);
                                    break;
                                }

                            }
                            if (containRoomType) {
                                break;
                            }
                        }
                        if (containRoomType) {
                            continue;
                        }

                    }
                    hotelLeveRoomTypeIdMap.put(hotelLevelKey, roomTypeIdStr);
                    List<RoomInfoResponse> levelRoomInfoResponseList = new ArrayList<>();
                    for (Long roomTypeId : roomTypeIdList) {
                        RoomInfoResponse roomInfoResponse = roomInfoResponseMap.get(roomTypeId);
                        levelRoomInfoResponseList.add(roomInfoResponse);
                    }
                    importHotelGroupBidDto.setRoomInfoResponseList(levelRoomInfoResponseList);
                } else {
                    importHotelGroupBidDto.setLanyonRoomDesc("Standard" + importHotelGroupBidDto.getLevelNo());
                }

                // 价格类型
                String priceTypeStr = CommonUtil.getCellValue(dataRow.getCell(10));
                if(!StringUtil.isValidString(priceTypeStr)){
                    invalidMsgList.add("第"+ (i+1) + "行价格类型为空");
                    continue;
                }
                Integer priceTypeId = Integer.valueOf(priceTypeStr);
                HotelPriceTypeEnum hotelPriceTypeEnum = HotelPriceTypeEnum.getEnumByKey(priceTypeId);
                if(hotelPriceTypeEnum == null){
                    invalidMsgList.add("第"+ (i+1) + "行价格类型不存在");
                    continue;
                }
                importHotelGroupBidDto.setPriceType(priceTypeId);

                // 早餐类型
                String breakfastNumStr = CommonUtil.getCellValue(dataRow.getCell(11));
                if(!StringUtil.isValidString(breakfastNumStr)){
                    invalidMsgList.add("第"+ (i+1) + "行早餐类型为空");
                    continue;
                }
                Integer breakfastNum = Integer.valueOf(breakfastNumStr);
                String breakfastNumValue = com.fangcang.rfp.common.enums.BreakfastNumEnum.getValueByKey(breakfastNum);
                if(!StringUtil.isValidString(breakfastNumValue)){
                    invalidMsgList.add("第"+ (i+1) + "行早餐类型不存在");
                    continue;
                }
                importHotelGroupBidDto.setBreakfastNum(breakfastNum);

                // 房价格
                String basePriceStr = CommonUtil.getCellValue(dataRow.getCell(12));
                if(!StringUtil.isValidString(basePriceStr)){
                    invalidMsgList.add("第"+ (i+1) + "行价格不能为空");
                    continue;
                }
                BigDecimal basePrice = new BigDecimal(basePriceStr);
                if(basePrice.compareTo(BigDecimal.ZERO) <= 0){
                    invalidMsgList.add("第"+ (i+1) + "行价格必须大于0");
                    continue;
                }
                importHotelGroupBidDto.setBasePrice(basePrice);

                // 适用星期
                String applicableWeeks = CommonUtil.getCellValue(dataRow.getCell(13));
                if(!StringUtil.isValidString(applicableWeeks)){
                    invalidMsgList.add("第"+ (i+1) + "行适用星期不能为空");
                    continue;
                }
                String[] weekDayStr = applicableWeeks.split(",");
                List<Integer> weekDayList = Arrays.stream(weekDayStr).map(Integer::new).collect(Collectors.toList());
                boolean isWeekDayPass =  true;
                for(Integer weekDay : weekDayList){
                    ApplicableWeeksTypeEnum weeksTypeEnum =  ApplicableWeeksTypeEnum.getEnumByKey(weekDay);
                    if(weeksTypeEnum == null){
                        isWeekDayPass = false;
                        invalidMsgList.add("第"+ (i+1) + "行适用星期不存在");
                        break;
                    }
                }
                if(!isWeekDayPass){
                    continue;
                }
                importHotelGroupBidDto.setApplicableWeeks(convertToChineseWeek(applicableWeeks, false));

                // 承若
                String lraStr = CommonUtil.getCellValue(dataRow.getCell(14));
                if(StringUtil.isEmpty(lraStr)){
                    invalidMsgList.add("第"+ (i+1) + "行LRA 为空");
                    continue;
                }
                Integer lra = Integer.valueOf(lraStr);
                if(lra != 0 && lra !=1){
                    invalidMsgList.add("第"+ (i+1) + "行LRA不存在");
                    continue;
                }
                importHotelGroupBidDto.setLra(lra);

                // 备注
                importHotelGroupBidDto.setRemark(CommonUtil.getCellValue(dataRow.getCell(15)));

                // 退改条款
                String cancelTypeStr = CommonUtil.getCellValue(dataRow.getCell(16));
                if(StringUtil.isEmpty(cancelTypeStr)){
                    invalidMsgList.add("第"+ (i+1) + "行退改条款类型为空");
                    continue;
                }
                Integer cancelType = Integer.valueOf(cancelTypeStr);
                if(CancelRestrictTypeEnum.getEnumByKey(cancelType) == null){
                    invalidMsgList.add("第"+ (i+1) + "行退改条款类型不存在");
                    continue;
                }
                importHotelGroupBidDto.setCancelRestrictType(cancelType);

                // 指定时间之前取消条款-提前天数
                String cancelDayStr = CommonUtil.getCellValue(dataRow.getCell(17));
                if(!StringUtil.isEmpty(cancelDayStr)){
                    Integer cancelDay = Integer.valueOf(cancelDayStr);
                    importHotelGroupBidDto.setCancelRestrictDay(cancelDay);
                }

                // 指定时间之前取消条款-指定时间
                String cancelTimeStr = CommonUtil.getCellValue(dataRow.getCell(18));
                if("0".equals(cancelTimeStr)){
                    invalidMsgList.add("第"+ (i+1) + "行指定时间之前取消条款-指定时间需要文本输入格式");
                    continue;
                }
                if(!StringUtil.isEmpty(cancelTimeStr)){
                    DateUtil.stringToDate("2021-01-01 " + cancelTimeStr + ":00");
                    importHotelGroupBidDto.setCancelRestrictTime(cancelTimeStr);
                }
                // 增加酒店报价
                importHotelDataMap.get(hotelId).add(importHotelGroupBidDto);
            } catch (Exception e) {
                logger.error("导入酒店集团报价异常：" ,e);
                invalidMsgList.add("第"+ (i+1) + "行导入异常");
            }
            // 多余200错误数据，不检查
            if(invalidMsgList.size() > 200){
                break;
            }
        }

        if(!invalidMsgList.isEmpty()){
            logger.error("检查失败信息：{}", JSON.toJSONString(invalidMsgList));
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setData(invalidMsgList);
        return response;
    }

    @Override
    public Response batchInsertLanyonBidData(String operator, Long projectId,
                                             Map<Long, ImportLanyonBidDto> importHotelDataMap,
                                             Map<Long, List<Map<String, String>>> lanyonHotelDataMap,
                                             UserDTO userDTO){
        List<String> failedInfoList = new ArrayList<>();

        // 根据项目ID查项目机构ID
        OrgSubject companyDefaultSubject = new OrgSubject();
        OrgSubject payDefaultSubject = new OrgSubject();
        ProjectBasicInfoResponse projectBasicInfoResponse = new ProjectBasicInfoResponse();
        Response projectBasicInformation = projectService.queryProjectBasicInformation(projectId);
        if (projectBasicInformation.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);

            // 查企业默认签约主体信息
            ArrayList<Long> defaultIds = new ArrayList<>();
            defaultIds.add(projectBasicInfoResponse.getTenderOrgId());
            List<OrgSubject> defaultSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(defaultIds);
            if (!CollectionUtils.isEmpty(defaultSubjects)) {
                BeanUtils.copyProperties(defaultSubjects.get(0),companyDefaultSubject);
            }

            // 查公付机构默认签约主体信息
            if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                ArrayList<Long> payIds = new ArrayList<>();
                payIds.add(projectBasicInfoResponse.getCoPayerOrgId());
                List<OrgSubject> paySubjects = orgSubjectDao.selectDefaultSubjectByOrgId(payIds);
                if (!CollectionUtils.isEmpty(paySubjects)) {
                    BeanUtils.copyProperties(paySubjects.get(0),payDefaultSubject);
                }
            }
        }

        // 根据酒店维度保存报价
        for(Long hotelId : importHotelDataMap.keySet()){
            ImportLanyonBidDto importLanyonBidDto = importHotelDataMap.get(hotelId);
            ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
            projectIntentHotelRequest.setHotelId(hotelId);
            projectIntentHotelRequest.setProjectId(projectId);
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
            boolean needAddNewIntent = projectIntentHotel == null;
            boolean needUpdateIntent = false;
            // 报价所属酒店若有其他机构邀约记录，但是未报价（项目邀约了酒店，酒店没有处理）。则删除原邀约记录
            if(projectIntentHotel != null  &&  projectIntentHotel.getBidState() == HotelBidStateEnum.NO_BID.bidState.intValue()){
                projectIntentHotelService.clearProjectHotelBidData(projectIntentHotel);
                needAddNewIntent = true;
            }
            // 报价所属酒店存在议价中报价，更新报价
            if(projectIntentHotel != null  && projectIntentHotel.getBidState() == HotelBidStateEnum.UNDER_NEGOTIATION.bidState.intValue()){
                needUpdateIntent = true;
            }

            if(needAddNewIntent) {
                // 新增一条项目意向酒店记录
                projectIntentHotel = new ProjectIntentHotel();
                projectIntentHotel.setProjectId(projectId);
                projectIntentHotel.setHotelId(hotelId);
                projectIntentHotel.setHotelOrgId(userDTO.getOrgDTO().getOrgId());
                projectIntentHotel.setBidOrgId(userDTO.getOrgDTO().getOrgId());
                projectIntentHotel.setBidOrgType(userDTO.getOrgDTO().getOrgType());
                projectIntentHotel.setBidContactName(importLanyonBidDto.getContactName());
                projectIntentHotel.setBidContactEmail(importLanyonBidDto.getContactEmail());
                projectIntentHotel.setBidContactMobile(importLanyonBidDto.getContactMobile());
                projectIntentHotel.setIsUpload(RfpConstant.constant_1);
                projectIntentHotel.setBidUploadSource(BidUploadSourceEnum.LANYON.key);
                projectIntentHotel.setInviteStatus(StateEnum.Invalid.key);
                projectIntentHotel.setSendMailStatus(StateEnum.Invalid.key);
                projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
                if (companyDefaultSubject.getSubjectId() != null) {
                    projectIntentHotel.setDistributorSubjectId(companyDefaultSubject.getSubjectId());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getBank())) {
                    projectIntentHotel.setDistributorBank(companyDefaultSubject.getBank());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountName())) {
                    projectIntentHotel.setDistributorAccountName(companyDefaultSubject.getAccountName());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountNumber())) {
                    projectIntentHotel.setDistributorAccountNumber(companyDefaultSubject.getAccountNumber());
                }

                // 只有项目选择了第三方公付服务才更新
                if (CoPayerTypeEnum.THIRD_PARTY_PAYMENT.key.equals(projectBasicInfoResponse.getCoPayerType())) {
                    if (payDefaultSubject.getSubjectId() != null) {
                        projectIntentHotel.setCoPayerSubjectId(payDefaultSubject.getSubjectId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getBank())) {
                        projectIntentHotel.setCoPayerBank(payDefaultSubject.getBank());
                    }
                    if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                        projectIntentHotel.setCoPayerOrgId(projectBasicInfoResponse.getCoPayerOrgId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountName())) {
                        projectIntentHotel.setCoPayerAccountName(payDefaultSubject.getAccountName());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountNumber())) {
                        projectIntentHotel.setCoPayerAccountNumber(payDefaultSubject.getAccountNumber());
                    }
                }

                // 酒店集团LANYON导入报价，酒店集团报价人信息自动带入导入报价人信息
                if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
                    projectIntentHotel.setHotelGroupBidContactName(userDTO.getUserName());
                    projectIntentHotel.setHotelGroupBidContactEmail(userDTO.getEmail());
                    projectIntentHotel.setHotelGroupBidContactMobile(userDTO.getMobile());
                }
                projectIntentHotel.setCreator(operator);
                projectIntentHotel.setModifier(operator);
                projectIntentHotelDao.insertProjectIntentHotel(projectIntentHotel);
            }
            // 更新报价信息
            if(needUpdateIntent){
                // 酒店集团LANYON导入报价，酒店集团报价人信息自动带入导入报价人信息
                if(Objects.equals(userDTO.getOrgDTO().getOrgType(), OrgTypeEnum.HOTELGROUP.key)){
                    projectIntentHotel.setHotelGroupBidContactName(userDTO.getUserName());
                    projectIntentHotel.setHotelGroupBidContactEmail(userDTO.getEmail());
                    projectIntentHotel.setHotelGroupBidContactMobile(userDTO.getMobile());
                }
                projectIntentHotel.setBidContactName(importLanyonBidDto.getContactName());
                projectIntentHotel.setBidContactEmail(importLanyonBidDto.getContactEmail());
                projectIntentHotel.setBidContactMobile(importLanyonBidDto.getContactMobile());
                projectIntentHotel.setIsUpload(RfpConstant.constant_1);
                projectIntentHotel.setBidUploadSource(BidUploadSourceEnum.LANYON.key);
                projectIntentHotel.setInviteStatus(StateEnum.Invalid.key);
                projectIntentHotel.setSendMailStatus(StateEnum.Invalid.key);
                projectIntentHotel.setBidState(HotelBidStateEnum.NO_BID.bidState);
                projectIntentHotelDao.updateBidContactInfo(projectIntentHotel);
            }


            // 项目ID和酒店ID联合索引
            ProjectHotelBidStrategy projectHotelBidStrategy = new ProjectHotelBidStrategy();
            projectHotelBidStrategy.setHotelId(projectIntentHotel.getHotelId());
            projectHotelBidStrategy.setProjectId(projectIntentHotel.getProjectId());
            ProjectHotelBidStrategy dbProjectHotelBidStrategy = projectHotelBidStrategyDao.selectByProjectIdAndHotelID(projectHotelBidStrategy);
            if(dbProjectHotelBidStrategy != null) {
                projectHotelBidStrategyDao.deleteByProjectAndHotelId(dbProjectHotelBidStrategy);
            }
            projectHotelBidStrategy.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            projectHotelBidStrategy.setCreator(operator);
            // 新增
            projectHotelBidStrategyDao.insert(projectHotelBidStrategy);

            // 初始化用户自定义策略
            insertProjectCustomBidStrategy(projectId, hotelId, projectIntentHotel.getProjectIntentHotelId(), importLanyonBidDto.getProjectCustomBidStrategyList(), operator);

            // 新增可用日期
            int insertApplicableDayCount = priceApplicableDayService.insertOrUpdateProjectHotelPriceApplicableDay(false, projectId, hotelId, importLanyonBidDto.getPriceApplicableDayList(), operator);
            if(insertApplicableDayCount == 0){
                failedInfoList.add("新增酒店可用日期失败: " + hotelId);
                continue;
            }

            // 新增不可用日期
            if(CollectionUtils.isNotEmpty(importLanyonBidDto.getUnApplicableDayList())) {
                List<PriceUnapplicableDay> priceUnapplicableDayList = priceUnapplicableDayService.selectPriceUnapplicableDayListByIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                if(CollectionUtils.isNotEmpty(priceUnapplicableDayList)){
                    for(PriceUnapplicableDay priceUnapplicableDay : priceUnapplicableDayList){
                        priceUnapplicableDayService.deletePriceUnapplicableDay(priceUnapplicableDay);
                    }
                }
                PriceUnapplicableDayDto priceUnapplicableDayDto = new PriceUnapplicableDayDto();
                priceUnapplicableDayDto.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                priceUnapplicableDayDto.setProjectId(projectId);
                priceUnapplicableDayDto.setHotelId(hotelId);
                priceUnapplicableDayDto.setCreator(operator);
                priceUnapplicableDayDto.setPriceUnapplicableDayList(importLanyonBidDto.getUnApplicableDayList());
                Response response = priceUnapplicableDayService.insertPriceUnapplicableDay(priceUnapplicableDayDto);
                if (!Objects.equals(response.getResult(), ReturnResultEnum.SUCCESS.errorNo)) {
                    failedInfoList.add("新增酒店不可用日期失败: " + hotelId);
                    continue;
                }
            }

            // 按照房档归类新增
            Map<Integer, List<ImportLanyonHotelPriceGroupBidDto>> levelHotelGroupBidMap = importHotelDataMap.get(hotelId).getLevelPriceGroupMap();
            for(Integer roomLevelNo : levelHotelGroupBidMap.keySet()){
                ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
                queryProjectHotelPriceGroup.setProjectId(projectId);
                queryProjectHotelPriceGroup.setHotelId(hotelId);
                queryProjectHotelPriceGroup.setRoomLevelNo(roomLevelNo);
                List<ProjectHotelPriceGroup> projectHotelPriceGroups = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
                Map<String, ProjectHotelPrice> dbPriceMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(projectHotelPriceGroups)){
                    boolean finalNeedUpdateIntent = needUpdateIntent;
                    projectHotelPriceGroups.forEach(item -> {
                        if(finalNeedUpdateIntent) {
                            List<ProjectHotelPrice> dbPriceList = projectHotelPriceDao.selectInfoByProjectHotelGroupId(item.getHotelPriceGroupId());
                            for (ProjectHotelPrice dbPrice : dbPriceList) {
                                dbPriceMap.put(generatePriceKey(item, dbPrice), dbPrice);
                            }
                        }
                        long groupId = item.getHotelPriceGroupId();
                        projectHotelPriceGroupDao.deleteByPrimaryKey(groupId);
                        projectHotelPriceDao.deleteByGroupId(groupId);
                    });
                }

                // 初始化价格档次
                ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse = new ProjectHotelPriceLevelResponse();
                projectHotelPriceLevelResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                projectHotelPriceLevelResponse.setRoomLevelNo(roomLevelNo);

                // 新增报价组
                List<ProjectHotelPriceGroupResponse> groupResponseList = new ArrayList<>();
                for(ImportLanyonHotelPriceGroupBidDto lanyonHotelPriceGroupBidDto : levelHotelGroupBidMap.get(roomLevelNo)){
                    ProjectHotelPriceGroupResponse priceGroup = new ProjectHotelPriceGroupResponse();
                    priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    priceGroup.setHotelId(hotelId);
                    priceGroup.setProjectId(projectId);
                    priceGroup.setRoomLevelNo(roomLevelNo);
                    priceGroup.setLanyonRoomDesc(importLanyonBidDto.getLevelRoomTypeDescMap().get(roomLevelNo));
                    priceGroup.setApplicableWeeks("2,3,4,5,6,7,1");
                    priceGroup.setLevelTotalRoomCount(importLanyonBidDto.getLevelRoomTypeNumberMap().get(roomLevelNo));
                    priceGroup.setLra(lanyonHotelPriceGroupBidDto.getLra());
                    priceGroup.setCancelRestrictType(lanyonHotelPriceGroupBidDto.getCancelRestrictType());
                    priceGroup.setCancelRestrictDay(lanyonHotelPriceGroupBidDto.getCancelRestrictDay());
                    priceGroup.setCancelRestrictTime(lanyonHotelPriceGroupBidDto.getCancelRestrictTime());
                    priceGroup.setCreator(operator);
                    Long hotelPriceGroupId = projectHotelPriceGroupDao.selectNextSequenceKey();
                    priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                    priceGroup.setRemark(lanyonHotelPriceGroupBidDto.getRemark());

                    List<ProjectHotelPrice> hotelPriceList = new ArrayList<>();
                    for(ImportLanyonHotelPriceBidDto priceBidDto : lanyonHotelPriceGroupBidDto.getImportLanyonHotelPriceBidDtos()){
                        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
                        projectHotelPrice.setHotelPriceGroupId(hotelPriceGroupId);
                        projectHotelPrice.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                        projectHotelPrice.setPriceType(priceBidDto.getPriceType());
                        projectHotelPrice.setProjectId(projectId);
                        projectHotelPrice.setHotelId(projectId);
                        projectHotelPrice.setRoomLevelNo(roomLevelNo);
                        projectHotelPrice.setBreakfastNum(priceBidDto.getBreakfastNum());
                        projectHotelPrice.setBasePrice(priceBidDto.getBasePrice());
                        projectHotelPrice.setCreator(operator);

                        if(needUpdateIntent){
                            String lastPriceKey = generatePriceKey(priceGroup, projectHotelPrice);
                            ProjectHotelPrice dbPrice = dbPriceMap.get(lastPriceKey);
                            if(dbPrice != null) {
                                projectHotelPrice.setLastBasePrice(dbPrice.getBasePrice());
                            }
                        }
                        hotelPriceList.add(projectHotelPrice);
                    }
                    priceGroup.setHotelPriceList(hotelPriceList);
                    groupResponseList.add(priceGroup);
                }
                // Set total level room count
                if(CollectionUtils.isNotEmpty(groupResponseList)) {
                    projectHotelPriceLevelResponse.setTotalRoomCount(groupResponseList.get(0).getLevelTotalRoomCount());
                }
                projectHotelPriceLevelResponse.setProjectHotelPriceGroupResponseList(groupResponseList);

                Response insertPriceLevelResponse = projectHotelPriceGroupService.insertProjectHotelPriceLevel(projectIntentHotel, projectHotelPriceLevelResponse, operator);
                if(!Objects.equals(insertPriceLevelResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                    failedInfoList.add("新增酒店房档失败酒店ID: " + hotelId + "房档号： " + roomLevelNo + "原因: " + insertPriceLevelResponse.getMsg());
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(failedInfoList)){
                continue;
            }

            // 更新上传状态 议价中报价改为修改报价，其他为新标状态
            ProjectIntentHotel updateProjectIntentHotel = new ProjectIntentHotel();
            updateProjectIntentHotel.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            updateProjectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);
            updateProjectIntentHotel.setBidState(needUpdateIntent ? HotelBidStateEnum.UPDATED_BID.bidState :  HotelBidStateEnum.NEW_BID.bidState);
            updateProjectIntentHotel.setModifier(userDTO.getOperator());
            projectIntentHotelDao.updateProjectIntentHotelBidState(updateProjectIntentHotel);

            // 记录更新日志
            bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, "Lanyon导入报价，签约状态为 " + HotelBidStateEnum.getValueByKey(updateProjectIntentHotel.getBidState()));

            // 记录lanyon导入报价json数据
            LanyonImportData lanyonImportData = new LanyonImportData();
            lanyonImportData.setProjectId(projectId);
            lanyonImportData.setHotelId(hotelId);
            lanyonImportData.setCreator(userDTO.getOperator());
            int i=1;
            for(Map<String, String> dataMap : lanyonHotelDataMap.get(hotelId)){
                lanyonImportData.setDataType(i);
                lanyonImportData.setJsonData(JSON.toJSONString(dataMap));
                lanyonImportService.insertOrUpdateLanyonImportData(lanyonImportData);
                i++;
            }
        }


        // 定义返回数据
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(failedInfoList.isEmpty() ? ReturnResultEnum.SUCCESS.message : "存在报价失败酒店");
        response.setData(failedInfoList);
        return response;
    }

    private void initProjectCustomBidStrategy(Long projectId, Long hotelId, Long projectIntentHotelId, List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses, String operator){
        // 新增自定义策略
        if (CollectionUtils.isNotEmpty(queryCustomTendStrategyResponses)) {
            QueryProjectCustomBidStrategyDto queryProjectCustomBidStrategyDto = new QueryProjectCustomBidStrategyDto();
            queryProjectCustomBidStrategyDto.setProjectId(projectId);
            queryProjectCustomBidStrategyDto.setHotelId(hotelId);
            List<ProjectCustomBidStrategy> dbprojectCustomBidStrategyList = projectCustomBidStrategyDao.queryProjectCustomBidStrategy(queryProjectCustomBidStrategyDto);
            if (CollectionUtils.isNotEmpty(dbprojectCustomBidStrategyList)) {
                projectCustomBidStrategyDao.deleteByProjectHotelId(queryProjectCustomBidStrategyDto);
            }
            List<ProjectCustomBidStrategy> projectCustomBidStrategyList = new ArrayList<>();
            for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : queryCustomTendStrategyResponses) {
                ProjectCustomBidStrategy projectCustomBidStrategy = new ProjectCustomBidStrategy();
                projectCustomBidStrategy.setProjectIntentHotelId(projectIntentHotelId);
                projectCustomBidStrategy.setCustomTendStrategyId(queryCustomTendStrategyResponse.getCustomTendStrategyId());
                projectCustomBidStrategy.setCreator(operator);
                projectCustomBidStrategy.setModifier(operator);
                projectCustomBidStrategy.setHotelId(hotelId);
                projectCustomBidStrategy.setProjectId(projectId);
                projectCustomBidStrategy.setStrategyType(queryCustomTendStrategyResponse.getStrategyType());
                projectCustomBidStrategy.setStrategyName(queryCustomTendStrategyResponse.getStrategyName());
                projectCustomBidStrategy.setSupportStrategyName(0);
                projectCustomBidStrategyList.add(projectCustomBidStrategy);
                if(!StringUtil.isValidString(projectCustomBidStrategy.getSupportStrategyText())) {
                    projectCustomBidStrategy.setSupportStrategyText("");
                }
            }
            projectCustomBidStrategyDao.batchMergeProjectCustomBidStrategy(projectCustomBidStrategyList);
        }

    }


    private void insertProjectCustomBidStrategy(Long projectId, Long hotelId, Long projectIntentHotelId, List<ProjectCustomBidStrategy> projectCustomBidStrategyList, String operator){
        // 新增自定义策略
        if (CollectionUtils.isNotEmpty(projectCustomBidStrategyList)) {
            QueryProjectCustomBidStrategyDto queryProjectCustomBidStrategyDto = new QueryProjectCustomBidStrategyDto();
            queryProjectCustomBidStrategyDto.setProjectId(projectId);
            queryProjectCustomBidStrategyDto.setHotelId(hotelId);
            List<ProjectCustomBidStrategy> dbprojectCustomBidStrategyList = projectCustomBidStrategyDao.queryProjectCustomBidStrategy(queryProjectCustomBidStrategyDto);
            if (CollectionUtils.isNotEmpty(dbprojectCustomBidStrategyList)) {
                projectCustomBidStrategyDao.deleteByProjectHotelId(queryProjectCustomBidStrategyDto);
            }
            for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategyList) {
                projectCustomBidStrategy.setProjectIntentHotelId(projectIntentHotelId);
                if(!StringUtil.isValidString(projectCustomBidStrategy.getSupportStrategyText())) {
                    projectCustomBidStrategy.setSupportStrategyText("");
                }
            }
            projectCustomBidStrategyDao.batchMergeProjectCustomBidStrategy(projectCustomBidStrategyList);
        }

    }
    @Override
    public Response batchInsertHotelGroupBidData(String operator, Long projectId, Map<Long, List<ImportHotelGroupBidDto>> importHotelDataMap, UserDTO userDTO) {
        List<String> failedInfoList = new ArrayList<>();

        // 根据项目ID查项目机构ID
        OrgSubject companyDefaultSubject = new OrgSubject();
        OrgSubject payDefaultSubject = new OrgSubject();
        ProjectBasicInfoResponse projectBasicInfoResponse = new ProjectBasicInfoResponse();
        Response projectBasicInformation = projectService.queryProjectBasicInformation(projectId);
        if (projectBasicInformation.getData() != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);

            // 查企业默认签约主体信息
            ArrayList<Long> defaultIds = new ArrayList<>();
            defaultIds.add(projectBasicInfoResponse.getTenderOrgId());
            List<OrgSubject> defaultSubjects = orgSubjectDao.selectDefaultSubjectByOrgId(defaultIds);
            if (!CollectionUtils.isEmpty(defaultSubjects)) {
                BeanUtils.copyProperties(defaultSubjects.get(0),companyDefaultSubject);
            }

            // 查公付机构默认签约主体信息
            if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                ArrayList<Long> payIds = new ArrayList<>();
                payIds.add(projectBasicInfoResponse.getCoPayerOrgId());
                List<OrgSubject> paySubjects = orgSubjectDao.selectDefaultSubjectByOrgId(payIds);
                if (!CollectionUtils.isEmpty(paySubjects)) {
                    BeanUtils.copyProperties(paySubjects.get(0),payDefaultSubject);
                }
            }
        }

        // 查询项目策略信息
        ProjectHotelTendStrategy projectHotelTendStrategy = projectHotelTendStrategyDao.selectByPrimaryKey(projectId);

        // 查询项目自定义策略
        QueryCustomTendStrategyRequest queryCustomTendStrategyRequest = new QueryCustomTendStrategyRequest();
        queryCustomTendStrategyRequest.setProjectId(projectId);
        List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses = projectCustomTendStrategyDao.queryProjectCustomTendStrategy(queryCustomTendStrategyRequest);

        // 根据酒店维度保存报价
        List<ProjectIntentHotel> uploadProjectIntentHotelList = new ArrayList<>();
        for(Long hotelId : importHotelDataMap.keySet()){
            ImportHotelGroupBidDto importHotelGroupBidDto = importHotelDataMap.get(hotelId).get(0);
            ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
            projectIntentHotelRequest.setHotelId(hotelId);
            projectIntentHotelRequest.setProjectId(projectId);
            ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);
            boolean needAddNewIntent = projectIntentHotel == null;
            // 报价所属酒店若有其他机构邀约记录，但是未报价（项目邀约了酒店，酒店没有处理）。则删除原邀约记录
            if(projectIntentHotel != null  &&  projectIntentHotel.getBidState() == HotelBidStateEnum.NO_BID.bidState.intValue()){
                projectIntentHotelService.clearProjectHotelBidData(projectIntentHotel);
                needAddNewIntent = true;
            }
            if(needAddNewIntent) {
                // 新增一条项目意向酒店记录
                projectIntentHotel = new ProjectIntentHotel();
                projectIntentHotel.setProjectId(projectId);
                projectIntentHotel.setHotelId(hotelId);
                projectIntentHotel.setHotelOrgId(userDTO.getOrgDTO().getOrgId());
                projectIntentHotel.setBidOrgId(userDTO.getOrgDTO().getOrgId());
                projectIntentHotel.setBidOrgType(userDTO.getOrgDTO().getOrgType());
                projectIntentHotel.setBidContactName(importHotelGroupBidDto.getContactName());
                projectIntentHotel.setBidContactEmail(importHotelGroupBidDto.getContactEmail());
                projectIntentHotel.setBidContactMobile(importHotelGroupBidDto.getContactMobile());
                projectIntentHotel.setIsUpload(RfpConstant.constant_1);
                projectIntentHotel.setBidUploadSource(BidUploadSourceEnum.JIALI.key);
                projectIntentHotel.setInviteStatus(StateEnum.Invalid.key);
                projectIntentHotel.setSendMailStatus(StateEnum.Invalid.key);
                projectIntentHotel.setBidState(HotelBidStateEnum.NEW_BID.bidState);
                if (companyDefaultSubject.getSubjectId() != null) {
                    projectIntentHotel.setDistributorSubjectId(companyDefaultSubject.getSubjectId());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getBank())) {
                    projectIntentHotel.setDistributorBank(companyDefaultSubject.getBank());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountName())) {
                    projectIntentHotel.setDistributorAccountName(companyDefaultSubject.getAccountName());
                }
                if (StringUtils.isNotEmpty(companyDefaultSubject.getAccountNumber())) {
                    projectIntentHotel.setDistributorAccountNumber(companyDefaultSubject.getAccountNumber());
                }

                // 只有项目选择了第三方公付服务才更新
                if (CoPayerTypeEnum.THIRD_PARTY_PAYMENT.key.equals(projectBasicInfoResponse.getCoPayerType())) {
                    if (payDefaultSubject.getSubjectId() != null) {
                        projectIntentHotel.setCoPayerSubjectId(payDefaultSubject.getSubjectId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getBank())) {
                        projectIntentHotel.setCoPayerBank(payDefaultSubject.getBank());
                    }
                    if (projectBasicInfoResponse.getCoPayerOrgId() != null) {
                        projectIntentHotel.setCoPayerOrgId(projectBasicInfoResponse.getCoPayerOrgId());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountName())) {
                        projectIntentHotel.setCoPayerAccountName(payDefaultSubject.getAccountName());
                    }
                    if (StringUtils.isNotEmpty(payDefaultSubject.getAccountNumber())) {
                        projectIntentHotel.setCoPayerAccountNumber(payDefaultSubject.getAccountNumber());
                    }
                }

                projectIntentHotel.setCreator(operator);
                projectIntentHotel.setModifier(operator);
                projectIntentHotelDao.insertProjectIntentHotel(projectIntentHotel);
            }

            // 酒店策略勾选
            if(projectHotelTendStrategy != null) {
                // 项目ID和酒店ID联合索引
                ProjectHotelBidStrategy queryProjectHotelBidStrategy = new ProjectHotelBidStrategy();
                queryProjectHotelBidStrategy.setHotelId(projectIntentHotel.getHotelId());
                queryProjectHotelBidStrategy.setProjectId(projectIntentHotel.getProjectId());
                ProjectHotelBidStrategy bidStrategyResult = projectHotelBidStrategyDao.selectByProjectIdAndHotelID(queryProjectHotelBidStrategy);
                if (bidStrategyResult != null) {
                    projectHotelBidStrategyDao.deleteByProjectAndHotelId(bidStrategyResult);
                }
            }
            ProjectHotelBidStrategy projectHotelBidStrategy = new ProjectHotelBidStrategy();
            projectHotelBidStrategy.setHotelId(hotelId);
            projectHotelBidStrategy.setProjectId(projectId);
            projectHotelBidStrategy.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            projectHotelBidStrategy.setCreator(operator);
            // 新增
            projectHotelBidStrategyDao.insert(projectHotelBidStrategy);

            // 初始化用户自定义策略
            initProjectCustomBidStrategy(projectId, hotelId, projectIntentHotel.getProjectIntentHotelId(), queryCustomTendStrategyResponses, operator);

            // 新增可用日期
            int insertApplicableDayCount = priceApplicableDayService.insertOrUpdateProjectHotelPriceApplicableDay(false, projectId, hotelId, importHotelGroupBidDto.getPriceApplicableDayList(), operator);
            if(insertApplicableDayCount == 0){
                failedInfoList.add("新增酒店可用日期失败: " + hotelId);
                continue;
            }

            // 新增不可用日期
            if(CollectionUtils.isNotEmpty(importHotelGroupBidDto.getUnApplicableDayList())) {
                List<PriceUnapplicableDay> priceUnapplicableDayList = priceUnapplicableDayService.selectPriceUnapplicableDayListByIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                if(CollectionUtils.isNotEmpty(priceUnapplicableDayList)){
                    for(PriceUnapplicableDay priceUnapplicableDay : priceUnapplicableDayList){
                        priceUnapplicableDayService.deletePriceUnapplicableDay(priceUnapplicableDay);
                    }
                }
                PriceUnapplicableDayDto priceUnapplicableDayDto = new PriceUnapplicableDayDto();
                priceUnapplicableDayDto.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                priceUnapplicableDayDto.setProjectId(projectId);
                priceUnapplicableDayDto.setHotelId(hotelId);
                priceUnapplicableDayDto.setCreator(operator);
                priceUnapplicableDayDto.setPriceUnapplicableDayList(importHotelGroupBidDto.getUnApplicableDayList());
                Response response = priceUnapplicableDayService.insertPriceUnapplicableDay(priceUnapplicableDayDto);
                if (!Objects.equals(response.getResult(), ReturnResultEnum.SUCCESS.errorNo)) {
                    failedInfoList.add("新增酒店不可用日期失败: " + hotelId);
                    continue;
                }

            }

            // 按照房档归类新增
            // 清空之前房型信息
            priceApplicableRoomDao.deleteByProjectHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
            Map<Integer, List<ImportHotelGroupBidDto>> levelHotelGroupBidMap = importHotelDataMap.get(hotelId).stream().collect(Collectors.groupingBy(ImportHotelGroupBidDto::getLevelNo));
            for(Integer roomLevelNo : levelHotelGroupBidMap.keySet()){
                ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
                queryProjectHotelPriceGroup.setProjectId(projectId);
                queryProjectHotelPriceGroup.setHotelId(hotelId);
                queryProjectHotelPriceGroup.setRoomLevelNo(roomLevelNo);
                List<ProjectHotelPriceGroup> projectHotelPriceGroups = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
                if(CollectionUtils.isNotEmpty(projectHotelPriceGroups)){
                    projectHotelPriceGroups.forEach(item -> {
                        long groupId = item.getHotelPriceGroupId();
                        projectHotelPriceGroupDao.deleteByPrimaryKey(groupId);
                        projectHotelPriceDao.deleteByGroupId(groupId);
                    });
                }
                ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse = new ProjectHotelPriceLevelResponse();
                projectHotelPriceLevelResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                projectHotelPriceLevelResponse.setRoomLevelNo(roomLevelNo);

                // 新增房型
                List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = new ArrayList<>();
                List<ImportHotelGroupBidDto> bidDtoList = levelHotelGroupBidMap.get(roomLevelNo);
                List<RoomInfoResponse> roomInfoResponseList = bidDtoList.get(0).getRoomInfoResponseList();
                if(CollectionUtils.isNotEmpty(roomInfoResponseList)) {
                    int i = 1;
                    for (RoomInfoResponse roomInfoResponse : roomInfoResponseList) {
                        PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse = new PriceApplicableRoomInfoResponse();
                        priceApplicableRoomInfoResponse.setProjectId(projectId);
                        priceApplicableRoomInfoResponse.setHotelId(hotelId);
                        priceApplicableRoomInfoResponse.setRoomTypeId(roomInfoResponse.getRoomId());
                        priceApplicableRoomInfoResponse.setRoomTypeName(roomInfoResponse.getRoomName());
                        priceApplicableRoomInfoResponse.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                        priceApplicableRoomInfoResponse.setDisplayOrder(i);
                        priceApplicableRoomInfoResponse.setRoomLevelNo(roomLevelNo);
                        priceApplicableRoomInfoResponse.setCreator(operator);
                        priceApplicableRoomInfoResponseList.add(priceApplicableRoomInfoResponse);

                        i++;
                    }
                    projectHotelPriceLevelResponse.setRoomResponseList(priceApplicableRoomInfoResponseList);
                }
                // 新增报价组
                Map<String, List<ImportHotelGroupBidDto>> bidDtoMap = bidDtoList.stream().collect(Collectors.groupingBy(o->generateHotelGroupPriceKey(o)));
                List<ProjectHotelPriceGroupResponse> groupResponseList = new ArrayList<>();
                for(String groupKey : bidDtoMap.keySet()){
                    ImportHotelGroupBidDto groupBidDto = bidDtoMap.get(groupKey).get(0);
                    ProjectHotelPriceGroupResponse priceGroup = new ProjectHotelPriceGroupResponse();
                    priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    priceGroup.setHotelId(hotelId);
                    priceGroup.setProjectId(projectId);
                    priceGroup.setRoomLevelNo(roomLevelNo);
                    priceGroup.setApplicableWeeks(groupBidDto.getApplicableWeeks());
                    priceGroup.setLra(groupBidDto.getLra());
                    priceGroup.setCancelRestrictType(groupBidDto.getCancelRestrictType());
                    priceGroup.setCancelRestrictDay(groupBidDto.getCancelRestrictDay());
                    priceGroup.setCancelRestrictTime(groupBidDto.getCancelRestrictTime());
                    priceGroup.setCreator(operator);
                    priceGroup.setLanyonRoomDesc(groupBidDto.getLanyonRoomDesc());
                    Long hotelPriceGroupId = projectHotelPriceGroupDao.selectNextSequenceKey();
                    priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                    priceGroup.setRemark(groupBidDto.getRemark());

                    List<ProjectHotelPrice> hotelPriceList = new ArrayList<>();
                    for(ImportHotelGroupBidDto priceBidDto : bidDtoMap.get(groupKey)){
                        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
                        projectHotelPrice.setHotelPriceGroupId(hotelPriceGroupId);
                        projectHotelPrice.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                        projectHotelPrice.setPriceType(priceBidDto.getPriceType());
                        projectHotelPrice.setProjectId(projectId);
                        projectHotelPrice.setHotelId(projectId);
                        projectHotelPrice.setRoomLevelNo(roomLevelNo);
                        projectHotelPrice.setBreakfastNum(priceBidDto.getBreakfastNum());
                        projectHotelPrice.setBasePrice(priceBidDto.getBasePrice());
                        projectHotelPrice.setCreator(operator);
                        projectHotelPrice.setRemark(priceBidDto.getRemark());
                        hotelPriceList.add(projectHotelPrice);
                    }
                    priceGroup.setHotelPriceList(hotelPriceList);
                    groupResponseList.add(priceGroup);
                }
                projectHotelPriceLevelResponse.setProjectHotelPriceGroupResponseList(groupResponseList);

                Response insertPriceLevelResponse = projectHotelPriceGroupService.insertProjectHotelPriceLevel(projectIntentHotel, projectHotelPriceLevelResponse, operator);
                if(!Objects.equals(insertPriceLevelResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                    failedInfoList.add("新增酒店房档失败酒店ID: " + hotelId + "房档号： " + roomLevelNo + "原因: " + insertPriceLevelResponse.getMsg());
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(failedInfoList)){
                continue;
            }
            // 设置默认房型
            HotelPriceMonitorRoom deleteHotelPriceMonitorRoom = new HotelPriceMonitorRoom();
            deleteHotelPriceMonitorRoom.setHotelId(hotelId);
            deleteHotelPriceMonitorRoom.setProjectId(projectId);
            hotelPriceMonitorRoomDao.deleteData(deleteHotelPriceMonitorRoom);
            MonitorRoomResponse monitorRoomResponse = projectHotelPriceService.selectDefaultMonitorRoomResponse(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
            if(monitorRoomResponse != null) {
                HotelPriceMonitorRoom hotelPriceMonitorRoom = new HotelPriceMonitorRoom();
                BeanUtils.copyProperties(monitorRoomResponse, hotelPriceMonitorRoom);
                hotelPriceMonitorRoom.setCreator(RfpConstant.CREATOR);
                int insertResult = hotelPriceMonitorRoomDao.insert(hotelPriceMonitorRoom);
                if (insertResult == 0) {
                    failedInfoList.add("酒店关注默认房型失败酒店ID: " + hotelId + "房型ID: " + hotelPriceMonitorRoom.getRoomTypeId() + "早餐: " + hotelPriceMonitorRoom.getBreakfastNum());
                }
            }
            uploadProjectIntentHotelList.add(projectIntentHotel);
        }

        Response response = new Response();
        // 更新为中签
        if(CollectionUtils.isEmpty(failedInfoList)){
            UpdateProjectIntentHotelDto updateProjectIntentHotelDto = new UpdateProjectIntentHotelDto();
            updateProjectIntentHotelDto.setProjectId(projectId);
            updateProjectIntentHotelDto.setHotelIds(new ArrayList<>(importHotelDataMap.keySet()));
            updateProjectIntentHotelDto.setOperateType(8);
            updateProjectIntentHotelDto.setBidState(HotelBidStateEnum.BID_WINNING.bidState);
            response = projectService.updateProjectIntentHotel(updateProjectIntentHotelDto, userDTO);
            if(!Objects.equals(response.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                logger.error("酒店报价上传成功，但是更新中签状态失败 {}", response);
                return response;
            }

            // 记录更新日志
            for(ProjectIntentHotel projectIntentHotel : uploadProjectIntentHotelList){
                bidOperateLogService.saveOperateLog(projectIntentHotel, userDTO, "加力导入报价，签约状态为 " + HotelBidStateEnum.getValueByKey(HotelBidStateEnum.BID_WINNING.bidState));
            }
        }

        // 定义返回数据
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(failedInfoList.isEmpty() ? ReturnResultEnum.SUCCESS.message : "存在报价失败酒店");
        response.setData(failedInfoList);
        return response;
    }

    private String generateHotelGroupPriceKey(ImportHotelGroupBidDto importHotelGroupBidDto){
        return importHotelGroupBidDto.getApplicableWeeks() + "#" + importHotelGroupBidDto.getLra() + "#" + importHotelGroupBidDto.getCancelRestrictType() + "#" + importHotelGroupBidDto.getCancelRestrictDay() + "#" + importHotelGroupBidDto.getCancelRestrictTime();
    }

    private Date lanyonStringToDate(String date) {
        return DateUtil.stringToDate(date, "yyyy/MM/dd");
    }

    private List<PriceApplicableDay> convertLanyonPriceApplicableDay(long projectId, long hotelId, String cellValue, int priceType){
        List<PriceApplicableDay> priceApplicableDayList = new ArrayList<>();
        if(StringUtil.isValidString(cellValue)){
            String[] days = cellValue.split("_");
            PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
            priceApplicableDay.setProjectId(projectId);
            priceApplicableDay.setHotelId(hotelId);
            priceApplicableDay.setPriceType(priceType);
            priceApplicableDay.setStartDate(lanyonStringToDate(days[0]));
            priceApplicableDay.setEndDate(lanyonStringToDate(days[1]));
            priceApplicableDayList.add(priceApplicableDay);
        }
        return priceApplicableDayList;
    }

    private PriceApplicableDay convertSinglePriceApplicableDay(long projectId, long hotelId, Date startDate, Date endDate, int priceType){
        PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
        priceApplicableDay.setProjectId(projectId);
        priceApplicableDay.setHotelId(hotelId);
        priceApplicableDay.setPriceType(priceType);
        priceApplicableDay.setStartDate(startDate);
        priceApplicableDay.setEndDate(endDate);
        return priceApplicableDay;
    }

    private PriceUnapplicableDay convertLanyonPriceUnApplicableDay(long projectId, long hotelId, Date startDate, Date endDate){
        PriceUnapplicableDay unApplicableDay = new PriceUnapplicableDay();
        unApplicableDay.setProjectId(projectId);
        unApplicableDay.setHotelId(hotelId);
        unApplicableDay.setStartDate(startDate);
        unApplicableDay.setEndDate(endDate);
        return unApplicableDay;
    }

    private List<PriceApplicableDay> convertPriceApplicableDay(long projectId, long hotelId, String cellValue, int priceType){
        List<PriceApplicableDay> priceApplicableDayList = new ArrayList<>();
        if(StringUtil.isValidString(cellValue)){
            String[] days = cellValue.split(",");
            for(String dayRange : days){
                String[] startEndDay =  dayRange.split("/");
                PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
                priceApplicableDay.setProjectId(projectId);
                priceApplicableDay.setHotelId(hotelId);
                priceApplicableDay.setPriceType(priceType);
                priceApplicableDay.setStartDate(DateUtil.stringToDate(startEndDay[0]));
                priceApplicableDay.setEndDate(DateUtil.stringToDate(startEndDay[1]));
                priceApplicableDayList.add(priceApplicableDay);
            }
        }
        return priceApplicableDayList;
    }

    private List<PriceUnapplicableDay> convertPriceUnApplicableDay(long projectId, long hotelId, String cellValue){
        List<PriceUnapplicableDay> priceUnApplicableDayList = new ArrayList<>();
        if(StringUtil.isValidString(cellValue)){
            String[] days = cellValue.split(",");
            for(String dayRange : days){
                String[] startEndDay =  dayRange.split("/");
                PriceUnapplicableDay unApplicableDay = new PriceUnapplicableDay();
                unApplicableDay.setProjectId(projectId);
                unApplicableDay.setHotelId(hotelId);
                unApplicableDay.setStartDate(DateUtil.stringToDate(startEndDay[0]));
                unApplicableDay.setEndDate(DateUtil.stringToDate(startEndDay[1]));
                priceUnApplicableDayList.add(unApplicableDay);
            }
        }
        return priceUnApplicableDayList;
    }

    // 处理object转List方法
    public List<Object> objToList(Object obj) {
        List<Object> list = new ArrayList<Object>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                list.add(o);
            }
            return list;
        }
        return null;
    }

    // 计算权重方法
    public BigDecimal calculateWeight(Long projectId, BidStrategyResponse bidStrategyResponse) {
        Project project = projectDao.selectByPrimaryKey(projectId);
        // 权重2.0计算报价权重
        if(project.getTotalWeight() != null && project.getTotalWeight().compareTo(BigDecimal.ZERO) > 0){
            try {
                // 满足条件，加入缓存队列
                RedisService.lpush(RedisConstant.PROJECT_HOTEL_WEIGHT_GENERATING,project.getProjectId() + "_" + bidStrategyResponse.getHotelId());
            } catch (Exception ex){
                logger.error("项目权重生成失败：" + ex.getMessage());
            }
            return BigDecimal.ZERO;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        BigDecimal sumWeight = new BigDecimal(0);
        List<HotelSummaryInfoResponseDto> hotelSummaryInfoResponseDtos = null;

        // 项目权重
        Response response = projectService.queryProjectHotelTendWeight(bidStrategyResponse.getProjectId());
        // 意向酒店
        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        BeanUtils.copyProperties(bidStrategyResponse,projectIntentHotelRequest);
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectInfoByProjectIdAndHotelId(projectIntentHotelRequest);

        ProjectHotelTendWeight projectHotelTendWeight = objectMapper.convertValue(response.getData(), ProjectHotelTendWeight.class);

        if (projectHotelTendWeight != null) {
            /** 间夜量 */
            if (projectHotelTendWeight.getWhtRoomNightState().equals(StateEnum.Effective.key)) {
                if (projectIntentHotel.getLatestYearRoomNight() != null && projectIntentHotel.getLatestYearRoomNight().longValue() >= 120) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtfRoomNight());
                    logger.info("间夜量：" + sumWeight);
                }
            }

            /** 间夜量(额外加分项) */
            if (projectHotelTendWeight.getWhtRoomNightExState().equals(StateEnum.Effective.key)) {
                if (projectIntentHotel.getLatestYearRoomNight() != null &&  projectIntentHotel.getLatestYearRoomNight().longValue() >= 600) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtRoomNightEx());
                    logger.info("间夜量(额外加分项)：" + sumWeight);
                }
            }

            // 酒店最低价  hotelMinPriceResponses 只有一条数据
            ArrayList<Long> longs = new ArrayList<>();
            longs.add(bidStrategyResponse.getHotelId());
            List<HotelMinPriceResponse> hotelMinPriceResponses = projectHotelPriceDao.selectMinPriceByHotelIdsAndProjectId(bidStrategyResponse.getProjectId(), longs);

            // 酒店7天起价
            HotelLowestPriceRequest hotelLowestPriceRequest = new HotelLowestPriceRequest();
            ArrayList<Long> lowerPriceHotelIds = new ArrayList<>();
            lowerPriceHotelIds.add(bidStrategyResponse.getHotelId());
            hotelLowestPriceRequest.setHotelIds(lowerPriceHotelIds);

            // tmc签名
            com.fangcang.hotel.delivery.tmc.common.tmchub.api.request.SignatureHelpRequestDto signatureHelpRequestDto = new com.fangcang.hotel.delivery.tmc.common.tmchub.api.request.SignatureHelpRequestDto();
            signatureHelpRequestDto.setPartnerCode(BaseConfig.getPartnerCode());
            signatureHelpRequestDto.setSecurityKey(BaseConfig.getSecurityKey());
            com.fangcang.tmc.hub.api.response.Response<HotelLowestPriceResponse> hotelLowestPriceResponseResponse = tmcHubApiManager.queryHotelLowestPrice(hotelLowestPriceRequest, signatureHelpRequestDto);

            Double minLowerPrice = null;
            if (!CollectionUtils.isEmpty(hotelLowestPriceResponseResponse.getBussinessResponse().getHotelLowestPrices())) {
                Optional<HotelPriceItem> min = hotelLowestPriceResponseResponse.getBussinessResponse().getHotelLowestPrices().get(0).getPriceItems().stream().min(Comparator.comparingDouble(HotelPriceItem::getSalePrice));
                minLowerPrice = min.get().getSalePrice();
            }

            /** 价格优势 */
            if (projectHotelTendWeight.getWhtPriceAdvantState().equals(StateEnum.Effective.key)) {
                if (CollectionUtils.isNotEmpty(hotelMinPriceResponses) && hotelMinPriceResponses.get(0).getMinPrice() != null  && minLowerPrice != null) {
                    BigDecimal minPrice = hotelMinPriceResponses.get(0).getMinPrice();
                    BigDecimal lowerPrice = new BigDecimal(minLowerPrice);
                    if (((lowerPrice.subtract(minPrice)).divide(lowerPrice, 2,BigDecimal.ROUND_HALF_UP)).compareTo(new BigDecimal(0.15)) > 0) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtPriceAdvant());
                        logger.info("价格优势：" + sumWeight);
                    }
                }
            }

            /** 价格优势(额外加分项) */
            if (projectHotelTendWeight.getWhtPriceAdvantExState().equals(StateEnum.Effective.key)) {
                if (CollectionUtils.isNotEmpty(hotelMinPriceResponses) && hotelMinPriceResponses.get(0).getMinPrice() != null && minLowerPrice != null) {
                    BigDecimal minPrice = hotelMinPriceResponses.get(0).getMinPrice();
                    BigDecimal lowerPrice = new BigDecimal(minLowerPrice);
                    if (((lowerPrice.subtract(minPrice)).divide(lowerPrice,2,BigDecimal.ROUND_HALF_UP)).compareTo(new BigDecimal(0.25)) > 0) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtPriceAdvantEx());
                        logger.info("价格优势(额外加分项) ：" + sumWeight);
                    }
                }
            }

            Set<String> poiCityCodeList = new HashSet<>();
            // 企业项目POI信息
            Response projectPoiInfoData = projectService.queryProjectPoiInfo(bidStrategyResponse.getProjectId());
            ArrayList<ProjectPoiInfoResponse> projectPoiInfoResponseList = new ArrayList<>();
            if (projectPoiInfoData != null && projectPoiInfoData.getData() != null) {
                List<Object> objects = objToList(projectPoiInfoData.getData());
                if (CollectionUtils.isNotEmpty(objects)) {
                    for (Object object : objects) {
                        projectPoiInfoResponseList.add((ProjectPoiInfoResponse)object);
                    }
                }
            }
            // 统计POI城市
            if(CollectionUtils.isNotEmpty(projectPoiInfoResponseList)){
                for(ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList){
                    poiCityCodeList.add(projectPoiInfoResponse.getCityCode());
                }
            }

            /** 计算位置 */
            if (projectHotelTendWeight.getWhtLocationState().equals(StateEnum.Effective.key)) {
                // 酒店基础信息
                HotelSummaryInfoQueryDto hotelSummaryInfoQueryDto = new HotelSummaryInfoQueryDto();
                ArrayList<Long> hotelIds = new ArrayList<>();
                hotelIds.add(bidStrategyResponse.getHotelId());
                hotelSummaryInfoQueryDto.setHotelIds(hotelIds);

                try {
                    hotelSummaryInfoResponseDtos = hotelInfoFacade.queryHotelSummaryInfo(hotelSummaryInfoQueryDto);
                } catch (Exception e) {
                    logger.error("查询酒店基础信息失败",e);
                }



                if (!CollectionUtils.isEmpty(projectPoiInfoResponseList) && !CollectionUtils.isEmpty(hotelSummaryInfoResponseDtos)) {
                    // 需要关联城市，在相同城市才计算距离
                    for (ProjectPoiInfoResponse projectPoiInfoResponse : projectPoiInfoResponseList) {
                        if (hotelSummaryInfoResponseDtos.get(0).getCityCode().equals(projectPoiInfoResponse.getCityCode())) {
                            if (hotelSummaryInfoResponseDtos.get(0).getLatBaidu() != null && hotelSummaryInfoResponseDtos.get(0).getLngBaidu() != null) {
                                double distance = LocationUtil.getDistance(projectPoiInfoResponse.getLatBaiDu(), projectPoiInfoResponse.getLngBaiDu(), hotelSummaryInfoResponseDtos.get(0).getLatBaidu(), hotelSummaryInfoResponseDtos.get(0).getLngBaidu());
                                BigDecimal bigDecimal = new BigDecimal(distance);
                                if (new BigDecimal(2).compareTo(bigDecimal) >= 0) {
                                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtLocation());
                                    logger.info("需要关联城市，在相同城市才计算距离 ：" + sumWeight);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            /** 计算城市 城市权重，酒店在项目POI所在城市 */
            if (projectHotelTendWeight.getWhtCityState().equals(StateEnum.Effective.key)) {
                if(CollectionUtils.isNotEmpty(poiCityCodeList)) {
                    HotelResponse hotelResponse = hotelDao.selectHotelInfo(bidStrategyResponse.getHotelId());
                    if(poiCityCodeList.contains(hotelResponse.getCity())) {
                        sumWeight = sumWeight.add(projectHotelTendWeight.getWhtCity());
                        logger.info("计算城市 城市权重，酒店在项目POI所在城市 ：" + sumWeight);
                    }
                }
            }

            /** 查询报价信息 */
            List<ProjectHotelPriceLevelResponse> hotelPriceLevelResponseList = projectHotelPriceService.selectProjectHotelPriceLevelList(projectIntentHotelRequest);

            /** 计算差旅标准 */
            if (projectHotelTendWeight.getWhtTravelStandardState().equals(StateEnum.Effective.key)) {
                Response projectBasicInformation = projectService.queryProjectBasicInformation(bidStrategyResponse.getProjectId());
                ProjectBasicInfoResponse projectBasicInfoResponse = objectMapper.convertValue(projectBasicInformation.getData(), ProjectBasicInfoResponse.class);
                Boolean isMatchPrice = false;


                for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()) {
                            BigDecimal diffMinAmount = projectBasicInfoResponse.getDiffMinAmount();
                            BigDecimal diffMaxAmount = projectBasicInfoResponse.getDiffMaxAmount();
                            if (projectHotelPrice.getBasePrice().compareTo(diffMinAmount) >= 0 && projectHotelPrice.getBasePrice().compareTo(diffMaxAmount) <= 0) {
                                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtTravelStandard());
                                logger.info(" 计算差旅标准 ：" + sumWeight);
                                isMatchPrice = true;
                                break;
                            }
                        }
                        if(isMatchPrice){
                            break;
                        }
                    }
                    if(isMatchPrice){
                        break;
                    }
                }
            }

            /** OTA评分 平均分 */
            if (projectHotelTendWeight.getWhtOtaScoreState().equals(StateEnum.Effective.key)) {

                // 酒店基本信息
                HotelInfoRequest hotelInfoRequest = new HotelInfoRequest();
                ArrayList<Long> list = new ArrayList<>();
                list.add(bidStrategyResponse.getHotelId());
                hotelInfoRequest.setHotelIds(list);

                ArrayList<String> strings = new ArrayList<>();
                strings.add("comment");
                hotelInfoRequest.setSettings(strings);

                com.fangcang.tmc.hub.api.response.Response<HotelInfoResponse> hotelInfoResponseResponse = tmcHubApiManager.queryHotelInfo(hotelInfoRequest, signatureHelpRequestDto);

                if (hotelInfoResponseResponse.getBussinessResponse() != null &&
                        CollectionUtils.isNotEmpty(hotelInfoResponseResponse.getBussinessResponse().getHotelInfos()) &&
                        CollectionUtils.isNotEmpty(hotelInfoResponseResponse.getBussinessResponse().getHotelInfos().get(0).getComment()) ) {
                    String average_score = hotelInfoResponseResponse.getBussinessResponse().getHotelInfos().get(0).getComment().get(0).getAverage_score();
                    if (StringUtils.isNotEmpty(average_score)) {
                        BigDecimal bigDecimal = new BigDecimal(average_score);
                        if (bigDecimal.compareTo(new BigDecimal(4.5)) > 0) {
                            sumWeight = sumWeight.add(projectHotelTendWeight.getWhtOtaScore());
                            logger.info(" OTA评分 ：" + sumWeight);
                        }
                    }
                }
            }

            /** 计算发票 */
            if (projectHotelTendWeight.getWhtInvoiceState().equals(StateEnum.Effective.key)) {
                if (bidStrategyResponse.getProvideInvoiceType().equals(InvoiceEnum.SPECIAL.getKey()) &&
                        bidStrategyResponse.getProvideInvoiceTaxRate().compareTo(new BigDecimal(6)) >= 0) {
                    sumWeight = sumWeight.add(projectHotelTendWeight.getWhtInvoice());
                    logger.info(" 计算发票 ：" + sumWeight);
                }
            }

            /** 公司统一支付 */
            if (projectHotelTendWeight.getWhtCoPayState().equals(StateEnum.Effective.key) &&
                    bidStrategyResponse.getSupportCoPay().equals(StateEnum.Effective.key)) {
                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtCoPay());
                logger.info(" 公司统一支付 ：" + sumWeight);
            }

            /** 早餐 */
            if (projectHotelTendWeight.getWhtBreakfastState().equals(StateEnum.Effective.key)) {
                Boolean isMatchBreakfast = false;
                for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()) {
                            if (!projectHotelPrice.getBreakfastNum().equals(BreakfastNumEnum.ZERO.key)) {
                                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtBreakfast());
                                logger.info(" 早餐 ：" + sumWeight);
                                isMatchBreakfast = true;
                                break;
                            }
                        }
                        if(isMatchBreakfast){
                            break;
                        }
                    }
                    if(isMatchBreakfast){
                        break;
                    }
                }
            }

            /** LRA */
            if (projectHotelTendWeight.getWhtLraState().equals(StateEnum.Effective.key)) {
                boolean isMatchLar = false;
                for(ProjectHotelPriceLevelResponse hotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : hotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        if (projectHotelPriceGroupResponse.getLra().equals(StateEnum.Effective.key)) {
                            sumWeight = sumWeight.add(projectHotelTendWeight.getWhtLra());
                            logger.info(" LRA ：" + sumWeight);
                            isMatchLar = true;
                            break;
                        }
                    }
                    if(isMatchLar){
                        break;
                    }
                }
            }

            /** 退改规则 */
            if (projectHotelTendWeight.getWhtCanceState().equals(StateEnum.Effective.key)) {
                boolean isMatchCancal = false;
                for(ProjectHotelPriceLevelResponse hotelPriceLevelResponse : hotelPriceLevelResponseList){
                    for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : hotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                        if (projectHotelPriceGroupResponse.getCancelRestrictType().intValue() == 2 && projectHotelPriceGroupResponse.getCancelRestrictDay().intValue() == 0) {
                            if (Integer.valueOf(projectHotelPriceGroupResponse.getCancelRestrictTime().substring(0, 1)) <= 18) {
                                sumWeight = sumWeight.add(projectHotelTendWeight.getWhtCancel());
                                logger.info(" 退改规则 ：" + sumWeight);
                                isMatchCancal = true;
                                break;
                            }
                        }
                    }
                    if(isMatchCancal){
                        break;
                    }
                }
            }

            /** 结算 */
            /**
             if (projectHotelTendWeight.getWhtBalanceState().equals(StateEnum.Effective.key) &&
             bidStrategyResponse.getSupportMonthlyBalance().equals(StateEnum.Effective.key)) {
             sumWeight = sumWeight.add(projectHotelTendWeight.getWhtBalance());
             }
             **/

        }

        //自定义采购策略权重
        List<ProjectCustomBidStrategy> projectCustomBidStrategies = bidStrategyResponse.getProjectCustomBidStrategies();
        List<QueryCustomTendStrategyResponse> queryCustomTendStrategyResponses = projectService.queryProjectCustomTendStrategy(bidStrategyResponse.getProjectId());
        if(CollectionUtils.isNotEmpty(queryCustomTendStrategyResponses) && CollectionUtils.isNotEmpty(projectCustomBidStrategies)){
            Map<Long,QueryCustomTendStrategyResponse> queryCustomTendStrategyResponseMap = new HashMap<>();
            for (QueryCustomTendStrategyResponse queryCustomTendStrategyResponse : queryCustomTendStrategyResponses) {
                queryCustomTendStrategyResponseMap.put(queryCustomTendStrategyResponse.getCustomTendStrategyId(),queryCustomTendStrategyResponse);
            }
            for (ProjectCustomBidStrategy projectCustomBidStrategy : projectCustomBidStrategies) {
                // 文本录入不记录权重分
                if(projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.TEXT.key){
                    continue;
                }
                Long customTendStrategyId = projectCustomBidStrategy.getCustomTendStrategyId();
                QueryCustomTendStrategyResponse queryCustomTendStrategyResponse = queryCustomTendStrategyResponseMap.get(customTendStrategyId);
                // 没有启用权重
                if(queryCustomTendStrategyResponse.getWhtStrategyNameState() != RfpConstant.constant_1){
                    continue;
                }
                // 是否权重
                if(projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.YSE_OR_NO.key){
                    if(queryCustomTendStrategyResponse.getWhtStrategyName().doubleValue() >0 && Objects.equals(projectCustomBidStrategy.getSupportStrategyName(), RfpConstant.constant_1)){
                        sumWeight = sumWeight.add(queryCustomTendStrategyResponse.getWhtStrategyName());
                        logger.info(" 是否权重 ：" + sumWeight);
                    }
                }
                // 单选或者多选权重
                if(projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.CHECKBOX.key || projectCustomBidStrategy.getStrategyType() == CustomStrategyTypeEnum.RADIO.key){
                    for(CustomStrategyOptionVO customStrategyOptionVO : queryCustomTendStrategyResponse.getOptions()){
                        if(customStrategyOptionVO.getWeightScore() == null || customStrategyOptionVO.getWeightScore().doubleValue() <=0){
                            continue;
                        }
                        for(ProjectCustomBidStrategyOption projectCustomBidStrategyOption : projectCustomBidStrategy.getOptions()){
                            if(Objects.equals(projectCustomBidStrategyOption.getOptionId(), customStrategyOptionVO.getOptionId()) &&
                                    Objects.equals(projectCustomBidStrategyOption.getIsSupport(), RfpConstant.constant_1)
                            ){
                                sumWeight = sumWeight.add(customStrategyOptionVO.getWeightScore());
                                logger.info(" 单选或者多选权重 ：" + customStrategyOptionVO.getOptionName() + " OptionId " + customStrategyOptionVO.getOptionId() + ":" +
                                        customStrategyOptionVO.getWeightScore() + " sumWeight " + sumWeight);
                            }
                        }
                    }
                }
            }
        }
        return sumWeight;
    }

    private String convertToChineseWeek(String weeks, boolean isLanyonUpload){
        if(isLanyonUpload){
            return weeks;
        }
        String chineseWeek = "";
        for(String week : weeks.split(",")){
            chineseWeek = chineseWeek +  WeekEnum.getWeekByChinaSeq(Integer.valueOf(week)) + ",";
        }
        return  chineseWeek.substring(0, chineseWeek.length()-1);
    }

    private String getCellCancPol(Cell cancPolCell){
        String cancPol = "";
        if(isDateCell(cancPolCell)){
            Date cancPolDateTime = cancPolCell.getDateCellValue();
            cancPol = DateUtil.dateToString(cancPolDateTime, "HH:mm:ss");
        } else {
            cancPol = String.valueOf(cancPolCell.getStringCellValue());
        }
        return cancPol;
    }

    private boolean isDateCell(Cell cancPolCell){
        boolean isDateCell = false;
        try {
            isDateCell = org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cancPolCell);
        } catch (Exception ex){
            logger.info(ex.getMessage());
        }
        return isDateCell;
    }


}
