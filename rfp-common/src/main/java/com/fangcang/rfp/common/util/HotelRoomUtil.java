package com.fangcang.rfp.common.util;

import com.fangcang.util.StringUtil;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class HotelRoomUtil {

    /**
     * 获取房型面积
     */
    public static BigDecimal matchRoomSize(String roomSize) {
        if (!StringUtil.isValidString(roomSize)) {
            return null;
        }
        String matchString = roomSize;
        Pattern pattern = Pattern.compile("(\\d+\\.?\\d*)");
        if(roomSize.contains("层")) {
            matchString = matchString.substring(0, matchString.indexOf("米"));
        };
        if(roomSize.contains("m")) {
            matchString = matchString.substring(0, matchString.indexOf("m"));
        };
        if(matchString.contains("-")) {
            String[] split = matchString.split("-");
            matchString = split[1];
        } else if(matchString.contains("°")){
            matchString = "";
        }
        Matcher matcher = pattern.matcher(matchString);
        String numberStr = matcher.find() ? matcher.group(1) : null;
        if(numberStr != null){
            return new BigDecimal(numberStr);
        }
        return null;
    }

}
