package com.fangcang.rfp.common.service.impl;

import com.fangcang.rfp.common.dao.DepartmentDao;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.AddDepartmentDto;
import com.fangcang.rfp.common.dto.request.QueryDepartmentRequest;
import com.fangcang.rfp.common.dto.request.UpdateDepartmentDto;
import com.fangcang.rfp.common.dto.response.QueryDepartmentResponse;
import com.fangcang.rfp.common.entity.Department;
import com.fangcang.rfp.common.entity.Org;
import com.fangcang.rfp.common.enums.OrgTypeEnum;
import com.fangcang.rfp.common.enums.ReturnResultEnum;
import com.fangcang.rfp.common.enums.RoleCodeEnum;
import com.fangcang.rfp.common.service.DepartmentService;
import com.fangcang.rfp.common.service.OrgService;
import com.fangcang.rfp.common.service.UserService;
import com.fangcang.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/21 11:01
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    private static Logger logger = LoggerFactory.getLogger(DepartmentServiceImpl.class);

    @Autowired
    private DepartmentDao departmentDao;

    @Autowired
    private UserService userService;

    @Autowired
    private OrgService orgService;

    @Override
    public Response addDepartment(AddDepartmentDto addDepartmentDto) {

        Response response = new Response();
        Long orgId = addDepartmentDto.getOrgId();
        String departName = addDepartmentDto.getDepartName();
        Long parentDepartId = addDepartmentDto.getParentDepartId();
        if (orgId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("机构id为空");
            return response;
        }
        if (!StringUtil.isValidString(departName)) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("部门名称为空");
            return response;
        }
        //没有父级部门id,说明新建的一级部门
        if (parentDepartId != null) {
            boolean valid = isValid(parentDepartId);
            if (!valid) {
                response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
                response.setMsg("新建部门不能超过五级");
                return response;
            }
        }
        Department department = new Department();
        department.setDepartName(departName);
        department.setOrgId(orgId);
        department.setParentDepartId(parentDepartId);
        department.setCreator(addDepartmentDto.getOperator());
        department.setModifier(addDepartmentDto.getOperator());
        int insert = departmentDao.insert(department);
        if (insert == 1) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        } else {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("新增部门失败");
        }
        return response;
    }

    @Override
    public Response queryDepartment(QueryDepartmentRequest queryDepartmentRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        Long departId = queryDepartmentRequest.getDepartId();
        Long orgId = queryDepartmentRequest.getOrgId();
        if (orgId == null) {
            response.setResult(ReturnResultEnum.PARAMETER_EXCEPTION.errorNo);
            response.setMsg("机构id为空");
            return response;
        }

        List<QueryDepartmentResponse> queryDepartmentResponses = new ArrayList<>();
        //部门id为空查询机构下所有的部门信息
        if (departId == null) {
            //查询一级部门
            List<Department> departments = departmentDao.queryFirstLevelDepartmentByOrgId(orgId);
            if (CollectionUtils.isEmpty(departments)) {
                return response;
            }
            for (Department department : departments) {
                queryDepartmentByParentDepartId(department, queryDepartmentResponses);
            }
            response.setData(queryDepartmentResponses);
        } else {
            List<Long> departIds = new ArrayList<>();
            departIds.add(departId);
            List<Department> departments = departmentDao.queryDepartmentByDepartId(departIds);
            if (CollectionUtils.isEmpty(departments)) {
                return response;
            }
            queryDepartmentByParentDepartId(departments.get(0), queryDepartmentResponses);
            response.setData(queryDepartmentResponses);
        }
        return response;
    }


    @Override
    public Response deleteDepartment(UpdateDepartmentDto updateDepartmentDto) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        Long orgId = updateDepartmentDto.getOrgId();
        Long departId = updateDepartmentDto.getDepartId();
        List<Long> departIds = new ArrayList<>();
        departIds.add(departId);
        List<Department> departments = departmentDao.queryDepartmentByDepartId(departIds);
        if (CollectionUtils.isEmpty(departments)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("部门信息不存在");
            return response;
        }
        Department department = departments.get(0);
        if (orgId == null || orgId.intValue() != department.getOrgId()) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("不能删除其它机构的部门");
            return response;
        }

        //删除之前，需要校验部门下是否还有子部门
        List<Department> departmentList = new ArrayList<>();
        querySonDepartments(departId, departmentList);
        if (CollectionUtils.isNotEmpty(departmentList)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("该机构下还有子机构，请先删除子机构");
            return response;
        }
        //需要校验部门中是否还有员工
        List<UserDTO> userDTOS = userService.queryUserByDepartId(departIds);
        if (CollectionUtils.isNotEmpty(userDTOS)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("该机构下存在有效的员工，请先迁移员工或无效员工");
            return response;
        }
        int i = departmentDao.updateDepartment(updateDepartmentDto);
        if (i != 1) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("删除失败");
        }
        return response;
    }

    @Override
    public Response editDepartment(UpdateDepartmentDto updateDepartmentDto) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        Long orgId = updateDepartmentDto.getOrgId();
        Long departId = updateDepartmentDto.getDepartId();

        List<Long> departIds = new ArrayList<>();
        departIds.add(departId);
        List<Department> departments = departmentDao.queryDepartmentByDepartId(departIds);
        if (CollectionUtils.isEmpty(departments)) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("部门信息不存在");
            return response;
        }
        Department department = departments.get(0);
        if (orgId == null || orgId.intValue() != department.getOrgId()) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("不能修改其它机构的部门");
            return response;
        }
        int i = departmentDao.updateDepartment(updateDepartmentDto);
        if (i != 1) {
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("修改失败");
        }
        return response;
    }

    @Override
    public String queryDepartmentName(Long departId) {
        List<Long> departIds = new ArrayList<>();
        departIds.add(departId);
        List<Department> departments = departmentDao.queryDepartmentByDepartId(departIds);
        if (CollectionUtils.isEmpty(departments)) {
            return null;
        }
        Department department = departments.get(0);
        String departName = department.getDepartName();
        Long parentDepartId = department.getParentDepartId();
        if (parentDepartId != null) {
            return queryDepartmentName(parentDepartId) + "-" + departName;
        }
        return departName;
    }

    @Override
    public void querySonDepartments(Long departId, List<Department> departmentList) {
        List<Department> sonDepartments = departmentDao.queryDepartmentByParentDepartId(departId);
        if (CollectionUtils.isNotEmpty(sonDepartments)) {
            departmentList.addAll(sonDepartments);
            for (Department sonDepartment : sonDepartments) {
                querySonDepartments(sonDepartment.getDepartId(), departmentList);
            }
        }
    }

    @Override
    public List<Long> queryUserByDepart(UserDTO userDTO) {
        List<Long> userIds = null;
        // 登录人的机构类型为企业
        if (userDTO.getOrgDTO().getOrgType().intValue() == OrgTypeEnum.DISTRIBUTOR.key.intValue()) {
            userIds = new ArrayList<>();
            //如果角色是机构负责人，则可以查询部门及部门下所有员工的签约酒店信息
            if (RoleCodeEnum.HEAD_ORGANIZATION.key.equals(userDTO.getRoleCodeType())) {
                List<Department> departments = new ArrayList<>();
                Long departId = userDTO.getDepartId();
                //部门id为空，说明是机构下的员工
                if(departId == null){
                    userIds.add(userDTO.getUserId());
                    Long orgId = userDTO.getOrgDTO().getOrgId();
                    //查询一级部门
                    List<Department> firstLevelDepartments = departmentDao.queryFirstLevelDepartmentByOrgId(orgId);
                    if (CollectionUtils.isEmpty(firstLevelDepartments)) {
                        return userIds;
                    }
                    for (Department firstLevelDepartment : firstLevelDepartments) {
                        querySonDepartments(firstLevelDepartment.getDepartId(), departments);
                    }
                }else {
                    querySonDepartments(userDTO.getDepartId(), departments);
                }
                List<Long> departIds = new ArrayList<>();
                if(departId != null) {
                    departIds.add(departId);
                }
                if (departments.size() > 0) {
                    for (Department department : departments) {
                        departIds.add(department.getDepartId());
                    }
                }
                List<UserDTO> userDTOS = userService.queryUserByDepartId(departIds);
                if (CollectionUtils.isNotEmpty(userDTOS)) {
                    for (UserDTO dto : userDTOS) {
                        userIds.add(dto.getUserId());
                    }
                }
            } else if (RoleCodeEnum.EMPLOYEE.key.equals(userDTO.getRoleCodeType())) {
                userIds.add(userDTO.getUserId());
            }
        }
        return userIds;
    }

    @Override
    public Response queryDepartmentByName(QueryDepartmentRequest queryDepartmentRequest) {
        Response response = new Response();
        List<QueryDepartmentResponse> queryDepartmentResponses = departmentDao.queryDepartmentByName(queryDepartmentRequest);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(queryDepartmentResponses);
        return response;
    }

    @Override
    public List<Long> queryUserIdByDepartId(Long departId) {
        List<Long> userIds = new ArrayList<>();
        List<Long> departIds = new ArrayList<>();
        List<Department> departments = new ArrayList<>();
        querySonDepartments(departId, departments);
        if(CollectionUtils.isNotEmpty(departments)){
            for (Department department : departments) {
                departIds.add(department.getDepartId());
            }
        }
        departIds.add(departId);
        List<UserDTO> userDTOS = userService.queryUserByDepartId(departIds);
        if (CollectionUtils.isNotEmpty(userDTOS)) {
            for (UserDTO dto : userDTOS) {
                userIds.add(dto.getUserId());
            }
        }
        return userIds;
    }

    private void queryDepartmentByParentDepartId(Department department, List<QueryDepartmentResponse> queryDepartmentResponses) {
        QueryDepartmentResponse queryDepartmentResponse = new QueryDepartmentResponse();
        queryDepartmentResponse.setDepartId(department.getDepartId());
        queryDepartmentResponse.setOrgId(department.getOrgId());
        queryDepartmentResponse.setDepartName(department.getDepartName());
        queryDepartmentResponse.setParentDepartId(department.getParentDepartId());
        //根据DepartId查询自己部门
        List<Department> sonDepartments = departmentDao.queryDepartmentByParentDepartId(department.getDepartId());
        if (!CollectionUtils.isEmpty(sonDepartments)) {
            List<QueryDepartmentResponse> sonDepartmentResponse = new ArrayList<>();
            for (Department sonDepartment : sonDepartments) {
                queryDepartmentByParentDepartId(sonDepartment, sonDepartmentResponse);
            }
            queryDepartmentResponse.setChildren(sonDepartmentResponse);
        }
        queryDepartmentResponses.add(queryDepartmentResponse);
    }

    public boolean isValid(Long departId) {
        // 部门层级不能超过5层
        int level = 1;
        while (departId != null && departId != 0) {
            List<Long> departIds = new ArrayList<>();
            departIds.add(departId);
            List<Department> departments = departmentDao.queryDepartmentByDepartId(departIds);
            if (CollectionUtils.isEmpty(departments)) {
                logger.error("父级部门不存在，参数有误");
                return false; // 如果查不到父部门，说明数据异常，返回false
            }
            departId = departments.get(0).getParentDepartId();
            level++;
            if (level >= 5) {
                return false;
            }
        }
        return true;
    }

}
