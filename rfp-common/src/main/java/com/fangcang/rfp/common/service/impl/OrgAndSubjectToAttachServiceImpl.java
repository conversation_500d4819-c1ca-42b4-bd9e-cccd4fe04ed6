package com.fangcang.rfp.common.service.impl;

import com.fangcang.rfp.common.dao.AttachmentDao;
import com.fangcang.rfp.common.dto.request.QueryAttachmentInfoParam;
import com.fangcang.rfp.common.entity.Attachment;
import com.fangcang.rfp.common.service.AttachmentService;
import com.fangcang.rfp.common.service.OrgAndSubjectToAttachService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @auther cjh
 * @description
 * @date 2022/9/15
 */
@Service
public class OrgAndSubjectToAttachServiceImpl implements OrgAndSubjectToAttachService {

    @Autowired
    private AttachmentDao attachmentDao;

    @Autowired
    private AttachmentService attachmentService;

    @Override
    public void AddOrgAttach(Long businessId, Long fileId, String modifyer) {
        Attachment attachment = new Attachment();
        attachment.setFileId(fileId);
        attachment.setExternalId(businessId);
        attachment.setModifier(modifyer);
        attachmentService.updateAttachmentExternalIdByFileId(attachment);
    }

    @Override
    public void DeleteOrgAttach(Long businessId, String modifyer,Integer businessType) {
        // 根据业务ID和类型查所有附件
        QueryAttachmentInfoParam queryAttachmentInfoParam = new QueryAttachmentInfoParam();
        queryAttachmentInfoParam.setExternalId(businessId);
        queryAttachmentInfoParam.setBusinessType(businessType);
        List<Attachment> attachments = attachmentDao.queryAttachment(queryAttachmentInfoParam);
        if (CollectionUtils.isNotEmpty(attachments) && attachments.size() == 1) {
            for (Attachment attachment : attachments) {
                // 删除该附件
                attachment.setModifier(modifyer);
                attachmentService.deleteAttachmentByFileId(attachment);
            }
        }
    }

    @Override
    public void UpdateOrgAttach(Long businessId, Long fileId, String modifyer,Integer businessType) {
        // 根据业务ID和类型查所有附件
        QueryAttachmentInfoParam queryAttachmentInfoParam = new QueryAttachmentInfoParam();
        queryAttachmentInfoParam.setExternalId(businessId);
        queryAttachmentInfoParam.setBusinessType(businessType);
        List<Attachment> attachments = attachmentDao.queryAttachment(queryAttachmentInfoParam);

        // 原来有附件
        if (CollectionUtils.isNotEmpty(attachments) && attachments.size() == 1) {
            // 没有做修改
            if (attachments.get(0).getFileId().equals(fileId)) {
                return;
            }else if (!attachments.get(0).getFileId().equals(fileId)){
                // 做了修改，删除旧附件
                Attachment attachment = new Attachment();
                attachment.setFileId(attachments.get(0).getFileId());
                attachment.setModifier(modifyer);
                attachmentService.deleteAttachmentByFileId(attachment);
                // 回写新附件
                AddOrgAttach(businessId, fileId, modifyer);
            }
        }
        // 原来没有附件
        if (CollectionUtils.isEmpty(attachments)) {
            // 回写业务ID
            AddOrgAttach(businessId, fileId, modifyer);
        }
    }
}
