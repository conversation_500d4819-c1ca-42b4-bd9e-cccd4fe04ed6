package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.fangcang.enums.HotelStarEnum;
import com.fangcang.enums.ResultEnum;
import com.fangcang.rfp.common.config.BaseConfig;
import com.fangcang.rfp.common.constants.RedisConstant;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.*;
import com.fangcang.rfp.common.dto.common.PageResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.json.*;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.ProjectHotelHistoryDataRecommendService;
import com.fangcang.rfp.common.service.ProjectService;
import com.fangcang.rfp.common.service.RecommendHotelService;
import com.fangcang.rfp.common.service.RedisService;
import com.fangcang.rfp.common.util.*;
import com.fangcang.tmc.hub.api.response.product.HotelPriceItem;
import com.fangcang.util.JSONUtils;
import com.fangcang.util.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目酒店历史数据推荐服务实现类
 */
@Service
public class ProjectHotelHistoryDataRecommendServiceImpl implements ProjectHotelHistoryDataRecommendService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectHotelHistoryDataRecommendServiceImpl.class);

    public static final String UPDATE_LOWEST_PRICE = "更新酒店商旅价格";


    @Autowired
    private ProjectHotelHistoryDataDao projectHotelHistoryDataDao;
    @Autowired
    private HotelViolationsMonitorDao hotelViolationsMonitorDao;
    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;
    @Autowired
    private OtaHotelDailyMinPriceDao otaHotelDailyMinPriceDao;
    @Autowired
    private RecommendHotelService recommendHotelService;
    @Autowired
    private ProjectHistoryRecommendDao projectHistoryRecommendDao;
    @Autowired
    private DisHotelDailyOrderDao disHotelDailyOrderDao;
    @Autowired
    private ProjectPoiDao projectPoiDao;
    @Autowired
    private ProjectRecommendStatLogDao projectRecommendStatLogDao;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private HotelDao hotelDao;

    public static Long recordLogHotelId = 0L;

    private ProjectRecommendStatLog initProjectRecommendStatLog(UserDTO userDTO, String statName, String statReferenceNo, Long projectId) {
        ProjectRecommendStatLog projectRecommendStatLog = new ProjectRecommendStatLog();
        projectRecommendStatLog.setProjectId(projectId);
        projectRecommendStatLog.setStatReferenceNo(statReferenceNo);
        projectRecommendStatLog.setStatName(statName);
        projectRecommendStatLog.setBeginTime(new Date());
        projectRecommendStatLog.setIsFinished(RfpConstant.constant_0);
        projectRecommendStatLog.setCreator(userDTO.getOperator());
        return projectRecommendStatLog;
    }

    private void recordFinishedLog(ProjectRecommendStatLog projectRecommendStatLog, Response response){
        // 记录完成统计日志
        projectRecommendStatLog.setIsFinished(RfpConstant.constant_1);
        projectRecommendStatLog.setEndTime(new Date());
        projectRecommendStatLog.setResult(response.getResult());
        projectRecommendStatLog.setResultMsg(response.getMsg());
        projectRecommendStatLogDao.finishRecord(projectRecommendStatLog);
    }
    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> generateRecommendLevel(UserDTO userDTO, String statReferenceNo, Long projectId, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("生成项目酒店推荐等级：" +projectId);

        // 记录统计日志
        String statName = "生成项目酒店推荐等级";
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 查询有推荐的酒店历史数据
            List<QueryHistoryProjectInfoResponse> recommendHotelHistoryDataList = projectHotelHistoryDataDao.queryAllRecommendHistoryProjectHotelList(projectId, null);
            // 初始设置为推荐等级为空
            recommendHotelHistoryDataList.forEach(item -> {
                item.setRecommendLevel(null);
            });
            List<QueryHistoryProjectInfoResponse> hotelHistoryDataList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(projectId,  null,null, null);
            Map<String, List<QueryHistoryProjectInfoResponse>> cityHotelHistoryDataMap = hotelHistoryDataList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));
            Map<Long, Integer> theSameLevelHotelAreaRoomCountMap = new HashMap<>();
            for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : recommendHotelHistoryDataList) {
                if (queryHistoryProjectInfoResponse.getRoomNightCount() == null) {
                    continue;
                }
                // 过滤间夜数或者总价为0的情况
                if (queryHistoryProjectInfoResponse.getRoomNightCount() == 0 || queryHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 签约要求间夜数
                int requireRoomNight = 10; // 默认为10的间夜数计算
                if(StringUtils.isNotEmpty(queryHistoryProjectInfoResponse.getRequiredRoomNight())){
                    requireRoomNight = Integer.parseInt(queryHistoryProjectInfoResponse.getRequiredRoomNight());
                }
                // 均价
                BigDecimal baseAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);
                BigDecimal hotelRecommendPrice = queryHistoryProjectInfoResponse.getReferencePrice();
                if(hotelRecommendPrice == null){
                    hotelRecommendPrice = queryHistoryProjectInfoResponse.getAdjustLowestPrice();
                }

                // 计算月成交间夜数
                int roomNightCountPerMonth = BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()).divide(new BigDecimal("12"), 0, RoundingMode.HALF_UP).intValue();

                // 记录日志
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                    logger.info("hotelID: {}, roomNightCountPerMonth {} hotelRecommendPrice: {}, {}", recordLogHotelId, roomNightCountPerMonth, hotelRecommendPrice, requireRoomNight);
                }

                // SSS级别 酒店推荐列表酒店 历史成交月均成交高于500
                if (roomNightCountPerMonth >= 500) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.SSS.key);
                    //SS：酒店推荐列表酒店 历史成交月均高于200，低于499
                } else if (roomNightCountPerMonth >= 200) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.SS.key);
                    //S：酒店推荐列表酒店 历史成交月均高于100，低于199
                } else if (roomNightCountPerMonth >= 100) {
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.S.key);
                } else if (roomNightCountPerMonth >= requireRoomNight && // 历史间夜和均价大于签约要求 （月均间夜数，签约价格）
                        hotelRecommendPrice != null && baseAvgPrice.compareTo(hotelRecommendPrice) >= 0
                ) {
                    //  A：酒店推荐列表酒店 历史成交月均高于酒店签约要求，低于99
                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.A.key);
                } else {
                    // B：酒店推荐列表酒店 历史成交没有高于酒店签约要求，但同城3公里范围内同价位预订酒店间夜超过酒店签约要求
                    // 过滤同城
                    if(hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0) {
                        List<QueryHistoryProjectInfoResponse> cityQueryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                        int totalRoomNightCount = 0;
                        for (QueryHistoryProjectInfoResponse distanceHistoryProjectInfoResponse : cityQueryHistoryProjectInfoResponseList) {
                            // 过滤3公里内同价位酒店
                            if (distanceHistoryProjectInfoResponse.getRoomNightCount() == 0 || distanceHistoryProjectInfoResponse.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            // 计算是否在3公里范围
                            Double distance = LocationUtil.getKmDistance(distanceHistoryProjectInfoResponse.getLatBaidu(), distanceHistoryProjectInfoResponse.getLngBaidu(), queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(),
                                    String.valueOf(distanceHistoryProjectInfoResponse.getHotelId()), String.valueOf(queryHistoryProjectInfoResponse.getHotelId()));
                            if (distance == null || distance > 3) {
                                continue;
                            }
                            // 计算是否同价位置 比如A酒店推荐合作报价为380，企业在这个酒店之前没有预订过，但是这个酒店周边3公里范围内，同价位区间有超过酒店签约要求的预订间数，这时为B
                            // 比如一个酒店没有录入酒店签约要求，但是独立起价我们查到近30天最低价为328，那么签约混淆价格为348，默认签约要求为10间夜/月，这个标准进行酒店推荐计算
                            boolean isTheSameLevelPrice = isTheSameLevelPrice(hotelRecommendPrice, distanceHistoryProjectInfoResponse);
                            if (isTheSameLevelPrice) {
                                totalRoomNightCount = totalRoomNightCount + distanceHistoryProjectInfoResponse.getRoomNightCount();
                                // 周边是否满足签约要求 , 满足设置B
                                if(distanceHistoryProjectInfoResponse.getRoomNightCount() >= requireRoomNight * 12){
                                    queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.B.key);
                                }
                            }
                        }
                        theSameLevelHotelAreaRoomCountMap.put(queryHistoryProjectInfoResponse.getHotelId(), totalRoomNightCount);
                        queryHistoryProjectInfoResponse.setPriceLevelRoomNight(totalRoomNightCount);
                    }
                }

                // C：酒店推荐列表酒店 历史成交没有高于酒店签约要求，3公里历史成交同价位酒店没有高于酒店签约要求，城市同价位酒店满足酒店签约要求，推荐值为C。
                if(queryHistoryProjectInfoResponse.getRecommendLevel() == null && hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0) {
                    List<QueryHistoryProjectInfoResponse> cityQueryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                    for (QueryHistoryProjectInfoResponse cityHistoryProjectInfoResponse : cityQueryHistoryProjectInfoResponseList) {
                        // 计算月成交间夜数
                        int compareRoomNightCountPerMonth = BigDecimal.valueOf(cityHistoryProjectInfoResponse.getRoomNightCount()).divide(new BigDecimal("12"), 0, RoundingMode.HALF_UP).intValue();
                        if (compareRoomNightCountPerMonth < requireRoomNight) {
                            continue;
                        }
                        // 计算是否同价位置
                        int avgPrice = cityHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(cityHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
                        // 记录日志
                        if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                            logger.info("avgPrice {} cityHistoryProjectInfoResponse hotelId: {}", avgPrice, cityHistoryProjectInfoResponse.getHotelId());
                        }
                        if (hotelRecommendPrice.subtract(new BigDecimal(avgPrice)).abs().intValue() <= 50) {
                            queryHistoryProjectInfoResponse.setRecommendLevel(RecommendLevelEnum.C.key);
                            break;
                        }
                    }
                }

                // 计算同价位周边3公里间夜数 （C不需要统计， B已经计算）
                if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                    logger.info("同价位周边酒店数量比较酒店价格 {}, {}, hotelRecommendPrice {}", recordLogHotelId, queryHistoryProjectInfoResponse.getAdjustLowestPrice(), hotelRecommendPrice);
                }
                if(queryHistoryProjectInfoResponse.getRecommendLevel() != null && hotelRecommendPrice != null && hotelRecommendPrice.compareTo(BigDecimal.ZERO) > 0 &&
                        queryHistoryProjectInfoResponse.getRecommendLevel() < RecommendLevelEnum.B.key) {
                    List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = cityHotelHistoryDataMap.get(queryHistoryProjectInfoResponse.getCityCode());
                    int totalTheSameLevelPriceRoomNightCount = 0;
                    for (QueryHistoryProjectInfoResponse distanceHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList) {
                        Double distance = LocationUtil.getKmDistance(distanceHistoryProjectInfoResponse.getLatBaidu(), distanceHistoryProjectInfoResponse.getLngBaidu(), queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(),
                                distanceHistoryProjectInfoResponse.getHotelId().toString(), queryHistoryProjectInfoResponse.getHotelId().toString()
                        );
                        if (distance == null || distance > 3) {
                            continue;
                        }
                        // 计算是否同价位置
                        if (distanceHistoryProjectInfoResponse.getRoomNightCount() == 0) {
                            continue;
                        }
                        // 均价
                        boolean isTheSameLevelPrice = isTheSameLevelPrice(hotelRecommendPrice, distanceHistoryProjectInfoResponse);
                        if (isTheSameLevelPrice) {
                            totalTheSameLevelPriceRoomNightCount = totalTheSameLevelPriceRoomNightCount + distanceHistoryProjectInfoResponse.getRoomNightCount();
                        }
                    }
                    if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                        logger.info("同价位周边酒店数量 {}, {}", recordLogHotelId, totalTheSameLevelPriceRoomNightCount);
                    }
                    theSameLevelHotelAreaRoomCountMap.put(queryHistoryProjectInfoResponse.getHotelId(), totalTheSameLevelPriceRoomNightCount);
                    queryHistoryProjectInfoResponse.setPriceLevelRoomNight(totalTheSameLevelPriceRoomNightCount);
                }
            }

            // 更新历史数据推荐等级
            recommendHotelHistoryDataList = recommendHotelHistoryDataList.stream().filter(o -> o.getRecommendLevel() != null).collect(Collectors.toList());
            // 清空旧推荐等级数据
            projectHotelHistoryDataDao.clearHistoryProjectHotelRecommendLevel(projectId);
            for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : recommendHotelHistoryDataList) {
                // 更新推荐等级和同价位3公里数据
                projectHotelHistoryDataDao.updateRecommendLevelAndPriceLevelRoomNight(projectId, queryHistoryProjectInfoResponse.getHotelId(),
                        queryHistoryProjectInfoResponse.getRecommendLevel(), queryHistoryProjectInfoResponse.getPriceLevelRoomNight());
            }

            // 优质商旅酒店推荐
            // 记录统计日志
            long beginTime = System.currentTimeMillis();
            statName = "优质商旅酒店推荐";
            ProjectRecommendStatLog projectHighQualityRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
            projectRecommendStatLogDao.insertLog(projectHighQualityRecommendStatLog);
            List<ProjectHistoryRecommend> highQualityRecommendList = new ArrayList<>();
            try {
                projectHistoryRecommendDao.resetHighQuality(projectId);
                if (!recommendHotelHistoryDataList.isEmpty()) {
                    recommendHotelHistoryDataList.forEach(item -> {
                        if (item.getRecommendLevel() == null) {
                            return;
                        }
                        if (item.getRecommendLevel() >= RecommendLevelEnum.C.key) {
                            return;
                        }
                        ProjectHistoryRecommend projectHistoryRecommend = new ProjectHistoryRecommend();
                        projectHistoryRecommend.setProjectId(projectId);
                        projectHistoryRecommend.setHotelId(item.getHotelId());
                        projectHistoryRecommend.setIsHighQuality(RfpConstant.constant_1);
                        projectHistoryRecommend.setCity(item.getCityCode());
                        projectHistoryRecommend.setCreator(userDTO.getOperator());
                        projectHistoryRecommend.setModifier(userDTO.getOperator());
                        projectHistoryRecommend.setIsHighQualityRecommend(RfpConstant.constant_1);
                        // 记录日志
                        if(Objects.equals(item.getHotelId(), recordLogHotelId)){
                            logger.info("优质商旅酒店推荐 hotelId: {}", item.getHotelId());
                        }
                        // 设置同价位3公里范围所有间夜数
                        if (theSameLevelHotelAreaRoomCountMap.containsKey(item.getHotelId())) {
                            projectHistoryRecommend.setPriceLevelRoomNight(theSameLevelHotelAreaRoomCountMap.get(item.getHotelId()));
                        }
                        int insertCount = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                        if (insertCount == 1) {
                            highQualityRecommendList.add(projectHistoryRecommend);
                            try {
                                RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(item.getHotelId()));
                            } catch (Exception e) {
                                logger.error("新增AI Review 异常 ", e);
                            }
                        }
                    });
                }
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg("优质商旅酒店推荐成功");
                logger.info("优质商旅酒店推荐，总耗时=" + (System.currentTimeMillis() - beginTime)  + "，projectId=" + projectId);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
                logger.error("优质商旅酒店推荐异常 projectId:" + projectId, ex);
                response.setResult(ResultEnum.FAILURE.key);
                response.setMsg("生成优质商旅酒店推荐异常");
            } finally {
                // 记录完成统计日志
                recordFinishedLog(projectHighQualityRecommendStatLog, response);
            }

            // 节省明星酒店推荐
            beginTime = System.currentTimeMillis();
            statName = "节省明星酒店推荐";
            ProjectRecommendStatLog savedRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, projectId);
            projectRecommendStatLogDao.insertLog(savedRecommendStatLog);
            try {
                projectHistoryRecommendDao.resetSavedHotel(projectId);
                String startDate = com.fangcang.util.DateUtil.dateToString(DateUtil.offset(new Date(), DateField.MONTH, -3));
                String endDate = com.fangcang.util.DateUtil.dateToString(new Date());
                Lists.partition(highQualityRecommendList, 100).forEach(subHighQualityRecommendList -> {
                    List<Long> hotelIdList = subHighQualityRecommendList.stream().map(ProjectHistoryRecommend::getHotelId).collect(Collectors.toList());
                    DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
                    disHotelDailyOrderRequest.setHotelIdList(hotelIdList);
                    disHotelDailyOrderRequest.setStartTime(startDate);
                    disHotelDailyOrderRequest.setEndTime(endDate);
                    List<HotelSavedAmountDto> hotelSavedAmountDtoList = disHotelDailyOrderDao.selectHotelSavedAmountGroupByOrg(disHotelDailyOrderRequest);
                    Map<Long, List<HotelSavedAmountDto>> hotelSavedAmountDtoMap = hotelSavedAmountDtoList.stream().collect(Collectors.groupingBy(HotelSavedAmountDto::getHotelId));
                    for(ProjectHistoryRecommend projectHistoryRecommend : subHighQualityRecommendList){
                        List<HotelSavedAmountDto> savedAmountHotelSavedAmountDtoList = hotelSavedAmountDtoMap.get(projectHistoryRecommend.getHotelId());
                        // 记录日志
                        if(Objects.equals(projectHistoryRecommend.getHotelId(), recordLogHotelId)){
                            logger.info("节省明星推荐 hotelId: {}, {}", projectHistoryRecommend.getHotelId(), JsonUtil.objectToJson(savedAmountHotelSavedAmountDtoList));
                        }
                        if(CollectionUtils.isEmpty(savedAmountHotelSavedAmountDtoList)){
                            continue;
                        }
                        // 取最大节省率
                        savedAmountHotelSavedAmountDtoList = savedAmountHotelSavedAmountDtoList.stream().filter(o -> o.getTotalOtaPrice() != null && o.getTotalOtaPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                        BigDecimal maxSavedAmount = BigDecimal.ZERO;
                        for(HotelSavedAmountDto item : savedAmountHotelSavedAmountDtoList) {
                            BigDecimal savedAmountRate = item.getSavedAmount().divide(item.getTotalOtaPrice(), 3, RoundingMode.HALF_UP);
                            if (savedAmountRate.compareTo(maxSavedAmount) > 0) {
                                maxSavedAmount = savedAmountRate;
                            }
                        }

                        if(maxSavedAmount.compareTo(BigDecimal.valueOf(0.2)) >=0){
                            logger.info("HotelId: {}, SavedAmountRate: {}", savedAmountHotelSavedAmountDtoList.get(0).getHotelId(), maxSavedAmount);
                            projectHistoryRecommend.setIsHighQuality(null);
                            projectHistoryRecommend.setIsHighQualityRecommend(null);
                            projectHistoryRecommend.setIsSavedHotel(RfpConstant.constant_1);
                            projectHistoryRecommend.setIsSavedHotelRecommend(RfpConstant.constant_1);
                            int insertCount = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                            if (insertCount == 1) {
                                try {
                                    RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(projectHistoryRecommend.getHotelId()));
                                } catch (Exception e) {
                                    logger.error("新增AI Review 异常 ", e);
                                }
                            }
                        }
                    }
                });
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg("节省明星酒店推荐成功");
                logger.info("节省明星酒店推荐，总耗时=" +(System.currentTimeMillis() - beginTime) + "，projectId=" + projectId);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
                logger.error("节省明星酒店推荐异常 projectId:" + projectId, ex);
                response.setResult(ResultEnum.FAILURE.key);
                response.setMsg("节省明星酒店推荐异常");
            } finally {
                // 记录完成统计日志
                recordFinishedLog(savedRecommendStatLog, response);
            }

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("生成项目酒店推荐等级成功");
            logger.info("生成项目酒店推荐等级，总耗时=" + watch.getStepSplitTime() + "，projectId=" + projectId);
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("生成项目酒店推荐等级异常 projectId:" + projectId, ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("生成项目酒店推荐等级异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");

        }

        return new AsyncResult<>(response);
    }

    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> updateLastYearViolationsCount(UserDTO userDTO, String statReferenceNo, Project project, List<Long> hotelIdList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店历史违规次数：" +project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店历史违规次数";
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 重置违规次数
            projectHotelHistoryDataDao.resetHistoryProjectHotelViolationsCount(project.getProjectId());

            // 更新酒店违规次数
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<Long> projectIntentHotelIdList = projectIntentHotelDao.selectProjectIntentHotelServicePoint(project.getRelatedProjectId(), hotelIdListPartition).stream().map(ProjectIntentHotel::getHotelId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(projectIntentHotelIdList)){
                    return;
                }
                // 默认更新有报价的酒店违规次数为0
                projectHotelHistoryDataDao.updateHistoryProjectHotelViolationsCount(project.getProjectId(), 0, projectIntentHotelIdList);

                // 查询违规次数
                List<HotelViolationCountStatDto> subHotelViolationCountList = hotelViolationsMonitorDao.queryHotelViolationCountStat(project.getRelatedProjectId(), hotelIdListPartition);
                if(CollectionUtils.isEmpty(subHotelViolationCountList)){
                    return;
                }
                // 更新酒店违规次数
                Map<Integer, List<HotelViolationCountStatDto>> hotelViolationCountMap = subHotelViolationCountList.stream().collect(Collectors.groupingBy(HotelViolationCountStatDto::getViolationCount));
                for (Integer violationCount : hotelViolationCountMap.keySet()) {
                    List<HotelViolationCountStatDto> hotelViolationCountStatDtoList = hotelViolationCountMap.get(violationCount);
                    List<Long> hotelViolationCountStatDtoListHotelIdList = hotelViolationCountStatDtoList.stream().map(HotelViolationCountStatDto::getHotelId).collect(Collectors.toList());
                    projectHotelHistoryDataDao.updateHistoryProjectHotelViolationsCount(project.getProjectId(), violationCount, hotelViolationCountStatDtoListHotelIdList);
                }
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店历史违规次数成功");
            logger.info("更新酒店历史违规次数，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店历史违规次数异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店历史违规次数异常");
        } finally {
            countDownLatch.countDown();
            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> updateLastYearServicePoint(UserDTO userDTO, String statReferenceNo, Project project, List<Long> hotelIdList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店历史服务分：" +project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店历史服务分";
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 重置服务分
            projectHotelHistoryDataDao.resetHistoryProjectHotelServicePoint(project.getProjectId());

            // 更新酒店去年服务分
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<ProjectIntentHotel> subProjectIntentHotelList = projectIntentHotelDao.selectProjectIntentHotelServicePoint(project.getRelatedProjectId(), hotelIdListPartition);
                if(CollectionUtils.isEmpty(subProjectIntentHotelList)){
                    return;
                }
                Map<BigDecimal, List<ProjectIntentHotel>> subProjectIntentHotelListMap = subProjectIntentHotelList.stream().collect(Collectors.groupingBy(ProjectIntentHotel::getHotelServicePoints));
                for (BigDecimal servicePoint : subProjectIntentHotelListMap.keySet()) {
                    List<ProjectIntentHotel> projectIntentHotelList = subProjectIntentHotelListMap.get(servicePoint);
                    List<Long> projectIntentHotelIdList = projectIntentHotelList.stream().map(ProjectIntentHotel::getHotelId).collect(Collectors.toList());
                    projectHotelHistoryDataDao.updateHistoryProjectHotelServicePoint(project.getProjectId(), servicePoint, projectIntentHotelIdList);
                }
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店历史服务分成功");
            logger.info("更新酒店历史服务分成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店历史服务分异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店历史服务分异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> updateLastYearOtaPrice(UserDTO userDTO, String statReferenceNo, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店去年OTA最低和最高价格：" +project.getProjectId());

        // 记录统计日志
        String statName = "更新酒店去年OTA最低和最高价格";
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 更新酒店违规次数
            Date now = new Date();
            String todayStr = com.fangcang.util.DateUtil.dateToString(now);
            String lastYearDateStr = com.fangcang.util.DateUtil.dateToString(DateUtil.offsetDay(now, -365));
            List<Long> hotelIdList = historyProjectInfoList.stream().filter(o -> o.getMinMaxOtaPriceDate() == null).map(QueryHistoryProjectInfoResponse::getHotelId).collect(Collectors.toList());
            Lists.partition(hotelIdList, 1000).forEach(hotelIdListPartition -> {
                List<OtaHotelMinMaxPriceVO> otaHotelMinMaxPriceVOList = otaHotelDailyMinPriceDao.queryOtaHotelMinMaxPriceVOList(hotelIdListPartition, lastYearDateStr, todayStr);
                if(CollectionUtils.isEmpty(otaHotelMinMaxPriceVOList)){
                    return;
                }
                otaHotelMinMaxPriceVOList.forEach(item -> {
                    projectHotelHistoryDataDao.updateHistoryProjectHotelMinMaxOtaPrice(project.getProjectId(), item.getHotelId(), item.getMinPrice(), item.getMaxPrice(), now);
                });
            });
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店去年OTA最低和最高价格成功");
            logger.info("更新酒店去年OTA最低和最高价格成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店去年OTA最低和最高价格异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店去年OTA最低和最高价格异常");
        } finally {
            countDownLatch.countDown();

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> updateLowestPrice(UserDTO userDTO, String statReferenceNo, Project project, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("更新酒店商旅价格：" +project.getProjectId());

        // 记录统计日志
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, UPDATE_LOWEST_PRICE, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 过滤已经存在酒店差旅价格酒店
            List<Long> needUpdateLowPriceHotelIdList = projectHotelHistoryDataDao.queryNeedUpdateLowestPriceHotelIds(project.getProjectId());
            Date now = new Date();
            // 更新酒店差旅价格
            Lists.partition(needUpdateLowPriceHotelIdList, 100).forEach(hotelIdListPartition -> {
                Response hotelLowestPriceResponse = recommendHotelService.queryHotelLowestPrice(hotelIdListPartition);
                List<HotelLowestPricesResponse> hotelLowestPricesResponses = null;
                if(Objects.equals(hotelLowestPriceResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                    hotelLowestPricesResponses = (List<HotelLowestPricesResponse>)hotelLowestPriceResponse.getData();
                }
                if(CollectionUtils.isEmpty(hotelLowestPricesResponses)) {
                    logger.info("hotelLowestPricesResponses is null {}", hotelIdListPartition);
                    return;
                }
                Map<Long, HotelLowestPricesResponse> hotelLowestPricesResponsesMap = hotelLowestPricesResponses.stream().collect(Collectors.toMap(HotelLowestPricesResponse::getHotelId, item -> item));
                hotelLowestPricesResponsesMap.forEach((hotelId, hotelLowestPricesResponse) -> {
                    if(hotelLowestPricesResponse.getLowestPrice() != null) {
                        BigDecimal adjustLowestPrice = generateAdjustLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                        String priceItemJson = JSON.toJSONString(hotelLowestPricesResponse.getPriceItems());
                        // 避免长度过大异常
                        if(priceItemJson.length() > 3500){
                            logger.error("projectHotelHistoryDataDao.updateHistoryProjectHotelLowestPrice too large price item {}", priceItemJson);
                            priceItemJson = "[]";
                        }
                        int updateResult = projectHotelHistoryDataDao.updateHistoryProjectHotelLowestPrice(project.getProjectId(),
                                hotelLowestPricesResponse.getHotelId(),
                                hotelLowestPricesResponse.getLowestPrice(), adjustLowestPrice, now,
                                priceItemJson);
                        if(updateResult == 0){
                            logger.error("projectHotelHistoryDataDao.updateHistoryProjectHotelLowestPrice update failed {}",hotelLowestPricesResponse.getHotelId());
                        }
                    }
                });

                // 减少调用频率
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("更新酒店商旅价格成功");
            logger.info("更新酒店商旅价格成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("更新酒店商旅价格异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店商旅价格异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }
        return new AsyncResult<>(response);
    }

    /**
     * 异步生成高频预订和同档预定酒店邀约推荐
     */
    @Override
    @Async("rfpCommonExecutor")
    public Future<Response> generateFrequencyRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("生成高频预订邀约推荐：" +project.getProjectId());

        // 定义返回值
        Response response = new Response();

        // 查看更新签约混合价是否完成 （查询等待10分钟是否完成）
        boolean isFinishedSucceeded = isStatTaskFinished(project.getProjectId(), statReferenceNo, UPDATE_LOWEST_PRICE, 120);
        if(!isFinishedSucceeded){
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("更新酒店商旅价格失败，不能生成高频预订邀约推荐");
            new AsyncResult<>(response);
        }

        // 记录统计日志
        String statName = "生成高频预订邀约推荐";
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);
        Map<Long, QueryHistoryProjectInfoResponse> historyProjectInfoMap = historyProjectInfoList.stream().collect(Collectors.toMap(QueryHistoryProjectInfoResponse::getHotelId, item -> item));

        StopWatch watch = new StopWatch();
        watch.start();
        try {
            List<QueryHistoryProjectInfoResponse> frequencyHistoryProjectInfoList = historyProjectInfoList.stream().filter(o -> o.getRoomNightCount() != null && o.getRoomNightCount() >= 30).collect(Collectors.toList());
            List<ProjectHistoryRecommend> frequencyHistoryRecommendList = new ArrayList<>();
            List<Long> frequencyHotelIdList = new ArrayList<>();
            Lists.partition(frequencyHistoryProjectInfoList, 1000).forEach(frequencyHistoryProjectInfoListListPartition -> {
                for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : frequencyHistoryProjectInfoListListPartition){
                    ProjectHistoryRecommend projectHistoryRecommend = new ProjectHistoryRecommend();
                    projectHistoryRecommend.setProjectId(project.getProjectId());
                    projectHistoryRecommend.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                    projectHistoryRecommend.setIsFrequency(RfpConstant.constant_1);
                    projectHistoryRecommend.setIsFrequencyRecommend(RfpConstant.constant_0);
                    projectHistoryRecommend.setCity(queryHistoryProjectInfoResponse.getCityCode());
                    projectHistoryRecommend.setCreator(userDTO.getOperator());
                    projectHistoryRecommend.setModifier(userDTO.getOperator());
                    List<Integer> recommendReasonList = new ArrayList<>();
                    if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 600){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_600.key);
                    } else if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 200){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_200_599.key);
                    } else if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 100){
                        recommendReasonList.add(RecommendFrequencyEnum.ROOM_NIGHT_100_199.key);
                    }
                    if(!recommendReasonList.isEmpty()){
                        projectHistoryRecommend.setIsFrequencyRecommend(RfpConstant.constant_1);
                        projectHistoryRecommend.setFrequencyRecommends(JSON.toJSONString(recommendReasonList));
                        frequencyHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                    }
                    frequencyHistoryRecommendList.add(projectHistoryRecommend);

                }
            });

            // 更新高频推荐
            projectHistoryRecommendDao.resetRecommendFrequency(project.getProjectId());
            if(!frequencyHistoryRecommendList.isEmpty()){
                frequencyHistoryRecommendList.forEach(item -> {
                    int insertCount = projectHistoryRecommendDao.insertOrUpdate(item);
                    if(insertCount == 1 && item.getIsFrequencyRecommend() == RfpConstant.constant_1) {
                        try {
                            RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(item.getHotelId()));
                        } catch (Exception e) {
                            logger.error("新增AI Review 异常 ", e);
                        }
                    }
                });
            }


        // 更新高频同档推荐
            if(CollectionUtils.isNotEmpty(frequencyHistoryRecommendList)){
                logger.info("生成高频预订同档推荐");
                // 过滤高频酒店
                Map<Long, ProjectHistoryRecommend> theSameLevelRecommendMap = new HashMap<>();
                Map<String, List<ProjectHistoryRecommend>> cityFrequencyHistoryRecommendMap =
                        frequencyHistoryRecommendList.stream().filter(o -> o.getIsFrequencyRecommend() == RfpConstant.constant_1
                                && !JSONUtils.toList(o.getFrequencyRecommends(), Integer.class).contains(RecommendFrequencyEnum.ROOM_NIGHT_100_199.key)
                        ).collect(Collectors.groupingBy(ProjectHistoryRecommend::getCity));
                // 按照城市分组查找同档周边酒店
                String lastYearStr = DateUtil.formatDate(cn.hutool.core.date.DateUtil.offset(new Date(), DateField.YEAR, -1));
                String todayStr = DateUtil.formatDate(new Date());
                Date lastTwoYearDate = DateUtil.offset(new Date(), DateField.YEAR, -2);
                BigDecimal rating49 = new BigDecimal("4.9");
                BigDecimal rating45 = new BigDecimal("4.5");
                for(String city : cityFrequencyHistoryRecommendMap.keySet()){
                    List<HotelResponse> hotelResponses = hotelDao.selectHotelInfoByCityAndRating(city, "4.2");
                    for(ProjectHistoryRecommend projectHistoryRecommend : cityFrequencyHistoryRecommendMap.get(city)){
                        List<Long> newHistoryProjectHotelIdList = new ArrayList<>();
                        Map<Long, BigDecimal> historyProjectHotelDistanceMap = new HashMap<>();
                        Map<Long, HotelResponse> frequencyTheSameLevelHotelMap = new HashMap<>();
                        QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = historyProjectInfoMap.get(projectHistoryRecommend.getHotelId());

                        // 过滤相同星级的酒店
                        List<HotelResponse> theSameLevelHotelResponses = hotelResponses.stream().filter(o ->
                                StringUtils.isNotEmpty(o.getHotelStar()) && !frequencyHotelIdList.contains(o.getHotelId()) && o.getHotelStar().equals(queryHistoryProjectInfoResponse.getHotelStar())
                                ).collect(Collectors.toList());
                        if(CollectionUtils.isEmpty(theSameLevelHotelResponses)){
                            continue;
                        }

                        BigDecimal lastYearAvgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);

                        // 检查酒店是否在2公里范围
                        for(HotelResponse hotelResponse : theSameLevelHotelResponses){
                            // 是否为2公里距离
                            if(hotelResponse.getLatBaidu() == null || hotelResponse.getLngBaidu() == null || queryHistoryProjectInfoResponse.getLatBaidu() == null || queryHistoryProjectInfoResponse.getLngBaidu() == null){
                                continue;
                            }
                            Double distance = LocationUtil.getKmDistance(hotelResponse.getLatBaidu(), hotelResponse.getLngBaidu(), queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(),
                                    hotelResponse.getHotelId().toString(), queryHistoryProjectInfoResponse.getHotelId().toString());
                            if(distance == null){
                                continue;
                            }
                            if(distance == 0.0 || distance > 2){
                                continue;
                            }

                            BigDecimal hotelDistance = new BigDecimal(distance).setScale(2, RoundingMode.HALF_UP);
                            // 已经推荐过酒店获取上次推荐信息, 如果上次已经被推荐，并且距离小于当前距离，不用处理
                            ProjectHistoryRecommend lastProjectHistoryRecommend = theSameLevelRecommendMap.get(hotelResponse.getHotelId());
                            if(lastProjectHistoryRecommend != null && lastProjectHistoryRecommend.getIsSameLevelRreqRecommend() == RfpConstant.constant_1 &&
                                    lastProjectHistoryRecommend.getSameLevelRreqHotelDistance().compareTo(hotelDistance) < 0){
                                logger.info("已经推荐过 {}", JSON.toJSONString(lastProjectHistoryRecommend));
                                continue;
                            }

                            historyProjectHotelDistanceMap.put(hotelResponse.getHotelId(), hotelDistance);
                            frequencyTheSameLevelHotelMap.put(hotelResponse.getHotelId(), hotelResponse);

                            // 一个高产酒店周边同档（同星级分组）的酒店，OTA评分4.9分及以上推荐邀约
                            QueryHistoryProjectInfoResponse recommendQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelResponse.getHotelId());
                            if(recommendQueryHistoryProjectInfoResponse == null){
                                recommendQueryHistoryProjectInfoResponse = new QueryHistoryProjectInfoResponse();
                                recommendQueryHistoryProjectInfoResponse.setProjectId(project.getProjectId());
                                recommendQueryHistoryProjectInfoResponse.setHotelId(hotelResponse.getHotelId());
                                recommendQueryHistoryProjectInfoResponse.setRoomNightCount(0);
                                recommendQueryHistoryProjectInfoResponse.setTotalAmount(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setCityCode(hotelResponse.getCity());
                                recommendQueryHistoryProjectInfoResponse.setCityOrder(0);
                                recommendQueryHistoryProjectInfoResponse.setSavedAmount(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setSavedAmountRate(BigDecimal.ZERO);
                                recommendQueryHistoryProjectInfoResponse.setCreator(userDTO.getOperator());
                                recommendQueryHistoryProjectInfoResponse.setIsUploaded(RfpConstant.constant_0);
                                recommendQueryHistoryProjectInfoResponse.setLatBaidu(hotelResponse.getLatBaidu());
                                recommendQueryHistoryProjectInfoResponse.setLngBaidu(hotelResponse.getLngBaidu());
                                newHistoryProjectHotelIdList.add(hotelResponse.getHotelId());
                                historyProjectInfoMap.put(hotelResponse.getHotelId(), recommendQueryHistoryProjectInfoResponse);
                            }
                        }

                        // 查询最近一年历史数据
                        if(CollectionUtils.isNotEmpty(newHistoryProjectHotelIdList)) {
                            DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
                            disHotelDailyOrderRequest.setHotelIdList(newHistoryProjectHotelIdList);
                            disHotelDailyOrderRequest.setStartTime(lastYearStr);
                            disHotelDailyOrderRequest.setEndTime(todayStr);
                            List<DisHotelDailyOrderResponse> disHotelDailyOrderResponseList = disHotelDailyOrderDao.selectHotelSavedAmountStatList(disHotelDailyOrderRequest);
                            for(DisHotelDailyOrderResponse disHotelDailyOrderResponse : disHotelDailyOrderResponseList){
                                QueryHistoryProjectInfoResponse hotelHistoryProjectInfoResponse = historyProjectInfoMap.get(disHotelDailyOrderResponse.getHotelId());
                                hotelHistoryProjectInfoResponse.setRoomNightCount(disHotelDailyOrderResponse.getRoomNightCount());
                                hotelHistoryProjectInfoResponse.setTotalAmount(disHotelDailyOrderResponse.getOrderAmount());
                                hotelHistoryProjectInfoResponse.setSavedAmount(disHotelDailyOrderResponse.getSavedAmount());
                                hotelHistoryProjectInfoResponse.setSavedAmountRate(disHotelDailyOrderResponse.getSavedAmountRate());
                            }

                            // 计算混淆价格
                            Response hotelLowestPriceResponse = recommendHotelService.queryHotelLowestPrice(newHistoryProjectHotelIdList);
                            List<HotelLowestPricesResponse> hotelLowestPricesResponses = null;
                            if(Objects.equals(hotelLowestPriceResponse.getResult(), ReturnResultEnum.SUCCESS.errorNo)){
                                hotelLowestPricesResponses = (List<HotelLowestPricesResponse>)hotelLowestPriceResponse.getData();
                            }
                            if(CollectionUtils.isNotEmpty(hotelLowestPricesResponses)) {
                                Map<Long, HotelLowestPricesResponse> hotelLowestPricesResponsesMap = hotelLowestPricesResponses.stream().collect(Collectors.toMap(HotelLowestPricesResponse::getHotelId, item -> item));
                                hotelLowestPricesResponsesMap.forEach((hotelId, hotelLowestPricesResponse) -> {
                                    if(hotelLowestPricesResponse.getLowestPrice() != null) {
                                        BigDecimal adjustLowestPrice = generateAdjustLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                                        QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse1 = historyProjectInfoMap.get(hotelId);
                                        queryHistoryProjectInfoResponse1.setAdjustLowestPrice(adjustLowestPrice);
                                        queryHistoryProjectInfoResponse1.setLowestPriceDate(new Date());
                                        queryHistoryProjectInfoResponse1.setLowestPrice(hotelLowestPricesResponse.getLowestPrice());
                                        queryHistoryProjectInfoResponse1.setLowestPriceItemInfo(JSON.toJSONString(hotelLowestPricesResponse.getPriceItems()));
                                    }
                                });
                            }

                            // 新增历史推荐数据
                            List<QueryHistoryProjectInfoResponse> addQueryHistoryProjectInfoResponseList = new ArrayList<>();
                            for(Long hotelId : newHistoryProjectHotelIdList){
                                QueryHistoryProjectInfoResponse newQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelId);
                                logger.info("Add newQueryHistoryProjectInfoResponse {}", newQueryHistoryProjectInfoResponse.getHotelId());
                                newQueryHistoryProjectInfoResponse.setRoomNightCount(0);
                                newQueryHistoryProjectInfoResponse.setTotalAmount(BigDecimal.ZERO);
                                addQueryHistoryProjectInfoResponseList.add(newQueryHistoryProjectInfoResponse);

                            }
                            if(CollectionUtils.isNotEmpty(addQueryHistoryProjectInfoResponseList)) {
                                projectHotelHistoryDataDao.batchMergeHistoryResponse(addQueryHistoryProjectInfoResponseList);
                            }
                        }
                        Set<Long> queryLast12MonthHotelIdSet = new HashSet<>();
                        for(Long hotelId : frequencyTheSameLevelHotelMap.keySet()){
                            HotelResponse hotelResponse = frequencyTheSameLevelHotelMap.get(hotelId);
                            QueryHistoryProjectInfoResponse theSameLevelHotelQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(hotelId);
                            ProjectHistoryRecommend theSameHotelStarHotelRecommend = new ProjectHistoryRecommend();
                            theSameHotelStarHotelRecommend.setProjectId(project.getProjectId());
                            theSameHotelStarHotelRecommend.setHotelId(hotelId);
                            theSameHotelStarHotelRecommend.setIsSameLevelRreq(RfpConstant.constant_1);
                            theSameHotelStarHotelRecommend.setIsSameLevelRreqRecommend(RfpConstant.constant_0);
                            theSameHotelStarHotelRecommend.setSameLevelRreqHotelId(projectHistoryRecommend.getHotelId());
                            theSameHotelStarHotelRecommend.setSameLevelRreqHotelDistance(historyProjectHotelDistanceMap.get(hotelId));

                            QueryHistoryProjectInfoResponse recommendQueryHistoryProjectInfoResponse = historyProjectInfoMap.get(projectHistoryRecommend.getHotelId());
                            RecommendFrequencySameLevelInfo recommendFrequencySameLevelInfo = new RecommendFrequencySameLevelInfo();
                            recommendFrequencySameLevelInfo.setHotelName(recommendQueryHistoryProjectInfoResponse.getHotelName());
                            recommendFrequencySameLevelInfo.setDistance(historyProjectHotelDistanceMap.get(hotelId));
                            recommendFrequencySameLevelInfo.setRoomNightCount(recommendQueryHistoryProjectInfoResponse.getRoomNightCount());
                            recommendFrequencySameLevelInfo.setAvgPrice(BigDecimal.ZERO);
                            if(recommendQueryHistoryProjectInfoResponse.getRoomNightCount() > 0) {
                                recommendFrequencySameLevelInfo.setAvgPrice(recommendQueryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(recommendQueryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP));
                            }
                            theSameHotelStarHotelRecommend.setSameLevelRreqHotelInfo(JSON.toJSONString(recommendFrequencySameLevelInfo));
                            theSameHotelStarHotelRecommend.setCreator(userDTO.getOperator());
                            theSameHotelStarHotelRecommend.setModifier(userDTO.getOperator());

                            List<Integer> recommendReasonList = new ArrayList<>();
                            BigDecimal theSameHotelRating = new BigDecimal(hotelResponse.getRatting());
                            // 查询酒店节省率
                            BigDecimal maxSaveAmountRate = queryMaxSavedAmountRate(hotelId, lastYearStr, todayStr);


                            if(StringUtils.isNotEmpty(hotelResponse.getHotelStar()) && hotelResponse.getHotelStar().equals(queryHistoryProjectInfoResponse.getHotelStar())) {
                                // 一个高产酒店周边同档（同星级分组）的酒店，OTA评分4.9分及以上推荐邀约
                                if (theSameHotelRating.compareTo(rating49) >= 0) {
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_49.key);
                                }
                                //一个高产酒店周边同档（同星级分组）的酒店，开业时间在近2年的推荐邀约 （今年2025年，2023年开业的推荐邀约）
                                if (hotelResponse.getPraciceDate() != null && hotelResponse.getPraciceDate().after(lastTwoYearDate)) {
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OPEN_IN_2_YEAR.key);
                                }

                                // OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低于10%，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%
                                if (theSameHotelRating.compareTo(rating45) >= 0 &&
                                        theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice() != null &&  theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice().compareTo(lastYearAvgPrice.multiply(new BigDecimal("0.9"))) <= 0 &&
                                        maxSaveAmountRate.compareTo(new BigDecimal("0.1")) >= 0
                                ) {
                                    logger.info("高频同档OTA_45_PRICE_LOWER_THAN_10_PERCENT {}, {}, {}, {}, {}", theSameLevelHotelQueryHistoryProjectInfoResponse.getHotelId(), theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice(), projectHistoryRecommend.getHotelId(), lastYearAvgPrice, maxSaveAmountRate);
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_45_PRICE_LOWER_THAN_10_PERCENT.key);
                                }
                                // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
                                queryLast12MonthHotelIdSet.add(hotelResponse.getHotelId());
                            } else if(StringUtils.isNotEmpty(hotelResponse.getHotelStar()) && hotelResponse.getHotelStar().compareTo(queryHistoryProjectInfoResponse.getHotelStar()) > 0) {
                                // ，一个高产酒店周边比当前高产酒店档次更高 OTA评分4.5分以上，签约混淆后价格比当前高产酒店去年成交均价低，且酒店+机构纬度（一个酒店不同机构签约价格可能不同，看签约最好的节省率）的最高节省率高于10%
                                //   （若这个酒店之前不在总名单中时，同时加入总名单）（例如，高产酒店为高档型，去年成交均价460，计算周边更高当次酒店独立起价时，有一个酒店为豪华型，签约最低价428，混淆后458，比当前高产酒店星级分组更高，价格更优，因此推荐签约）
                                if (theSameHotelRating.compareTo(rating45) >= 0 &&
                                        theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice() != null && theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice().compareTo(lastYearAvgPrice) <= 0 &&
                                        maxSaveAmountRate.compareTo(new BigDecimal("0.1")) >= 0
                                ) {
                                    logger.info("OTA_45_LOWER_PRICE {}, {}, {}, {}, {}", theSameLevelHotelQueryHistoryProjectInfoResponse.getHotelId(), theSameLevelHotelQueryHistoryProjectInfoResponse.getAdjustLowestPrice(), projectHistoryRecommend.getHotelId(), lastYearAvgPrice, maxSaveAmountRate);
                                    recommendReasonList.add(RecommendFrequencySameLevelEnum.OTA_45_LOWER_PRICE.key);
                                }
                            }

                            // 设置推荐
                            if(CollectionUtils.isNotEmpty(recommendReasonList)){
                                theSameHotelStarHotelRecommend.setIsSameLevelRreqRecommend(RfpConstant.constant_1);
                                theSameHotelStarHotelRecommend.setSameLevelRreqRecommends(JSON.toJSONString(recommendReasonList));
                            }
                            theSameLevelRecommendMap.put(theSameHotelStarHotelRecommend.getHotelId(), theSameHotelStarHotelRecommend);
                        }
                        if(CollectionUtils.isNotEmpty(queryLast12MonthHotelIdSet)){
                            addRecommendForLast12MonthRoomNight(new ArrayList<>(queryLast12MonthHotelIdSet), lastYearStr, todayStr, theSameLevelRecommendMap);
                        }
                    }
                }


                // 新增或者更新统计
                projectHistoryRecommendDao.resetRecommendFrequencySameLevelInfo(project.getProjectId());
                if(!theSameLevelRecommendMap.isEmpty()){
                    for(ProjectHistoryRecommend projectHistoryRecommend : theSameLevelRecommendMap.values()){
                        int insertCount = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                        if(insertCount == 1 && projectHistoryRecommend.getIsSameLevelRreqRecommend()== RfpConstant.constant_1) {
                            try {
                                RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(projectHistoryRecommend.getHotelId()));
                            } catch (Exception e) {
                                logger.error("新增AI Review 异常 ", e);
                            }
                        }
                    }
                }
                logger.info("更新高频同档推荐统计数量: {}", theSameLevelRecommendMap.size());
            } else {
                logger.info("生成高频预订同档推荐 没有满足要求的酒店");
            }

            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("生成高频预订邀约推荐成功");
            logger.info("生成高频预订邀约推荐成功，总耗时=" + watch.getStepSplitTime() + "，projectId=" + project.getProjectId());
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            logger.error("生成高频预订邀约推荐异常", ex);
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("生成高频预订邀约推荐异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Override
    public Future<Response> generatePoiNearHotelRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("POI周边优质酒店邀约推荐：" +project.getProjectId());

        String statName = RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.value;
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            List<QueryHistoryProjectInfoResponse> poiNearHotelList = historyProjectInfoList.stream().filter(o ->
                            o.getPoiId() != null && o.getPoiId() > 0 && o.getPoiDistance() <=3).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(poiNearHotelList)){
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg(ResultEnum.SUCCESS.value);
                logger.info("POI周边优质酒店邀约推荐为空：" +project.getProjectId());
                return new AsyncResult<>(response);
            }

            BigDecimal ota49 = new BigDecimal("4.9");
            Date lastTwoYearDate = DateUtil.offset(new Date(), DateField.YEAR, -2);
            String lastTwoYearDateStr = com.fangcang.util.DateUtil.dateToString(lastTwoYearDate);
            String todayDateStr = com.fangcang.util.DateUtil.dateToString(new Date());
            List<ProjectHistoryRecommend> projectHistoryRecommendList = new ArrayList<>();
            Map<Long, List<QueryHistoryProjectInfoResponse>> poiNearHotelMap = poiNearHotelList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getPoiId));
            for(Long poiId : poiNearHotelMap.keySet()){
                List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = poiNearHotelMap.get(poiId);
                GeneratePoiStatVO generatePoiStatVO = generatePoiStatVO(queryHistoryProjectInfoResponseList);
                // 统计POI推荐
                Set<Long> poiNearHotelIds = new HashSet<>();
                for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
                    boolean isPoiNearHotel = false;
                    boolean isMaxRoomNightHotel = false;
                    if(StringUtils.isEmpty(queryHistoryProjectInfoResponse.getHotelStar())){
                        continue;
                    }
                    if(queryHistoryProjectInfoResponse.getHotelStar().equals("19") && generatePoiStatVO.getFiveStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getFiveStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if(queryHistoryProjectInfoResponse.getHotelStar().equals("29") && generatePoiStatVO.getQuisaFiveStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getQuisaFiveStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("39") || queryHistoryProjectInfoResponse.getHotelStar().equals("49")) && generatePoiStatVO.getFourAndQuisaFourStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getFourAndQuisaFourStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("59") || queryHistoryProjectInfoResponse.getHotelStar().equals("64")) && generatePoiStatVO.getThreeAndQuisaThreeStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getThreeAndQuisaThreeStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("66") && generatePoiStatVO.getQuisaTwoStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getQuisaTwoStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    } else if ((queryHistoryProjectInfoResponse.getHotelStar().equals("69") || queryHistoryProjectInfoResponse.getHotelStar().equals("79")) && generatePoiStatVO.getTwoAndDownStarHotelRoomNightCount() >= 100){
                        isPoiNearHotel = true;
                        isMaxRoomNightHotel = generatePoiStatVO.getTwoAndDownStarHotelIds().contains(queryHistoryProjectInfoResponse.getHotelId());
                    }

                    if(isPoiNearHotel){
                        ProjectHistoryRecommend poiNearHotelRecommend = new ProjectHistoryRecommend();
                        poiNearHotelRecommend.setProjectId(project.getProjectId());
                        poiNearHotelRecommend.setHotelId(queryHistoryProjectInfoResponse.getHotelId());
                        poiNearHotelRecommend.setIsPoiNearHotel(RfpConstant.constant_1);
                        poiNearHotelRecommend.setPoiNearHotelPoiId(poiId);
                        poiNearHotelRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_0);
                        poiNearHotelRecommend.setCreator(userDTO.getOperator());
                        poiNearHotelRecommend.setModifier(userDTO.getOperator());
                        List<Integer> recommendReasonList = new ArrayList<>();
                        // POI周边进入总名单酒店，如果酒店去年预订间夜超过100间夜，进入推荐邀约
                        if(queryHistoryProjectInfoResponse.getRoomNightCount() >= 100){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.ROOM_NIGHT_MORE_THEN_100.key);
                        }
                        // POI周边进入总名单酒店，如果酒店在当前星级预订间夜量第一，进入推荐邀约  （比如，豪华型酒店比较分散，第一只有85间夜，那么因为这个酒店在豪华型星级排名第一，所以推荐邀约）
                        if(isMaxRoomNightHotel){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.SAME_LEVEL_ROOM_NIGHT_FIRST.key);
                        }
                        // POI周边进入总名单酒店，如果酒店评分4.9分及以上，进入推荐邀约
                        if(StringUtils.isNotEmpty(queryHistoryProjectInfoResponse.getRating()) && new BigDecimal(queryHistoryProjectInfoResponse.getRating()).compareTo(ota49) >= 0){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.OTA_49.key);
                        }
                        //POI周边进入总名单酒店，如果酒店为近2年开业，进入推荐邀约
                        if(queryHistoryProjectInfoResponse.getPraciceDate() != null && queryHistoryProjectInfoResponse.getPraciceDate().after(lastTwoYearDate)){
                            recommendReasonList.add(RecommendPoiNearHotelEnum.OPEN_IN_2_YEAR.key);
                        }
                        poiNearHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                        // 设置POI推荐
                        if(CollectionUtils.isNotEmpty(recommendReasonList)){
                            poiNearHotelRecommend.setPoiNearHotelRecommends(JSON.toJSONString(recommendReasonList));
                            poiNearHotelRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_1);
                        }
                        projectHistoryRecommendList.add(poiNearHotelRecommend);
                    }
                }

                // POI周边进入总名单酒店，如果酒店商旅近12个月预订总间夜超过600间夜，进入推荐邀约
                if(CollectionUtils.isNotEmpty(poiNearHotelIds) && CollectionUtils.isNotEmpty(projectHistoryRecommendList)){
                    Map<Long, ProjectHistoryRecommend> projectHistoryRecommendMap = projectHistoryRecommendList.stream().collect(Collectors.toMap(ProjectHistoryRecommend::getHotelId, Function.identity()));
                    addPoiNearRecommendForLast12MonthRoomNight(new ArrayList<>(poiNearHotelIds), lastTwoYearDateStr, todayDateStr, projectHistoryRecommendMap);
                    projectHistoryRecommendList = new ArrayList<>(projectHistoryRecommendMap.values());
                }

                // 更新POI3公里 统计
                ProjectPoi projectPoi = new ProjectPoi();
                projectPoi.setPoiId(poiId);
                projectPoi.setProjectId(project.getProjectId());
                projectPoi.setModifier(userDTO.getOperator());
                projectPoi.setPoiHotelStat3Km(JSON.toJSONString(generatePoiStatVO.getRecommendNearPoiStat()));
                projectPoiDao.updateProjectPoi3KmStatInfo(projectPoi);
            }

            // 新增或者更新统计
            projectHistoryRecommendDao.resetRecommendPoiNearHotelInfo(project.getProjectId());
            if(!projectHistoryRecommendList.isEmpty()){
                for(ProjectHistoryRecommend projectHistoryRecommend : projectHistoryRecommendList){
                   int insertResult = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                   if(insertResult == RfpConstant.constant_1 && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_1) {
                       try {
                           RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(projectHistoryRecommend.getHotelId()));
                       } catch (Exception e) {
                           logger.error("新增AI Review 异常 ", e);
                       }
                   }
                }
            }

            // 设置成功
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("POI周边优质酒店邀约推荐成功");
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("POI周边优质酒店邀约推荐异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    private GeneratePoiStatVO generatePoiStatVO(List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList){
        // 500以上均价酒店
        int up500HotelCount = 0;
        int _400_500HotelCount = 0;
        int _300_400HotelCount = 0;
        int _200_300HotelCount = 0;
        int down200HotelCount = 0;
        // 五星级 19
        int fiveStarHotelCount = 0;
        // 豪华型 (29)
        int quisaFiveStarHotelCount = 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelCount = 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelCount = 0;
        //  经济型、(69)
        int quisaTwoStarHotelCount = 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelCount = 0;
        // 五星级 19
        int fiveStarHotelRoomNightCount = 0;
        // 豪华型 (29)
        int quisaFiveStarHotelRoomNightCount = 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelRoomNightCount = 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelRoomNightCount = 0;
        //  经济型、(69)
        int quisaTwoStarHotelRoomNightCount = 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelRoomNightCount = 0;
        // 五星级 19
        List<Long> fiveStarHotelIds = new ArrayList<>();
        // 豪华型 (29)
        List<Long> quisaFiveStarHotelIds = new ArrayList<>();
        //  四星级/高档型百分比 (39,49)
        List<Long> fourAndQuisaFourStarHotelIds = new ArrayList<>();
        //  三星/舒适型百分比 (59,64)
        List<Long> threeAndQuisaThreeStarHotelIds = new ArrayList<>();
        //  经济型、(69)
        List<Long> quisaTwoStarHotelIds = new ArrayList<>();
        //  二星及以下/公寓、(66,79)
        List<Long> twoAndDownStarHotelIds = new ArrayList<>();
        // 五星级 19
        int fiveStarHotelMaxRoomNightCount= 0;
        // 豪华型 (29)
        int quisaFiveStarHotelMaxRoomNightCount= 0;
        //  四星级/高档型百分比 (39,49)
        int fourAndQuisaFourStarHotelMaxRoomNightCount= 0;
        //  三星/舒适型百分比 (59,64)
        int threeAndQuisaThreeStarHotelMaxRoomNightCount= 0;
        //  经济型、(69)
        int quisaTwoStarHotelMaxRoomNightCount= 0;
        //  二星及以下/公寓、(66,79)
        int twoAndDownStarHotelMaxRoomNightCount= 0;

        BigDecimal price500 = new BigDecimal(500);
        BigDecimal price400 = new BigDecimal(400);
        BigDecimal price300 = new BigDecimal(300);
        BigDecimal price200 = new BigDecimal(200);
        BigDecimal bigDecimal100 = new BigDecimal("100");
        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : queryHistoryProjectInfoResponseList){
            if(queryHistoryProjectInfoResponse.getRoomNightCount() == 0){
                continue;
            }
            BigDecimal avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(new BigDecimal(queryHistoryProjectInfoResponse.getRoomNightCount()), 0, RoundingMode.HALF_UP);
            if(avgPrice.compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            // 价格统计
            if(avgPrice.compareTo(price500) >= 0){
                up500HotelCount++;
            } else if(avgPrice.compareTo(price400) >= 0){
                _400_500HotelCount++;
            } else if(avgPrice.compareTo(price300) >= 0){
                _300_400HotelCount++;
            } else if(avgPrice.compareTo(price200) >= 0){
                _200_300HotelCount++;
            } else {
                down200HotelCount++;
            }
            // 星级统计
            if(StringUtils.isEmpty(queryHistoryProjectInfoResponse.getHotelStar())){
                continue;
            }
            if(queryHistoryProjectInfoResponse.getHotelStar().equals("19")){
                fiveStarHotelCount++;
                fiveStarHotelRoomNightCount = fiveStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > fiveStarHotelMaxRoomNightCount){
                    fiveStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    fiveStarHotelIds.clear();
                    fiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == fiveStarHotelMaxRoomNightCount){
                    fiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if(queryHistoryProjectInfoResponse.getHotelStar().equals("29")){
                quisaFiveStarHotelCount++;
                quisaFiveStarHotelRoomNightCount = quisaFiveStarHotelRoomNightCount + + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > quisaFiveStarHotelMaxRoomNightCount){
                    quisaFiveStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    quisaFiveStarHotelIds.clear();
                    quisaFiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == quisaFiveStarHotelMaxRoomNightCount){
                    quisaFiveStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("39") || queryHistoryProjectInfoResponse.getHotelStar().equals("49")){
                fourAndQuisaFourStarHotelCount++;
                fourAndQuisaFourStarHotelRoomNightCount = fourAndQuisaFourStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > fourAndQuisaFourStarHotelMaxRoomNightCount){
                    fourAndQuisaFourStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    fourAndQuisaFourStarHotelIds.clear();
                    fourAndQuisaFourStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == fourAndQuisaFourStarHotelMaxRoomNightCount){
                    fourAndQuisaFourStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("59") || queryHistoryProjectInfoResponse.getHotelStar().equals("64")){
                threeAndQuisaThreeStarHotelCount++;
                threeAndQuisaThreeStarHotelRoomNightCount = threeAndQuisaThreeStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > threeAndQuisaThreeStarHotelMaxRoomNightCount){
                    threeAndQuisaThreeStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    threeAndQuisaThreeStarHotelIds.clear();
                    threeAndQuisaThreeStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == threeAndQuisaThreeStarHotelMaxRoomNightCount){
                    threeAndQuisaThreeStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("66")){
                quisaTwoStarHotelCount++;
                quisaTwoStarHotelRoomNightCount = quisaTwoStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > quisaTwoStarHotelMaxRoomNightCount){
                    quisaTwoStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    quisaTwoStarHotelIds.clear();
                    quisaTwoStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == quisaTwoStarHotelMaxRoomNightCount){
                    quisaTwoStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            } else if (queryHistoryProjectInfoResponse.getHotelStar().equals("69") || queryHistoryProjectInfoResponse.getHotelStar().equals("79")){
                twoAndDownStarHotelCount++;
                twoAndDownStarHotelRoomNightCount = twoAndDownStarHotelRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                if(queryHistoryProjectInfoResponse.getRoomNightCount() > twoAndDownStarHotelMaxRoomNightCount){
                    twoAndDownStarHotelMaxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    twoAndDownStarHotelIds.clear();
                    twoAndDownStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == twoAndDownStarHotelMaxRoomNightCount){
                    twoAndDownStarHotelIds.add(queryHistoryProjectInfoResponse.getHotelId());
                }
            }
        }

        // 计算POI统计
        RecommendNearPoiStatInfo recommendNearPoiStat = new RecommendNearPoiStatInfo();
        int totalHotelCount = up500HotelCount + _400_500HotelCount + _300_400HotelCount + _200_300HotelCount + down200HotelCount;
        BigDecimal totalHotelCountDecimal = new BigDecimal(totalHotelCount);
        int totalStarHotelCount = fiveStarHotelCount + quisaFiveStarHotelCount + fourAndQuisaFourStarHotelCount +
                threeAndQuisaThreeStarHotelCount + quisaTwoStarHotelCount + twoAndDownStarHotelCount;
        BigDecimal totalStarHotelCountDecimal = new BigDecimal(totalStarHotelCount);
        // 计算均价统计百分比
        if(totalHotelCount > 0){
            if(up500HotelCount > 0){
                recommendNearPoiStat.setUp500Percentage(new BigDecimal(up500HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_400_500HotelCount > 0){
                recommendNearPoiStat.set_400_500Percentage(new BigDecimal(_400_500HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_300_400HotelCount > 0){
                recommendNearPoiStat.set_300_400Percentage(new BigDecimal(_300_400HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(_200_300HotelCount > 0){
                recommendNearPoiStat.set_200_300Percentage(new BigDecimal(_200_300HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(down200HotelCount > 0){
                recommendNearPoiStat.setDown200Percentage(new BigDecimal(down200HotelCount).divide(totalHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            BigDecimal totalPercentage = recommendNearPoiStat.getUp500Percentage().add(recommendNearPoiStat.get_400_500Percentage())
                    .add(recommendNearPoiStat.get_300_400Percentage()).add(recommendNearPoiStat.get_200_300Percentage())
                    .add(recommendNearPoiStat.getDown200Percentage());
            BigDecimal adjustPercentage = bigDecimal100.subtract(totalPercentage);
            if(adjustPercentage.compareTo(BigDecimal.ZERO) != 0) {
                if (up500HotelCount > 0) {
                    recommendNearPoiStat.setUp500Percentage(recommendNearPoiStat.getUp500Percentage().add(adjustPercentage));
                } else if (_400_500HotelCount > 0) {
                    recommendNearPoiStat.set_400_500Percentage(recommendNearPoiStat.get_400_500Percentage().add(adjustPercentage));
                } else if (_300_400HotelCount > 0) {
                    recommendNearPoiStat.set_300_400Percentage(recommendNearPoiStat.get_300_400Percentage().add(adjustPercentage));
                } else if (_200_300HotelCount > 0) {
                    recommendNearPoiStat.set_200_300Percentage(recommendNearPoiStat.get_200_300Percentage().add(adjustPercentage));
                } else if (down200HotelCount > 0) {
                    recommendNearPoiStat.setDown200Percentage(recommendNearPoiStat.getDown200Percentage().add(adjustPercentage));
                }
            }
        }
        // 计算星级分布百分比
        if(totalStarHotelCount > 0){
            if(fiveStarHotelCount > 0){
                recommendNearPoiStat.setFiveStarPercentage(new BigDecimal(fiveStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(quisaFiveStarHotelCount > 0){
                recommendNearPoiStat.setQuisaFiveStarPercentage(new BigDecimal(quisaFiveStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(fourAndQuisaFourStarHotelCount > 0){
                recommendNearPoiStat.setFourAndQuisaFourStarPercentage(new BigDecimal(fourAndQuisaFourStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(threeAndQuisaThreeStarHotelCount > 0){
                recommendNearPoiStat.setThreeAndQuisaThreeStarPercentage(new BigDecimal(threeAndQuisaThreeStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(quisaTwoStarHotelCount > 0){
                recommendNearPoiStat.setQuisaTwoStarPercentage(new BigDecimal(quisaTwoStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            if(twoAndDownStarHotelCount > 0){
                recommendNearPoiStat.setTwoAndDownStarPercentage(new BigDecimal(twoAndDownStarHotelCount).divide(totalStarHotelCountDecimal, 3, RoundingMode.HALF_UP).multiply(bigDecimal100));
            }
            BigDecimal totalPercentage = recommendNearPoiStat.getFiveStarPercentage().add(recommendNearPoiStat.getQuisaFiveStarPercentage())
                    .add(recommendNearPoiStat.getFourAndQuisaFourStarPercentage()).add(recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage())
                    .add(recommendNearPoiStat.getQuisaTwoStarPercentage()).add(recommendNearPoiStat.getTwoAndDownStarPercentage());
            BigDecimal adjustPercentage = bigDecimal100.subtract(totalPercentage);
            if(adjustPercentage.compareTo(BigDecimal.ZERO) != 0) {
                if (fiveStarHotelCount > 0) {
                    recommendNearPoiStat.setFiveStarPercentage(recommendNearPoiStat.getFiveStarPercentage().add(adjustPercentage));
                } else if (quisaFiveStarHotelCount > 0) {
                    recommendNearPoiStat.setQuisaFiveStarPercentage(recommendNearPoiStat.getQuisaFiveStarPercentage().add(adjustPercentage));
                } else if (fourAndQuisaFourStarHotelCount > 0) {
                    recommendNearPoiStat.setFourAndQuisaFourStarPercentage(recommendNearPoiStat.getFourAndQuisaFourStarPercentage().add(adjustPercentage));
                } else if (threeAndQuisaThreeStarHotelCount > 0) {
                    recommendNearPoiStat.setThreeAndQuisaThreeStarPercentage(recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage().add(adjustPercentage));
                } else if (quisaTwoStarHotelCount > 0) {
                    recommendNearPoiStat.setQuisaTwoStarPercentage(recommendNearPoiStat.getQuisaTwoStarPercentage().add(adjustPercentage));
                } else if (twoAndDownStarHotelCount > 0) {
                    recommendNearPoiStat.setTwoAndDownStarPercentage(recommendNearPoiStat.getTwoAndDownStarPercentage().add(adjustPercentage));
                }
            }
        }

        GeneratePoiStatVO generatePoiStatVO = new GeneratePoiStatVO();
        // 五星级 19
        generatePoiStatVO.setFiveStarHotelRoomNightCount(fiveStarHotelRoomNightCount);
        // 豪华型 (29)
        generatePoiStatVO.setQuisaFiveStarHotelRoomNightCount(quisaFiveStarHotelRoomNightCount);
        //  四星级/高档型百分比 (39,49)
        generatePoiStatVO.setFourAndQuisaFourStarHotelRoomNightCount(fourAndQuisaFourStarHotelRoomNightCount);
        //  三星/舒适型百分比 (59,64)
        generatePoiStatVO.setThreeAndQuisaThreeStarHotelRoomNightCount(threeAndQuisaThreeStarHotelRoomNightCount);
        //  经济型、(69)
        generatePoiStatVO.setQuisaTwoStarHotelRoomNightCount(quisaTwoStarHotelRoomNightCount);
        //  二星及以下/公寓、(66,79)
        generatePoiStatVO.setTwoAndDownStarHotelRoomNightCount(twoAndDownStarHotelRoomNightCount);

        // 五星级 19
        generatePoiStatVO.setFiveStarHotelIds(fiveStarHotelIds);
        // 豪华型 (29)
        generatePoiStatVO.setQuisaFiveStarHotelIds(quisaFiveStarHotelIds);
        //  四星级/高档型百分比 (39,49)
        generatePoiStatVO.setFourAndQuisaFourStarHotelIds(fourAndQuisaFourStarHotelIds);
        //  三星/舒适型百分比 (59,64)
        generatePoiStatVO.setThreeAndQuisaThreeStarHotelIds(threeAndQuisaThreeStarHotelIds);
        //  经济型、(69)
        generatePoiStatVO.setQuisaTwoStarHotelIds(quisaTwoStarHotelIds);
        //  二星及以下/公寓、(66,79)
        generatePoiStatVO.setTwoAndDownStarHotelIds(twoAndDownStarHotelIds);

        generatePoiStatVO.setRecommendNearPoiStat(recommendNearPoiStat);
        return generatePoiStatVO;
    }

    @Override
    public Future<Response> generateAreaGatherRecommend(String statReferenceNo, UserDTO userDTO, Project project) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("散布聚量邀约推荐：" +project.getProjectId());

        String statName = RecommendHotelNameEnum.AREA_GATHER_RECOMMEND.value;
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            // 查看其他异步统计是否已经完成，需要等待其他统计完成才开始 最多等待2小时
            for(int i=0; i<720; i++) {
                List<ProjectRecommendStatLog> projectRecommendStatLogList = projectRecommendStatLogDao.queryLogList(projectRecommendStatLog.getProjectId(), projectRecommendStatLog.getStatReferenceNo());
                List<ProjectRecommendStatLog> filterProjectRecommendStatLogList = projectRecommendStatLogList.stream().filter(o -> o.getIsFinished() == RfpConstant.constant_0 && !o.getStatName().equals(statName)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterProjectRecommendStatLogList)) {
                    try {
                        Thread.sleep(10000);
                    } catch (Exception ex) {
                        logger.error(ex.getMessage());
                    }
                } else {
                    break;
                }
            }

            List<ProjectHistoryRecommend> projectHistoryRecommendList = new ArrayList<>();

            // 查询已经推荐过的酒店ID并且过滤
            QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest = new QueryProjectRecommendHotelRequest();
            queryProjectRecommendHotelRequest.setProjectId(project.getProjectId());
            List<Long> recommendHotelIdList = projectHotelHistoryDataDao.queryTotalRecommendHotelIdNotIncldeAreaGaterList(queryProjectRecommendHotelRequest);

            // 过滤去年已经推荐过酒店
            List<QueryHistoryProjectInfoResponse> historyProjectInfoList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(project.getProjectId(), null,null, null);
            List<QueryHistoryProjectInfoResponse> projectHistoryDataList = historyProjectInfoList.stream().filter(o -> !recommendHotelIdList.contains(o.getHotelId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(projectHistoryDataList)){
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg(ResultEnum.SUCCESS.value);
                logger.info("散布聚量邀约推荐为空：" + project.getProjectId());
                return new AsyncResult<>(response);
            }

            // 城市分组计算
            Map<String, List<QueryHistoryProjectInfoResponse>> cityNoPoiHotAreaRecommendListMap = projectHistoryDataList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));
            Map<String, List<QueryHistoryProjectInfoResponse>> cityRecommendListMap = historyProjectInfoList.stream().filter(h-> recommendHotelIdList.contains(h.getHotelId())).collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));

            // 周边2KM没有同星级分组的推荐邀约酒店，且周边2KM同星级分组预订间夜大于200间夜的酒店，满足此条件为区域聚量
            for(String cityCode : cityNoPoiHotAreaRecommendListMap.keySet()) {
                // 区域聚量
                List<Long> cityAreaGatherHotelIds = new ArrayList<>();
                List<QueryHistoryProjectInfoResponse> cityHistoryProjectInfoList = cityNoPoiHotAreaRecommendListMap.get(cityCode);
                if(cityHistoryProjectInfoList == null){
                    continue;
                }
                // 按照星级分组
                Map<String, List<QueryHistoryProjectInfoResponse>> cityHotelStarGroupMap = convertToHotelStarGroupMap(cityHistoryProjectInfoList);
                // 设置推荐城市星级列表
                Map<String, List<QueryHistoryProjectInfoResponse>> cityRecommendHotelStarGroupMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(cityRecommendListMap.get(cityCode))){
                    cityRecommendHotelStarGroupMap = convertToHotelStarGroupMap(cityRecommendListMap.get(cityCode));
                }
                for(String hotelStarGroup : cityHotelStarGroupMap.keySet()) {
                    boolean hasAreaStarRecommend = false;
                    // 按照间夜数排序
                    List<QueryHistoryProjectInfoResponse> cityHotelStarGroupList = cityHotelStarGroupMap.get(hotelStarGroup).stream().sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getRoomNightCount).reversed()).collect(Collectors.toList());

                    List<Long> recommedHotelIdList = new ArrayList<>();
                    for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : cityHotelStarGroupList){
                        if(cityAreaGatherHotelIds.contains(queryHistoryProjectInfoResponse.getHotelId())){
                            continue;
                        }

                        // 检查酒店为中心2公里范围内是否有推荐同星级酒店的酒店
                        boolean has2DistanceRecommendHotel = false;
                        if(CollectionUtils.isNotEmpty(cityRecommendHotelStarGroupMap.get(hotelStarGroup))){
                            for(QueryHistoryProjectInfoResponse cityRecommendHotelStarGroup : cityRecommendHotelStarGroupMap.get(hotelStarGroup)){
                                Double distance = LocationUtil.getKmDistance(queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(),
                                        cityRecommendHotelStarGroup.getLatBaidu(), cityRecommendHotelStarGroup.getLngBaidu(), queryHistoryProjectInfoResponse.getHotelId().toString(), cityRecommendHotelStarGroup.getHotelId().toString());
                                if(distance != null && distance <= 2){
                                    has2DistanceRecommendHotel = true;
                                    break;
                                }
                            }
                            if(has2DistanceRecommendHotel){
                                continue;
                            }
                        }

                        Map<Long, Double> hotelDistanceMap = new HashMap<>();
                        Long centerHotelId = queryHistoryProjectInfoResponse.getHotelId();
                        hotelDistanceMap.put(centerHotelId, 0.0);
                        int totalRoomNightCount = 0;
                        List<QueryHistoryProjectInfoResponse> recommendHistoryProjectInfoList = new ArrayList<>();
                        List<Long> maxRoomNightHotelIdList = new ArrayList<>();
                        List<Long> lowestPriceHotelIdList = new ArrayList<>();
                        int maxRoomNightCount = -1;
                        BigDecimal lowestPrice = new BigDecimal(1000000);
                        BigDecimal maxPrice = new BigDecimal(0);
                        for(QueryHistoryProjectInfoResponse centerNearHotel : cityHotelStarGroupList){
                            // 过滤已经处理过的酒店
                            if(cityAreaGatherHotelIds.contains(centerNearHotel.getHotelId())){
                                continue;
                            }
                            // 过滤大于2公里范围酒店
                            Double distance = LocationUtil.getKmDistance(queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(),
                                    centerNearHotel.getLatBaidu(), centerNearHotel.getLngBaidu(), queryHistoryProjectInfoResponse.getHotelId().toString(), centerNearHotel.getHotelId().toString());
                            if (distance == null || distance > 2) {
                                continue;
                            }
                            hotelDistanceMap.put(centerNearHotel.getHotelId(), distance);
                            totalRoomNightCount = totalRoomNightCount + centerNearHotel.getRoomNightCount();
                            recommendHistoryProjectInfoList.add(centerNearHotel);

                            // 计算最高间夜数酒店和最低价格酒店
                            if(centerNearHotel.getRoomNightCount() == 0){
                                continue;
                            }
                            if(centerNearHotel.getRoomNightCount() > maxRoomNightCount){
                                maxRoomNightHotelIdList.clear();
                                maxRoomNightHotelIdList.add(centerNearHotel.getHotelId());
                                maxRoomNightCount = centerNearHotel.getRoomNightCount();
                            } else if(centerNearHotel.getRoomNightCount() == maxRoomNightCount){
                                maxRoomNightHotelIdList.add(centerNearHotel.getHotelId());
                            }
                            BigDecimal avgPrice = centerNearHotel.getTotalAmount().divide(BigDecimal.valueOf(centerNearHotel.getRoomNightCount()), 2, RoundingMode.HALF_UP);
                            if(avgPrice.compareTo(lowestPrice) < 0){
                                lowestPriceHotelIdList.clear();
                                lowestPriceHotelIdList.add(centerNearHotel.getHotelId());
                                lowestPrice = avgPrice;
                            } else if(avgPrice.compareTo(lowestPrice) == 0){
                                lowestPriceHotelIdList.add(centerNearHotel.getHotelId());
                            }

                            if(avgPrice.compareTo(maxPrice) > 0){
                                maxPrice = avgPrice;
                            }
                            recommedHotelIdList.add(centerNearHotel.getHotelId());
                        }
                        if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                            logger.info("散布聚量邀约推荐 hotelId: {}, totalRoomNightCount {}", queryHistoryProjectInfoResponse.getHotelId(), totalRoomNightCount);
                        }
                        // 大于200间夜的酒店，满足此条件为区域聚量
                        if(totalRoomNightCount >= 200){
                            logger.info("centerHotelId区域 {} ", centerHotelId);
                            logger.info("centerHotelId区域HotelId {}", recommedHotelIdList);
                            RecommendAreaGatherHotelStatInfo recommendAreaGatherHotelStatInfo = new RecommendAreaGatherHotelStatInfo();
                            recommendAreaGatherHotelStatInfo.setName(queryHistoryProjectInfoResponse.getHotelName());
                            recommendAreaGatherHotelStatInfo.setAreaTotalRoomNight(totalRoomNightCount);
                            recommendAreaGatherHotelStatInfo.setMinPrice(lowestPrice);
                            recommendAreaGatherHotelStatInfo.setMaxPrice(maxPrice);
                            recommendAreaGatherHotelStatInfo.setStarGroup(HotelStarGroupEnum.getEnumByName(hotelStarGroup).message);

                            for(QueryHistoryProjectInfoResponse recommendHistoryProjectInfoResponse : recommendHistoryProjectInfoList) {
                                ProjectHistoryRecommend areaGatherHotelRecommend = new ProjectHistoryRecommend();
                                areaGatherHotelRecommend.setProjectId(project.getProjectId());
                                areaGatherHotelRecommend.setHotelId(recommendHistoryProjectInfoResponse.getHotelId());
                                areaGatherHotelRecommend.setIsAreaGather(RfpConstant.constant_1);
                                areaGatherHotelRecommend.setAreaGatherHotelId(centerHotelId);
                                recommendAreaGatherHotelStatInfo.setDistance(hotelDistanceMap.get(recommendHistoryProjectInfoResponse.getHotelId()));
                                String jsonInfo = JsonUtil.objectToJson(recommendAreaGatherHotelStatInfo);
                                areaGatherHotelRecommend.setAreaGatherHotelInfo(jsonInfo);
                                areaGatherHotelRecommend.setIsAreaGatherRecommend(RfpConstant.constant_0);
                                areaGatherHotelRecommend.setCreator(userDTO.getOperator());
                                areaGatherHotelRecommend.setModifier(userDTO.getOperator());
                                List<Integer> recommendReasonList = new ArrayList<>();
                                // 员工预订最多的酒店
                                if(maxRoomNightHotelIdList.contains(recommendHistoryProjectInfoResponse.getHotelId())){
                                    recommendReasonList.add(RecommendAreaGatherEnum.AREA_ROOM_NIGHT_FIRST.key);
                                }
                                // 预订均价最低的酒店
                                if(lowestPriceHotelIdList.contains(recommendHistoryProjectInfoResponse.getHotelId())){
                                    recommendReasonList.add(RecommendAreaGatherEnum.AREA_LOWEST_PRICE.key);
                                }
                                if(!recommendReasonList.isEmpty()){
                                    hasAreaStarRecommend = true;
                                    areaGatherHotelRecommend.setIsAreaGatherRecommend(RfpConstant.constant_1);
                                    cityAreaGatherHotelIds.add(areaGatherHotelRecommend.getHotelId());
                                }
                                areaGatherHotelRecommend.setAreaGatherRecommends(JsonUtil.objectToJson(recommendReasonList));
                                projectHistoryRecommendList.add(areaGatherHotelRecommend);
                            }
                        }
                    }

                    // 如果没有区域集合，计算有没有同城聚合
                    if(!hasAreaStarRecommend && CollectionUtils.isEmpty(cityRecommendHotelStarGroupMap.get(hotelStarGroup))){
                        int totalRoomNightCount = 0;
                        List<Long> maxRoomNightHotelIdList = new ArrayList<>();
                        List<Long> lowestPriceHotelIdList = new ArrayList<>();
                        int maxRoomNightCount = -1;
                        BigDecimal lowestPrice = new BigDecimal(1000000);
                        BigDecimal maxPrice = new BigDecimal(0);
                        for(QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : cityHotelStarGroupList){
                            totalRoomNightCount = totalRoomNightCount + queryHistoryProjectInfoResponse.getRoomNightCount();
                            // 计算最高间夜数酒店和最低价格酒店
                            if(queryHistoryProjectInfoResponse.getRoomNightCount() == 0){
                                continue;
                            }
                            if(queryHistoryProjectInfoResponse.getRoomNightCount() > maxRoomNightCount){
                                maxRoomNightHotelIdList.clear();
                                maxRoomNightHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                                maxRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                            } else if(queryHistoryProjectInfoResponse.getRoomNightCount() == maxRoomNightCount){
                                maxRoomNightHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                            }
                            BigDecimal avgPrice = queryHistoryProjectInfoResponse.getTotalAmount().divide(BigDecimal.valueOf(queryHistoryProjectInfoResponse.getRoomNightCount()), 2, RoundingMode.HALF_UP);
                            if(avgPrice.compareTo(lowestPrice) < 0){
                                lowestPriceHotelIdList.clear();
                                lowestPriceHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                                lowestPrice = avgPrice;
                            } else if(avgPrice.compareTo(lowestPrice) == 0){
                                lowestPriceHotelIdList.add(queryHistoryProjectInfoResponse.getHotelId());
                            }

                            if(avgPrice.compareTo(maxPrice) > 0){
                                maxPrice = avgPrice;
                            }
                        }
                        logger.info("聚合城市 {}，totalRoomNightCount{}", cityCode, totalRoomNightCount);
                        // 大于200间夜的酒店，满足此条件为区域聚量
                        if(totalRoomNightCount >= 200){
                            logger.info("聚合城市 {}，星级分组{}", cityCode, hotelStarGroup);
                            RecommendAreaGatherHotelStatInfo recommendAreaGatherHotelStatInfo = new RecommendAreaGatherHotelStatInfo();
                            String cityName = cityNoPoiHotAreaRecommendListMap.get(cityCode).get(0).getCityName();
                            recommendAreaGatherHotelStatInfo.setName(cityName);
                            recommendAreaGatherHotelStatInfo.setAreaTotalRoomNight(totalRoomNightCount);
                            recommendAreaGatherHotelStatInfo.setMinPrice(lowestPrice);
                            recommendAreaGatherHotelStatInfo.setMaxPrice(maxPrice);
                            recommendAreaGatherHotelStatInfo.setStarGroup(HotelStarGroupEnum.getEnumByName(hotelStarGroup).message);
                            List<Long> cityRecommendHotelIds = new ArrayList<>();
                            for(QueryHistoryProjectInfoResponse recommendHistoryProjectInfoResponse : cityHotelStarGroupList) {
                                ProjectHistoryRecommend areaGatherHotelRecommend = new ProjectHistoryRecommend();
                                areaGatherHotelRecommend.setProjectId(project.getProjectId());
                                areaGatherHotelRecommend.setHotelId(recommendHistoryProjectInfoResponse.getHotelId());
                                areaGatherHotelRecommend.setIsAreaGather(RfpConstant.constant_1);
                                areaGatherHotelRecommend.setIsAreaGatherRecommend(RfpConstant.constant_0);
                                areaGatherHotelRecommend.setAreaGatherHotelInfo(JsonUtil.objectToJson(recommendAreaGatherHotelStatInfo));
                                areaGatherHotelRecommend.setCreator(userDTO.getOperator());
                                areaGatherHotelRecommend.setModifier(userDTO.getOperator());
                                List<Integer> recommendReasonList = new ArrayList<>();
                                // 员工预订最多的酒店
                                if(maxRoomNightHotelIdList.contains(recommendHistoryProjectInfoResponse.getHotelId())){
                                    recommendReasonList.add(RecommendAreaGatherEnum.CITY_ROOM_NIGHT_FIRST.key);
                                }
                                // 预订均价最低的酒店
                                if(lowestPriceHotelIdList.contains(recommendHistoryProjectInfoResponse.getHotelId())){
                                    recommendReasonList.add(RecommendAreaGatherEnum.CITY_LOWEST_PRICE.key);
                                }
                                if(!recommendReasonList.isEmpty()){
                                    areaGatherHotelRecommend.setIsAreaGatherRecommend(RfpConstant.constant_1);
                                }
                                areaGatherHotelRecommend.setAreaGatherRecommends(JsonUtil.objectToJson(recommendReasonList));
                                cityRecommendHotelIds.add(recommendHistoryProjectInfoResponse.getHotelId());
                                projectHistoryRecommendList.add(areaGatherHotelRecommend);
                            }
                            logger.info("聚合城市 {}，cityRecommendHotelIds{}", cityCode, cityRecommendHotelIds);
                        }
                    }
                }
            }
            // 新增或者更新统计
            projectHistoryRecommendDao.resetRecommendAreaGather(project.getProjectId());
            if(!projectHistoryRecommendList.isEmpty()){
                for(ProjectHistoryRecommend projectHistoryRecommend : projectHistoryRecommendList){
                    int insertCount = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                    if(insertCount == 1 && projectHistoryRecommend.getIsAreaGatherRecommend() == RfpConstant.constant_1) {
                        try {
                            RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(projectHistoryRecommend.getHotelId()));
                        } catch (Exception e) {
                            logger.error("新增AI Review 异常 ", e);
                        }
                    }
                }
            }
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("散布聚量邀约推荐生成成功");
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("散布聚量邀约推荐异常");
        } finally {

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    @Override
    public Future<Response> generateNoPoiHotAreaRecommend(String statReferenceNo, UserDTO userDTO, Project project, List<QueryHistoryProjectInfoResponse> historyProjectInfoList, CountDownLatch countDownLatch) {
        MDC.put("traceId", IdUtil.objectId());
        logger.info("非POI热订区域邀约推荐：" +project.getProjectId());

        String statName = RecommendHotelNameEnum.NO_POI_HOT_AREA_RECOMMEND.value;
        ProjectRecommendStatLog projectRecommendStatLog = initProjectRecommendStatLog(userDTO, statName, statReferenceNo, project.getProjectId());
        projectRecommendStatLogDao.insertLog(projectRecommendStatLog);

        // 定义返回值
        Response response = new Response();
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            List<ProjectHistoryRecommend> projectHistoryRecommendList = new ArrayList<>();

            // 按产量由高到低进行排序，挨个计算去年预订间夜超过50间夜的酒店是否距离同城上最近的POI距离大于6KM
            List<QueryHistoryProjectInfoResponse> centerPoiHotAreaRecommendList = historyProjectInfoList.stream().filter(o ->
                    o.getPoiId() != null && o.getPoiId() > 0 && o.getPoiDistance() >= 6 && o.getRoomNightCount() >= 50).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(centerPoiHotAreaRecommendList)){
                response.setResult(ResultEnum.SUCCESS.key);
                response.setMsg(ResultEnum.SUCCESS.value);
                logger.info("非POI热订区域邀约推荐为空：" +project.getProjectId());
                return new AsyncResult<>(response);
            }

            BigDecimal ota49 = new BigDecimal("4.9");
            Date lastTwoYearDate = DateUtil.offset(new Date(), DateField.YEAR, -2);
            String lastTwoYearDateStr = com.fangcang.util.DateUtil.dateToString(lastTwoYearDate);
            String todayDateStr = com.fangcang.util.DateUtil.dateToString(new Date());
            Map<String, List<QueryHistoryProjectInfoResponse>> centerCityNoPoiHotAreaRecommendListMap = centerPoiHotAreaRecommendList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));
            Map<String, List<QueryHistoryProjectInfoResponse>> cityNoPoiHotAreaRecommendListMap = historyProjectInfoList.stream().collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getCityCode));

            // 判断酒店是否为非POI热地区域酒店
            // 酒店周边3KM，所有酒店去年销售总间夜，总间夜超过600间夜
            // 过滤POi距离大于6KM, 不属于非poi区域酒店
            Set<Long> addedNoPoiHotelIdSet = new HashSet<>();
            for(String cityCode : centerCityNoPoiHotAreaRecommendListMap.keySet()) {
                Set<Long> cityNoPoiHotAreaHotelIds = new HashSet<>();
                // 按照间夜数高到低排序
                List<QueryHistoryProjectInfoResponse> centerCityNoPoiHotAreaRecommendList = centerCityNoPoiHotAreaRecommendListMap.get(cityCode).stream().filter(o -> !cityNoPoiHotAreaHotelIds.contains(o.getHotelId()))
                        .sorted(Comparator.comparing(QueryHistoryProjectInfoResponse::getRoomNightCount).reversed()).collect(Collectors.toList());
                // 查找非POI区域中心酒店
                for (QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse : centerCityNoPoiHotAreaRecommendList) {
                    // 当前酒店3KM里以内的所有酒店不在参加非POI热订区计算（避免重复计算）
                    if(addedNoPoiHotelIdSet.contains(queryHistoryProjectInfoResponse.getHotelId())){
                        continue;
                    }
                    Long noPoiHotelId = queryHistoryProjectInfoResponse.getHotelId();
                    int totalRoomNightCount = queryHistoryProjectInfoResponse.getRoomNightCount();
                    List<QueryHistoryProjectInfoResponse> noPoiAllHotelList = new ArrayList<>();
                    Map<Long, Double> noPoiDistanceMap = new HashMap<>();

                    // 新增NO POI中心酒店信息
                    noPoiAllHotelList.add(queryHistoryProjectInfoResponse);
                    noPoiDistanceMap.put(noPoiHotelId, 0.0);

                    // 城市No POI
                    List<QueryHistoryProjectInfoResponse> cityNoPoiHotAreaRecommendList = cityNoPoiHotAreaRecommendListMap.get(cityCode);
                    for (QueryHistoryProjectInfoResponse noPoiNearHotel : cityNoPoiHotAreaRecommendList) {
                        Long noPoiNearHotelId = noPoiNearHotel.getHotelId();
                        if(addedNoPoiHotelIdSet.contains(noPoiNearHotelId)){
                            continue;
                        }
                        // 过滤非POI区酒店ID
                        if (noPoiHotelId.equals(noPoiNearHotelId)) {
                            continue;
                        }
                        // 过滤大于3公里范围酒店
                        Double distance = LocationUtil.getKmDistance(noPoiNearHotel.getLatBaidu(), noPoiNearHotel.getLngBaidu(),
                                queryHistoryProjectInfoResponse.getLatBaidu(), queryHistoryProjectInfoResponse.getLngBaidu(), noPoiNearHotel.getHotelId().toString(), queryHistoryProjectInfoResponse.getHotelId().toString());
                        if (distance == null || distance > 3) {
                            continue;
                        }
                        noPoiDistanceMap.put(noPoiNearHotelId, distance);

                        // 计算总间夜数
                        totalRoomNightCount = totalRoomNightCount + noPoiNearHotel.getRoomNightCount();
                        noPoiAllHotelList.add(noPoiNearHotel);
                    }
                    if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                        logger.info("非Poi统计 hotelId: {}, {}", queryHistoryProjectInfoResponse.getHotelId(), totalRoomNightCount);
                    }

                    // 总间夜数大于600间夜数设定为非POI区域
                    if (totalRoomNightCount >= 600) {
                        logger.info("noPoiHotelId区域 {}", noPoiHotelId);
                        logger.info("noPoiHotelId区域Hotel {}", noPoiDistanceMap);
                        cityNoPoiHotAreaHotelIds.addAll(noPoiDistanceMap.keySet());
                        addedNoPoiHotelIdSet.addAll(noPoiDistanceMap.keySet());

                        // 生成统计信息
                        GeneratePoiStatVO generatePoiStatVO = generatePoiStatVO(noPoiAllHotelList);
                        RecommendNoPoiHotelStatInfo recommendNoPoiHotelStatInfo = new RecommendNoPoiHotelStatInfo();
                        recommendNoPoiHotelStatInfo.setHotelName(queryHistoryProjectInfoResponse.getHotelName());
                        recommendNoPoiHotelStatInfo.setAreaTotalRoomNight(totalRoomNightCount);
                        BeanUtils.copyProperties(generatePoiStatVO.getRecommendNearPoiStat(), recommendNoPoiHotelStatInfo);

                        // 设置推荐信息
                        Set<Long> noPoiNearHotHotelIdSet = new HashSet<>();
                        for (QueryHistoryProjectInfoResponse noPoiNearHotel : noPoiAllHotelList) {
                            //  非POI热订区域3KM内星级分组统计总间夜数超过100间夜的星级分组，列入当前POI主要预订星级分组。当前非POI热订区域主要预订星级分组在周边3KM内的全部酒店进入总名单
                            boolean isProjectHistoryRecommendData = false;
                            if(StringUtils.isEmpty(noPoiNearHotel.getHotelStar())){
                                continue;
                            }
                            if (noPoiNearHotel.getHotelStar().equals("19") && generatePoiStatVO.getFiveStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            } else if (noPoiNearHotel.getHotelStar().equals("29") && generatePoiStatVO.getQuisaFiveStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            } else if ((noPoiNearHotel.getHotelStar().equals("39") || noPoiNearHotel.getHotelStar().equals("49")) && generatePoiStatVO.getFourAndQuisaFourStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            } else if ((noPoiNearHotel.getHotelStar().equals("59") || noPoiNearHotel.getHotelStar().equals("64")) && generatePoiStatVO.getThreeAndQuisaThreeStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            } else if (noPoiNearHotel.getHotelStar().equals("66") && generatePoiStatVO.getQuisaTwoStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            } else if ((noPoiNearHotel.getHotelStar().equals("69") || noPoiNearHotel.getHotelStar().equals("79")) && generatePoiStatVO.getTwoAndDownStarHotelRoomNightCount() >= 100) {
                                isProjectHistoryRecommendData = true;
                            }
                            if (!isProjectHistoryRecommendData) {
                                continue;
                            }
                            if(Objects.equals(queryHistoryProjectInfoResponse.getHotelId(), recordLogHotelId)){
                                logger.info("非Poi统计 hotelId: {}, noPoiNearHotel {}", queryHistoryProjectInfoResponse.getHotelId(), JsonUtil.objectToJson(noPoiNearHotel));
                            }
                            noPoiNearHotHotelIdSet.add(noPoiNearHotel.getHotelId());
                            ProjectHistoryRecommend noPoiNearHotAreaRecommend = new ProjectHistoryRecommend();
                            noPoiNearHotAreaRecommend.setProjectId(project.getProjectId());
                            noPoiNearHotAreaRecommend.setHotelId(noPoiNearHotel.getHotelId());
                            noPoiNearHotAreaRecommend.setIsNoPoiHotArea(RfpConstant.constant_1);
                            noPoiNearHotAreaRecommend.setNoPoiHotelId(noPoiHotelId);
                            recommendNoPoiHotelStatInfo.setDistance(noPoiDistanceMap.get(noPoiNearHotel.getHotelId()));
                            String noPoiHotelInfoJsonInfo = JsonUtil.objectToJson(recommendNoPoiHotelStatInfo);
                            noPoiNearHotAreaRecommend.setNoPoiHotelInfo(noPoiHotelInfoJsonInfo);
                            noPoiNearHotAreaRecommend.setIsNoPoiHotAreaRecommend(RfpConstant.constant_0);
                            noPoiNearHotAreaRecommend.setCreator(userDTO.getOperator());
                            noPoiNearHotAreaRecommend.setModifier(userDTO.getOperator());
                            List<Integer> recommendReasonList = new ArrayList<>();
                            // 非POI热订区域进入总名单酒店，如果酒店去年预订间夜超过100间夜，进入推荐邀约
                            if (noPoiNearHotel.getRoomNightCount() >= 100) {
                                noPoiNearHotAreaRecommend.setIsNoPoiHotAreaRecommend(RfpConstant.constant_1);
                                recommendReasonList.add(RecommendNoPoiHotArealEnum.ROOM_NIGHT_MORE_THEN_100.key);
                            }
                            //    非POI热订区域进入总名单酒店，如果酒店在当前星级预订间夜量第一，进入推荐邀约  （比如，豪华型酒店比较分散，第一只有85间夜，那么因为这个酒店在豪华型星级排名第一，所以推荐邀约
                            boolean isMaxRoomNightHotel = false;
                            if (noPoiNearHotel.getHotelStar().equals("19")) {
                                isMaxRoomNightHotel = generatePoiStatVO.getFiveStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            } else if (noPoiNearHotel.getHotelStar().equals("29")) {
                                isMaxRoomNightHotel = generatePoiStatVO.getQuisaFiveStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            } else if ((noPoiNearHotel.getHotelStar().equals("39") || noPoiNearHotel.getHotelStar().equals("49"))) {
                                isMaxRoomNightHotel = generatePoiStatVO.getFourAndQuisaFourStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            } else if ((noPoiNearHotel.getHotelStar().equals("59") || noPoiNearHotel.getHotelStar().equals("64"))) {
                                isMaxRoomNightHotel = generatePoiStatVO.getThreeAndQuisaThreeStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            } else if (noPoiNearHotel.getHotelStar().equals("66")) {
                                isMaxRoomNightHotel = generatePoiStatVO.getQuisaTwoStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            } else if ((noPoiNearHotel.getHotelStar().equals("69") || noPoiNearHotel.getHotelStar().equals("79"))) {
                                isMaxRoomNightHotel = generatePoiStatVO.getTwoAndDownStarHotelIds().contains(noPoiNearHotel.getHotelId());
                            }
                            if (isMaxRoomNightHotel) {
                                recommendReasonList.add(RecommendNoPoiHotArealEnum.SAME_LEVEL_ROOM_NIGHT_FIRST.key);
                            }
                            // POI周边进入总名单酒店，如果酒店评分4.9分及以上，进入推荐邀约
                            if (StringUtils.isNotEmpty(noPoiNearHotel.getRating()) && new BigDecimal(noPoiNearHotel.getRating()).compareTo(ota49) >= 0) {
                                recommendReasonList.add(RecommendNoPoiHotArealEnum.OTA_49.key);
                            }
                            //POI周边进入总名单酒店，如果酒店为近2年开业，进入推荐邀约
                            if (noPoiNearHotel.getPraciceDate() != null && noPoiNearHotel.getPraciceDate().after(lastTwoYearDate)) {
                                recommendReasonList.add(RecommendNoPoiHotArealEnum.OPEN_IN_2_YEAR.key);
                            }
                            if(!recommendReasonList.isEmpty()){
                                noPoiNearHotAreaRecommend.setIsNoPoiHotAreaRecommend(RfpConstant.constant_1);
                            }
                            noPoiNearHotAreaRecommend.setNoPoiHotAreaRecommends(JsonUtil.objectToJson(recommendReasonList));
                            projectHistoryRecommendList.add(noPoiNearHotAreaRecommend);
                        }
                        // POI周边进入总名单酒店，如果酒店商旅近12个月预订总间夜超过600间夜，进入推荐邀约
                        if (CollectionUtils.isNotEmpty(noPoiNearHotHotelIdSet) && CollectionUtils.isNotEmpty(projectHistoryRecommendList)) {
                            Map<Long, ProjectHistoryRecommend> projectHistoryRecommendMap = projectHistoryRecommendList.stream().collect(Collectors.toMap(ProjectHistoryRecommend::getHotelId, Function.identity()));
                            addNoPoiHotAreaRecommendForLast12MonthRoomNight(new ArrayList<>(noPoiNearHotHotelIdSet), lastTwoYearDateStr, todayDateStr, projectHistoryRecommendMap);
                        }
                    }
                }
            }

            // 新增或者更新统计
            projectHistoryRecommendDao.resetRecommendNoPoiHotArea(project.getProjectId());
            if(!projectHistoryRecommendList.isEmpty()){
                for(ProjectHistoryRecommend projectHistoryRecommend : projectHistoryRecommendList){
                    int insertCount = projectHistoryRecommendDao.insertOrUpdate(projectHistoryRecommend);
                    if(insertCount == 1 && projectHistoryRecommend.getIsNoPoiHotAreaRecommend()== RfpConstant.constant_1) {
                        try {
                            RedisService.sadd(RedisConstant.AI_REVIEW_HOTEL_IDS, String.valueOf(projectHistoryRecommend.getHotelId()));
                        } catch (Exception e) {
                            logger.error("新增AI Review 异常 ", e);
                        }
                    }
                }
            }
            response.setResult(ResultEnum.SUCCESS.key);
            response.setMsg("非POI热订区域邀约推荐生成成功");
        } catch (Exception ex){
            logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            response.setResult(ResultEnum.FAILURE.key);
            response.setMsg("非POI热订区域邀约推荐异常");
        } finally {
            if(countDownLatch != null) {
                countDownLatch.countDown();
            }

            // 记录完成统计日志
            recordFinishedLog(projectRecommendStatLog, response);

            MDC.remove("traceId");
        }

        return new AsyncResult<>(response);
    }

    private void setQueryProjectTotalRecommendHotelResponseData(QueryProjectTotalRecommendHotelResponse queryProjectTotalRecommendHotelResponse){
        // 设置是否推荐
        queryProjectTotalRecommendHotelResponse.setIsRecommend(queryProjectTotalRecommendHotelResponse.getIsFrequencyRecommend() + queryProjectTotalRecommendHotelResponse.getIsSameLevelFrequencyRecommend() +
                queryProjectTotalRecommendHotelResponse.getIsPoiNearHotelRecommend() + queryProjectTotalRecommendHotelResponse.getIsNoPoiHotAreaRecommend() +
                queryProjectTotalRecommendHotelResponse.getIsAreaGatherRecommend() + queryProjectTotalRecommendHotelResponse.getIsHighQualityRecommend() +
                queryProjectTotalRecommendHotelResponse.getIsSavedHotelRecommend() > RfpConstant.constant_0 ? RfpConstant.constant_1 : RfpConstant.constant_0 );

        // 设置推荐原因
        List<String> recommendReasonList = new ArrayList<>();
        if(queryProjectTotalRecommendHotelResponse.getIsFrequencyRecommend() == RfpConstant.constant_1){
            List<Integer> frequencyRecommends = JsonUtil.jsonToList(queryProjectTotalRecommendHotelResponse.getFrequencyRecommends(), String.class).stream().map(Integer::parseInt).collect(Collectors.toList());
            for(Integer frequencyRecommend : frequencyRecommends){
                recommendReasonList.add(RecommendFrequencyEnum.getValueByKey(frequencyRecommend));
            }
        }
        if(queryProjectTotalRecommendHotelResponse.getIsSameLevelFrequencyRecommend() == RfpConstant.constant_1){
            List<Integer> sameLevelFreqRecommends = JsonUtil.jsonToList(queryProjectTotalRecommendHotelResponse.getSameLevelFreqRecommends(), String.class).stream().map(Integer::parseInt).collect(Collectors.toList());
            for(Integer sameLevelFreqRecommend : sameLevelFreqRecommends){
                recommendReasonList.add(RecommendFrequencySameLevelEnum.getValueByKey(sameLevelFreqRecommend));
            }
        }
        if(queryProjectTotalRecommendHotelResponse.getIsPoiNearHotelRecommend() == RfpConstant.constant_1){
            List<Integer> poiNearHotelRecommends = JsonUtil.jsonToList(queryProjectTotalRecommendHotelResponse.getPoiNearHotelRecommends(), String.class).stream().map(Integer::parseInt).collect(Collectors.toList());
            for(Integer poiNearHotelRecommendKey : poiNearHotelRecommends){
                recommendReasonList.add(RecommendPoiNearHotelEnum.getValueByKey(poiNearHotelRecommendKey));
            }
        }
        if(queryProjectTotalRecommendHotelResponse.getIsNoPoiHotAreaRecommend() == RfpConstant.constant_1){
            List<Integer> noPoiHotAreaRecommendKeys = JsonUtil.jsonToList(queryProjectTotalRecommendHotelResponse.getNoPoiHotAreaRecommends(), String.class).stream().map(Integer::parseInt).collect(Collectors.toList());
            for(Integer noPoiHotAreaRecommendKey : noPoiHotAreaRecommendKeys){
                recommendReasonList.add(RecommendNoPoiHotelAreaEnum.getValueByKey(noPoiHotAreaRecommendKey));
            }
        }
        if(queryProjectTotalRecommendHotelResponse.getIsAreaGatherRecommend() == RfpConstant.constant_1){
            List<Integer> areaGatherRecommendKeys = JsonUtil.jsonToList(queryProjectTotalRecommendHotelResponse.getAreaGatherRecommends(), String.class).stream().map(Integer::parseInt).collect(Collectors.toList());
            for(Integer areaGatherRecommendKey : areaGatherRecommendKeys){
                recommendReasonList.add(RecommendAreaGatherEnum.getValueByKey(areaGatherRecommendKey));
            }
        }
        // 设置推荐等级
        if(StringUtils.isNotEmpty(queryProjectTotalRecommendHotelResponse.getRecommendLevel())) {
            if (queryProjectTotalRecommendHotelResponse.getIsHighQualityRecommend() == RfpConstant.constant_1) {
                recommendReasonList.add(RecommendHighQualityEnum.HIGH_QUALITY.value + "推荐等级" + RecommendLevelEnum.getValueByKey(Integer.valueOf(queryProjectTotalRecommendHotelResponse.getRecommendLevel())));
            }
        }
        if(queryProjectTotalRecommendHotelResponse.getIsSavedHotelRecommend() == RfpConstant.constant_1){
            recommendReasonList.add(RecommendSavedAmountEnum.SAVED_AMOUNT.value);
        }
        queryProjectTotalRecommendHotelResponse.setRecommends(recommendReasonList);

        setQueryProjectRecommendHotelResponseData(queryProjectTotalRecommendHotelResponse);

    }


    private void setQueryProjectRecommendHotelResponseData(QueryProjectRecommendHotelResponse queryProjectRecommendHotelResponse){
        // 设置星级名称
        if(StringUtils.isNotEmpty(queryProjectRecommendHotelResponse.getHotelStar()) && NumberUtil.isNumber(queryProjectRecommendHotelResponse.getHotelStar())){
            queryProjectRecommendHotelResponse.setHotelStarName(HotelStarEnum.getValueByKey(Integer.valueOf(queryProjectRecommendHotelResponse.getHotelStar())));
        }
        // 设置报价状态
        queryProjectRecommendHotelResponse.setHotelBidStateName(getBidStateName(queryProjectRecommendHotelResponse.getHotelBidState()));

        // 设置装修日期
        if(queryProjectRecommendHotelResponse.getFitmentDate() != null){
            queryProjectRecommendHotelResponse.setFitmentDateStr(com.fangcang.util.DateUtil.dateToString(queryProjectRecommendHotelResponse.getFitmentDate(), "yyyy"));
        }
        // 设置开业日期
        if(queryProjectRecommendHotelResponse.getPraciceDate() != null){
            queryProjectRecommendHotelResponse.setPraciceDateStr(com.fangcang.util.DateUtil.dateToString(queryProjectRecommendHotelResponse.getPraciceDate(), "yyyy"));
        }

        //是否邀约 invitedString
        queryProjectRecommendHotelResponse.setInvitedString(queryProjectRecommendHotelResponse.getIsInvited() == RfpConstant.constant_1 ? "是" : "否");

        // 设置OTA价格区间
        if(queryProjectRecommendHotelResponse.getOtaMinPrice() != null && queryProjectRecommendHotelResponse.getOtaMaxPrice() != null){
            queryProjectRecommendHotelResponse.setOtaPriceRange(queryProjectRecommendHotelResponse.getOtaMinPrice() + "~" + queryProjectRecommendHotelResponse.getOtaMaxPrice());
        }

        // 设置均价
        BigDecimal avgPrice = new BigDecimal(0);
        if(queryProjectRecommendHotelResponse.getSalesRoomNight() != null && queryProjectRecommendHotelResponse.getSalesRoomNight() > 0){
            avgPrice = queryProjectRecommendHotelResponse.getTotalAmount().divide(new BigDecimal(String.valueOf(queryProjectRecommendHotelResponse.getSalesRoomNight())), 2, RoundingMode.HALF_UP);
        }
        queryProjectRecommendHotelResponse.setAvgPrice(avgPrice);

        // 设置酒店签约要求
        String recommendHotelRequired = "";
        if(queryProjectRecommendHotelResponse.getBreakfastNum() != null){
            queryProjectRecommendHotelResponse.setBreakfastNumStr(BreakfastNumEnum.getValueByKey(queryProjectRecommendHotelResponse.getBreakfastNum()));
            String lra = queryProjectRecommendHotelResponse.getLastRoomAvailable() == RfpConstant.constant_1 ? "LRA ":"NLRA";
            recommendHotelRequired = recommendHotelRequired + BreakfastNumEnum.getValueByKey(queryProjectRecommendHotelResponse.getBreakfastNum()) + " " +
                    queryProjectRecommendHotelResponse.getReferencePrice() + " " +
                    lra + " " +
                    "月均" + queryProjectRecommendHotelResponse.getRequiredRoomNight() + "间夜";
            queryProjectRecommendHotelResponse.setRecommendHotelRequired(recommendHotelRequired);
        }

        // 设置参考均价
        String lowestPriceStr = "";
        if(queryProjectRecommendHotelResponse.getLowestPrice() != null) {
            lowestPriceStr = queryProjectRecommendHotelResponse.getLowestPrice()  +"起";
            queryProjectRecommendHotelResponse.setLowestPriceStr(lowestPriceStr);
        }

        //是否推荐
        queryProjectRecommendHotelResponse.setRecommendString(queryProjectRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1 ? "是" : "否");

        // 设置推荐原因
        String recommendReason = "";
        if(CollectionUtils.isNotEmpty(queryProjectRecommendHotelResponse.getRecommends())){
            for(int i=1; i<=queryProjectRecommendHotelResponse.getRecommends().size(); i++){
                if(i == queryProjectRecommendHotelResponse.getRecommends().size()){
                    recommendReason += i + ":" + queryProjectRecommendHotelResponse.getRecommends().get(i-1) ;
                }else{
                    recommendReason += i + ":" + queryProjectRecommendHotelResponse.getRecommends().get(i-1)+ "\n";
                }
            }
        }
        queryProjectRecommendHotelResponse.setRecommendReason(recommendReason);

        // 设置POI保留2位小数
        if(queryProjectRecommendHotelResponse.getPoiDistance() != null) {
            queryProjectRecommendHotelResponse.setPoiDistance(queryProjectRecommendHotelResponse.getPoiDistance().setScale(2, RoundingMode.HALF_UP));
        }

        // 设置推荐等级text
        if(StringUtils.isNotEmpty(queryProjectRecommendHotelResponse.getRecommendLevel())){
            queryProjectRecommendHotelResponse.setRecommendLevelText(RecommendLevelEnum.getValueByKey(Integer.valueOf(queryProjectRecommendHotelResponse.getRecommendLevel())));
        }

    }
    @Override
    public Response queryProjectTotalRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest){
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryProjectTotalRecommendHotelResponse> projectTotalRecommendHotelList = projectHotelHistoryDataDao.queryProjectTotalRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectTotalRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryProjectTotalRecommendHotelResponse> pageList = (List<QueryProjectTotalRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryProjectTotalRecommendHotelResponse queryProjectTotalRecommendHotelResponse : pageList){
                hotelIds.add(queryProjectTotalRecommendHotelResponse.getHotelId());
                // 设置值
                setQueryProjectTotalRecommendHotelResponseData(queryProjectTotalRecommendHotelResponse);

            }
            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(hotelIds), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for (QueryProjectRecommendHotelResponse queryProjectRecommendHotelResponse : pageList) {
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryProjectRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryProjectRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }

        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public InputStream exportProjectRecommendHotel(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest, HttpServletResponse response) throws Exception {
        queryProjectRecommendHotelRequest.setPage(false);
        String excelFileName = BaseConfig.getAccessFtpFileUrl() + FileTypeAndPathEnum.EXPORT_EXCEL_TEMPLATE.filePath
                + "/export" + queryProjectRecommendHotelRequest.getRecommendName() + ".xlsx";
        Map data = new HashMap();
        if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.TOTAL_RECOMMEND.key)){
            List<QueryProjectTotalRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryProjectTotalRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryProjectTotalRecommendHotelResponse queryProjectTotalRecommendHotelResponse : projectRecommendHotelList){
                setQueryProjectTotalRecommendHotelResponseData(queryProjectTotalRecommendHotelResponse);
            }
            // 设置值
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_RECOMMEND.key)){
            List<QueryProjectFrequencyRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryProjectFrequencyRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryProjectFrequencyRecommendHotelResponse queryProjectFrequencyRecommendHotelResponse : projectRecommendHotelList){
                setQueryProjectFrequencyRecommendHotelData(queryProjectFrequencyRecommendHotelResponse);
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_SAME_LEVEL_RECOMMEND.key)){
            List<QueryProjectFrequencySameLevelRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryProjectFrequencySameLevelRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryProjectFrequencySameLevelRecommendHotelResponse queryProjectFrequencySameLevelRecommendHotelResponse : projectRecommendHotelList){
                setQueryProjectFrequencySameLevelRecommendHotelData(queryProjectFrequencySameLevelRecommendHotelResponse);
                RecommendFrequencySameLevelInfo recommendFrequencySameLevelInfo = queryProjectFrequencySameLevelRecommendHotelResponse.getRecommendFrequencySameLevelInfo();
                String sameLevelFreqHotelInfo = "附近#{hotelName},#{distance}KM,#{roomNightCount}间夜,均价￥#{avgPrice}";
                if(recommendFrequencySameLevelInfo != null && StringUtils.isNotEmpty(recommendFrequencySameLevelInfo.getHotelName())){
                    sameLevelFreqHotelInfo = sameLevelFreqHotelInfo.replace("#{distance}", formatDistance(recommendFrequencySameLevelInfo.getDistance().doubleValue()));
                    sameLevelFreqHotelInfo = sameLevelFreqHotelInfo.replace("#{hotelName}", String.valueOf(recommendFrequencySameLevelInfo.getHotelName()));
                    sameLevelFreqHotelInfo = sameLevelFreqHotelInfo.replace("#{roomNightCount}", String.valueOf(recommendFrequencySameLevelInfo.getRoomNightCount()));
                    sameLevelFreqHotelInfo = sameLevelFreqHotelInfo.replace("#{avgPrice}", String.valueOf(recommendFrequencySameLevelInfo.getAvgPrice()));
                    queryProjectFrequencySameLevelRecommendHotelResponse.setSameLevelFreqHotelInfo(sameLevelFreqHotelInfo);
                }
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.key)){
            List<QueryPoiNearHotelRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryPoiNearHotelRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryPoiNearHotelRecommendHotelResponse queryPoiNearHotelRecommendHotelResponse : projectRecommendHotelList){
                setQueryProjectPoiNearRecommendHotelData(queryPoiNearHotelRecommendHotelResponse, true);
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.NO_POI_HOT_AREA_RECOMMEND.key)){
            List<QueryNoPoiHotAreaRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryNoPoiHotAreaRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryNoPoiHotAreaRecommendHotelResponse queryNoPoiHotAreaRecommendHotelResponse : projectRecommendHotelList){
                setQueryNoPoiHotAreaRecommendHotelData(queryNoPoiHotAreaRecommendHotelResponse, true);
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.AREA_GATHER_RECOMMEND.key)){
            List<QueryAreaGatherRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryAreaGatherRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryAreaGatherRecommendHotelResponse queryAreaGatherRecommendHotelResponse : projectRecommendHotelList){
                //设置推荐原因
                setQueryProjectAreaGatherRecommendHotelData(queryAreaGatherRecommendHotelResponse);
                if(StringUtils.isNotEmpty(queryAreaGatherRecommendHotelResponse.getAreaGatherHotelInfo())){
                    RecommendAreaGatherHotelStatInfo recommendAreaGatherHotelStatInfo = JSON.parseObject(queryAreaGatherRecommendHotelResponse.getAreaGatherHotelInfo(), RecommendAreaGatherHotelStatInfo.class);
                    queryAreaGatherRecommendHotelResponse.setRecommendAreaGatherHotelStatInfo(recommendAreaGatherHotelStatInfo);
                    if(recommendAreaGatherHotelStatInfo != null){
                        queryAreaGatherRecommendHotelResponse.setTheSameLevelTotalRoomNight(recommendAreaGatherHotelStatInfo.getAreaTotalRoomNight());
                        if(recommendAreaGatherHotelStatInfo.getMinPrice() != null && recommendAreaGatherHotelStatInfo.getMaxPrice() != null){
                            queryAreaGatherRecommendHotelResponse.setPriceRange(recommendAreaGatherHotelStatInfo.getMinPrice().intValue() + "-" + recommendAreaGatherHotelStatInfo.getMaxPrice().intValue());
                        }
                        // 城市聚集
                        if(StringUtils.isEmpty(recommendAreaGatherHotelStatInfo.getName())) {
                            queryAreaGatherRecommendHotelResponse.setAreaGatherNameAndDistance(queryAreaGatherRecommendHotelResponse.getCityName() + recommendAreaGatherHotelStatInfo.getStarGroup() + "散布聚量");
                        }  else {
                            String areaGatherNameAndDistance = "#{name}及周边距离#{distance}KM";
                            areaGatherNameAndDistance = areaGatherNameAndDistance.replace("#{name}", String.valueOf(recommendAreaGatherHotelStatInfo.getName()));
                            areaGatherNameAndDistance = areaGatherNameAndDistance.replace("#{distance}", formatDistance(recommendAreaGatherHotelStatInfo.getDistance()));
                            queryAreaGatherRecommendHotelResponse.setAreaGatherNameAndDistance(areaGatherNameAndDistance);
                        }
                    }
                }
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.HIGH_QUALITY_RECOMMEND.key)){
            List<QueryHighQualityRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryHighQualityRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QueryHighQualityRecommendHotelResponse queryHighQualityRecommendHotelResponse : projectRecommendHotelList){
                //设置推荐原因
                setQueryProjectHighQualityRecommendHotelData(queryHighQualityRecommendHotelResponse);
            }
            data.put("reportList", projectRecommendHotelList);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.SAVED_HOTEL_RECOMMEND.key)){
            List<QuerySavedRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.querySavedRecommendHotelList(queryProjectRecommendHotelRequest);
            for(QuerySavedRecommendHotelResponse querySavedRecommendHotelResponse : projectRecommendHotelList){
                //设置推荐原因
                setQueryProjectSavedRecommendHotelData(querySavedRecommendHotelResponse);
            }
            data.put("reportList", projectRecommendHotelList);
        }

        return ExcelHelper.exportFromRemote(data, excelFileName);
    }

    private String formatDistance(Double distance){
        if(distance == null){
            return "";
        }
        return String.format("%.2f", distance);
    }
    @Override
    public Response queryProjectRecommendHotelStat(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        QueryProjectRecommendHotelStatResponse queryProjectRecommendHotelStatResponse = new QueryProjectRecommendHotelStatResponse();
        if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.TOTAL_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryProjectTotalRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryProjectFrequencyRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_SAME_LEVEL_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryProjectFrequencySameLevelRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryPoiNearHotelRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.NO_POI_HOT_AREA_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryNoPoiHotAreaRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.AREA_GATHER_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryAreaGatherRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.HIGH_QUALITY_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.queryHighQualityRecommendHotelStat(queryProjectRecommendHotelRequest);
        } else if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.SAVED_HOTEL_RECOMMEND.key)){
            queryProjectRecommendHotelStatResponse = projectHotelHistoryDataDao.querySavedHotelRecommendHotelStat(queryProjectRecommendHotelRequest);
        }

        response.setData(queryProjectRecommendHotelStatResponse);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    private String getBidStateName(Integer hotelBidState){
        String result = HotelBidStateEnum.NO_BID.value;
        if(hotelBidState != null){
            result = HotelBidStateEnum.getValueByKey(hotelBidState);
        }
        return result;
    }

    private void setQueryProjectFrequencyRecommendHotelData(QueryProjectFrequencyRecommendHotelResponse queryProjectFrequencyRecommendHotelResponse){
        //是否推荐
        List<String> recommendReasonList = new ArrayList<>();
        if(queryProjectFrequencyRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1){
            List<String>  jsonRecommendReasonList = JsonUtil.jsonToList(queryProjectFrequencyRecommendHotelResponse.getRecommendString(), String.class);
            for(String recommendReason : jsonRecommendReasonList){
                recommendReasonList.add(RecommendFrequencyEnum.getValueByKey(Integer.valueOf(recommendReason)));
            }
        }
        queryProjectFrequencyRecommendHotelResponse.setRecommends(recommendReasonList);

        setQueryProjectRecommendHotelResponseData(queryProjectFrequencyRecommendHotelResponse);
    }

    private void setQueryProjectAreaGatherRecommendHotelData(QueryAreaGatherRecommendHotelResponse queryAreaGatherRecommendHotelResponse){
        //是否推荐
        List<String> recommendReasonList = new ArrayList<>();
        if(queryAreaGatherRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1){
            List<String>  jsonRecommendReasonList = JsonUtil.jsonToList(queryAreaGatherRecommendHotelResponse.getRecommendString(), String.class);
            for(String recommendReason : jsonRecommendReasonList){
                recommendReasonList.add(RecommendAreaGatherEnum.getValueByKey(Integer.valueOf(recommendReason)));
            }
        }
        queryAreaGatherRecommendHotelResponse.setRecommends(recommendReasonList);

        setQueryProjectRecommendHotelResponseData(queryAreaGatherRecommendHotelResponse);
    }

    private void setQueryProjectHighQualityRecommendHotelData(QueryHighQualityRecommendHotelResponse queryHighQualityRecommendHotelResponse){
        //是否推荐
        if(queryHighQualityRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1) {
            List<String> recommendReasonList = new ArrayList<>();
            recommendReasonList.add(RecommendHighQualityEnum.HIGH_QUALITY.value + "推荐等级" + RecommendLevelEnum.getValueByKey(Integer.valueOf(queryHighQualityRecommendHotelResponse.getRecommendLevel())));
            queryHighQualityRecommendHotelResponse.setRecommends(recommendReasonList);
        }

        setQueryProjectRecommendHotelResponseData(queryHighQualityRecommendHotelResponse);
    }

    private void setQueryProjectSavedRecommendHotelData(QuerySavedRecommendHotelResponse querySavedRecommendHotelResponse){
        //是否推荐
        if(querySavedRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1) {
            List<String> recommendReasonList = new ArrayList<>();
            recommendReasonList.add(RecommendSavedAmountEnum.SAVED_AMOUNT.value);
            querySavedRecommendHotelResponse.setRecommends(recommendReasonList);
        }
        setQueryProjectRecommendHotelResponseData(querySavedRecommendHotelResponse);
    }

    @Override
    public Response queryProjectFrequencyRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryProjectFrequencyRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryProjectFrequencyRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryProjectFrequencyRecommendHotelResponse> pageList = (List<QueryProjectFrequencyRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryProjectFrequencyRecommendHotelResponse queryProjectFrequencyRecommendHotelResponse : pageList){
                hotelIds.add(queryProjectFrequencyRecommendHotelResponse.getHotelId());
                // 设置值
                setQueryProjectFrequencyRecommendHotelData(queryProjectFrequencyRecommendHotelResponse);
            }

            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QueryProjectFrequencyRecommendHotelResponse queryProjectFrequencyRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryProjectFrequencyRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryProjectFrequencyRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }
        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    private void setQueryProjectFrequencySameLevelRecommendHotelData(QueryProjectFrequencySameLevelRecommendHotelResponse queryProjectFrequencySameLevelRecommendHotelResponse){
        //设置推荐原因
        List<String> recommendReasonList = new ArrayList<>();
        if(queryProjectFrequencySameLevelRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1){
            List<String> jsonRecommendReasonList = JsonUtil.jsonToList(queryProjectFrequencySameLevelRecommendHotelResponse.getRecommendString(), String.class);
            for(String recommendReason : jsonRecommendReasonList){
                recommendReasonList.add(RecommendFrequencySameLevelEnum.getValueByKey(Integer.valueOf(recommendReason)));
            }
        }
        queryProjectFrequencySameLevelRecommendHotelResponse.setRecommends(recommendReasonList);

        // 设置同档高频酒店信息
        if(StringUtils.isNotEmpty(queryProjectFrequencySameLevelRecommendHotelResponse.getSameLevelFreqHotelInfo())) {

            queryProjectFrequencySameLevelRecommendHotelResponse.setRecommendFrequencySameLevelInfo(JSON.parseObject(queryProjectFrequencySameLevelRecommendHotelResponse.getSameLevelFreqHotelInfo(), RecommendFrequencySameLevelInfo.class));
        }
        setQueryProjectRecommendHotelResponseData(queryProjectFrequencySameLevelRecommendHotelResponse);
    }

    @Override
    public Response queryProjectFrequencySameLevelRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryProjectFrequencySameLevelRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryProjectFrequencySameLevelRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryProjectFrequencySameLevelRecommendHotelResponse> pageList = (List<QueryProjectFrequencySameLevelRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryProjectFrequencySameLevelRecommendHotelResponse queryProjectFrequencySameLevelRecommendHotelResponse : pageList){
                hotelIds.add(queryProjectFrequencySameLevelRecommendHotelResponse.getHotelId());
                setQueryProjectFrequencySameLevelRecommendHotelData(queryProjectFrequencySameLevelRecommendHotelResponse);
            }

            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    logger.info("图片查询结果：" + JSON.toJSONString(imageResponseList));
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, o->o, (o1,o2)->o1));
                    for(QueryProjectFrequencySameLevelRecommendHotelResponse queryProjectFrequencySameLevelRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryProjectFrequencySameLevelRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryProjectFrequencySameLevelRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }
        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    private void setQueryProjectPoiNearRecommendHotelData(QueryPoiNearHotelRecommendHotelResponse queryPoiNearHotelRecommendHotelResponse, boolean isExport){
        //设置推荐原因
        List<String> recommendReasonList = new ArrayList<>();
        if(queryPoiNearHotelRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1){
            List<String> jsonRecommendReasonList = JsonUtil.jsonToList(queryPoiNearHotelRecommendHotelResponse.getRecommendString(), String.class);
            for(String recommendReason : jsonRecommendReasonList){
                recommendReasonList.add(RecommendPoiNearHotelEnum.getValueByKey(Integer.valueOf(recommendReason)));
            }
        }
        queryPoiNearHotelRecommendHotelResponse.setRecommends(recommendReasonList);

        // 设置POI统计信息
        if(StringUtils.isNotEmpty(queryPoiNearHotelRecommendHotelResponse.getPoiHotelStat3Km())) {
            RecommendNearPoiStatInfo recommendNearPoiStat = JSON.parseObject(queryPoiNearHotelRecommendHotelResponse.getPoiHotelStat3Km(), RecommendNearPoiStatInfo.class);
            queryPoiNearHotelRecommendHotelResponse.setRecommendNearPoiStat(recommendNearPoiStat);
            Map<String, String> poiHotelStat1Map = new HashMap<>();
            poiHotelStat1Map.put("500以上", recommendNearPoiStat.getUp500Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("400-500", recommendNearPoiStat.get_400_500Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("300-400", recommendNearPoiStat.get_300_400Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("200-300", recommendNearPoiStat.get_200_300Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("200以下", recommendNearPoiStat.getDown200Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            queryPoiNearHotelRecommendHotelResponse.setPoiHotelStat1(JSON.toJSONString(poiHotelStat1Map));
            if(isExport){
                String poiHotelStat1 = "500以上:up500Percentage%, 400-500:400_500Percentage%, 300-400:300_400Percentage%, 200-300:200_300Percentage%, 200以下:Down200Percentage%";
                poiHotelStat1 = poiHotelStat1.replace("up500Percentage", String.valueOf(recommendNearPoiStat.getUp500Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("400_500Percentage", String.valueOf(recommendNearPoiStat.get_400_500Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("300_400Percentage", String.valueOf(recommendNearPoiStat.get_300_400Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("200_300Percentage", String.valueOf(recommendNearPoiStat.get_200_300Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("Down200Percentage", String.valueOf(recommendNearPoiStat.getDown200Percentage().setScale(1, RoundingMode.HALF_UP)));
                queryPoiNearHotelRecommendHotelResponse.setPoiHotelStat1(poiHotelStat1);
            }

            Map<String, String> poiHotelStat2Map = new HashMap<>();
            poiHotelStat1Map.put("五星", recommendNearPoiStat.getFiveStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("豪华型", recommendNearPoiStat.getQuisaFiveStarPercentage().setScale(1, RoundingMode.HALF_UP)+ "%");
            poiHotelStat1Map.put("四星/高档型", recommendNearPoiStat.getFourAndQuisaFourStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("三星/舒适型", recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("经济型", recommendNearPoiStat.getQuisaTwoStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("二星及以下/公寓", recommendNearPoiStat.getTwoAndDownStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            queryPoiNearHotelRecommendHotelResponse.setPoiHotelStat2(JSON.toJSONString(poiHotelStat2Map));
            if(isExport){
                String poiHotelStat2 = "五星:FivePercentage%, 豪华型:QuisaFiveStarPercentage%, 四星/高档型:FourAndQuisaFourStarPercentage%, 三星/舒适型:ThreeAndQuisaThreeStarPercentage%, 经济型:QuisaTwoStarPercentage%，二星及以下/公寓:TwoAndDownStarPercentage%";
                poiHotelStat2 = poiHotelStat2.replace("FivePercentage", String.valueOf(recommendNearPoiStat.getFiveStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("QuisaFiveStarPercentage", String.valueOf(recommendNearPoiStat.getQuisaFiveStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("FourAndQuisaFourStarPercentage", String.valueOf(recommendNearPoiStat.getFourAndQuisaFourStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("ThreeAndQuisaThreeStarPercentage", String.valueOf(recommendNearPoiStat.getThreeAndQuisaThreeStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("QuisaTwoStarPercentage", String.valueOf(recommendNearPoiStat.getQuisaTwoStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("TwoAndDownStarPercentage", String.valueOf(recommendNearPoiStat.getTwoAndDownStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                queryPoiNearHotelRecommendHotelResponse.setPoiHotelStat2(poiHotelStat2);
            }
        }
        setQueryProjectRecommendHotelResponseData(queryPoiNearHotelRecommendHotelResponse);
    }

    private void setQueryNoPoiHotAreaRecommendHotelData(QueryNoPoiHotAreaRecommendHotelResponse queryNoPoiHotAreaRecommendHotelResponse, boolean isExport){
        //设置推荐原因
        List<String> recommendReasonList = new ArrayList<>();
        if(queryNoPoiHotAreaRecommendHotelResponse.getIsRecommend() == RfpConstant.constant_1){
            List<String> jsonRecommendReasonList = JsonUtil.jsonToList(queryNoPoiHotAreaRecommendHotelResponse.getRecommendString(), String.class);
            for(String recommendReason : jsonRecommendReasonList){
                recommendReasonList.add(RecommendNoPoiHotelAreaEnum.getValueByKey(Integer.valueOf(recommendReason)));
            }
        }
        queryNoPoiHotAreaRecommendHotelResponse.setRecommends(recommendReasonList);

        // 设置POI统计信息
        if(StringUtils.isNotEmpty(queryNoPoiHotAreaRecommendHotelResponse.getNoPoiHotelInfo())) {
            RecommendNoPoiHotelStatInfo recommendStat = JSON.parseObject(queryNoPoiHotAreaRecommendHotelResponse.getNoPoiHotelInfo(), RecommendNoPoiHotelStatInfo.class);
            queryNoPoiHotAreaRecommendHotelResponse.setNoPoiHotelStatInfo(recommendStat);
            Map<String, String> poiHotelStat1Map = new HashMap<>();
            poiHotelStat1Map.put("500以上", recommendStat.getUp500Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("400-500", recommendStat.get_400_500Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("300-400", recommendStat.get_300_400Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("200-300", recommendStat.get_200_300Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("200以下", recommendStat.getDown200Percentage().setScale(1, RoundingMode.HALF_UP) + "%");
            queryNoPoiHotAreaRecommendHotelResponse.setPoiHotelStat1(JSON.toJSONString(poiHotelStat1Map));
            if(isExport){
                String poiHotelStat1 = "500以上:up500Percentage%, 400-500:400_500Percentage%, 300-400:300_400Percentage%, 200-300:200_300Percentage%, 200以下:Down200Percentage%";
                poiHotelStat1 = poiHotelStat1.replace("up500Percentage", String.valueOf(recommendStat.getUp500Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("400_500Percentage", String.valueOf(recommendStat.get_400_500Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("300_400Percentage", String.valueOf(recommendStat.get_300_400Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("200_300Percentage", String.valueOf(recommendStat.get_200_300Percentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat1 = poiHotelStat1.replace("Down200Percentage", String.valueOf(recommendStat.getDown200Percentage().setScale(1, RoundingMode.HALF_UP)));
                queryNoPoiHotAreaRecommendHotelResponse.setPoiHotelStat1(poiHotelStat1);
            }

            Map<String, String> poiHotelStat2Map = new HashMap<>();
            poiHotelStat1Map.put("五星", recommendStat.getFiveStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("豪华型", recommendStat.getQuisaFiveStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("四星/高档型", recommendStat.getFourAndQuisaFourStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("三星/舒适型", recommendStat.getThreeAndQuisaThreeStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("经济型", recommendStat.getQuisaTwoStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            poiHotelStat1Map.put("二星及以下/公寓", recommendStat.getTwoAndDownStarPercentage().setScale(1, RoundingMode.HALF_UP) + "%");
            queryNoPoiHotAreaRecommendHotelResponse.setPoiHotelStat2(JSON.toJSONString(poiHotelStat2Map));
            if(isExport){
                String poiHotelStat2 = "五星:FivePercentage%, 豪华型:QuisaFiveStarPercentage%, 四星/高档型:FourAndQuisaFourStarPercentage%, 三星/舒适型:ThreeAndQuisaThreeStarPercentage%, 经济型:QuisaTwoStarPercentage%，二星及以下/公寓:TwoAndDownStarPercentage%";
                poiHotelStat2 = poiHotelStat2.replace("FivePercentage", String.valueOf(recommendStat.getFiveStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("QuisaFiveStarPercentage", String.valueOf(recommendStat.getQuisaFiveStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("FourAndQuisaFourStarPercentage", String.valueOf(recommendStat.getFourAndQuisaFourStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("ThreeAndQuisaThreeStarPercentage", String.valueOf(recommendStat.getThreeAndQuisaThreeStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("QuisaTwoStarPercentage", String.valueOf(recommendStat.getQuisaTwoStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                poiHotelStat2 = poiHotelStat2.replace("TwoAndDownStarPercentage", String.valueOf(recommendStat.getTwoAndDownStarPercentage().setScale(1, RoundingMode.HALF_UP)));
                queryNoPoiHotAreaRecommendHotelResponse.setPoiHotelStat2(poiHotelStat2);
            }
            if(isExport) {
                String noPoiHotelStatInfoStr = "#{name}及周边距离#{distance}KM";
                noPoiHotelStatInfoStr = noPoiHotelStatInfoStr.replace("#{name}", String.valueOf(recommendStat.getHotelName()));
                noPoiHotelStatInfoStr = noPoiHotelStatInfoStr.replace("#{distance}",formatDistance(recommendStat.getDistance()));
                queryNoPoiHotAreaRecommendHotelResponse.setNoPoiHotelStatInfoStr(noPoiHotelStatInfoStr);
                queryNoPoiHotAreaRecommendHotelResponse.setTotalNightRoomCount(recommendStat.getAreaTotalRoomNight());
            }
        }

        setQueryProjectRecommendHotelResponseData(queryNoPoiHotAreaRecommendHotelResponse);
    }

    @Override
    public Response queryPoiNearHotelRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryPoiNearHotelRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryPoiNearHotelRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryPoiNearHotelRecommendHotelResponse> pageList = (List<QueryPoiNearHotelRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryPoiNearHotelRecommendHotelResponse queryPoiNearHotelRecommendHotelResponse : pageList){
                hotelIds.add(queryPoiNearHotelRecommendHotelResponse.getHotelId());
                setQueryProjectPoiNearRecommendHotelData(queryPoiNearHotelRecommendHotelResponse, false);
            }
            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QueryPoiNearHotelRecommendHotelResponse queryPoiNearHotelRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryPoiNearHotelRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryPoiNearHotelRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }

        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryNoPoiHotelAreaRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryNoPoiHotAreaRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryNoPoiHotAreaRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryNoPoiHotAreaRecommendHotelResponse> pageList = (List<QueryNoPoiHotAreaRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryNoPoiHotAreaRecommendHotelResponse queryNoPoiHotAreaRecommendHotelResponse : pageList){
                hotelIds.add(queryNoPoiHotAreaRecommendHotelResponse.getHotelId());
                setQueryNoPoiHotAreaRecommendHotelData(queryNoPoiHotAreaRecommendHotelResponse, false);
            }
            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QueryNoPoiHotAreaRecommendHotelResponse queryNoPoiHotAreaRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryNoPoiHotAreaRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryNoPoiHotAreaRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }

        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryHighQualityRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryHighQualityRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryHighQualityRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryHighQualityRecommendHotelResponse> pageList = (List<QueryHighQualityRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryHighQualityRecommendHotelResponse queryHighQualityRecommendHotelResponse : pageList){
                hotelIds.add(queryHighQualityRecommendHotelResponse.getHotelId());
                // 设置值
                setQueryProjectHighQualityRecommendHotelData(queryHighQualityRecommendHotelResponse);
            }

            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QueryHighQualityRecommendHotelResponse queryHighQualityRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryHighQualityRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryHighQualityRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }
        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response querySavedRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QuerySavedRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.querySavedRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QuerySavedRecommendHotelResponse> pageList = (List<QuerySavedRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QuerySavedRecommendHotelResponse querySavedRecommendHotelResponse : pageList){
                hotelIds.add(querySavedRecommendHotelResponse.getHotelId());
                // 设置值
                setQueryProjectSavedRecommendHotelData(querySavedRecommendHotelResponse);
            }

            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QuerySavedRecommendHotelResponse querySavedRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(querySavedRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            querySavedRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }
        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryAreaGatherRecommendHotelPage(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        PageHelper.startPage(queryProjectRecommendHotelRequest.getCurrentPage(), queryProjectRecommendHotelRequest.getPageSize());
        List<QueryAreaGatherRecommendHotelResponse> projectRecommendHotelList = projectHotelHistoryDataDao.queryAreaGatherRecommendHotelList(queryProjectRecommendHotelRequest);
        PageResult pageResult = PageUtil.makePageResult(projectRecommendHotelList);
        if(CollectionUtils.isNotEmpty(pageResult.getList())) {
            List<QueryAreaGatherRecommendHotelResponse> pageList = (List<QueryAreaGatherRecommendHotelResponse>) pageResult.getList();
            List<Long> hotelIds = new ArrayList<>();
            for(QueryAreaGatherRecommendHotelResponse queryAreaGatherRecommendHotelResponse : pageList){
                hotelIds.add(queryAreaGatherRecommendHotelResponse.getHotelId());
                // 设置值
                setQueryProjectAreaGatherRecommendHotelData(queryAreaGatherRecommendHotelResponse);
                // 设置聚集信息
                if(StringUtils.isNotEmpty(queryAreaGatherRecommendHotelResponse.getAreaGatherHotelInfo())) {
                    RecommendAreaGatherHotelStatInfo recommendAreaGatherHotelStatInfo = JSON.parseObject(queryAreaGatherRecommendHotelResponse.getAreaGatherHotelInfo(), RecommendAreaGatherHotelStatInfo.class);
                    queryAreaGatherRecommendHotelResponse.setRecommendAreaGatherHotelStatInfo(recommendAreaGatherHotelStatInfo);
                }
            }

            // 设置酒店图片
            if(CollectionUtils.isNotEmpty(hotelIds)) {
                Response imageResponse = null;
                try {
                    imageResponse = recommendHotelService.queryHotelImage(hotelIds);
                } catch (Exception e) {
                    logger.error("查询图片异常，请求参数：" + JSON.toJSONString(queryProjectRecommendHotelRequest), e);
                }
                if (imageResponse != null && imageResponse.getData() != null) {
                    List<ImageResponse> imageResponseList = (List<ImageResponse>) imageResponse.getData();
                    Map<Long, ImageResponse> hotelImageMap = imageResponseList.stream().collect(Collectors.toMap(ImageResponse::getHotelId, Function.identity()));
                    for(QueryAreaGatherRecommendHotelResponse queryAreaGatherRecommendHotelResponse : pageList){
                        ImageResponse imageResponseInfo = hotelImageMap.get(queryAreaGatherRecommendHotelResponse.getHotelId());
                        if(imageResponseInfo != null) {
                            queryAreaGatherRecommendHotelResponse.setHotelImageUrl(imageResponseInfo.getImageUrl());
                        }
                    }
                }
            }
        }
        response.setData(pageResult);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryLowestPriceItemInfo(Long projectId, Long hotelId) {
        Response response = new Response();
        String lowestPriceItemInfo  = projectHotelHistoryDataDao.queryLowestPriceItemInfo(projectId, hotelId);
        List<HotelPriceItem> hotelPriceItemList = new ArrayList<>();
        if(StringUtils.isNotEmpty(lowestPriceItemInfo)){
            hotelPriceItemList = JsonUtil.jsonToList(lowestPriceItemInfo, HotelPriceItem.class);
        }
        response.setData(hotelPriceItemList);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryHotelOtaMinPriceList(Long projectId, Long hotelId) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        response.setData(new ArrayList<>());
        // Query Date
        List<QueryHistoryProjectInfoResponse> queryHistoryProjectInfoResponseList = projectHotelHistoryDataDao.queryHistoryProjectHotelList(projectId, hotelId, null, null);
        if(CollectionUtils.isEmpty(queryHistoryProjectInfoResponseList)){
            return response;
        }
        QueryHistoryProjectInfoResponse queryHistoryProjectInfoResponse = queryHistoryProjectInfoResponseList.get(0);
        if(queryHistoryProjectInfoResponse.getMinMaxOtaPriceDate() == null){
            return response;
        }

        // Query min price list
        List<HotelOtaDailyPriceResponse> data = new ArrayList<>();
        Date salesDateFrom = DateUtil.offset(queryHistoryProjectInfoResponse.getMinMaxOtaPriceDate(), DateField.YEAR, -1);
        Date salesDateTo= queryHistoryProjectInfoResponse.getMinMaxOtaPriceDate();

        List<OtaHotelDailyMinPriceResponse> minPriceResponseList = otaHotelDailyMinPriceDao.queryList(hotelId, com.fangcang.util.DateUtil.dateToString(salesDateFrom),
                com.fangcang.util.DateUtil.dateToString(salesDateTo));
        Map<String, OtaHotelDailyMinPriceResponse> minPriceMap = minPriceResponseList.stream().collect(Collectors.toMap(o -> com.fangcang.util.DateUtil.dateToString(o.getSalesDate()), Function.identity()));
        for(int i=0; i<365; i++){
            String dateStr = com.fangcang.util.DateUtil.dateToString(DateUtil.offsetDay(salesDateFrom, i));
            OtaHotelDailyMinPriceResponse otaHotelDailyMinPriceResponse = minPriceMap.get(dateStr);
            HotelOtaDailyPriceResponse hotelOtaDailyPriceResponse = new HotelOtaDailyPriceResponse();
            hotelOtaDailyPriceResponse.setDate(dateStr);
            if(otaHotelDailyMinPriceResponse != null){
                hotelOtaDailyPriceResponse.setPrice(otaHotelDailyMinPriceResponse.getMinPrice());
            }
            data.add(hotelOtaDailyPriceResponse);
        }
        response.setData(data);
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        return response;
    }

    @Override
    public Response queryProjectRecommendNeedInvitedHotelCount(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest) {
        Response response = new Response();
        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);
        int count = 0;
        if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.TOTAL_RECOMMEND.key)) {
            count = projectHotelHistoryDataDao.queryTotalRecommendNeedInvitedHotelCount(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_RECOMMEND.key)) {
            count = projectHotelHistoryDataDao.queryFrequencyRecommendNeedInvitedHotelCount(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_SAME_LEVEL_RECOMMEND.key)) {
            count = projectHotelHistoryDataDao.queryFrequencySameLevelRecommendNeedInvitedHotelCount(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.key)) {
            count = projectHotelHistoryDataDao.queryPoiNearRecommendNeedInvitedHotelCount(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.NO_POI_HOT_AREA_RECOMMEND.key)) {
            count = projectHotelHistoryDataDao.queryNoPoiHotAreaRecommendInvitedHotelCount(queryProjectRecommendHotelRequest);
        }
        response.setData(count);
        return response;
    }

    @Override
    public Response addProjectAllRecommendNeedInvitedHotel(QueryProjectRecommendHotelRequest queryProjectRecommendHotelRequest, UserDTO userDTO) {
        List<Long> hotelIdList = new ArrayList<>();
        if(queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.TOTAL_RECOMMEND.key)) {
            hotelIdList = projectHotelHistoryDataDao.queryTotalRecommendNeedInvitedHotelIdList(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_RECOMMEND.key)) {
            hotelIdList = projectHotelHistoryDataDao.queryFrequencyRecommendNeedInvitedHotelIdList(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.FREQUENCY_SAME_LEVEL_RECOMMEND.key)) {
            hotelIdList = projectHotelHistoryDataDao.queryFrequencySameLevelRecommendNeedInvitedHotelIdList(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.POI_NEAR_HOTEL_RECOMMEND.key)) {
            hotelIdList = projectHotelHistoryDataDao.queryPoiNearRecommendNeedInvitedHotelIdList(queryProjectRecommendHotelRequest);
        } else if (queryProjectRecommendHotelRequest.getRecommendName().equals(RecommendHotelNameEnum.NO_POI_HOT_AREA_RECOMMEND.key)) {
            hotelIdList = projectHotelHistoryDataDao.queryNoPoiHotAreaRecommendNeedInvitedHotelIdList(queryProjectRecommendHotelRequest);
        }
        AddProjectIntentHotelDto addProjectIntentHotelDto = new AddProjectIntentHotelDto();
        addProjectIntentHotelDto.setProjectId(queryProjectRecommendHotelRequest.getProjectId());
        addProjectIntentHotelDto.setHotelIds(hotelIdList);
        addProjectIntentHotelDto.setUserDTO(userDTO);
        addProjectIntentHotelDto.setOperator(userDTO.getOperator());
        addProjectIntentHotelDto.setUserDTO(userDTO);

        return projectService.addProjectIntentHotel(addProjectIntentHotelDto);
    }


    private void addRecommendForLast12MonthRoomNight(List<Long> hotelIdList, String lastYearDateStr, String todayDateStr, Map<Long, ProjectHistoryRecommend> theSameLevelRecommendMap){
        // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
        Lists.partition(hotelIdList, 1000).forEach(subHotelIds -> {
            QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
            queryRoomNightsDto.setHotelIds(hotelIdList);
            queryRoomNightsDto.setStartTime(lastYearDateStr);
            queryRoomNightsDto.setEndTime(todayDateStr);
            List<HotelRoomNightCountDto> hotelRoomNightCountDtoList = disHotelDailyOrderDao.selectHotelRoomNights(queryRoomNightsDto);
            for(HotelRoomNightCountDto hotelRoomNightCountDto : hotelRoomNightCountDtoList){
                if (hotelRoomNightCountDto.getRoomNightCount() != null && hotelRoomNightCountDto.getRoomNightCount() >= 600) {
                    ProjectHistoryRecommend projectHistoryRecommend = theSameLevelRecommendMap.get(hotelRoomNightCountDto.getHotelId());
                    if(projectHistoryRecommend != null && projectHistoryRecommend.getIsSameLevelRreqRecommend() == RfpConstant.constant_1){ //IsSameLevelRreqRecommend
                        List<Integer> recommends = JSONUtils.toList(projectHistoryRecommend.getSameLevelRreqRecommends(), Integer.class);
                        recommends.add(RecommendFrequencySameLevelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setSameLevelRreqRecommends(JSON.toJSONString(recommends));
                    } else if(projectHistoryRecommend != null && projectHistoryRecommend.getIsSameLevelRreqRecommend() == RfpConstant.constant_0) {
                        projectHistoryRecommend.setIsSameLevelRreqRecommend(RfpConstant.constant_1);
                        List<Integer> recommends = new ArrayList<>();
                        recommends.add(RecommendFrequencySameLevelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setSameLevelRreqRecommends(JSON.toJSONString(recommends));
                    }
                }
            }
        });
    }

    private void addPoiNearRecommendForLast12MonthRoomNight(List<Long> hotelIdList, String lastYearDateStr, String todayDateStr, Map<Long, ProjectHistoryRecommend> projectHistoryRecommendMap){
        // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
        Lists.partition(hotelIdList, 1000).forEach(subHotelIds -> {
            QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
            queryRoomNightsDto.setHotelIds(hotelIdList);
            queryRoomNightsDto.setStartTime(lastYearDateStr);
            queryRoomNightsDto.setEndTime(todayDateStr);
            List<HotelRoomNightCountDto> hotelRoomNightCountDtoList = disHotelDailyOrderDao.selectHotelRoomNights(queryRoomNightsDto);
            for(HotelRoomNightCountDto hotelRoomNightCountDto : hotelRoomNightCountDtoList){
                if (hotelRoomNightCountDto.getRoomNightCount() != null && hotelRoomNightCountDto.getRoomNightCount() >= 600) {
                    ProjectHistoryRecommend projectHistoryRecommend = projectHistoryRecommendMap.get(hotelRoomNightCountDto.getHotelId());
                    if(projectHistoryRecommend != null && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_1){ //IsPoiNearHotelRecommend
                        List<Integer> recommends = JSONUtils.toList(projectHistoryRecommend.getPoiNearHotelRecommends(), Integer.class);
                        recommends.add(RecommendPoiNearHotelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setPoiNearHotelRecommends(JSON.toJSONString(recommends));
                    } else if(projectHistoryRecommend != null && projectHistoryRecommend.getIsPoiNearHotelRecommend() == RfpConstant.constant_0) {
                        projectHistoryRecommend.setIsPoiNearHotelRecommend(RfpConstant.constant_1);
                        List<Integer> recommends = new ArrayList<>();
                        recommends.add(RecommendPoiNearHotelEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setPoiNearHotelRecommends(JSON.toJSONString(recommends));
                    }
                }
            }
        });
    }

    private void addNoPoiHotAreaRecommendForLast12MonthRoomNight(List<Long> hotelIdList, String lastYearDateStr, String todayDateStr, Map<Long, ProjectHistoryRecommend> projectHistoryRecommendMap){
        // 一个高产酒店周边同档（同星级分组）的酒店，商旅近12个月产量超过600间夜推荐邀约（不限客户，看预订监控该酒店所有分销商总和间夜数）。
        Lists.partition(hotelIdList, 1000).forEach(subHotelIds -> {
            QueryRoomNightsDto queryRoomNightsDto = new QueryRoomNightsDto();
            queryRoomNightsDto.setHotelIds(hotelIdList);
            queryRoomNightsDto.setStartTime(lastYearDateStr);
            queryRoomNightsDto.setEndTime(todayDateStr);
            List<HotelRoomNightCountDto> hotelRoomNightCountDtoList = disHotelDailyOrderDao.selectHotelRoomNights(queryRoomNightsDto);
            for(HotelRoomNightCountDto hotelRoomNightCountDto : hotelRoomNightCountDtoList){
                if (hotelRoomNightCountDto.getRoomNightCount() != null && hotelRoomNightCountDto.getRoomNightCount() >= 600) {
                    ProjectHistoryRecommend projectHistoryRecommend = projectHistoryRecommendMap.get(hotelRoomNightCountDto.getHotelId());
                    if(projectHistoryRecommend != null && projectHistoryRecommend.getIsNoPoiHotAreaRecommend() == RfpConstant.constant_1){ //IsPoiNearHotelRecommend
                        List<Integer> recommends = JSONUtils.toList(projectHistoryRecommend.getNoPoiHotAreaRecommends(), Integer.class);
                        recommends.add(RecommendNoPoiHotelAreaEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setNoPoiHotAreaRecommends(JSON.toJSONString(recommends));
                    } else if(projectHistoryRecommend != null && projectHistoryRecommend.getIsNoPoiHotAreaRecommend() == RfpConstant.constant_0) {
                        projectHistoryRecommend.setIsNoPoiHotAreaRecommend(RfpConstant.constant_1);
                        List<Integer> recommends = new ArrayList<>();
                        recommends.add(RecommendNoPoiHotelAreaEnum.LAST_YEAR_ROOM_NIGHT_600.key);
                        projectHistoryRecommend.setNoPoiHotAreaRecommends(JSON.toJSONString(recommends));
                    }
                }
            }
        });
    }
    private BigDecimal queryMaxSavedAmountRate(Long hotelId, String startTime, String endTime){
        DisHotelDailyOrderRequest disHotelDailyOrderRequest = new DisHotelDailyOrderRequest();
        disHotelDailyOrderRequest.setHotelId(hotelId);
        disHotelDailyOrderRequest.setStartTime(startTime);
        disHotelDailyOrderRequest.setEndTime(endTime);
        List<HotelSavedAmountDto> hotelSavedAmountDtoList = disHotelDailyOrderDao.selectHotelSavedAmount(disHotelDailyOrderRequest)
                .stream().filter(o -> o.getSavedAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        BigDecimal maxSaveAmountRate = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(hotelSavedAmountDtoList)) {
            for (HotelSavedAmountDto hotelSavedAmountDto : hotelSavedAmountDtoList) {
                BigDecimal savedAmountRate = hotelSavedAmountDto.getSavedAmount().divide(hotelSavedAmountDto.getTotalOtaPrice(), 2, RoundingMode.HALF_UP);
                if (maxSaveAmountRate.compareTo(BigDecimal.ZERO) == 0 || savedAmountRate.compareTo(maxSaveAmountRate) > 0) {
                    maxSaveAmountRate = savedAmountRate;
                }
            }
        }
        return maxSaveAmountRate;
    }

    /**
     * 生成混合价格
     */
    private BigDecimal generateAdjustLowestPrice(BigDecimal lowestPrice){
        BigDecimal adjustLowestPrice = lowestPrice;
        if(lowestPrice.compareTo(new BigDecimal("500")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("50"));
        } else if(lowestPrice.compareTo(new BigDecimal("400")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("30"));
        } else if(lowestPrice.compareTo(new BigDecimal("300")) > 0){
            adjustLowestPrice = adjustLowestPrice.add(new BigDecimal("20"));
        }
        return adjustLowestPrice;
    }

    // 是否完成统计
    private boolean isStatTaskFinished(Long projectId, String statReferenceNo, String statName, int loopCount){
        boolean isFinishedSucceeded = false;
        ProjectRecommendStatLog updateLowestPriceProjectRecommendStatLog = null;
        for(int i = 0; i < loopCount; i++){
            updateLowestPriceProjectRecommendStatLog = projectRecommendStatLogDao.getProjectRecommendStatLog(projectId, statReferenceNo, statName);
            if(updateLowestPriceProjectRecommendStatLog == null){ // 为空 默认通过
                isFinishedSucceeded = true;
                break;
            }
            if(updateLowestPriceProjectRecommendStatLog.getIsFinished() == RfpConstant.constant_1 &&
                    updateLowestPriceProjectRecommendStatLog.getResult() == ResultEnum.SUCCESS.key){
                isFinishedSucceeded = true;
                break;
            }
            try {
                Thread.sleep(5000L);
            } catch (Exception ex){
                logger.error(ExceptionUtility.getDetailedExceptionString(ex));
            }
        }
        if(!isFinishedSucceeded) {
            logger.info(statName + "失败，不能生成推荐 {}", JSON.toJSONString(updateLowestPriceProjectRecommendStatLog));
        }
        return isFinishedSucceeded;
    }

    private Map<String, List<QueryHistoryProjectInfoResponse>> convertToHotelStarGroupMap(List<QueryHistoryProjectInfoResponse> historyProjectInfoList){
        Map<String, List<QueryHistoryProjectInfoResponse>> resultMap = new HashMap<>();
        Map<String, List<QueryHistoryProjectInfoResponse>> cityHotelStarGroupMap = historyProjectInfoList.stream().filter(o -> StringUtils.isNotEmpty(o.getHotelStar())).collect(Collectors.groupingBy(QueryHistoryProjectInfoResponse::getHotelStar));
        historyProjectInfoList.forEach(item -> {
            if(StringUtils.isEmpty(item.getHotelStar())){
                logger.error("存在空星级酒店 {}", JSON.toJSONString(item));
            }
        });
        for(String hotelStar : cityHotelStarGroupMap.keySet()){
            HotelStarGroupEnum hotelStarGroupEnum = HotelStarGroupEnum.getEnumByHotelStar(hotelStar);
            if(hotelStarGroupEnum == null){
                logger.error("酒店星级分组为空 {}", hotelStar);
                continue;
            }
            if(resultMap.containsKey(hotelStarGroupEnum.name())){
                resultMap.get(hotelStarGroupEnum.name()).addAll(cityHotelStarGroupMap.get(hotelStar));
            } else {
                resultMap.put(hotelStarGroupEnum.name(), cityHotelStarGroupMap.get(hotelStar));
            }
        }

        return resultMap;
    }

    // 是否是同价位
    // 签约参考价  or   混淆价格 去找 历史交易数据里面所有酒店的  成交均价   进行匹配，在上面这个区间范围内的就是他的同价位
    private boolean isTheSameLevelPrice(BigDecimal basePrice,  QueryHistoryProjectInfoResponse historyProjectInfo){
        int avgPrice = historyProjectInfo.getTotalAmount().divide(BigDecimal.valueOf(historyProjectInfo.getRoomNightCount()), 2, RoundingMode.HALF_UP).intValue();
        // 记录日志
        if(Objects.equals(historyProjectInfo.getHotelId(), recordLogHotelId)){
            logger.info("avgPrice {} distanceHistoryProjectInfoResponse hotelId: {}", avgPrice, historyProjectInfo.getHotelId());
        }
        if (basePrice.subtract(new BigDecimal(avgPrice)).abs().intValue() <= 50) {
           return true;
        }
        return false;
    }
}
