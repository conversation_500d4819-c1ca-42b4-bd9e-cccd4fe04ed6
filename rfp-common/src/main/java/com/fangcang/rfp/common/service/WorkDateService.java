package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.file.FileResponse;
import com.fangcang.rfp.common.dto.request.DownloadFileToStreamRequest;
import com.fangcang.rfp.common.dto.request.QueryAttachmentInfoParam;
import com.fangcang.rfp.common.entity.Attachment;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2024/7/3 15:35
 */
public interface WorkDateService {

    /**
     * 获取工作日节假日
     * @return
     * @throws Exception
     */
    void getWorkDateFromApi(String jobParam);

    int getWorkDateFlag(String date);

}
