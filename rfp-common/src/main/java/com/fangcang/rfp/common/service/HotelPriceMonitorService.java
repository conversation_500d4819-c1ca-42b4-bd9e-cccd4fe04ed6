package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.entity.HotelPriceMonitor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/16 18:37
 */
public interface HotelPriceMonitorService {

    void insert(List<HotelPriceMonitor> monitors);

    void batchUpdate(HotelPriceMonitor minDateHotelPriceMonitor, List<HotelPriceMonitor> monitors);

    List<HotelPriceMonitor> selectHotelPriceMonitorList(HotelPriceMonitor hotelPriceMonitor);

    List<Date> selectHotelPriceMonitorViolationDay(HotelPriceMonitor hotelPriceMonitor);

    List<HotelPriceMonitor> selectHotelPriceLast15MonitorList(HotelPriceMonitor hotelPriceMonitor);

}
