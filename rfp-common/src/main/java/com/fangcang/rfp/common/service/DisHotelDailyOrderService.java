package com.fangcang.rfp.common.service;


import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.request.DisHotelDailyOrderRequest;
import com.fangcang.rfp.common.dto.request.QueryHotelViolationMonitorStatRequest;
import com.fangcang.rfp.common.dto.request.RankMonitorRequest;
import com.fangcang.rfp.common.dto.request.SelectHotelOrderMonitorStatRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

public interface DisHotelDailyOrderService {

    /**
     * 查询订单监控列表
     * @param disHotelDailyOrderRequest
     * @return
     */
    Response selectOrderMonitorList(DisHotelDailyOrderRequest disHotelDailyOrderRequest);

    /**
     * 查询订单监控详情
     * @param disHotelDailyOrderRequest
     * @return
     */
    Response selectOrderMonitorDetail(DisHotelDailyOrderRequest disHotelDailyOrderRequest);

    /**
     * 查询订单预订监控统计
     * @return
     */
    Response selectHotelOrderMonitorStatList(SelectHotelOrderMonitorStatRequest selectHotelOrderMonitorStatListRequest);

    /**
     * 查询订单预订监控统计详情
     * @return
     */
    Response selectHotelOrderMonitorStatDetail(SelectHotelOrderMonitorStatRequest selectHotelOrderMonitorStatListRequest);

    /**
     * 成交排行监控
     * @param rankMonitorRequest
     * @return
     * @throws Exception
     */
    Response transactionRankingMonitoring(RankMonitorRequest rankMonitorRequest) throws Exception;

    /**
     * 预定分布
     * @param rankMonitorRequest
     * @return
     * @throws Exception
     */
    Response predeterminedDistribution(RankMonitorRequest rankMonitorRequest) throws Exception;

    /**
     * 平均房价金额监控
     * @return
     * @throws Exception
     */
    Response averageAmount(DisHotelDailyOrderRequest disHotelDailyOrderRequest) throws Exception;

    /**
     * 节省榜或浪费榜
     * @return
     */
    Response selectSaveAndWateList(DisHotelDailyOrderRequest disHotelDailyOrderRequest);



    /**
     * 导出订单监控列表
     * @return
     */
    void exportOrderMonitorList(DisHotelDailyOrderRequest disHotelDailyOrderRequest, HttpServletResponse response) throws Exception;


    /**
     * 导出订单预订监控列表
     * @return
     */
    void exportOrderMonitorStatList(SelectHotelOrderMonitorStatRequest selectHotelOrderMonitorStatListRequest, HttpServletResponse response) throws Exception;



}
