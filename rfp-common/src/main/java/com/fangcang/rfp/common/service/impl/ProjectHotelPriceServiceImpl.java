package com.fangcang.rfp.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.fangcang.enums.BreakfastNumEnum;
import com.fangcang.enums.ResultEnum;
import com.fangcang.rfp.common.constants.RfpConstant;
import com.fangcang.rfp.common.dao.*;
import com.fangcang.rfp.common.dto.ProjectHotelPriceCountResult;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.ProjectIntentHotelRequest;
import com.fangcang.rfp.common.dto.response.*;
import com.fangcang.rfp.common.entity.*;
import com.fangcang.rfp.common.enums.*;
import com.fangcang.rfp.common.service.*;
import com.fangcang.rfp.common.util.StringUtils;
import com.fangcang.rfp.common.util.TimeUtil;
import com.fangcang.util.DateUtil;
import com.fangcang.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.sf.json.util.JSONUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @auther cjh
 * @description 项目酒店报价服务
 * @date 2022/10/21
 */
@Service
public class ProjectHotelPriceServiceImpl implements ProjectHotelPriceService {

    private static Logger logger = LoggerFactory.getLogger(ProjectHotelPriceServiceImpl.class);

    @Autowired
    private ProjectHotelPriceDao projectHotelPriceDao;

    @Autowired
    private ProjectHotelPriceGroupDao projectHotelPriceGroupDao;

    @Autowired
    private ProjectHotelPriceGroupService projectHotelPriceGroupService;

    @Autowired
    private PriceApplicableRoomDao priceApplicableRoomDao;

    @Autowired
    private PriceApplicableRoomService priceApplicableRoomService;

    @Autowired
    private PriceApplicableDayDao priceApplicableDayDao;

    @Autowired
    private PriceApplicableDayService priceApplicableDayService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private HotelPriceMonitorRoomDao hotelPriceMonitorRoomDao;

    @Autowired
    private ProjectIntentHotelDao projectIntentHotelDao;

    @Autowired
    private HotelPriceMonitorRoomService hotelPriceMonitorRoomService;

    @Autowired
    private ConvertPriceFailedRecordDao convertPriceFailedRecordDao;

    @Override
    public ProjectHotelPriceGroupResponse selectProjectHotelPriceGroup(ProjectIntentHotelRequest projectIntentHotelRequest) {
        // 查询房价组信息
        ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
        BeanUtils.copyProperties(projectIntentHotelRequest, queryProjectHotelPriceGroup);
        ProjectHotelPriceGroup projectHotelPriceGroup = projectHotelPriceGroupDao.selectByPrimaryKey(projectIntentHotelRequest.getHotelPriceGroupId());
        ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse = null;
        if(projectHotelPriceGroup != null) {
            projectHotelPriceGroupResponse = new ProjectHotelPriceGroupResponse();
            BeanUtils.copyProperties(projectHotelPriceGroup, projectHotelPriceGroupResponse);
            // 查询房价
            ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
            projectHotelPrice.setHotelId(projectHotelPriceGroup.getHotelId());
            projectHotelPrice.setProjectId(projectHotelPriceGroup.getProjectId());
            projectHotelPrice.setHotelPriceGroupId(projectIntentHotelRequest.getHotelPriceGroupId());
            // projectHotelPrices 有多个价格码
            List<ProjectHotelPrice> projectHotelPrices = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(projectHotelPrice);
            projectHotelPriceGroupResponse.setHotelPriceList(projectHotelPrices);
        }
        /**
        if (!CollectionUtils.isEmpty(projectHotelPriceGroupList)) {
            // 查询房型  通过 项目意向酒店id、项目ID、酒店ID、价格码 拿到房型ID、房型名称
            for (ProjectHotelPriceGroupResponse projectHotelPriceGroupRespons : projectHotelPriceGroupList) {
                PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
                BeanUtils.copyProperties(projectHotelPriceRespons,priceApplicableRoom);
                List<PriceApplicableRoomResponse> priceApplicableRoomResponses = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);
                projectHotelPriceRespons.setRoomResponseList(priceApplicableRoomResponses);
            }

            // 查询适用日期 通过 项目ID、酒店ID、价格码
            for (ProjectHotelPriceResponse projectHotelPriceRespons : projectHotelPriceResponses) {
                PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
                BeanUtils.copyProperties(projectHotelPriceRespons,priceApplicableDay);
                List<PriceApplicableDay> priceApplicableDays = priceApplicableDayDao.selectPriceApplicableDayList(priceApplicableDay);
                projectHotelPriceRespons.setApplicableDayList(priceApplicableDays);
            }
        }
         **/

        // 翻译返回的数据
        //changeReturnList(projectHotelPriceResponses);

        return projectHotelPriceGroupResponse;
    }

    @Override
    public List<ProjectHotelPriceResponse> selectProjectHotelPriceList(ProjectIntentHotelRequest projectIntentHotelRequest) {
        ArrayList<ProjectHotelPriceResponse> projectHotelPriceResponses = new ArrayList<>();

        // 查询房价
        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
        BeanUtils.copyProperties(projectIntentHotelRequest,projectHotelPrice);
        // projectHotelPrices 有多个价格码
        List<ProjectHotelPrice> projectHotelPrices = projectHotelPriceDao.selectOldInfoByProjectIdAndHotelId(projectHotelPrice);
        if (CollectionUtils.isNotEmpty(projectHotelPrices)) {
            for (ProjectHotelPrice hotelPrice : projectHotelPrices) {
                ProjectHotelPriceResponse projectHotelPriceResponse = new ProjectHotelPriceResponse();
                BeanUtils.copyProperties(hotelPrice,projectHotelPriceResponse);
                projectHotelPriceResponses.add(projectHotelPriceResponse);
            }
        }

        if (!CollectionUtils.isEmpty(projectHotelPriceResponses)) {
            // 查询房型  通过 项目意向酒店id、项目ID、酒店ID、价格码 拿到房型ID、房型名称
            for (ProjectHotelPriceResponse projectHotelPriceRespons : projectHotelPriceResponses) {
                PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
                BeanUtils.copyProperties(projectHotelPriceRespons,priceApplicableRoom);
                List<PriceApplicableRoomResponse> priceApplicableRoomResponses = priceApplicableRoomDao.selectOldPriceApplicableRoom(priceApplicableRoom);
                projectHotelPriceRespons.setRoomResponseList(priceApplicableRoomResponses);
            }

            // 查询适用日期 通过 项目ID、酒店ID、价格码
            for (ProjectHotelPriceResponse projectHotelPriceRespons : projectHotelPriceResponses) {
                PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
                BeanUtils.copyProperties(projectHotelPriceRespons,priceApplicableDay);
                List<PriceApplicableDay> priceApplicableDays = priceApplicableDayDao.selectOldPriceApplicableDayList(priceApplicableDay);
                projectHotelPriceRespons.setApplicableDayList(priceApplicableDays);
            }
        }

        // 翻译返回的数据
        //changeReturnList(projectHotelPriceResponses);

        return projectHotelPriceResponses;
    }

    @Override
    public List<ProjectHotelPrice> selectProjectHotelPriceList(Long projectIntentHotelId) {
        return projectHotelPriceDao.selectInfoByProjectIntentHotelId(projectIntentHotelId);
    }

    @Override
    public List<ProjectHotelPriceLevelResponse> selectProjectHotelPriceLevelList(ProjectIntentHotelRequest projectIntentHotelRequest) {
        // 定义返回值
        ArrayList<ProjectHotelPriceLevelResponse> projectHotelPriceLevelList = new ArrayList<>();

        // 查询房价组信息
        ProjectHotelPriceGroup queryProjectHotelPriceGroup = new ProjectHotelPriceGroup();
        BeanUtils.copyProperties(projectIntentHotelRequest, queryProjectHotelPriceGroup);
        List<ProjectHotelPriceGroup> projectHotelPriceGroups = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(queryProjectHotelPriceGroup);
        if(CollectionUtils.isEmpty(projectHotelPriceGroups)){
            return projectHotelPriceLevelList;
        }

        // 查询房价信息
        projectIntentHotelRequest.setProjectIntentHotelId(projectHotelPriceGroups.get(0).getProjectIntentHotelId());
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectInfoByProjectIntentHotelId(projectIntentHotelRequest.getProjectIntentHotelId());
        Map<Long, List<ProjectHotelPrice>> projectHotelPriceMap = projectHotelPriceList.stream().collect(Collectors.groupingBy(ProjectHotelPrice::getHotelPriceGroupId));
        List<ProjectHotelPriceGroupResponse> groupResponseList = new ArrayList<>();
        for(ProjectHotelPriceGroup projectHotelPriceGroup : projectHotelPriceGroups){
            ProjectHotelPriceGroupResponse response = new ProjectHotelPriceGroupResponse();
            BeanUtils.copyProperties(projectHotelPriceGroup, response);
            groupResponseList.add(response);
        }
        changeReturnList(groupResponseList);


        // 查询房档等级
        List<PriceApplicableRoomInfoResponse> roomResponseList = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelId(null, projectIntentHotelRequest.getProjectIntentHotelId(), null);
        Map<Integer, List<PriceApplicableRoomInfoResponse>> roomResponseMap = roomResponseList.stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getRoomLevelNo));
        Map<Integer, ProjectHotelPriceLevelResponse> projectHotelPriceLevelResponseMap = new HashMap<>();
        for(ProjectHotelPriceGroupResponse projectHotelPriceGroup : groupResponseList){
            if(!projectHotelPriceLevelResponseMap.containsKey(projectHotelPriceGroup.getRoomLevelNo())){
                ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse = new ProjectHotelPriceLevelResponse();
                projectHotelPriceLevelResponse.setProjectIntentHotelId(projectHotelPriceGroup.getProjectIntentHotelId());
                projectHotelPriceLevelResponse.setRoomLevelNo(projectHotelPriceGroup.getRoomLevelNo());
                projectHotelPriceLevelResponse.setRoomResponseList(new ArrayList<>());
                projectHotelPriceLevelResponse.setTotalRoomCount(projectHotelPriceGroup.getLevelTotalRoomCount());
                projectHotelPriceLevelResponse.setLevelRoomType1Count(projectHotelPriceGroup.getLevelRoomType1Count());
                projectHotelPriceLevelResponse.setLevelRoomType2Count(projectHotelPriceGroup.getLevelRoomType2Count());
                if(roomResponseMap.containsKey(projectHotelPriceLevelResponse.getRoomLevelNo())){
                    projectHotelPriceLevelResponse.setRoomResponseList(roomResponseMap.get(projectHotelPriceLevelResponse.getRoomLevelNo()));

                }
                // Lanyon room response data
                if(StringUtils.isNotEmpty(projectHotelPriceGroup.getLanyonRoomDesc()) && CollectionUtils.isEmpty(projectHotelPriceLevelResponse.getRoomResponseList())){
                    List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = new ArrayList<>();
                    String[] lanyonRoomDescArray = projectHotelPriceGroup.getLanyonRoomDesc().split(",");
                    for(String lanyonRoomDesc : lanyonRoomDescArray){
                        PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse = new PriceApplicableRoomInfoResponse();
                        priceApplicableRoomInfoResponse.setRoomTypeName(lanyonRoomDesc);
                        priceApplicableRoomInfoResponseList.add(priceApplicableRoomInfoResponse);
                    }
                    projectHotelPriceLevelResponse.setRoomResponseList(priceApplicableRoomInfoResponseList);
                }

                projectHotelPriceLevelResponse.setProjectHotelPriceGroupResponseList(new ArrayList<>());
                projectHotelPriceLevelResponseMap.put(projectHotelPriceGroup.getRoomLevelNo(), projectHotelPriceLevelResponse);

            }
            projectHotelPriceGroup.setHotelPriceList(projectHotelPriceMap.get(projectHotelPriceGroup.getHotelPriceGroupId()));
            projectHotelPriceLevelResponseMap.get(projectHotelPriceGroup.getRoomLevelNo()).getProjectHotelPriceGroupResponseList().add(projectHotelPriceGroup);
        }

        projectHotelPriceLevelList = new ArrayList<>(projectHotelPriceLevelResponseMap.values());

        return projectHotelPriceLevelList;
    }

    @Override
    public List<MonitorRoomResponse> selectAllMonitorRoomResponseList(Long projectId, Long hotelId) {
        // 查询协议价格列表
        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
        projectHotelPrice.setProjectId(projectId);
        projectHotelPrice.setHotelId(hotelId);
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(projectHotelPrice);
        // 查询房型
        PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
        priceApplicableRoom.setProjectId(projectId);
        priceApplicableRoom.setHotelId(hotelId);
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);

        // 查询关注房型
        List<String> monitorRoomTypeBreakfastNumList = new ArrayList<>();
        List<HotelPriceMonitorRoom> hotelPriceMonitorRoomList = hotelPriceMonitorRoomDao.selectMonitorRoomList(projectId, hotelId);
        if(CollectionUtils.isNotEmpty(hotelPriceMonitorRoomList)) {
            monitorRoomTypeBreakfastNumList = hotelPriceMonitorRoomList.stream().map(o -> o.getRoomTypeId() + "_" + o.getBreakfastNum()).collect(Collectors.toList());
        }
        Map<String, MonitorRoomResponse> monitorRoomResponseMap = new LinkedHashMap<>();
        for(PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse : priceApplicableRoomInfoResponseList){
            for(ProjectHotelPrice hotelPrice : projectHotelPriceList){
                if(!Objects.equals(hotelPrice.getRoomLevelNo(), priceApplicableRoomInfoResponse.getRoomLevelNo())){
                    continue;
                }
                String key = priceApplicableRoomInfoResponse.getRoomTypeId() + "_" + hotelPrice.getBreakfastNum();
                if(monitorRoomResponseMap.containsKey(key)){
                    continue;
                }
                MonitorRoomResponse monitorRoomResponse = new MonitorRoomResponse();
                monitorRoomResponse.setRoomLevelNo(priceApplicableRoomInfoResponse.getRoomLevelNo());
                monitorRoomResponse.setBreakfastNum(hotelPrice.getBreakfastNum());
                monitorRoomResponse.setProjectIntentHotelId(hotelPrice.getProjectIntentHotelId());
                monitorRoomResponse.setHotelPriceGroupId(hotelPrice.getHotelPriceGroupId());
                monitorRoomResponse.setRoomTypeId(priceApplicableRoomInfoResponse.getRoomTypeId());
                monitorRoomResponse.setRoomTypeName(priceApplicableRoomInfoResponse.getRoomTypeName());
                monitorRoomResponse.setIsMonitor(monitorRoomTypeBreakfastNumList.contains(key) ? RfpConstant.constant_1 : RfpConstant.constant_0);
                monitorRoomResponseMap.put(key, monitorRoomResponse);
            }
        }
        return new ArrayList<>(monitorRoomResponseMap.values());
    }


    public MonitorRoomResponse selectDefaultMonitorRoomResponse(Long projectId, Long hotelId){
        // 查询协议价格列表 取最小房档最低价格
        ProjectHotelPrice queryProjectHotelPrice = new ProjectHotelPrice();
        queryProjectHotelPrice.setProjectId(projectId);
        queryProjectHotelPrice.setHotelId(hotelId);
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectMinPriceInfoByProjectIdAndHotelIdAndGroupId(queryProjectHotelPrice);
        if(CollectionUtils.isEmpty(projectHotelPriceList)){
            return null;
        }
        ProjectHotelPrice projectHotelPrice = projectHotelPriceList.get(0);

        // 查询房型
        PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
        priceApplicableRoom.setProjectId(projectId);
        priceApplicableRoom.setHotelId(hotelId);
        priceApplicableRoom.setRoomLevelNo(projectHotelPrice.getRoomLevelNo());
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);
        if(CollectionUtils.isEmpty(priceApplicableRoomInfoResponseList)) {
            return null;
        }
        MonitorRoomResponse monitorRoomResponse = new MonitorRoomResponse();
        monitorRoomResponse.setBreakfastNum(projectHotelPrice.getBreakfastNum());
        monitorRoomResponse.setHotelPriceGroupId(projectHotelPrice.getHotelPriceGroupId());
        monitorRoomResponse.setRoomTypeId(priceApplicableRoomInfoResponseList.get(0).getRoomTypeId());
        monitorRoomResponse.setRoomTypeName(priceApplicableRoomInfoResponseList.get(0).getRoomTypeName());
        monitorRoomResponse.setProjectId(projectId);
        monitorRoomResponse.setHotelId(hotelId);
        monitorRoomResponse.setProjectIntentHotelId(projectHotelPrice.getProjectIntentHotelId());

        return monitorRoomResponse;

    }
    public MonitorRoomResponse selectLowestMonitorRoomResponse(Long projectId, Long hotelId){
        // 查询协议价格列表
        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
        projectHotelPrice.setProjectId(projectId);
        projectHotelPrice.setHotelId(hotelId);
        projectHotelPrice.setPriceType(HotelPriceTypeEnum.BASE_PRICE.key);
        List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(projectHotelPrice);

        // 查询房型
        PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
        priceApplicableRoom.setProjectId(projectId);
        priceApplicableRoom.setHotelId(hotelId);
        priceApplicableRoom.setRoomLevelNo(projectHotelPriceList.get(0).getRoomLevelNo());
        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomDao.selectPriceApplicableRoom(priceApplicableRoom);

        MonitorRoomResponse monitorRoomResponse = new MonitorRoomResponse();
        monitorRoomResponse.setBreakfastNum(projectHotelPriceList.get(0).getBreakfastNum());
        monitorRoomResponse.setHotelPriceGroupId(projectHotelPriceList.get(0).getHotelPriceGroupId());
        monitorRoomResponse.setRoomTypeId(priceApplicableRoomInfoResponseList.get(0).getRoomTypeId());
        monitorRoomResponse.setRoomTypeName(priceApplicableRoomInfoResponseList.get(0).getRoomTypeName());

        return monitorRoomResponse;

    }

    public void changeReturnList(List<ProjectHotelPriceGroupResponse> projectHotelPriceGroupResponses) {

        for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponses) {

            // 转换LRA
            if (projectHotelPriceGroupResponse.getLra().equals(StateEnum.Effective.key)) {
                projectHotelPriceGroupResponse.setLraDesc("LRA");
            }else {
                projectHotelPriceGroupResponse.setLraDesc("No LRA");
            }

            // 转换星期
            if (StringUtils.isNotEmpty(projectHotelPriceGroupResponse.getApplicableWeeks())) {
                String[] split = projectHotelPriceGroupResponse.getApplicableWeeks().split(",");
                StringBuffer buffer = new StringBuffer();
                for (String s : split) {
                    String valueByKey = ApplicableWeeksTypeEnum.getValueByKey(Integer.valueOf(s));
                    buffer.append(valueByKey + ",");
                }
                String substring = buffer.substring(0, buffer.length() - 1);
                projectHotelPriceGroupResponse.setApplicableWeeksDesc(substring);
            }

            // 转换退改条款
            if (projectHotelPriceGroupResponse.getCancelRestrictType().equals(CancelRestrictTypeEnum.FREE.key)) {
                projectHotelPriceGroupResponse.setCancelRestrictDesc(CancelRestrictTypeEnum.FREE.value);
            } else if (projectHotelPriceGroupResponse.getCancelRestrictType().equals(CancelRestrictTypeEnum.PAY.key)) {
                projectHotelPriceGroupResponse.setCancelRestrictDesc("提前"+projectHotelPriceGroupResponse.getCancelRestrictDay()+"天"+projectHotelPriceGroupResponse.getCancelRestrictTime()+"点之前可免费退改，之后不可退改");
            } else if (projectHotelPriceGroupResponse.getCancelRestrictType().equals(CancelRestrictTypeEnum.PAY_FIRST_NIGHT.key)) {
                projectHotelPriceGroupResponse.setCancelRestrictDesc("提前"+projectHotelPriceGroupResponse.getCancelRestrictDay()+"天"+projectHotelPriceGroupResponse.getCancelRestrictTime()+"点之前可免费退改，之后收取首晚房费的退订费");
            }
        }
    }


    // 判断是否符合企业房价的招标策略
    @Override
    public void isSatisfyProjectTender(Long projectId, List<ProjectHotelPriceLevelResponse> hotelPriceLevelResponseList, Response response) {
        ArrayList<String> resultStrings = new ArrayList<>();

        // 拿到企业的招标策略
        Response projectHotelTendStrategyResponse = projectService.queryProjectHotelTendStrategy(projectId);
        ObjectMapper objectMapper = new ObjectMapper();
        if (projectHotelTendStrategyResponse.getData() == null) {
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        }else {
            ProjectHotelTendStrategy projectHotelTendStrategy = objectMapper.convertValue(projectHotelTendStrategyResponse.getData(), ProjectHotelTendStrategy.class);

            // 判断企业的是否有金额限制，有的话，酒店端是否满足
            Boolean supportPriceFlag = true;
            List<ProjectHotelPriceGroupResponse> projectHotelPriceGroupResponseList = new ArrayList<>();
            List<ProjectHotelPrice> projectHotelPriceList = new ArrayList<>();
            List<PriceApplicableRoomInfoResponse> roomResponseList = new ArrayList<>();
            for(ProjectHotelPriceLevelResponse projectHotelPriceLevelResponse : hotelPriceLevelResponseList){
                projectHotelPriceGroupResponseList.addAll(projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList());
                for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceLevelResponse.getProjectHotelPriceGroupResponseList()){
                    projectHotelPriceList.addAll(projectHotelPriceGroupResponse.getHotelPriceList());
                }
                roomResponseList.addAll(projectHotelPriceLevelResponse.getRoomResponseList());
            }

            if (projectHotelTendStrategy.getSupportPriceLimit().equals(StateEnum.Effective.key)) {
                supportPriceFlag = false;
                for (ProjectHotelPrice projectHotelPrice : projectHotelPriceList) {
                    BigDecimal basePrice = projectHotelPrice.getBasePrice();
                    BigDecimal limitMinPrice = projectHotelTendStrategy.getLimitMinPrice();
                    BigDecimal limitMaxPrice = projectHotelTendStrategy.getLimitMaxPrice();
                    if (basePrice.compareTo(limitMinPrice) >= 0 && basePrice.compareTo(limitMaxPrice) <= 0) {
                        supportPriceFlag = true;
                        if(projectHotelTendStrategy.getPriceLimitCount().intValue() == 1) {
                            //至少有一条
                            break;
                        }
                    }else{
                        if(projectHotelTendStrategy.getPriceLimitCount().intValue() == 2) {
                            supportPriceFlag = false;
                            //全部
                            break;
                        }
                    }
                }
                if (!supportPriceFlag) {
                    if(projectHotelTendStrategy.getPriceLimitCount().intValue() == 1) {
                        String supportPrice = "至少含一条金额限制" + projectHotelTendStrategy.getLimitMinPrice() + "-" + projectHotelTendStrategy.getLimitMaxPrice() + "范围内的报价";
                        resultStrings.add(supportPrice);
                    }else {
                        String supportPrice = "全部金额限制" + projectHotelTendStrategy.getLimitMinPrice() + "-" + projectHotelTendStrategy.getLimitMaxPrice() + "范围内的报价";
                        resultStrings.add(supportPrice);
                    }
                }
            }

            // 判断企业的是否有18点前免费取消的报价限制，有的话，酒店端是否满足  (废弃)
            /**
            Boolean supportFreeCancelFlag = true;
            if (projectHotelTendStrategy.getSupportFreeCancel().equals(StateEnum.Effective.key)) {
                supportFreeCancelFlag = false;
                for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                    if ((projectHotelPriceGroupResponse.getCancelRestrictType().intValue() == 2 || projectHotelPriceGroupResponse.getCancelRestrictType().intValue() == 3) && projectHotelPriceGroupResponse.getCancelRestrictDay().intValue() == 0) {
                        if (Integer.valueOf(projectHotelPriceGroupResponse.getCancelRestrictTime().substring(0, 2)) >= 18) {
                            supportFreeCancelFlag = true;
                            if(projectHotelTendStrategy.getFreeCancelCount().intValue() == 1) {
                                //至少有一条
                                break;
                            }
                        }else{
                            if(projectHotelTendStrategy.getFreeCancelCount().intValue() == 2) {
                                //全部
                                supportFreeCancelFlag = false;
                                break;
                            }
                        }
                    }else if((projectHotelPriceGroupResponse.getCancelRestrictType().intValue() == 2 || projectHotelPriceGroupResponse.getCancelRestrictType().intValue() == 3)&& projectHotelPriceGroupResponse.getCancelRestrictDay().intValue() != 0){
                        if(projectHotelTendStrategy.getFreeCancelCount().intValue() == 2) {
                            //全部
                            supportFreeCancelFlag = false;
                            break;
                        }
                    }
                }
                if (!supportFreeCancelFlag) {
                    if(projectHotelTendStrategy.getFreeCancelCount().intValue() == 1) {
                        String upportFreeCancel = "至少含一条支持" + projectHotelTendStrategy.getSupportCancelDay() +  "天" + projectHotelTendStrategy.getSupportCancelTime()+ ":00前免费取消的报价";
                        resultStrings.add(upportFreeCancel);
                    }else{
                        String upportFreeCancel = "全部支持" + projectHotelTendStrategy.getSupportCancelDay() +  "天" + projectHotelTendStrategy.getSupportCancelTime()+ ":00前免费取消的报价";
                        resultStrings.add(upportFreeCancel);
                    }
                }
            }
             **/

            // 酒店报价 至少一条  支持当天18点前免费取消，之后收取首晚房费的报价 勾选：SUPPORT_1ST_CANCEL_COUNT=1， FREE_EXCLUDE_1ST_CANCEL_COUNT（1:至少一天，2：全部）
            // 酒店报价 至少一条、支持当天18点前免费取消的报价 SUPPORT_FREE_CANCEL=1，  FREE_CANCEL_COUNT（1:至少一天，2：全部）
            // 酒店报价XXX支持当天18点前免费取消，之后收取首晚房费 默认2(1:至少一天，2：全部)， 有的话酒店是否满足
            boolean supportFreeExcludeFirstDaysFlag = true;
            if(projectHotelTendStrategy.getSupport1stCancelCount().equals(StateEnum.Effective.key)) {
                supportFreeExcludeFirstDaysFlag = false;
                if(projectHotelTendStrategy.getFreeExclude1stCancelCount() == 1){ // 至少一条
                    for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                        String validateResult = projectHotelPriceGroupService.validateCancelRestrict(projectHotelTendStrategy, projectHotelPriceGroupResponse);
                        if (!StringUtil.isValidString(validateResult)) {
                            supportFreeExcludeFirstDaysFlag = true;
                            break;
                        }
                    }
                } else if(projectHotelTendStrategy.getFreeExclude1stCancelCount() == 2) { // 全部
                    supportFreeExcludeFirstDaysFlag = true; // 默认全部包含
                    for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                        if(projectHotelPriceGroupResponse.getCancelRestrictType().intValue() != 3){
                            supportFreeExcludeFirstDaysFlag = false;
                            break;
                        }
                        String validateResult = projectHotelPriceGroupService.validateCancelRestrict(projectHotelTendStrategy, projectHotelPriceGroupResponse);
                        if(StringUtil.isValidString(validateResult)){
                            supportFreeExcludeFirstDaysFlag = false;
                            break;
                        }
                    }
                }
                if (!supportFreeExcludeFirstDaysFlag) {
                    if (projectHotelTendStrategy.getFreeExclude1stCancelCount() == 1) {
                        String upportFreeCancel = "【酒店报价需要至少含一条支持" + projectHotelTendStrategy.getSupportCancelDay() +  "天 " + projectHotelTendStrategy.getSupportCancelTime()+ "前免费取消的报价，之后收取首晚房费的报价】";
                        resultStrings.add(upportFreeCancel);
                    } else {
                        String upportFreeCancel = "【酒店报价需要全部支持" + projectHotelTendStrategy.getSupportCancelDay() +  "天 " + projectHotelTendStrategy.getSupportCancelTime()+ "前免费取消，之后收取首晚房费的报价】";
                        resultStrings.add(upportFreeCancel);
                    }
                }
            }

            // 判断企业招标是否有LRA报价
            Boolean supportLraFlag = true;
            if (projectHotelTendStrategy.getSupportLra().equals(StateEnum.Effective.key)) {
                supportLraFlag = false;

                for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                    if (projectHotelPriceGroupResponse.getLra().equals(StateEnum.Effective.key)) {
                        supportLraFlag = true;
                        if(projectHotelTendStrategy.getSupportLraCount().intValue() == 1) {
                            //至少有一条
                            break;
                        }
                    }else{
                        if(projectHotelTendStrategy.getSupportLraCount().intValue() == 2) {
                            //全部
                            supportLraFlag = false;
                            break;
                        }
                    }
                }
                if (!supportLraFlag) {
                    if(projectHotelTendStrategy.getSupportLraCount().intValue() == 1) {
                        String upportFreeCancel = "至少含一条支持LRA的报价";
                        resultStrings.add(upportFreeCancel);
                    }else{
                        String upportFreeCancel = "全部支持LRA的报价";
                        resultStrings.add(upportFreeCancel);
                    }
                }
            }

            // 是否必须提供无早 1:是，0否
            boolean isIncludeBreakfastFlag = true;
            // 价格组维度检查早餐是否满足必须条件
            if(projectHotelTendStrategy.getIsIncludeNoBreakfast() == RfpConstant.constant_1 ||
                    projectHotelTendStrategy.getIsIncludeOneBreakfast() == RfpConstant.constant_1 ||
                    projectHotelTendStrategy.getIsIncludeTwoBreakfast() == RfpConstant.constant_1
            ) {
                for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                    Map<Integer, List<ProjectHotelPrice>> priceTypePriceMap = projectHotelPriceGroupResponse.getHotelPriceList().stream().collect(Collectors.groupingBy(ProjectHotelPrice::getPriceType));
                    // 检查每一个season是否包含价格
                    for(Integer priceType : priceTypePriceMap.keySet()){
                        boolean isIncludeNoBreakfast = false;
                        boolean isIncludeOneBreakfast = false;
                        boolean isIncludeTwoBreakfast = false;
                        List<ProjectHotelPrice> projectHotelPrices = priceTypePriceMap.get(priceType);
                        for(ProjectHotelPrice projectHotelPrice : projectHotelPrices){
                            if(projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ZERO.key){
                                isIncludeNoBreakfast = true;
                            }
                            if(projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.ONE.key){
                                isIncludeOneBreakfast = true;
                            }
                            if(projectHotelPrice.getBreakfastNum() == BreakfastNumEnum.TWO.key){
                                isIncludeTwoBreakfast = true;
                            }
                        }
                        if(projectHotelTendStrategy.getIsIncludeNoBreakfast() == RfpConstant.constant_1 && !isIncludeNoBreakfast){
                            resultStrings.add("必须提供无早餐价格");
                            isIncludeBreakfastFlag = false;
                        }
                        if(projectHotelTendStrategy.getIsIncludeOneBreakfast() == RfpConstant.constant_1 && !isIncludeOneBreakfast){
                            resultStrings.add("必须提供单早餐价格");
                            isIncludeBreakfastFlag = false;
                        }
                        if(projectHotelTendStrategy.getIsIncludeTwoBreakfast() == RfpConstant.constant_1 && !isIncludeTwoBreakfast){
                            resultStrings.add("必须提供双早餐价格");
                            isIncludeBreakfastFlag = false;
                        }
                        if(!isIncludeBreakfastFlag){
                            break;
                        }
                    }
                    if(!isIncludeBreakfastFlag){
                        break;
                    }
                }
            }

            // 酒店报价是否包括大床房，双床，总数物理间数量
            Boolean isIncludeLevelRoomCount = true;
            if(projectHotelTendStrategy.getIsIncludeLevelRoomCount() == RfpConstant.constant_1){
                Map<Integer, List<ProjectHotelPriceGroupResponse>> levelProjectHotelPriceGroupResponseMap = projectHotelPriceGroupResponseList.stream().collect(Collectors.groupingBy(ProjectHotelPriceGroupResponse::getRoomLevelNo));

                for (ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupResponseList) {
                    if(projectHotelPriceGroupResponse.getLevelTotalRoomCount() == null ||
                            projectHotelPriceGroupResponse.getLevelRoomType1Count() == null ||
                            projectHotelPriceGroupResponse.getLevelRoomType2Count() == null
                    ) {
                        List<ProjectHotelPriceGroupResponse> roomLevelNoProjectHotelPriceGroupResponseList = levelProjectHotelPriceGroupResponseMap.get(projectHotelPriceGroupResponse.getRoomLevelNo()).stream().filter(
                                o -> o.getLevelTotalRoomCount() != null || o.getLevelRoomType1Count() != null || o.getLevelRoomType2Count() != null).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(roomLevelNoProjectHotelPriceGroupResponseList)){
                            ProjectHotelPriceGroupResponse roomLevelNoProjectHotelPriceGroupResponse = roomLevelNoProjectHotelPriceGroupResponseList.get(0);
                            projectHotelPriceGroupResponse.setLevelTotalRoomCount(roomLevelNoProjectHotelPriceGroupResponse.getLevelTotalRoomCount());
                            projectHotelPriceGroupResponse.setLevelRoomType1Count(roomLevelNoProjectHotelPriceGroupResponse.getLevelRoomType1Count());
                            projectHotelPriceGroupResponse.setLevelRoomType2Count(roomLevelNoProjectHotelPriceGroupResponse.getLevelRoomType2Count());
                        } else {
                            resultStrings.add("酒店报价必须填写房档对应大、双、总物理房间数信息");
                            isIncludeLevelRoomCount = false;
                            break;
                        }
                    }
                }
            }

            //酒店报价最多不超过X个房档。
            Boolean supportMaxPriceLevelCountFlag = true;
            if(projectHotelTendStrategy.getSupportMaxRoomTypeCount() != null && projectHotelTendStrategy.getSupportMaxRoomTypeCount() == StateEnum.Effective.key){
                if(hotelPriceLevelResponseList.size() > projectHotelTendStrategy.getMaxRoomTypeCount()){
                    supportMaxPriceLevelCountFlag = false;
                    String supportMaxPriceLevelCountString = "酒店报价中房档最多不能超过"+ projectHotelTendStrategy.getMaxRoomTypeCount() + "个";
                    resultStrings.add(supportMaxPriceLevelCountString);
                }
            }

            // 必须全部满足才不返回信息，只有有一条不满足则返回全部策略要求
            if (supportPriceFlag && supportLraFlag &&
                    supportFreeExcludeFirstDaysFlag && isIncludeBreakfastFlag &&
                    supportMaxPriceLevelCountFlag && isIncludeLevelRoomCount) { // isIncludeAllWeekFlag
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                response.setMsg(ReturnResultEnum.SUCCESS.message);
            }else {
                response.setResult(ReturnResultEnum.SUCCESS.errorNo);
                response.setData(resultStrings);
            }
        }

    }

    @Override
    public ProjectHotelLowPriceResponse selectHotelLowestPrice(Long projectIntentHotelId) {
        List<ProjectHotelLowPriceResponse> projectHotelLowPriceResponses = projectHotelPriceDao.selectHotelMinPriceByIntentHotelId(projectIntentHotelId);
        return projectHotelLowPriceResponses.get(0);
    }

    @Override
    public Response existHotelPrice(Long hotelId, Long projectId) {
        Response response = new Response();

        ProjectHotelPrice projectHotelPrice = new ProjectHotelPrice();
        projectHotelPrice.setHotelId(hotelId);
        projectHotelPrice.setProjectId(projectId);
        List<ProjectHotelPrice> projectHotelPrices = projectHotelPriceDao.selectInfoByProjectIdAndHotelIdAndGroupId(projectHotelPrice);
        if(CollectionUtils.isNotEmpty(projectHotelPrices)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("所选酒店已经在项目中进行报价，不可重复新增");
        }else{
            response.setResult(ReturnResultEnum.SUCCESS.errorNo);
            response.setMsg(ReturnResultEnum.SUCCESS.message);
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response convertToLevelPrice(Long id) {
        Response response = new Response();

        // 查询所有需要处理的hotelPrice
        List<ProjectIntentHotel> projectIntentHotelList  = projectIntentHotelDao.selectAllProjectIntentHotel();
        for(ProjectIntentHotel projectIntentHotel : projectIntentHotelList){
            if(id != null && id > 0 && projectIntentHotel.getProjectIntentHotelId().longValue() != id.longValue()){
                continue;
            }

            logger.info("Process projectIntentHotel {}", projectIntentHotel.getProjectIntentHotelId());
            // 查询可用日期
            PriceApplicableDay priceApplicableDay = new PriceApplicableDay();
            priceApplicableDay.setProjectId(projectIntentHotel.getProjectId());
            priceApplicableDay.setHotelId(projectIntentHotel.getHotelId());
            List<PriceApplicableDay> priceApplicableDayList = priceApplicableDayDao.selectAllPriceApplicableDayList(priceApplicableDay);
            boolean isProceedProjectIntentHotel = false;
            for(PriceApplicableDay pad : priceApplicableDayList){
                if(pad.getPriceCode() == null && pad.getPriceType() != null){
                    isProceedProjectIntentHotel = true;
                    logger.info("已经处理过的履约报价 projectIntentHotel {}", projectIntentHotel.getProjectIntentHotelId());
                    break;
                }
            }
            if(isProceedProjectIntentHotel){
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "已经处理过的履约报价");
                continue;
            }

            // 查询价格组
            ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
            projectHotelPriceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectPriceGroup(projectHotelPriceGroup);
            if(CollectionUtils.isNotEmpty(projectHotelPriceGroupList)){
                logger.info("已经存在价格组，不能数据迁移 {}", JSON.toJSONString(projectIntentHotel));
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "已经存在价格组");
                continue;
            }

            Map<Long, List<PriceApplicableDay>> priceApplicableDayMap = priceApplicableDayList.stream().collect(Collectors.groupingBy(PriceApplicableDay::getPriceCode));

            // 查询价格
            ProjectHotelPrice queryProjectHotelPrice = new ProjectHotelPrice();
            queryProjectHotelPrice.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
            List<ProjectHotelPrice> projectHotelPriceList = projectHotelPriceDao.selectAllNoGroupIdHotelPrice(queryProjectHotelPrice);
            if(CollectionUtils.isEmpty(projectHotelPriceList)){
                logger.info("不存在报价，不能数据迁移 {}", JSON.toJSONString(projectIntentHotel));
               // insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "不存在报价");
                continue;
            }
            //<PriceCode, ProjectHotelPrice>
            Map<Long, ProjectHotelPrice> projectHotelPriceMap = projectHotelPriceList.stream().collect(Collectors.toMap(ProjectHotelPrice::getPriceCode, Function.identity()));


            List<PriceApplicableRoom> applicableRoomList = priceApplicableRoomDao.selectAllPriceApplicableRoom(projectIntentHotel.getProjectIntentHotelId());

            // 归类基础价，Season1, Season2 日期范围
            List<ProjectHotelPriceCountResult> priceCountResultList = projectHotelPriceDao.selectProjectHotelPriceCount(projectIntentHotel.getProjectIntentHotelId());
            ProjectHotelPriceCountResult priceCountResult = priceCountResultList.get(0);
            if(priceCountResult.getPriceCount() > 3){
                logger.info("房型加早餐报价数量超过3，需要人工处理 {}, {}", projectIntentHotel.getProjectIntentHotelId(), JSON.toJSONString(priceCountResult));
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "房型加早餐报价数量超过3:" + JSON.toJSONString(priceCountResult));
                continue;
            }
            int breakfastNum = priceCountResult.getBreakfastNum();
            Long roomTypeId = priceCountResult.getRoomTypeId();
            List<Long> roomPriceCodeList = applicableRoomList.stream().filter(o -> o.getRoomTypeId().longValue() == roomTypeId.longValue()).map(PriceApplicableRoom::getPriceCode).collect(Collectors.toList());
            List<Long> applyDayPriceCodeList = projectHotelPriceList.stream().filter(o -> o.getBreakfastNum() == breakfastNum && roomPriceCodeList.contains(o.getPriceCode())).map(ProjectHotelPrice::getPriceCode).collect(Collectors.toList());

            // 根据价格分组可用日期
            // <Price, List<PriceApplicableDay>>
            Map<String, List<PriceApplicableDay>> priceApplicableDayListMap = new HashMap<>();
            // 根据价格分组计算期间统计
            // <Price, totalDays>
            Map<String, Long> priceApplicableDaysMap = new HashMap<>();

            HashSet<String> applicableDayKeySet = new HashSet<>();
            for(PriceApplicableDay applicableDay : priceApplicableDayList){
                String startDateEndDateKey = DateUtil.dateToString(applicableDay.getStartDate()) + "#" + DateUtil.dateToString(applicableDay.getEndDate());
                applicableDayKeySet.add(startDateEndDateKey);
                if(!applyDayPriceCodeList.contains(applicableDay.getPriceCode())){
                    continue;
                }
                ProjectHotelPrice projectHotelPrice = projectHotelPriceMap.get(applicableDay.getPriceCode());
                String priceKey = projectHotelPrice.getBasePrice().toString();
                if(!priceApplicableDayListMap.containsKey(priceKey)){
                    priceApplicableDayListMap.put(priceKey, new ArrayList<>());
                    priceApplicableDaysMap.put(priceKey, 0L);
                }
                // <价格， 价格适用日期>
                priceApplicableDayListMap.get(priceKey).add(applicableDay);

                long priceTotalDays = priceApplicableDaysMap.get(priceKey) + DateUtil.getDay(applicableDay.getStartDate(), applicableDay.getEndDate());
                priceApplicableDaysMap.put(priceKey, priceTotalDays);
            }

            // 新增价格适用日期
            // <PriceType, List<PriceApplicableDay>>
            Map<Integer, List<PriceApplicableDay>> newPriceTypeApplicableDayMap = new HashMap<>();


            // 倒序排序价格日期数map, 日期范围最大为基础类型，第二为season1, 第三为season2
            List<Map.Entry<String,Long>> priceApplicableDaysMapList = new ArrayList<>(priceApplicableDaysMap.entrySet());
            priceApplicableDaysMapList.sort(Map.Entry.comparingByValue());
            ListUtil.reverse(priceApplicableDaysMapList);
            Map<String,Long> sortPriceApplicableDaysMap = new LinkedHashMap<>();
            for(Map.Entry<String,Long> entry: priceApplicableDaysMapList){
                sortPriceApplicableDaysMap.put(entry.getKey(), entry.getValue());
            }

            // 协议日期范围
            // 价格可用日期范围对应价格类型集合
            // <startDate#endDate, PriceType>
            Map<String, Integer> newApplicableDayPriceTypeMap = new HashMap<>();
            int i=0;
            for(String priceKey : sortPriceApplicableDaysMap.keySet()){
                i++;
                if(!newPriceTypeApplicableDayMap.containsKey(i)){
                    newPriceTypeApplicableDayMap.put(i, new ArrayList<>());
                }
                // 价格范围对应价格key获取可用日期集合
                List<PriceApplicableDay> priceTypePriceApplicableDayList = priceApplicableDayListMap.get(priceKey);
                for(PriceApplicableDay applicableDay : priceTypePriceApplicableDayList) {
                    PriceApplicableDay newApplicableDay = new PriceApplicableDay();
                    newApplicableDay.setApplicableDayId(priceApplicableDayService.selectNextApplicableDayId());
                    newApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                    newApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                    newApplicableDay.setStartDate(applicableDay.getStartDate());
                    newApplicableDay.setEndDate(applicableDay.getEndDate());
                    newApplicableDay.setPriceType(i);
                    newApplicableDay.setCreator(RfpConstant.CREATOR);
                    newPriceTypeApplicableDayMap.get(i).add(newApplicableDay);
                    String startDateEndDateKey = DateUtil.dateToString(applicableDay.getStartDate()) + "#" + DateUtil.dateToString(applicableDay.getEndDate());
                    newApplicableDayPriceTypeMap.put(startDateEndDateKey, i);
                }
            }
            if(i > 3){
                logger.info("日期价格类型超过3，需要人工处理 {}, {}, {}", projectIntentHotel.getProjectIntentHotelId(), i, JSON.toJSONString(newApplicableDayPriceTypeMap));
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "日期价格类型超过3,存在价格类型数量" + i);
                continue;
            }

            // 特殊处理，如果价格分组只有一个，但是存在小于3个报价日期，则分别放到3个价格类型里面
            if(newApplicableDayPriceTypeMap.size() == 1 && applicableDayKeySet.size() <= 3){
                for(String startDateEndDateKey : applicableDayKeySet){
                    if(newApplicableDayPriceTypeMap.containsKey(startDateEndDateKey)){
                        continue;
                    }
                    i++;
                    if(!newPriceTypeApplicableDayMap.containsKey(i)){
                        newPriceTypeApplicableDayMap.put(i, new ArrayList<>());
                    }
                    String[] startEndDays = startDateEndDateKey.split("#");

                    PriceApplicableDay newApplicableDay = new PriceApplicableDay();
                    newApplicableDay.setApplicableDayId(priceApplicableDayService.selectNextApplicableDayId());
                    newApplicableDay.setProjectId(projectIntentHotel.getProjectId());
                    newApplicableDay.setHotelId(projectIntentHotel.getHotelId());
                    newApplicableDay.setStartDate(DateUtil.stringToDate(startEndDays[0]));
                    newApplicableDay.setEndDate(DateUtil.stringToDate(startEndDays[1]));
                    newApplicableDay.setPriceType(i);
                    newApplicableDay.setCreator(RfpConstant.CREATOR);
                    newPriceTypeApplicableDayMap.get(i).add(newApplicableDay);
                    newApplicableDayPriceTypeMap.put(startDateEndDateKey, i);
                }
            }

            // 新增房型
            Map<Integer, List<PriceApplicableRoom>> levelPriceApplicableRoomMap = new HashMap<>();
            Map<Long, PriceApplicableRoom> roomTypePriceApplicableRoomMap = new HashMap<>();
            // 新增价格组
            // <Level, groupList>
            Map<Integer, List<ProjectHotelPriceGroup>> newProjectHotelPriceGroupMap = new HashMap<>();

            // 房档价格 房型+早餐+价格+group 维度统计
            // <Level, Set<priceType#breakfastnum#price#groupkey>>
            Map<Integer, Set<String>> levelKeyGroupMap = new HashMap<>();
            // 更新hotelPrice
            Map<Integer, List<ProjectHotelPrice>> levelProjectHotelPriceMap = new HashMap<>();

            // 根据房型查询报价日期类型
            Map<Long, List<PriceApplicableRoom>> priceApplicableRoomMap = priceApplicableRoomDao.selectAllPriceApplicableRoom(projectIntentHotel.getProjectIntentHotelId()).stream().collect(Collectors.groupingBy(PriceApplicableRoom::getPriceCode));

            // priceCountResultList 生产房档
            List<Long> proceedRoomTypeIdList = new ArrayList<>();
            i = 0;

            HotelPriceMonitorRoom hotelPriceMonitorRoom = null;
            boolean isConvertPriceFailed = false;
            String failedReason = "";
            for(ProjectHotelPriceCountResult priceCount : priceCountResultList){
                if(proceedRoomTypeIdList.contains(priceCount.getRoomTypeId())){
                    continue;
                }
                int j=0;
                for(ProjectHotelPrice projectHotelPrice : projectHotelPriceList){
                    List<PriceApplicableRoom> priceApplicableRoomList = priceApplicableRoomMap.get(projectHotelPrice.getPriceCode());
                    List<Long> priceApplicableRoomIdList = priceApplicableRoomList.stream().map(PriceApplicableRoom::getRoomTypeId).collect(Collectors.toList());
                    boolean containRoomType = priceApplicableRoomIdList.contains(priceCount.getRoomTypeId());
                    if(containRoomType){
                        List<PriceApplicableDay> applicableDayList = priceApplicableDayMap.get(projectHotelPrice.getPriceCode());

                        // 设置PriceType
                        Integer priceType = null;
                        for(PriceApplicableDay priceApplicableDay1 : applicableDayList){
                            String startDateEndDateKey = DateUtil.dateToString(priceApplicableDay1.getStartDate()) + "#" + DateUtil.dateToString(priceApplicableDay1.getEndDate());
                            if(newApplicableDayPriceTypeMap.containsKey(startDateEndDateKey)) {
                                priceType = newApplicableDayPriceTypeMap.get(startDateEndDateKey);
                                break;
                            }
                        }
                        if(priceType == null){
                            logger.info("未能处理的酒店价格 找不到日期对应价格类型:{}, {}",JSON.toJSONString(applicableDayList), JSON.toJSONString(projectHotelPrice));
                            isConvertPriceFailed = true;
                            failedReason = "找不到日期对应价格类型，价格ID " + JSON.toJSONString(projectHotelPrice);
                            break;
                        }
                        projectHotelPrice.setPriceType(priceType);
                        String hotelPriceKey = generateHotelPriceKey(projectHotelPrice);
                        // 第一条数据默认第一房档
                        if(i==0 && j==0) {
                            int levelNo = RoomLevelEnum.ONE.key;
                            for(PriceApplicableRoom priceApplicableRoom : priceApplicableRoomList) {
                                PriceApplicableRoom newPriceApplicableRoom = new PriceApplicableRoom();
                                newPriceApplicableRoom.setApplicableRoomId(priceApplicableRoomService.selectNextApplicableRoomId());
                                newPriceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                newPriceApplicableRoom.setPriceCode(projectHotelPrice.getPriceCode());
                                newPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                                newPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                                newPriceApplicableRoom.setRoomTypeId(priceApplicableRoom.getRoomTypeId());
                                newPriceApplicableRoom.setCreator(RfpConstant.CREATOR);
                                newPriceApplicableRoom.setRoomLevelNo(RoomLevelEnum.ONE.key);
                                newPriceApplicableRoom.setDisplayOrder(j);
                                if(!levelPriceApplicableRoomMap.containsKey(levelNo)) {
                                    levelPriceApplicableRoomMap.put(levelNo, new ArrayList<>());
                                }
                                levelPriceApplicableRoomMap.get(levelNo).add(newPriceApplicableRoom);
                                roomTypePriceApplicableRoomMap.put(newPriceApplicableRoom.getRoomTypeId(), newPriceApplicableRoom);
                            }

                            ProjectHotelPriceGroup priceGroup = new ProjectHotelPriceGroup();
                            Long hotelPriceGroupId = projectHotelPriceGroupService.getNextHotelPriceGroupId();
                            priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                            priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                            priceGroup.setProjectId(projectIntentHotel.getProjectId());
                            priceGroup.setHotelId(projectIntentHotel.getHotelId());
                            priceGroup.setRoomLevelNo(levelNo);
                            priceGroup.setApplicableWeeks(projectHotelPrice.getApplicableWeeks());
                            priceGroup.setLra(projectHotelPrice.getLra());
                            priceGroup.setRemark(projectHotelPrice.getRemark());
                            priceGroup.setCancelRestrictType(projectHotelPrice.getCancelRestrictType());
                            priceGroup.setCancelRestrictDay(projectHotelPrice.getCancelRestrictDay());
                            priceGroup.setCancelRestrictTime(projectHotelPrice.getCancelRestrictTime());
                            priceGroup.setCreator(RfpConstant.CREATOR);

                            newProjectHotelPriceGroupMap.put(levelNo, new ArrayList<>());
                            newProjectHotelPriceGroupMap.get(levelNo).add(priceGroup);

                            projectHotelPrice.setHotelPriceGroupId(hotelPriceGroupId);
                            projectHotelPrice.setRoomLevelNo(levelNo);
                            levelProjectHotelPriceMap.put(levelNo, new ArrayList<>());
                            levelProjectHotelPriceMap.get(levelNo).add(projectHotelPrice);

                            String levePriceKey = generateLevelPriceKey(projectHotelPrice);
                            levelKeyGroupMap.put(levelNo, new HashSet<>());
                            levelKeyGroupMap.get(levelNo).add(levePriceKey);

                            // 设置关注房型
                            hotelPriceMonitorRoom = new HotelPriceMonitorRoom();
                            hotelPriceMonitorRoom.setProjectId(projectHotelPrice.getProjectId());
                            hotelPriceMonitorRoom.setHotelId(projectHotelPrice.getHotelId());
                            hotelPriceMonitorRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                            hotelPriceMonitorRoom.setBreakfastNum(projectHotelPrice.getBreakfastNum());
                            hotelPriceMonitorRoom.setRoomTypeId(priceApplicableRoomList.get(0).getRoomTypeId());
                            hotelPriceMonitorRoom.setHotelPriceGroupId(priceGroup.getHotelPriceGroupId());
                            hotelPriceMonitorRoom.setCreator(RfpConstant.CREATOR);
                            hotelPriceMonitorRoom.setModifier(RfpConstant.CREATOR);

                        } else {
                            // 如果存在相同房型, 归类到同一个房档
                            Long containProceedRoomTypeId = null;
                            for(PriceApplicableRoom priceApplicableRoom : priceApplicableRoomList){
                                if(roomTypePriceApplicableRoomMap.containsKey(priceApplicableRoom.getRoomTypeId())){
                                    boolean isTheSameRoom = isTheSameLevelRoomType(priceApplicableRoom.getRoomTypeId(), priceCount.getRoomTypeId(), applicableRoomList, projectHotelPriceMap);
                                    if(isTheSameRoom){
                                        containProceedRoomTypeId = priceApplicableRoom.getRoomTypeId();
                                    }
                                    break;
                                }
                            }
                            if(containProceedRoomTypeId != null){
                                PriceApplicableRoom newPriceApplicableRoom =  roomTypePriceApplicableRoomMap.get(containProceedRoomTypeId);
                                // 新增其他房型到level
                                for(PriceApplicableRoom priceApplicableRoom : priceApplicableRoomList) {
                                    if(roomTypePriceApplicableRoomMap.containsKey(priceApplicableRoom.getRoomTypeId())){
                                        continue;
                                    }

                                    // 检查所有房档是否一致
                                    boolean isTheSameRoom = isTheSameLevelRoomType(newPriceApplicableRoom.getRoomTypeId(), priceApplicableRoom.getRoomTypeId(), applicableRoomList, projectHotelPriceMap);
                                    if(!isTheSameRoom){
                                        continue;
                                    }

                                    PriceApplicableRoom newAddPriceApplicableRoom = new PriceApplicableRoom();
                                    newAddPriceApplicableRoom.setApplicableRoomId(priceApplicableRoomService.selectNextApplicableRoomId());
                                    newAddPriceApplicableRoom.setProjectIntentHotelId(newPriceApplicableRoom.getProjectIntentHotelId());
                                    newAddPriceApplicableRoom.setPriceCode(projectHotelPrice.getPriceCode());
                                    newAddPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                                    newAddPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                                    newAddPriceApplicableRoom.setRoomTypeId(priceApplicableRoom.getRoomTypeId());
                                    newAddPriceApplicableRoom.setCreator(RfpConstant.CREATOR);
                                    newAddPriceApplicableRoom.setRoomLevelNo(newPriceApplicableRoom.getRoomLevelNo());
                                    newAddPriceApplicableRoom.setDisplayOrder(j);
                                    levelPriceApplicableRoomMap.get(newPriceApplicableRoom.getRoomLevelNo()).add(newAddPriceApplicableRoom);
                                    roomTypePriceApplicableRoomMap.put(newAddPriceApplicableRoom.getRoomTypeId(), newAddPriceApplicableRoom);
                                }

                                // 价格类型
                                List<ProjectHotelPriceGroup> levelGroupList = newProjectHotelPriceGroupMap.get(newPriceApplicableRoom.getRoomLevelNo());
                                ProjectHotelPriceGroup theSameGroup = null;
                                for(ProjectHotelPriceGroup priceGroup : levelGroupList){
                                    String groupKey = generateHotelGroupPriceKey(priceGroup);
                                    if(hotelPriceKey.equals(groupKey)){
                                        theSameGroup = priceGroup;
                                        break;
                                    }
                                }
                                // 如果存在相同价组，归类到同一价格组
                                if(theSameGroup != null){
                                    projectHotelPrice.setHotelPriceGroupId(theSameGroup.getHotelPriceGroupId());
                                    projectHotelPrice.setRoomLevelNo(theSameGroup.getRoomLevelNo());
                                    // 不存在相同价格组，新增一个价格组
                                } else {
                                    Long hotelPriceGroupId = projectHotelPriceGroupService.getNextHotelPriceGroupId();
                                    ProjectHotelPriceGroup priceGroup = new ProjectHotelPriceGroup();
                                    priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                                    priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                    priceGroup.setProjectId(projectIntentHotel.getProjectId());
                                    priceGroup.setHotelId(projectIntentHotel.getHotelId());
                                    priceGroup.setRoomLevelNo(newPriceApplicableRoom.getRoomLevelNo());
                                    priceGroup.setApplicableWeeks(projectHotelPrice.getApplicableWeeks());
                                    priceGroup.setLra(projectHotelPrice.getLra());
                                    priceGroup.setRemark(projectHotelPrice.getRemark());
                                    priceGroup.setCancelRestrictType(projectHotelPrice.getCancelRestrictType());
                                    priceGroup.setCancelRestrictDay(projectHotelPrice.getCancelRestrictDay());
                                    priceGroup.setCancelRestrictTime(projectHotelPrice.getCancelRestrictTime());
                                    priceGroup.setCreator(RfpConstant.CREATOR);
                                    newProjectHotelPriceGroupMap.get(newPriceApplicableRoom.getRoomLevelNo()).add(priceGroup);

                                    projectHotelPrice.setHotelPriceGroupId(priceGroup.getHotelPriceGroupId());
                                    projectHotelPrice.setRoomLevelNo(priceGroup.getRoomLevelNo());
                                }
                                List<ProjectHotelPrice> hotelPriceList = levelProjectHotelPriceMap.get(newPriceApplicableRoom.getRoomLevelNo());
                                boolean isSavedHotelPrice = false;
                                for(ProjectHotelPrice projectHotelPrice1 : hotelPriceList){
                                    if(projectHotelPrice1.getPriceCode() == null){
                                        continue;
                                    }
                                    if(projectHotelPrice1.getPriceCode().longValue() ==  projectHotelPrice.getPriceCode().longValue()){
                                        isSavedHotelPrice = true;
                                        break;
                                    }
                                }
                                if(!isSavedHotelPrice){
                                    levelProjectHotelPriceMap.get(newPriceApplicableRoom.getRoomLevelNo()).add(projectHotelPrice);
                                }
                                String levelPriceKey = generateLevelPriceKey(projectHotelPrice);
                                levelKeyGroupMap.get(projectHotelPrice.getRoomLevelNo()).add(levelPriceKey);
                            } else {
                                // 未处理过的房型 查看是否存在相同房档， （房型不同，但是对应的key一样，也归类到同一房档）
                                Set<String> roomLevelKeySet = new HashSet<>();
                                List<Long> priceCodeApplicableRoomList = priceApplicableRoomList.stream().map(PriceApplicableRoom::getPriceCode).collect(Collectors.toList());
                                for(ProjectHotelPrice roomProjectHotelPrice : projectHotelPriceList){
                                    if(!priceCodeApplicableRoomList.contains(roomProjectHotelPrice.getPriceCode())){
                                        continue;
                                    }
                                    String roomLevelKey = generateLevelPriceKey(roomProjectHotelPrice);
                                    roomLevelKeySet.add(roomLevelKey);
                                }
                                CollectionUtil.sort(roomLevelKeySet, (o1, o2) -> o1.compareTo(o2));
                                // 查看已经生成房档是否存在相同level key set, 如果存在就归类到对应房档
                                Integer mappedRoomLevelNo = null;
                                for(Integer roomLevelNo : levelKeyGroupMap.keySet()){
                                    Set<String> levelKeySet = levelKeyGroupMap.get(roomLevelNo);
                                    CollectionUtil.sort(levelKeySet, (o1, o2) -> o1.compareTo(o2));
                                    if(CollectionUtil.isEqualList(levelKeySet, roomLevelKeySet)){
                                        mappedRoomLevelNo = roomLevelNo;
                                    }
                                }
                                // 存在符合规则的房档类型
                                if(mappedRoomLevelNo != null){
                                    // 新增房型
                                    for(Long newRoomTypeId : priceApplicableRoomIdList) {
                                        PriceApplicableRoom newPriceApplicableRoom = new PriceApplicableRoom();
                                        if(roomTypePriceApplicableRoomMap.containsKey(newRoomTypeId)){
                                            continue;
                                        }
                                        newPriceApplicableRoom.setApplicableRoomId(priceApplicableRoomService.selectNextApplicableRoomId());
                                        newPriceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                        newPriceApplicableRoom.setPriceCode(projectHotelPrice.getPriceCode());
                                        newPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                                        newPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                                        newPriceApplicableRoom.setRoomTypeId(newRoomTypeId);
                                        newPriceApplicableRoom.setCreator(RfpConstant.CREATOR);
                                        newPriceApplicableRoom.setRoomLevelNo(mappedRoomLevelNo);
                                        newPriceApplicableRoom.setDisplayOrder(j);
                                        levelPriceApplicableRoomMap.get(mappedRoomLevelNo).add(newPriceApplicableRoom);
                                        roomTypePriceApplicableRoomMap.put(newPriceApplicableRoom.getRoomTypeId(), newPriceApplicableRoom);
                                    }

                                    // 价格类型
                                    List<ProjectHotelPriceGroup> levelGroupList = newProjectHotelPriceGroupMap.get(mappedRoomLevelNo);
                                    ProjectHotelPriceGroup theSameGroup = null;
                                    for(ProjectHotelPriceGroup priceGroup : levelGroupList){
                                        String groupKey = generateHotelGroupPriceKey(priceGroup);
                                        if(hotelPriceKey.equals(groupKey)){
                                            theSameGroup = priceGroup;
                                            break;
                                        }
                                    }
                                    // 如果存在相同价组，归类到同一价格组
                                    if(theSameGroup != null){
                                        projectHotelPrice.setHotelPriceGroupId(theSameGroup.getHotelPriceGroupId());
                                        projectHotelPrice.setRoomLevelNo(theSameGroup.getRoomLevelNo());
                                        // 不存在相同价格组，新增一个价格组
                                    } else {
                                        Long hotelPriceGroupId = projectHotelPriceGroupService.getNextHotelPriceGroupId();
                                        ProjectHotelPriceGroup priceGroup = new ProjectHotelPriceGroup();
                                        priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                                        priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                        priceGroup.setProjectId(projectIntentHotel.getProjectId());
                                        priceGroup.setHotelId(projectIntentHotel.getHotelId());
                                        priceGroup.setRoomLevelNo(mappedRoomLevelNo);
                                        priceGroup.setApplicableWeeks(projectHotelPrice.getApplicableWeeks());
                                        priceGroup.setLra(projectHotelPrice.getLra());
                                        priceGroup.setRemark(projectHotelPrice.getRemark());
                                        priceGroup.setCancelRestrictType(projectHotelPrice.getCancelRestrictType());
                                        priceGroup.setCancelRestrictDay(projectHotelPrice.getCancelRestrictDay());
                                        priceGroup.setCancelRestrictTime(projectHotelPrice.getCancelRestrictTime());
                                        priceGroup.setCreator(RfpConstant.CREATOR);
                                        newProjectHotelPriceGroupMap.get(mappedRoomLevelNo).add(priceGroup);

                                        projectHotelPrice.setHotelPriceGroupId(priceGroup.getHotelPriceGroupId());
                                        projectHotelPrice.setRoomLevelNo(priceGroup.getRoomLevelNo());
                                    }
                                    levelProjectHotelPriceMap.get(mappedRoomLevelNo).add(projectHotelPrice);
                                    // 新增房档
                                } else {
                                    Integer currentMaxLevelNo = 0;
                                    for(Integer lNo : levelKeyGroupMap.keySet()){
                                        if(lNo > currentMaxLevelNo){
                                            currentMaxLevelNo = lNo;
                                        }
                                    }
                                    if(currentMaxLevelNo == 0){
                                        logger.error("currentMaxLevelNo is 0"  + JSON.toJSONString(projectHotelPrice));
                                        throw new RuntimeException("当前levelNo为0");
                                    };
                                    int newLevelNo = currentMaxLevelNo + 1;

                                    boolean isProceedPrice = isProceedPrice(levelProjectHotelPriceMap, projectHotelPrice);

                                    // 新增房型
                                    levelPriceApplicableRoomMap.put(newLevelNo, new ArrayList<>());
                                    for(Long newRoomTypeId : priceApplicableRoomIdList) {
                                        PriceApplicableRoom newPriceApplicableRoom = new PriceApplicableRoom();
                                        if(roomTypePriceApplicableRoomMap.containsKey(newRoomTypeId)){
                                            continue;
                                        }
                                        newPriceApplicableRoom.setApplicableRoomId(priceApplicableRoomService.selectNextApplicableRoomId());
                                        newPriceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                        newPriceApplicableRoom.setPriceCode(null);
                                        newPriceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                                        newPriceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                                        newPriceApplicableRoom.setRoomTypeId(newRoomTypeId);
                                        newPriceApplicableRoom.setCreator(RfpConstant.CREATOR);
                                        newPriceApplicableRoom.setRoomLevelNo(newLevelNo);
                                        newPriceApplicableRoom.setDisplayOrder(j);
                                        levelPriceApplicableRoomMap.get(newLevelNo).add(newPriceApplicableRoom);
                                        roomTypePriceApplicableRoomMap.put(newPriceApplicableRoom.getRoomTypeId(), newPriceApplicableRoom);
                                    }

                                    Long hotelPriceGroupId = projectHotelPriceGroupService.getNextHotelPriceGroupId();
                                    ProjectHotelPriceGroup priceGroup = new ProjectHotelPriceGroup();
                                    priceGroup.setHotelPriceGroupId(hotelPriceGroupId);
                                    priceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                                    priceGroup.setProjectId(projectIntentHotel.getProjectId());
                                    priceGroup.setHotelId(projectIntentHotel.getHotelId());
                                    priceGroup.setRoomLevelNo(newLevelNo);
                                    priceGroup.setApplicableWeeks(projectHotelPrice.getApplicableWeeks());
                                    priceGroup.setLra(projectHotelPrice.getLra());
                                    priceGroup.setRemark(projectHotelPrice.getRemark());
                                    priceGroup.setCancelRestrictType(projectHotelPrice.getCancelRestrictType());
                                    priceGroup.setCancelRestrictDay(projectHotelPrice.getCancelRestrictDay());
                                    priceGroup.setCancelRestrictTime(projectHotelPrice.getCancelRestrictTime());
                                    priceGroup.setCreator(RfpConstant.CREATOR);
                                    newProjectHotelPriceGroupMap.put(newLevelNo, new ArrayList<>());
                                    newProjectHotelPriceGroupMap.get(newLevelNo).add(priceGroup);

                                    projectHotelPrice.setRoomLevelNo(priceGroup.getRoomLevelNo());
                                    levelProjectHotelPriceMap.put(newLevelNo, new ArrayList<>());
                                    if(isProceedPrice) {
                                        ProjectHotelPrice newProjectHotelPrice = new ProjectHotelPrice();
                                        BeanUtils.copyProperties(projectHotelPrice, newProjectHotelPrice);
                                        newProjectHotelPrice.setPriceCode(null);
                                        newProjectHotelPrice.setCreator("Convert");
                                        levelProjectHotelPriceMap.get(newLevelNo).add(newProjectHotelPrice);
                                    } else {
                                        levelProjectHotelPriceMap.get(newLevelNo).add(projectHotelPrice);
                                    }
                                    projectHotelPrice.setHotelPriceGroupId(priceGroup.getHotelPriceGroupId());
                                    levelKeyGroupMap.put(newLevelNo, new HashSet<>());
                                    String levelPriceKey = generateLevelPriceKey(projectHotelPrice);
                                    levelKeyGroupMap.get(newLevelNo).add(levelPriceKey);
                                }
                            }
                        }
                        j++;
                    }
                }
                if(isConvertPriceFailed){
                    insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), failedReason);
                    break;
                }
                i++;
                proceedRoomTypeIdList.add(priceCount.getRoomTypeId());
            }
            // 转换价格失败
            if(isConvertPriceFailed){
                continue;
            }
            // 检查是否价格全部处理完
            List<ProjectHotelPrice> updateProjectHotelPriceList = new ArrayList<>();
            for(Integer levelNo : levelProjectHotelPriceMap.keySet()) {
                updateProjectHotelPriceList.addAll(levelProjectHotelPriceMap.get(levelNo));
            }
            if(updateProjectHotelPriceList.size() < projectHotelPriceList.size()){
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "更新价格数" + updateProjectHotelPriceList.size() + "小于原始价格数" + projectHotelPriceList.size() );
                logger.info("转换失败需要人工处理 ProjectIntentHotelId : {}", projectIntentHotel.getProjectIntentHotelId());
                logger.info("Convert ProjectHotelPriceList failed : {}", JSON.toJSONString(updateProjectHotelPriceList));
                continue;
            }
            Boolean validateRoomResult = true;
            int failedRoomLevelNo = 0;
            for(Integer roomLevelNo: levelPriceApplicableRoomMap.keySet()){
                List<PriceApplicableRoom> priceApplicableRoomList  = levelPriceApplicableRoomMap.get(roomLevelNo);
                if(CollectionUtils.isEmpty(priceApplicableRoomList)){
                    validateRoomResult = false;
                    failedRoomLevelNo = roomLevelNo;
                    logger.info("转换失败需要人工处理 "+roomLevelNo+ "房档房型为空 ProjectIntentHotelId : {}", projectIntentHotel.getProjectIntentHotelId());
                    logger.info("Convert ProjectHotelPriceList failed : {}", JSON.toJSONString(updateProjectHotelPriceList));
                    break;
                }
            }
            if(!validateRoomResult){
                insertFailedRecord(projectIntentHotel.getProjectIntentHotelId(), "房档" + failedRoomLevelNo + "房型为空");
                continue;
            }
            // 新增可用日期
            for(Integer priceType : newPriceTypeApplicableDayMap.keySet()){
                List<PriceApplicableDay> priceApplicableDays = newPriceTypeApplicableDayMap.get(priceType);
                priceApplicableDayDao.insertBatchWithId(priceApplicableDays);
            }

            // 删除旧房型 软删除
            priceApplicableRoomDao.updateDeleteByProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());

            // 新增价格组
            for(Integer levelNo : newProjectHotelPriceGroupMap.keySet()){
                List<ProjectHotelPriceGroup> groupList = newProjectHotelPriceGroupMap.get(levelNo);
                Map<Long, String> groupRemarkMap = new HashMap<>();
                // 更新价格
                List<ProjectHotelPrice> priceList = levelProjectHotelPriceMap.get(levelNo);
                for(ProjectHotelPrice price : priceList) {
                    if(price.getPriceCode() == null){
                        projectHotelPriceDao.insert(price);
                    } else {
                        projectHotelPriceDao.updateByPrimaryKeySelective(price);
                    }
                    if(StringUtils.isNotEmpty(price.getRemark())){
                        if(!groupRemarkMap.containsKey(price.getHotelPriceGroupId())){
                            groupRemarkMap.put(price.getHotelPriceGroupId(), "");
                        }
                        String groupRemark = groupRemarkMap.get(price.getHotelPriceGroupId());
                        if(groupRemark.indexOf(price.getRemark()) > 0){
                            continue;
                        }
                        if(groupRemark.length() > 1000){
                            continue;
                        }
                        groupRemarkMap.put(price.getHotelPriceGroupId(), groupRemarkMap.get(price.getHotelPriceGroupId()) + price.getRemark() + "<br/>");
                    }
                }

                // 新增价格组
                for(ProjectHotelPriceGroup priceGroup : groupList) {
                    priceGroup.setCreator("Convert");
                    if(groupRemarkMap.containsKey(priceGroup.getHotelPriceGroupId())){
                        String groupRemark = groupRemarkMap.get(priceGroup.getHotelPriceGroupId());
                        priceGroup.setRemark(groupRemark.substring(0, groupRemark.length() - "<br/>".length()));
                    }
                    projectHotelPriceGroupDao.insertWithId(priceGroup);
                }

                List<PriceApplicableRoom> priceApplicableRoomList = levelPriceApplicableRoomMap.get(levelNo);
                int displayOrder = 1;
                for(PriceApplicableRoom priceApplicableRoom : priceApplicableRoomList){
                    priceApplicableRoom.setDisplayOrder(displayOrder);
                    displayOrder++;
                }
                // 新增房型
                priceApplicableRoomDao.insertBatchWithId(priceApplicableRoomList);

            }
            // 新增默认关注房型
            if(hotelPriceMonitorRoom != null){
                hotelPriceMonitorRoomService.monitorRoomType(hotelPriceMonitorRoom);
            }
        }

        response.setData(projectIntentHotelList.size());
        response.setResult(ReturnResultEnum.FAILED.errorNo);
        response.setMsg("处理完成");
        return response;
    }

    @Override
    public List<HotelMinPriceResponse> selectProjectIntentLowestHotelPrice(List<Long> projectIntentHotelIdList) {
        List<HotelMinPriceResponse> hotelMinPriceResponseList = new ArrayList<>();
        List<ProjectHotelPrice> projectHotelPrices = projectHotelPriceDao.selectInfoByProjectIntentHotelIds(projectIntentHotelIdList);
        Map<Long, ProjectHotelPrice> projectHotelPriceMap = new HashMap<>();
        List<Long> projectHotelPriceGroupIdList = new ArrayList<>();
        for(ProjectHotelPrice projectHotelPrice : projectHotelPrices){
            if(projectHotelPriceMap.containsKey(projectHotelPrice.getProjectIntentHotelId())){
               continue;
            }
            projectHotelPriceMap.put(projectHotelPrice.getProjectIntentHotelId(), projectHotelPrice);
            projectHotelPriceGroupIdList.add(projectHotelPrice.getHotelPriceGroupId());
        }
        if(CollectionUtils.isEmpty(projectHotelPriceGroupIdList)){
            logger.error("不存在报价 {}", projectIntentHotelIdList);
            return hotelMinPriceResponseList;
        }
        List<ProjectHotelPriceGroup> projectHotelPriceGroupList = projectHotelPriceGroupDao.selectInfoByProjectGroupIds(projectHotelPriceGroupIdList);
        Map<Long, ProjectHotelPriceGroup> projectHotelPriceGroupMap = projectHotelPriceGroupList.stream().collect(Collectors.toMap(ProjectHotelPriceGroup::getProjectIntentHotelId, Function.identity()));

        List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponses = priceApplicableRoomDao.queryPriceApplicableRoomInfoByProjectIntentHotelIds(projectIntentHotelIdList);
        Map<Long, List<PriceApplicableRoomInfoResponse>> priceApplicableRoomInfoResponseMap = priceApplicableRoomInfoResponses.stream().collect(Collectors.groupingBy(PriceApplicableRoomInfoResponse::getProjectIntentHotelId));
        for(Long projectIntentHotelId : projectIntentHotelIdList) {
            ProjectHotelPrice projectHotelPrice = projectHotelPriceMap.get(projectIntentHotelId);
            HotelMinPriceResponse hotelMinPriceResponse = new HotelMinPriceResponse();
            if(projectHotelPrice == null){
                logger.error("projectIntentHotelId 的最近报价为空 " + projectIntentHotelId);
                continue;
            }
            hotelMinPriceResponse.setHotelId(projectHotelPrice.getHotelId());
            hotelMinPriceResponse.setMinPrice(projectHotelPrice.getBasePrice());
            hotelMinPriceResponse.setBreakfastNum(projectHotelPrice.getBreakfastNum());
            hotelMinPriceResponse.setProjectIntentHotelId(projectIntentHotelId);
            List<PriceApplicableRoomInfoResponse> roomInfoResponseList = new ArrayList<>();
            List<PriceApplicableRoomInfoResponse> priceApplicableRoomInfoResponseList = priceApplicableRoomInfoResponseMap.get(projectIntentHotelId);
            // 设置最低价格对应房型
            if (CollectionUtils.isNotEmpty(priceApplicableRoomInfoResponseList)) {
                for (PriceApplicableRoomInfoResponse priceApplicableRoomInfoResponse : priceApplicableRoomInfoResponseList) {
                    if (Objects.equals(projectHotelPrice.getRoomLevelNo(), priceApplicableRoomInfoResponse.getRoomLevelNo())) {
                        roomInfoResponseList.add(priceApplicableRoomInfoResponse);
                    }
                }
            }
            hotelMinPriceResponse.setPriceApplicableRoomInfoResponseList(roomInfoResponseList);

            // 设置房型描述 lanyon
            ProjectHotelPriceGroup projectHotelPriceGroup =  projectHotelPriceGroupMap.get(projectIntentHotelId);
            hotelMinPriceResponse.setRoomTypeDesc(projectHotelPriceGroup.getLanyonRoomDesc());
            hotelMinPriceResponseList.add(hotelMinPriceResponse);
        }

        return hotelMinPriceResponseList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateHotelPriceByLastYearBid(Long lastYearProjectId, Long hotelId, Long projectIntentHotelId, UserDTO userDTO) {
        Response response = new Response();

        // 查询去年报价
        ProjectIntentHotelRequest projectIntentHotelRequest = new ProjectIntentHotelRequest();
        projectIntentHotelRequest.setProjectId(lastYearProjectId);
        projectIntentHotelRequest.setHotelId(hotelId);
        List<ProjectHotelPriceLevelResponse> lastYearProjectHotelPriceLevelList =  this.selectProjectHotelPriceLevelList(projectIntentHotelRequest);
        if(CollectionUtils.isEmpty(lastYearProjectHotelPriceLevelList)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("去年报价为空");
        }

        // 新增今年报价
        ProjectIntentHotel projectIntentHotel = projectIntentHotelDao.selectByPrimaryKey(projectIntentHotelId);
        if(projectIntentHotel == null){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("报价ID为空");
        }
        if(!Objects.equals(projectIntentHotel.getHotelId(), hotelId)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("报价ID和酒店不匹配");
        }
        if(!Objects.equals(projectIntentHotel.getBidState(), HotelBidStateEnum.NO_BID.bidState)){
            response.setResult(ReturnResultEnum.FAILED.errorNo);
            response.setMsg("报价状态为" + HotelBidStateEnum.getValueByKey(projectIntentHotel.getBidState()) +", 快速报价");
        }
        // 删除已经存在报价信息
        // 删除房型
        priceApplicableRoomDao.deleteByProjectHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        // 删除价格组
        projectHotelPriceGroupDao.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());
        // 删除价格
        projectHotelPriceDao.deleteByProjectAndHotelId(projectIntentHotel.getProjectId(), projectIntentHotel.getHotelId());

        // 新增价格
        List<PriceApplicableRoom> newPriceApplicableRoomList = new ArrayList<>();
        for(ProjectHotelPriceLevelResponse projectHotelPriceLevel : lastYearProjectHotelPriceLevelList){
            // 新增房型
            if(CollectionUtils.isNotEmpty(projectHotelPriceLevel.getRoomResponseList())){
                for(PriceApplicableRoomInfoResponse room : projectHotelPriceLevel.getRoomResponseList()){
                    // 过滤lanyon 上传报价
                    if(room.getRoomTypeId() == null){
                        continue;
                    }
                    PriceApplicableRoom priceApplicableRoom = new PriceApplicableRoom();
                    priceApplicableRoom.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                    priceApplicableRoom.setProjectId(projectIntentHotel.getProjectId());
                    priceApplicableRoom.setHotelId(projectIntentHotel.getHotelId());
                    priceApplicableRoom.setRoomTypeId(room.getRoomTypeId());
                    priceApplicableRoom.setRoomLevelNo(room.getRoomLevelNo());
                    priceApplicableRoom.setDisplayOrder(room.getDisplayOrder());
                    priceApplicableRoom.setCreator(userDTO.getCreator());
                    newPriceApplicableRoomList.add(priceApplicableRoom);
                }
            }
            // 新增价格组
            List<ProjectHotelPriceGroupResponse> projectHotelPriceGroupList = projectHotelPriceLevel.getProjectHotelPriceGroupResponseList();
            if(CollectionUtils.isEmpty(projectHotelPriceGroupList)) {
                continue;
            }
            for(ProjectHotelPriceGroupResponse projectHotelPriceGroupResponse : projectHotelPriceGroupList){
                ProjectHotelPriceGroup projectHotelPriceGroup = new ProjectHotelPriceGroup();
                BeanUtils.copyProperties(projectHotelPriceGroupResponse, projectHotelPriceGroup);
                Long newHotelPriceGroupId = projectHotelPriceGroupService.getNextHotelPriceGroupId();
                projectHotelPriceGroup.setHotelPriceGroupId(newHotelPriceGroupId);
                projectHotelPriceGroup.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                projectHotelPriceGroup.setProjectId(projectIntentHotel.getProjectId());
                projectHotelPriceGroup.setHotelId(projectIntentHotel.getHotelId());
                projectHotelPriceGroup.setCreator(userDTO.getOperator());
                projectHotelPriceGroup.setModifier(userDTO.getOperator());
                projectHotelPriceGroup.setLanyonRoomDesc(projectHotelPriceLevel.getRoomTypeDesc());
                projectHotelPriceGroupDao.insertWithId(projectHotelPriceGroup);
                if(CollectionUtils.isNotEmpty(projectHotelPriceGroupResponse.getHotelPriceList())){
                    List<ProjectHotelPrice> projectHotelPrices = new ArrayList<>();
                    for(ProjectHotelPrice projectHotelPrice : projectHotelPriceGroupResponse.getHotelPriceList()){
                        ProjectHotelPrice newProjectHotelPrice = new ProjectHotelPrice();
                        BeanUtils.copyProperties(projectHotelPrice, newProjectHotelPrice);
                        newProjectHotelPrice.setHotelPriceGroupId(newHotelPriceGroupId);
                        newProjectHotelPrice.setProjectIntentHotelId(projectIntentHotel.getProjectIntentHotelId());
                        newProjectHotelPrice.setProjectId(projectIntentHotel.getProjectId());
                        newProjectHotelPrice.setHotelId(projectIntentHotel.getHotelId());
                        newProjectHotelPrice.setLastBasePrice(null);
                        newProjectHotelPrice.setCreator(userDTO.getOperator());
                        newProjectHotelPrice.setModifier(userDTO.getOperator());
                        projectHotelPrices.add(newProjectHotelPrice);
                    }
                    projectHotelPriceDao.insertBatch(projectHotelPrices);
                }
            }
        }

        // 新增房型
        if(CollectionUtils.isNotEmpty(newPriceApplicableRoomList)){
            priceApplicableRoomDao.insertBatch(newPriceApplicableRoomList);
        }

        response.setResult(ReturnResultEnum.SUCCESS.errorNo);
        response.setMsg(ReturnResultEnum.SUCCESS.message);

        return response;
    }

    private boolean isTheSameLevelRoomType(Long mappedRoomTypeId, Long compareRoomTypeId, //被比较的房型
                                         List<PriceApplicableRoom> applicableRoomList,
                                           Map<Long, ProjectHotelPrice> projectHotelPriceMap
                                           ){
        HashSet<Long> mappedRoomPriceCodeSet = new HashSet<>(applicableRoomList.stream().filter(o -> o.getRoomTypeId().longValue() == mappedRoomTypeId.longValue()).map(PriceApplicableRoom::getPriceCode).collect(Collectors.toList()));
        HashSet<Long> compareRoomPriceCodeSet = new HashSet<>(applicableRoomList.stream().filter(o -> o.getRoomTypeId().longValue() == compareRoomTypeId) .map(PriceApplicableRoom::getPriceCode).collect(Collectors.toList()));

        List<String> mappedPriceKeyList = new ArrayList<>();
        List<String> comparePriceKeyList = new ArrayList<>();
        for(Long priceCode : mappedRoomPriceCodeSet){
            ProjectHotelPrice hotelPrice = projectHotelPriceMap.get(priceCode);
            if(hotelPrice == null){
                continue;
            }
            String priceKey = generateHotelPriceKey(hotelPrice);
            mappedPriceKeyList.add(priceKey);
        }
        for(Long priceCode : compareRoomPriceCodeSet){
            ProjectHotelPrice hotelPrice = projectHotelPriceMap.get(priceCode);
            if(hotelPrice == null){
                continue;
            }
            String priceKey = generateHotelPriceKey(hotelPrice);
            comparePriceKeyList.add(priceKey);
        }

        if(mappedPriceKeyList.size() != comparePriceKeyList.size()){
            return false;
        }
        CollectionUtil.sort(mappedPriceKeyList, (o1, o2) -> o1.compareTo(o2));
        CollectionUtil.sort(comparePriceKeyList, (o1, o2) -> o1.compareTo(o2));
        if(CollectionUtil.isEqualList(mappedPriceKeyList, comparePriceKeyList)){
            return true;
        }
        return false;
    }
    private boolean isProceedPrice(Map<Integer, List<ProjectHotelPrice>> levelProjectHotelPriceMap, ProjectHotelPrice projectHotelPrice){
        for(Integer levelNo : levelProjectHotelPriceMap.keySet()){
            for(ProjectHotelPrice projectHotelPrice1 : levelProjectHotelPriceMap.get(levelNo)){
                if(projectHotelPrice1.getPriceCode() == null){
                    continue;
                }
                if(projectHotelPrice1.getPriceCode().longValue() == projectHotelPrice.getPriceCode().longValue()){
                    return true;
                }
            }
        }
        return false;
    }

    private String generateHotelPriceKey(ProjectHotelPrice projectHotelPrice){
        return projectHotelPrice.getApplicableWeeks() + "#" + projectHotelPrice.getLra() + "#" + projectHotelPrice.getCancelRestrictType() + "#" + projectHotelPrice.getCancelRestrictDay() + "#" + projectHotelPrice.getCancelRestrictTime();
    }

    private String generateHotelGroupPriceKey(ProjectHotelPriceGroup priceGroup){
        return priceGroup.getApplicableWeeks() + "#" + priceGroup.getLra() + "#" + priceGroup.getCancelRestrictType() + "#" + priceGroup.getCancelRestrictDay() + "#" + priceGroup.getCancelRestrictTime();
    }


    // priceType#breakfastnum#price#groupkey
    private String generateLevelPriceKey(ProjectHotelPrice projectHotelPrice){
        return projectHotelPrice.getPriceType() + "#" + projectHotelPrice.getBreakfastNum() + "#" + projectHotelPrice.getBasePrice() + "#" + generateHotelPriceKey(projectHotelPrice);
    }

    private void insertFailedRecord(Long projectIntentHotelId, String reason){
        ConvertPriceFailedRecord convertPriceFailedRecord = new ConvertPriceFailedRecord();
        convertPriceFailedRecord.setProjectIntentHotelId(projectIntentHotelId);
        convertPriceFailedRecord.setFailedReason(reason);
        convertPriceFailedRecord.setCreator(RfpConstant.CREATOR);
        convertPriceFailedRecordDao.insertData(convertPriceFailedRecord);
    }


}

