package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 首旅如家报价导入服务接口
 */
public interface HomeInnsImportService {

    /**
     * 下载首旅如家报价模板
     */
    void downloadHomeInnsBidTemplate(HttpServletResponse httpServletResponse);

    /**
     * 导入首旅如家报价
     */
    Response importHomeInnsBid(MultipartFile uploadFile, Long projectId, UserDTO userDTO);
}