package com.fangcang.rfp.common.service;

import com.fangcang.rfp.common.dto.common.OrgDTO;
import com.fangcang.rfp.common.dto.common.Response;
import com.fangcang.rfp.common.dto.common.UserDTO;
import com.fangcang.rfp.common.dto.request.*;
import com.fangcang.rfp.common.dto.response.HotelResponse;
import com.fangcang.rfp.common.dto.response.ProjectHotelTentResponse;
import com.fangcang.rfp.common.dto.response.YesterdayProjectHotelResponse;
import com.fangcang.rfp.common.entity.HotelViolationsMonitor;
import com.fangcang.rfp.common.entity.ProjectHotelWeight;
import com.fangcang.rfp.common.entity.ProjectIntentHotel;

import java.util.List;

/** 酒店投标服务
 * @auther chenjianhua
 * @date 2022/10/8
*/
public interface ProjectIntentHotelService {

    /**
     * 查询投标列表
     * @param projectIntentHotelRequest
     * @return
     */
    Response selectProjectHotelTentList(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 判断酒店是否满足企业招标项目的基础信息
     * @param intentHotelRequest
     * @return
     */
    Response isHotelSatisfyProjectBaseInfo(IntentHotelRequest intentHotelRequest);

    /**
     * 确定同意协议
     * @param projectIntentHotelRequest
     * @return
     */
    Response confirmAgreeProtocol(ProjectIntentHotelRequest projectIntentHotelRequest, UserDTO userDTO);

    /**
     * 指派酒店销售跟进人
     * @param hotelConcatAddRequest
     * @return
     */
    Response appointHotelConcatInfo(HotelConcatAddRequest hotelConcatAddRequest);


    /**
     * 根据酒店Id查酒店和房型信息
     * @param projectIntentHotelRequest
     * @return
     */
    Response selectHotelAndRoomListByHotelId(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     * 根据项目id、酒店集合、相关状态查询
     *
     * @param 
     * @return
     */
    Response queryByProjectIdAndHotelIds(QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto);

    /**
     * 根据项目ID和酒店ID查意向酒店信息
     * @param projectIntentHotelRequest
     * @return
     */
    Response queryInfoByProjectIdAndHotelId(ProjectIntentHotelRequest projectIntentHotelRequest);


    /**
     * 项目意向酒店(标书)明细信息：投标策略，报价信息等
     * @param queryProjectIntentHotelDetailDto
     * @return
     */
    Response queryProjectIntentHotelDetailByCondition(QueryProjectIntentHotelDetailDto queryProjectIntentHotelDetailDto);

    /**
     * 更新投标人信息
     */
    void updateBidContactInfo(ProjectIntentHotel projectIntentHotel);

    /**
     * 更新投标人信息
     */
    void updateBidContactInfoOnly(ProjectIntentHotel projectIntentHotel);

    /**
     * 更新酒店集团投标人信息
     */
    void updateHotelGroupBidContactInfoOnly(ProjectIntentHotel projectIntentHotel);
    /**
     * 更新销售人信息
     */
    void updateContactInfoOnly(ProjectIntentHotel projectIntentHotel);



    /**
     * 处理前一年时间
     * @param projectIntentHotelRequest
     */
    public void dealLastYear(ProjectIntentHotelRequest projectIntentHotelRequest);


    /**
     * 根据项目ID和时间查询已中标酒店列表
     * @param projectId
     * @return
     */
    Response selectBidHotelListByIdAndTime(Long projectId);

    /**
     * 查询酒店机构报价项目
     * */
    List<ProjectHotelTentResponse> selectHotelOrgTentList(ProjectIntentHotelRequest projectIntentHotelRequest);

    /**
     *  生成并更新总间夜数量
     */
    int generateAndUpdateTotalRoomNight(YesterdayProjectHotelResponse yesterdayProjectHotelResponse);

    /**
     * 通过违规监控数据统计
     */
    void statByHotelViolationMonitor(ProjectIntentHotel projectIntentHotel, HotelViolationsMonitor hotelViolationsMonitor);

    /**
     * 查询酒店信息
     * @return
     */
    Response selectHotelResponse(Long hotelId);


    /**
     * 根据组件id查询
     */
    ProjectIntentHotel getByProjectIntentHotelId(Long projectIntentHotelId);

    /**
     * 修改报价签约主体信息
     * @param
     * @return
     */
    public Response updateHotelSubject(Long projectIntentHotelId, Long subjectId, String modifier);

    /**
     * 查询报价项目信息
     */
    public Response queryBidProjectInfo(QueryBidProjectInfoRequest queryBidProjectInfoRequest);

    /**
     * 酒店集团审核报价通过
     */
    public Response hotelGroupApproveBid(Long projectIntentHotelId, UserDTO userDTO);

    /**
     * 酒店集团审核 拒绝报价
     */
    public Response hotelGroupRejectBid(Long projectIntentHotelId, String rejectRemark, UserDTO userDTO);

    /**
     * 酒店集团查询报价统计数据
     */
    public Response queryBidStatCount(UserDTO userDTO);

    /**
     * 是否需要酒店集团审核报价
     */
    public boolean isNeedHotelGroupApprove(Long projectId, HotelResponse hotelResponse, UserDTO userDTO);


    /**
     * 清空酒店报价相关数据
     * @param projectIntentHotel
     */
    public void clearProjectHotelBidData(ProjectIntentHotel projectIntentHotel);

    public void setHotelGroupBidContentInfo(ProjectIntentHotel projectIntentHotel, UserDTO userDTO);

    public Response getHotelBidCertInfo(Long projectIntentHotelId, Long hotelId);

    public void batchUpdateProjectWeight(Long projectId, Long hotelId, List<ProjectHotelWeight> projectHotelWeightList);

}
